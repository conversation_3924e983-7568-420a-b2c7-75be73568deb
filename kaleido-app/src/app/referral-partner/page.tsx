'use client';

import { referralApi } from '@/app/referral-partner/services/referralApi';
import { DashboardData } from '@/app/referral-partner/types';
import ReferralPageHeader from '@/components/common/ReferralPageHeader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { useUser } from '@auth0/nextjs-auth0/client';
import { Check, Copy, TrendingUp, Users } from 'lucide-react';
import { useEffect, useState } from 'react';
import { DashboardCharts } from './components/DashboardCharts';
import { ReferralPartnerTabs } from './components/ReferralPartnerTabs';

export default function ReferralPartnerPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [copied, setCopied] = useState(false);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.sub) {
        setError('User not authenticated.');
        return;
      }

      const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

      if (!partnerResponse) {
        setError('No referral partner profile found for your account.');
        return;
      }

      const dashboard = await referralApi.getDashboard(partnerResponse.id);
      setDashboardData(dashboard);
    } catch (err) {
      console.error('Error loading dashboard:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user && !userLoading) {
      loadDashboardData();
    }
  }, [user, userLoading]);

  const copyReferralLink = () => {
    if (!dashboardData) return;
    const referralLink = 'https://kaleidotalent.com/jobs?ref=' + dashboardData.partner.referralCode;
    navigator.clipboard.writeText(referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error || !dashboardData ? (
        <div className="absolute inset-0 h-screen w-screen top-[-10px]">
          {/* Full-page background image */}
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('/images/insights/performance_analytics_turquoise.png')`,
            }}
          >
            {/* Beautiful gradient overlay - similar to PageHeader */}
            <div className="absolute inset-0 bg-gradient-to-t from-purple-950/95 via-purple-800/60 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 via-purple-600/30 to-transparent"></div>

            {/* Additional subtle overlays for depth */}
            <div className="absolute inset-0 bg-gradient-to-br from-pink-900/20 via-transparent to-indigo-900/20"></div>
            <div className="absolute inset-0 bg-gradient-to-tl from-purple-900/10 via-transparent to-pink-900/10"></div>
          </div>

          {/* Content overlay */}
          <div className="relative z-10 flex h-full w-full flex-col items-center justify-center p-8">
            <div className="mb-8 text-center">
              <div className="mb-6 inline-flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm">
                <Users className="h-10 w-10 text-white" />
              </div>
              <h1 className="mb-4 text-3xl font-bold text-white">Activate Referral Program</h1>
              <p className="mx-auto max-w-md text-white/70">
                Join our referral partner program to earn commissions by referring qualified
                candidates to open positions.
              </p>
              {error && (
                <div className="mt-6 rounded-lg bg-red-500/10 backdrop-blur-sm border border-red-500/20 p-4">
                  <p className="text-sm text-red-200">{error}</p>
                </div>
              )}
              <button className="mt-8 inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 px-8 py-3 font-semibold text-white shadow-lg transition-all hover:shadow-xl hover:scale-105">
                Activate Referral Partner
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="h-full flex flex-col relative">
          {/* Hero Header */}
          <ReferralPageHeader
            title="Referral Partner Dashboard"
            description={`Welcome back, ${dashboardData.partner.partnerName}`}
            icon={Users}
            imageSrc="/images/landing/open-jobs/open-jobs-7.webp"
            stats={{
              totalEarnings: dashboardData.earnings.total,
              pendingEarnings: dashboardData.earnings.pending,
              paidEarnings: dashboardData.earnings.paid || 0,
              conversionRate: dashboardData.metrics?.conversionRate || 0,
            }}
          />

          {/* Spacer for fixed header */}
          <div className="h-[320px] flex-shrink-0"></div>

          {/* Tab Navigation */}
          <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur h-12 sm:h-14">
            <div className="container mx-auto px-4 sm:px-6 h-full flex items-center justify-between">
              <ReferralPartnerTabs className="h-full flex-1" />
            </div>
          </div>

          {/* Main Dashboard Content */}
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
              {/* Performance Analytics Dashboard */}
              <div className="mb-8">
                <div className="mb-8 relative">
                  {/* Animated background gradient */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-blue-600/20 blur-3xl opacity-50 animate-pulse"></div>

                  {/* Header content */}
                  <div className="relative">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-4 lg:mb-2 gap-4 lg:gap-0">
                      <div>
                        <h1 className="text-lg sm:text-xl font-bold mb-1 flex items-center gap-2 sm:gap-3">
                          <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                            <TrendingUp className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-white" />
                          </div>
                          <span className="bg-gradient-to-r from-white via-pink-100 to-white bg-clip-text text-transparent">
                            Performance Analytics
                          </span>
                        </h1>
                        <p className="text-xs sm:text-sm text-white/60">
                          Track your referral performance and earnings in real-time
                        </p>
                      </div>

                      {/* Referral Code Badge */}
                      <div className="flex items-center gap-3">
                        <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl px-3 sm:px-4 py-2">
                          <p className="text-xs text-white/60 mb-1">Your Referral Code</p>
                          <div className="flex items-center gap-2">
                            <code className="text-xs sm:text-sm font-mono text-white">
                              {dashboardData.partner.referralCode}
                            </code>
                            <button
                              onClick={copyReferralLink}
                              className="p-1 hover:bg-white/10 rounded transition-colors"
                              title="Copy referral link"
                            >
                              {copied ? (
                                <Check className="w-3 h-3 text-green-400" />
                              ) : (
                                <Copy className="w-3 h-3 text-white/60 hover:text-white" />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Quick stats ribbon */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6 mt-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                        <span className="text-xs text-white/60">Live Dashboard</span>
                      </div>
                      <div className="hidden sm:block h-4 w-px bg-white/20"></div>
                      <span className="text-xs text-white/60">Last updated: Just now</span>
                    </div>
                  </div>
                </div>

                <DashboardCharts data={dashboardData} />
              </div>
            </div>
          </div>
        </div>
      )}
    </AppLayout>
  );
}
