export interface ReferralPartner {
  id: string;
  referralCode: string;
  clientId: string;
  companyId?: string;
  partnerName: string;
  contactEmail: string;
  contactPhone?: string;
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
  isActive: boolean;
  stripeAccountId?: string;
  stripeAccountStatus?: 'pending' | 'active' | 'restricted' | 'disabled';
  settings?: {
    defaultBountyPercentage?: number;
    customBountyRules?: any[];
    paymentPreferences?: {
      method: string;
      details: any;
    };
  };
  dashboardMetrics?: {
    totalReferrals: number;
    successfulPlacements: number;
    conversionRate: number;
    averageBounty: number;
  };
  referrals?: Referral[];
  createdAt: string;
  updatedAt: string;
}

export interface Referral {
  id: string;
  referralPartnerId: string;
  candidateId: string;
  jobId: string;
  companyId?: string;
  referralCode: string;
  status: ReferralStatus;
  bountyAmount?: number;
  bountyCalculation?: {
    baseSalary?: number;
    percentage?: number;
    fixedAmount?: number;
    calculationType: 'PERCENTAGE' | 'FIXED' | 'TIERED';
  };
  candidateAppliedAt?: string;
  candidateHiredAt?: string;
  bountyPaidAt?: string;
  trackingData?: {
    source: string;
    medium: string;
    campaign?: string;
    clickedAt: string;
    ipAddress?: string;
    userAgent?: string;
  };
  createdAt: string;
  updatedAt: string;
  // Relations
  candidate?: {
    id: string;
    fullName: string;
    email: string;
  };
  job?: {
    id: string;
    jobType: string;
    companyName: string;
    department?: string;
  };
  company?: {
    id: string;
    companyName: string;
  };
}

export enum ReferralStatus {
  PENDING = 'PENDING',
  CANDIDATE_APPLIED = 'CANDIDATE_APPLIED',
  CANDIDATE_INTERVIEWED = 'CANDIDATE_INTERVIEWED',
  CANDIDATE_HIRED = 'CANDIDATE_HIRED',
  BOUNTY_APPROVED = 'BOUNTY_APPROVED',
  BOUNTY_PAID = 'BOUNTY_PAID',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
}

export interface ReferralEarnings {
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
  referrals: Array<{
    id: string;
    candidateName: string;
    jobTitle: string;
    status: string;
    bountyAmount: number | null;
    createdAt: string;
  }>;
}

export interface DashboardData {
  partner: ReferralPartner & {
    referrals?: Array<{
      id: string;
      candidateName: string;
      jobTitle: string;
      status: string;
      bountyAmount: number | null;
      createdAt: string;
    }>;
  };
  metrics: ReferralPartner['dashboardMetrics'];
  earnings: {
    total: number;
    pending: number;
    paid: number;
  };
}
