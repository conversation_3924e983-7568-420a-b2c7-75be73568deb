'use client';

import { referralApi } from '@/app/referral-partner/services/referralApi';
import { ReferralPartner } from '@/app/referral-partner/types';
import ReferralPageHeader from '@/components/common/ReferralPageHeader';
import { JobDetailsSlider } from '@/components/ReferralJobs/JobDetailsSlider';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useInfiniteLoopProtection } from '@/hooks/useInfiniteLoopProtection';
import { useUser } from '@auth0/nextjs-auth0/client';
import { motion } from 'framer-motion';
import {
  Briefcase,
  Building2,
  Check,
  ChevronLeft,
  ChevronRight,
  Clock,
  Coins,
  Copy,
  MapPin,
  Search,
  Users,
  X,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { ReferralPartnerTabs } from '../components/ReferralPartnerTabs';

interface Job {
  id: string;
  jobType: string;
  department: string;
  companyName: string;
  location?: string[];
  salaryRange?: string;
  experienceLevel?: string;
  typeOfJob?: string;
  isPublished: boolean;
  slug?: string;
  createdAt: string;
  referralSettings?: {
    acceptsReferrals: boolean;
    bountyConfiguration?: {
      type: 'PERCENTAGE' | 'FIXED';
      value: number;
    };
  };
  metrics?: {
    views: number;
    applications: number;
  };
}

const ITEMS_PER_PAGE = 10;

const formatJobType = (type?: string): string => {
  if (!type) return 'Full-time';

  const typeMap: Record<string, string> = {
    FULL_TIME: 'Full-time',
    PART_TIME: 'Part-time',
    CONTRACT: 'Contract',
    FREELANCE: 'Freelance',
    INTERNSHIP: 'Internship',
    TEMPORARY: 'Temporary',
    VOLUNTEER: 'Volunteer',
    REMOTE: 'Remote',
  };

  return (
    typeMap[type] ||
    type
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase())
  );
};

export default function ReferralJobBoardPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [totalJobs, setTotalJobs] = useState(0);
  const [partner, setPartner] = useState<ReferralPartner | null>(null);
  const [copiedJobId, setCopiedJobId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [isJobDetailsVisible, setIsJobDetailsVisible] = useState(false);

  // Add infinite loop protection
  useInfiniteLoopProtection('ReferralJobBoardPage', {
    maxRenders: 30,
    timeWindow: 3000,
    onLoopDetected: () => {
      console.error('Infinite loop detected in ReferralJobBoardPage');
      setError('An error occurred while loading the page. Please refresh.');
    },
  });

  const loadData = useCallback(
    async (page: number = 1, search: string = '') => {
      try {
        setLoading(true);
        setError(null);

        if (!user?.sub) {
          setError('User not authenticated.');
          setLoading(false);
          return;
        }

        // Get partner data if not already loaded
        if (!partner) {
          const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

          if (!partnerResponse) {
            setError('No referral partner profile found.');
            setLoading(false);
            return;
          }

          setPartner(partnerResponse);
        }

        // Calculate offset for pagination
        const offset = (page - 1) * ITEMS_PER_PAGE;

        // Fetch published jobs from the referral-specific endpoint with pagination
        const jobsResponse = await referralApi.getAvailableJobs({
          limit: ITEMS_PER_PAGE,
          offset,
          sort: 'createdAt',
          order: 'DESC',
          search: search || undefined,
        });

        // The backend now returns paginated jobs
        const eligibleJobs = jobsResponse.jobs || [];
        const total = jobsResponse.total || 0;

        setJobs(eligibleJobs);
        setTotalJobs(total);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load jobs. Please try again later.');
      } finally {
        setLoading(false);
      }
    },
    [user, partner]
  );

  useEffect(() => {
    if (user && !userLoading) {
      loadData(currentPage, searchQuery);
    }
  }, [user, userLoading, currentPage, searchQuery]);

  const handleSearch = () => {
    if (searchInput.length === 0 || searchInput.length >= 3) {
      setSearchQuery(searchInput);
      setCurrentPage(1);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalJobs / ITEMS_PER_PAGE);

  const getReferralLink = (jobId: string) => {
    if (!partner) return '';
    return `https://app.kaleidotalent.com/open-jobs/${jobId}?referral=${partner.referralCode}`;
  };

  const copyReferralLink = (jobId: string) => {
    const link = getReferralLink(jobId);
    navigator.clipboard.writeText(link);
    setCopiedJobId(jobId);
    setTimeout(() => setCopiedJobId(null), 2000);
  };

  const formatBounty = (job: Job) => {
    const bounty = job.referralSettings?.bountyConfiguration;
    if (!bounty) return '10% commission';

    if (bounty.type === 'PERCENTAGE') {
      return `${bounty.value}%`;
    } else {
      return `$${bounty.value.toLocaleString()}`;
    }
  };

  const isHighValueBounty = (job: Job) => {
    const bounty = job.referralSettings?.bountyConfiguration;
    if (!bounty) return false;

    if (bounty.type === 'PERCENTAGE') {
      return bounty.value >= 15; // 15% or higher
    } else {
      return bounty.value >= 5000; // $5000 or higher
    }
  };

  const handleJobClick = (job: Job) => {
    setSelectedJob(job);
    setIsJobDetailsVisible(true);
  };

  const handleCloseJobDetails = () => {
    setIsJobDetailsVisible(false);
    setSelectedJob(null);
  };

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error ? (
        <div className="relative flex h-full w-full items-center justify-center p-3">
          <Card className="max-w-md border-white/10 bg-red-900/20 backdrop-blur-lg">
            <CardContent className="p-8 text-center">
              <p className="text-white/80">{error}</p>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="h-full flex flex-col relative">
          {/* Hero Header */}
          <ReferralPageHeader
            title="Referral Job Board"
            description="Browse available positions and share your referral links"
            icon={Briefcase}
          />

          {/* Spacer for fixed header */}
          <div className="h-[320px] flex-shrink-0"></div>

          {/* Tab Navigation */}
          <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur h-12 sm:h-14">
            <div className="container mx-auto px-4 sm:px-6 h-full flex items-center justify-between">
              <ReferralPartnerTabs className="h-full flex-1" />
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
              <motion.div
                className="relative"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {/* Tab Body Title */}
                <div className="mb-8 relative">
                  {/* Animated background gradient */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-blue-600/20 blur-3xl opacity-50 animate-pulse"></div>

                  {/* Header content */}
                  <div className="relative">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-4 lg:mb-2 gap-4 lg:gap-0">
                      <div>
                        <h1 className="text-lg sm:text-xl font-bold mb-1 flex items-center gap-2 sm:gap-3">
                          <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                            <Briefcase className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-white" />
                          </div>
                          <span className="bg-gradient-to-r from-white via-pink-100 to-white bg-clip-text text-transparent">
                            Job Opportunities
                          </span>
                        </h1>
                        <p className="text-xs sm:text-sm text-white/60">
                          Browse available positions and generate your referral links
                        </p>
                      </div>
                    </div>

                    {/* Quick stats ribbon */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6 mt-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                        <span className="text-xs text-white/60">Live Job Feed</span>
                      </div>
                      <div className="hidden sm:block h-4 w-px bg-white/20"></div>
                      <span className="text-xs text-white/60">Earn up to 20% commission</span>
                    </div>
                  </div>
                </div>

                {/* Jobs Table */}
                <Card className="border-white/10 bg-black/10 backdrop-blur-lg">
                  {/* Table Header with Search and Controls */}
                  <div className="p-4 border-b border-white/10">
                    <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
                      {/* Search Bar */}
                      <div className="relative flex-1 max-w-lg">
                        <div className="flex items-center bg-black/10 backdrop-blur-md border border-white/20 rounded-full overflow-hidden focus-within:ring-2 focus-within:ring-purple-500">
                          <Search className="w-5 h-5 text-gray-500 ml-4" />
                          <input
                            type="search"
                            placeholder="Search jobs (min. 3 characters)..."
                            className="flex-1 px-4 py-3 bg-transparent text-white placeholder:text-white/50 focus:outline-none"
                            value={searchInput}
                            onChange={e => setSearchInput(e.target.value)}
                            onKeyPress={handleKeyPress}
                          />
                          {searchInput && (
                            <button
                              onClick={() => {
                                setSearchInput('');
                                setSearchQuery('');
                              }}
                              className="p-2 text-gray-500 hover:text-white transition-colors"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          )}
                          <Button
                            onClick={handleSearch}
                            size="sm"
                            className="m-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-full border-0"
                            disabled={searchInput.length > 0 && searchInput.length < 3}
                          >
                            <Search className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Results Count and Pagination */}
                      <div className="flex items-center gap-4">
                        <div className="text-sm text-white/60">
                          Showing {jobs.length} of {totalJobs} jobs
                        </div>

                        {totalPages > 1 && (
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                              disabled={currentPage === 1}
                              className="border-white/20 bg-white/5 hover:bg-white/10 text-white disabled:opacity-50"
                            >
                              <ChevronLeft className="w-4 h-4" />
                            </Button>
                            <div className="flex items-center px-3 py-1 text-sm text-white/80 bg-white/5 rounded-md">
                              {currentPage} / {totalPages}
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                              disabled={currentPage === totalPages}
                              className="border-white/20 bg-white/5 hover:bg-white/10 text-white disabled:opacity-50"
                            >
                              <ChevronRight className="w-4 h-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-white/10">
                          <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                            <div className="flex items-center gap-2">
                              <Briefcase className="w-4 h-4" />
                              Job Title
                            </div>
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                            <div className="flex items-center gap-2">
                              <Building2 className="w-4 h-4" />
                              Company
                            </div>
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                            <div className="flex items-center gap-2">
                              <Users className="w-4 h-4" />
                              Department
                            </div>
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                            <div className="flex items-center gap-2">
                              <MapPin className="w-4 h-4" />
                              Location
                            </div>
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                            <div className="flex items-center gap-2">
                              <Coins className="w-4 h-4" />
                              Commission
                            </div>
                          </th>
                          <th className="px-4 py-3 text-left text-sm font-medium text-white/70">
                            <div className="flex items-center gap-2">
                              <Clock className="w-4 h-4" />
                              Type
                            </div>
                          </th>
                          <th className="px-4 py-3 text-center text-sm font-medium text-white/70">
                            Action
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {jobs.map(job => (
                          <tr
                            key={job.id}
                            className="border-b border-white/5 hover:bg-white/5 transition-colors"
                          >
                            <td className="px-4 py-3 text-sm">
                              <button
                                onClick={() => handleJobClick(job)}
                                className="text-white hover:text-purple-400 transition-colors font-medium hover:underline text-left"
                              >
                                {job.jobType}
                              </button>
                            </td>
                            <td className="px-4 py-3 text-sm">
                              <button
                                onClick={() => handleJobClick(job)}
                                className="text-white/80 hover:text-purple-400 transition-colors hover:underline text-left"
                              >
                                {job.companyName}
                              </button>
                            </td>
                            <td className="px-4 py-3 text-sm">
                              <button
                                onClick={() => handleJobClick(job)}
                                className="text-white/60 hover:text-purple-400 transition-colors hover:underline text-left"
                              >
                                {job.department}
                              </button>
                            </td>
                            <td className="px-4 py-3 text-sm text-white/60">
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3" />
                                {job.location?.length ? job.location[0] : 'Remote'}
                              </div>
                            </td>
                            <td className="px-4 py-3 text-sm">
                              <span
                                className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                                  isHighValueBounty(job)
                                    ? 'bg-yellow-500/20 text-yellow-300'
                                    : 'bg-green-500/20 text-green-300'
                                }`}
                              >
                                <Coins className="w-3 h-3" />
                                {formatBounty(job)}
                              </span>
                            </td>
                            <td className="px-4 py-3 text-sm text-white/60">
                              {formatJobType(job.typeOfJob)}
                            </td>
                            <td className="px-4 py-3 text-center">
                              <button
                                onClick={() => copyReferralLink(job.id)}
                                className="inline-flex items-center justify-center p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-all"
                                title="Copy referral link"
                              >
                                {copiedJobId === job.id ? (
                                  <Check className="w-4 h-4 text-green-400" />
                                ) : (
                                  <Copy className="w-4 h-4" />
                                )}
                              </button>
                            </td>
                          </tr>
                        ))}
                        {jobs.length === 0 && (
                          <tr>
                            <td colSpan={7} className="px-4 py-8 text-center text-white/60">
                              No jobs found matching your search criteria
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      )}

      {/* Job Details Slider */}
      <JobDetailsSlider
        isVisible={isJobDetailsVisible}
        job={selectedJob}
        onClose={handleCloseJobDetails}
      />
    </AppLayout>
  );
}
