'use client';

import { referralApi } from '@/app/referral-partner/services/referralApi';
import {
  Referral as APIReferral,
  ReferralPartner,
  ReferralStatus,
} from '@/app/referral-partner/types';
import ReferralPageHeader from '@/components/common/ReferralPageHeader';
import StripeConnectBanner from '@/components/referral-partner/StripeConnectBanner';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useUser } from '@auth0/nextjs-auth0/client';
import { format, parseISO, subMonths } from 'date-fns';
import { motion } from 'framer-motion';
import { CheckCircle, CreditCard, DollarSign, Download, TrendingUp } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { ReferralPartnerTabs } from '../components/ReferralPartnerTabs';

interface EarningsData {
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
  totalReferrals: number;
  hiredReferrals: number;
  conversionRate: number;
  monthlyEarnings: {
    month: string;
    earnings: number;
    referrals: number;
  }[];
}

export default function EarningsPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [partner, setPartner] = useState<ReferralPartner | null>(null);
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);
  const [stripeConnected, setStripeConnected] = useState(false);
  const [stripeLoading, setStripeLoading] = useState(false);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.sub) {
        setError('User not authenticated.');
        return;
      }

      // Get partner data
      const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

      if (!partnerResponse) {
        setError('No referral partner profile found.');
        return;
      }

      setPartner(partnerResponse);

      // Check if Stripe is connected
      if (partnerResponse.stripeAccountId) {
        setStripeConnected(true);
      }

      // Fetch earnings data
      const earningsResponse = await referralApi.getEarnings(partnerResponse.id);

      // Fetch referrals
      const referralsResponse = await referralApi.getReferrals({
        referralPartnerId: partnerResponse.id,
      });

      // Process the data into the format expected by the UI
      const processedEarningsData: EarningsData = {
        totalEarnings: earningsResponse.totalEarnings || 0,
        pendingEarnings: earningsResponse.pendingEarnings || 0,
        paidEarnings: earningsResponse.paidEarnings || 0,
        totalReferrals: referralsResponse.length,
        hiredReferrals: referralsResponse.filter(r => r.status === ReferralStatus.CANDIDATE_HIRED)
          .length,
        conversionRate:
          referralsResponse.length > 0
            ? (referralsResponse.filter(r => r.status === ReferralStatus.CANDIDATE_HIRED).length /
                referralsResponse.length) *
              100
            : 0,
        monthlyEarnings: generateMonthlyEarnings(referralsResponse),
      };

      setEarningsData(processedEarningsData);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load earnings data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [user?.sub]);

  // Generate monthly earnings from referrals
  const generateMonthlyEarnings = (referrals: APIReferral[]) => {
    const monthlyData: { [key: string]: { earnings: number; referrals: number } } = {};

    // Get last 6 months
    for (let i = 5; i >= 0; i--) {
      const month = format(subMonths(new Date(), i), 'yyyy-MM');
      monthlyData[month] = { earnings: 0, referrals: 0 };
    }

    // Calculate earnings per month
    referrals.forEach(referral => {
      if (referral.bountyAmount && referral.candidateHiredAt) {
        const month = format(parseISO(referral.candidateHiredAt), 'yyyy-MM');
        if (monthlyData[month]) {
          monthlyData[month].earnings += referral.bountyAmount;
          monthlyData[month].referrals += 1;
        }
      }
    });

    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      earnings: data.earnings,
      referrals: data.referrals,
    }));
  };

  useEffect(() => {
    if (user && !userLoading) {
      loadData();
    }
  }, [user, userLoading, loadData]);

  const handleStripeConnect = async () => {
    setStripeLoading(true);
    try {
      if (partner) {
        // Call API to create Stripe Connect account and get onboarding link
        const response = await referralApi.createStripeConnectAccount(partner.id);
        if (response.url) {
          window.location.href = response.url;
        }
      }
    } catch (error) {
      console.error('Error connecting Stripe:', error);
    } finally {
      setStripeLoading(false);
    }
  };

  const handleRequestPayout = async () => {
    if (!partner || !earningsData) return;

    try {
      await referralApi.requestPayment(partner.id, earningsData.pendingEarnings);
      // Refresh data
      await loadData();
    } catch (error) {
      console.error('Error requesting payout:', error);
    }
  };

  const downloadReport = () => {
    // Implement CSV download functionality
    console.log('Downloading earnings report...');
  };

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error ? (
        <div className="relative flex h-full w-full items-center justify-center p-3">
          <Card className="max-w-md border-white/10 bg-red-900/20 backdrop-blur-lg">
            <CardContent className="p-8 text-center">
              <p className="text-white/80">{error}</p>
            </CardContent>
          </Card>
        </div>
      ) : earningsData ? (
        <div className="h-full flex flex-col relative">
          {/* Hero Header */}
          <ReferralPageHeader
            title="Earnings Dashboard"
            description="Track your referral earnings and payment history"
            icon={DollarSign}
            stats={{
              totalEarnings: earningsData.totalEarnings,
              pendingEarnings: earningsData.pendingEarnings,
              paidEarnings: earningsData.paidEarnings,
              conversionRate: earningsData.conversionRate,
            }}
          />

          {/* Spacer for fixed header */}
          <div className="h-[320px] flex-shrink-0"></div>

          {/* Tab Navigation */}
          <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur h-12 sm:h-14">
            <div className="container mx-auto px-4 sm:px-6 h-full flex items-center justify-between">
              <ReferralPartnerTabs className="h-full flex-1" />
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
              <motion.div
                className="relative"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {/* Tab Body Title */}
                <div className="mb-8 relative">
                  {/* Animated background gradient */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-blue-600/20 blur-3xl opacity-50 animate-pulse"></div>

                  {/* Header content */}
                  <div className="relative">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-4 lg:mb-2 gap-4 lg:gap-0">
                      <div>
                        <h1 className="text-lg sm:text-xl font-bold mb-1 flex items-center gap-2 sm:gap-3">
                          <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                            <DollarSign className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-white" />
                          </div>
                          <span className="bg-gradient-to-r from-white via-pink-100 to-white bg-clip-text text-transparent">
                            Earnings Management
                          </span>
                        </h1>
                        <p className="text-xs sm:text-sm text-white/60">
                          Track your referral earnings and manage payouts
                        </p>
                      </div>
                    </div>

                    {/* Quick stats ribbon */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6 mt-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                        <span className="text-xs text-white/60">Real-time Earnings</span>
                      </div>
                      <div className="hidden sm:block h-4 w-px bg-white/20"></div>
                      <span className="text-xs text-white/60">Secure payouts via Stripe</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between mb-6">
                  <div></div>

                  <div className="flex items-center gap-4">
                    {/* Stripe Status Indicator */}
                    <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-black/5 border border-white/10">
                      {stripeConnected ? (
                        <>
                          <CheckCircle className="w-4 h-4 text-green-400" />
                          <span className="text-xs text-green-300">Connected to Stripe</span>
                        </>
                      ) : (
                        <>
                          <CreditCard className="w-4 h-4 text-orange-400" />
                          <span className="text-xs text-orange-300">Stripe Not Connected</span>
                        </>
                      )}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      className="border-white/20 bg-white/5 hover:bg-white/10 text-white"
                      onClick={downloadReport}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
                {/* Stripe Connect Banner */}
                <StripeConnectBanner
                  isConnected={stripeConnected}
                  isLoading={stripeLoading}
                  onConnect={handleStripeConnect}
                />

                {/* Payout Section - only show if Stripe is connected and there are pending earnings */}
                {stripeConnected && earningsData.pendingEarnings > 0 && (
                  <div className="bg-purple-500/5 border border-purple-500/20 rounded-lg p-6 mb-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-white">Available for Payout</p>
                        <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-white to-purple-200">
                          ${earningsData.pendingEarnings.toLocaleString()}
                        </p>
                      </div>
                      <Button
                        onClick={handleRequestPayout}
                        className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                      >
                        Request Payout
                      </Button>
                    </div>
                  </div>
                )}

                <div className="space-y-6">
                  {/* Monthly Trend Chart */}
                  <Card className="border-white/10 bg-black/5 backdrop-blur-lg">
                    <CardHeader>
                      <CardTitle className="text-lg text-white flex items-center">
                        <TrendingUp className="w-5 h-5 mr-2 text-pink-400" />
                        Monthly Earnings Trend
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-64 flex items-end justify-between gap-4">
                        {earningsData.monthlyEarnings.map((month, index) => {
                          const maxEarnings = Math.max(
                            ...earningsData.monthlyEarnings.map(m => m.earnings)
                          );
                          const heightPercentage =
                            maxEarnings > 0 ? (month.earnings / maxEarnings) * 100 : 0;

                          return (
                            <div key={month.month} className="flex-1 flex flex-col items-center">
                              <div className="relative w-full">
                                <motion.div
                                  initial={{ height: 0 }}
                                  animate={{ height: `${heightPercentage * 2}px` }}
                                  transition={{ delay: index * 0.1, duration: 0.5 }}
                                  className="bg-gradient-to-t from-purple-600 to-pink-500 rounded-t-lg relative group cursor-pointer"
                                >
                                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-gray-900/90 px-2 py-1 rounded text-xs whitespace-nowrap">
                                    ${month.earnings.toLocaleString()}
                                  </div>
                                </motion.div>
                              </div>
                              <p className="text-xs text-white/60 mt-2">
                                {format(parseISO(month.month + '-01'), 'MMM')}
                              </p>
                              <p className="text-xs text-white/40">{month.referrals} referrals</p>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      ) : null}
    </AppLayout>
  );
}
