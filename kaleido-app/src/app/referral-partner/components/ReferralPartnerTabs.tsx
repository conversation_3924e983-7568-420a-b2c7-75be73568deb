'use client';

import React from 'react';
import { TabNavigation, TabItem } from '@/components/common/TabNavigation';
import { LayoutDashboard, Briefcase, FileText, DollarSign } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';

export interface ReferralPartnerTabsProps {
  className?: string;
}

export const ReferralPartnerTabs: React.FC<ReferralPartnerTabsProps> = ({ className = '' }) => {
  const router = useRouter();
  const pathname = usePathname();

  const tabs: TabItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: LayoutDashboard,
    },
    {
      id: 'jobs',
      label: 'Referral Jobs',
      icon: Briefcase,
    },
    {
      id: 'referrals',
      label: 'Referral Details',
      icon: FileText,
    },
    {
      id: 'earnings',
      label: 'Earnings & Payouts',
      icon: DollarSign,
    },
  ];

  const getActiveTab = () => {
    if (pathname === '/referral-partner') return 'dashboard';
    if (pathname === '/referral-partner/jobs') return 'jobs';
    if (pathname === '/referral-partner/referrals') return 'referrals';
    if (pathname === '/referral-partner/earnings') return 'earnings';
    return 'dashboard';
  };

  const handleTabChange = (tabId: string) => {
    const routes: Record<string, string> = {
      dashboard: '/referral-partner',
      jobs: '/referral-partner/jobs',
      referrals: '/referral-partner/referrals',
      earnings: '/referral-partner/earnings',
    };

    router.push(routes[tabId]);
  };

  return (
    <TabNavigation
      tabs={tabs}
      activeTab={getActiveTab()}
      onTabChange={handleTabChange}
      variant="gradient"
      gradientColors={{
        from: 'from-purple-600',
        to: 'to-pink-600',
      }}
      className={className}
      showBorder={true}
      showSeparators={true}
      mobileCollapse={true}
    />
  );
};
