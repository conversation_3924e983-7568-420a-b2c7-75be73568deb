'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  RadialBarChart,
  RadialBar,
} from 'recharts';
import { DashboardData } from '../types';
import { TrendingUp, Users, DollarSign, Award } from 'lucide-react';

interface DashboardChartsProps {
  data: DashboardData;
}

export const DashboardCharts: React.FC<DashboardChartsProps> = ({ data }) => {
  // Mock data for charts - in production, this would come from your API
  const monthlyEarnings = [
    { month: 'Jan', earnings: 0, referrals: 0 },
    { month: 'Feb', earnings: 0, referrals: 0 },
    { month: 'Mar', earnings: 0, referrals: 0 },
    { month: 'Apr', earnings: 0, referrals: 0 },
    { month: 'May', earnings: 0, referrals: 0 },
    { month: 'Jun', earnings: 0, referrals: 0 },
  ];

  const hasReferralData = (data.metrics?.totalReferrals || 0) > 0;

  const referralStatus = hasReferralData
    ? [
        { name: 'Pending', value: 1, color: '#fbbf24' },
        { name: 'Interviewed', value: 0, color: '#60a5fa' },
        { name: 'Hired', value: 0, color: '#34d399' },
        { name: 'Rejected', value: 0, color: '#f87171' },
      ]
    : [{ name: 'No Data', value: 1, color: '#6b7280' }];

  const performanceMetrics = [
    {
      name: 'Conversion Rate',
      value: data.metrics?.conversionRate || 0,
      fill: '#8b5cf6',
      max: 100,
    },
  ];

  const topPerformingJobs = [{ job: 'No data yet', referrals: 0, hires: 0 }];

  return (
    <div className="space-y-6">
      {/* Summary Cards Row */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-white/5 bg-gradient-to-br from-green-500/10 to-green-600/5 hover:from-green-500/15 hover:to-green-600/10 transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs font-medium text-white/70 group-hover:text-white/90 transition-colors">
              Total Earnings
            </CardTitle>
            <DollarSign className="h-4 w-4 text-green-400 group-hover:text-green-300 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-white group-hover:text-white/95 transition-colors">
              ${data.earnings.total}
            </div>
            <p className="text-[10px] text-white/50 mt-1 group-hover:text-white/70 transition-colors">
              All time earnings
            </p>
          </CardContent>
        </Card>

        <Card className="border-white/5 bg-gradient-to-br from-yellow-500/10 to-yellow-600/5 hover:from-yellow-500/15 hover:to-yellow-600/10 transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs font-medium text-white/70 group-hover:text-white/90 transition-colors">
              Pending Earnings
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-yellow-400 group-hover:text-yellow-300 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-white group-hover:text-white/95 transition-colors">
              ${data.earnings.pending}
            </div>
            <p className="text-[10px] text-white/50 mt-1 group-hover:text-white/70 transition-colors">
              Awaiting payment
            </p>
          </CardContent>
        </Card>

        <Card className="border-white/5 bg-gradient-to-br from-blue-500/10 to-blue-600/5 hover:from-blue-500/15 hover:to-blue-600/10 transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs font-medium text-white/70 group-hover:text-white/90 transition-colors">
              Total Referrals
            </CardTitle>
            <Users className="h-4 w-4 text-blue-400 group-hover:text-blue-300 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-white group-hover:text-white/95 transition-colors">
              {data.metrics?.totalReferrals || 0}
            </div>
            <p className="text-[10px] text-white/50 mt-1 group-hover:text-white/70 transition-colors">
              Candidates referred
            </p>
          </CardContent>
        </Card>

        <Card className="border-white/5 bg-gradient-to-br from-purple-500/10 to-purple-600/5 hover:from-purple-500/15 hover:to-purple-600/10 transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs font-medium text-white/70 group-hover:text-white/90 transition-colors">
              Success Rate
            </CardTitle>
            <Award className="h-4 w-4 text-purple-400 group-hover:text-purple-300 transition-colors" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold text-white group-hover:text-white/95 transition-colors">
              {data.metrics?.conversionRate?.toFixed(1) || 0}%
            </div>
            <p className="text-[10px] text-white/50 mt-1 group-hover:text-white/70 transition-colors">
              Placement rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Monthly Earnings Trend */}
        <Card className="border-white/5 bg-white/5 backdrop-blur hover:bg-white/[0.07] transition-all duration-300">
          <CardHeader>
            <CardTitle className="text-sm font-semibold text-white/90">
              Monthly Earnings Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <AreaChart data={monthlyEarnings}>
                <defs>
                  <linearGradient id="colorEarnings" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="month" stroke="#9ca3af" tick={{ fontSize: 11 }} />
                <YAxis stroke="#9ca3af" tick={{ fontSize: 11 }} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(17, 24, 39, 0.9)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '8px',
                    backdropFilter: 'blur(10px)',
                  }}
                  labelStyle={{ color: '#e5e7eb', fontSize: 12 }}
                  itemStyle={{ color: '#e5e7eb', fontSize: 11 }}
                />
                <Area
                  type="monotone"
                  dataKey="earnings"
                  stroke="#8b5cf6"
                  fillOpacity={1}
                  fill="url(#colorEarnings)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Referral Status Breakdown */}
        <Card className="border-white/5 bg-white/5 backdrop-blur hover:bg-white/[0.07] transition-all duration-300">
          <CardHeader>
            <CardTitle className="text-sm font-semibold text-white/90">
              Referral Status Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            {hasReferralData ? (
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={referralStatus}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={70}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {referralStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(17, 24, 39, 0.9)',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '8px',
                      backdropFilter: 'blur(10px)',
                      fontSize: 12,
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex flex-col items-center justify-center h-[250px] text-center">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center mb-4">
                  <Users className="w-8 h-8 text-white/40" />
                </div>
                <h3 className="text-sm font-medium text-white/80 mb-2">No Referral Data</h3>
                <p className="text-xs text-white/60 max-w-[200px] leading-relaxed">
                  Start referring candidates to see status breakdown here
                </p>
                <div className="flex items-center gap-4 mt-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                    <span className="text-xs text-white/60">Pending</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                    <span className="text-xs text-white/60">Interview</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-green-400"></div>
                    <span className="text-xs text-white/60">Hired</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* Conversion Rate Gauge */}
        <Card className="border-white/5 bg-gradient-to-br from-purple-500/10 via-purple-600/5 to-pink-500/10 backdrop-blur hover:from-purple-500/15 hover:to-pink-500/15 transition-all duration-300 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-50"></div>
          <CardHeader className="relative">
            <CardTitle className="text-sm font-semibold text-white/90 flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-pink-400"></div>
              Conversion Rate
            </CardTitle>
          </CardHeader>
          <CardContent className="relative">
            <ResponsiveContainer width="100%" height={180}>
              <RadialBarChart
                cx="50%"
                cy="50%"
                innerRadius="60%"
                outerRadius="90%"
                data={performanceMetrics}
              >
                <defs>
                  <linearGradient id="conversionGradient" x1="0" y1="0" x2="1" y2="1">
                    <stop offset="0%" stopColor="#8b5cf6" />
                    <stop offset="100%" stopColor="#ec4899" />
                  </linearGradient>
                </defs>
                <RadialBar dataKey="value" cornerRadius={10} fill="url(#conversionGradient)" />
                <text
                  x="50%"
                  y="50%"
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className="fill-white"
                >
                  <tspan x="50%" dy="-0.5em" fontSize="20" fontWeight="bold">
                    {data.metrics?.conversionRate?.toFixed(1) || 0}%
                  </tspan>
                  <tspan x="50%" dy="1.5em" fontSize="12" className="fill-white/60">
                    Success Rate
                  </tspan>
                </text>
              </RadialBarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Average Bounty */}
        <Card className="border-white/5 bg-gradient-to-br from-emerald-500/10 via-green-600/5 to-teal-500/10 backdrop-blur hover:from-emerald-500/15 hover:to-teal-500/15 transition-all duration-300 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-500/5 opacity-50"></div>
          <CardHeader className="relative">
            <CardTitle className="text-sm font-semibold text-white/90 flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-emerald-400 to-teal-400"></div>
              Average Bounty
            </CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center h-[180px] relative">
            <div className="text-3xl font-bold bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent mb-2">
              ${data.metrics?.averageBounty?.toFixed(2) || '0.00'}
            </div>
            <p className="text-white/60 text-xs">Per successful placement</p>
            <div className="absolute top-2 right-2">
              <DollarSign className="w-4 h-4 text-emerald-400/40" />
            </div>
          </CardContent>
        </Card>

        {/* Referral Activity */}
        <Card className="border-white/5 bg-gradient-to-br from-blue-500/10 via-indigo-600/5 to-cyan-500/10 backdrop-blur hover:from-blue-500/15 hover:to-cyan-500/15 transition-all duration-300 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 opacity-50"></div>
          <CardHeader className="relative">
            <CardTitle className="text-sm font-semibold text-white/90 flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-cyan-400 animate-pulse"></div>
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent className="relative">
            <div className="space-y-4 pt-2">
              <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                <span className="text-xs text-white/70 flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
                  This Week
                </span>
                <span className="text-xs font-medium text-blue-300">0 referrals</span>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                <span className="text-xs text-white/70 flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-indigo-400"></div>
                  This Month
                </span>
                <span className="text-xs font-medium text-indigo-300">0 referrals</span>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                <span className="text-xs text-white/70 flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-cyan-400"></div>
                  Last Month
                </span>
                <span className="text-xs font-medium text-cyan-300">0 referrals</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Jobs */}
      <Card className="border-white/5 bg-gradient-to-br from-slate-500/10 via-gray-600/5 to-zinc-500/10 backdrop-blur hover:from-slate-500/15 hover:to-zinc-500/15 transition-all duration-300 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-zinc-500/5 opacity-50"></div>
        <CardHeader className="relative">
          <CardTitle className="text-sm font-semibold text-white/90 flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-slate-400 to-zinc-400"></div>
            Top Performing Jobs
          </CardTitle>
        </CardHeader>
        <CardContent className="relative">
          {(data.metrics?.totalReferrals || 0) > 0 ? (
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={topPerformingJobs}>
                <defs>
                  <linearGradient id="referralsGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#60a5fa" />
                    <stop offset="100%" stopColor="#3b82f6" />
                  </linearGradient>
                  <linearGradient id="hiresGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#34d399" />
                    <stop offset="100%" stopColor="#10b981" />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="job" stroke="#9ca3af" tick={{ fontSize: 11 }} />
                <YAxis stroke="#9ca3af" tick={{ fontSize: 11 }} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(17, 24, 39, 0.9)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '8px',
                    backdropFilter: 'blur(10px)',
                    fontSize: 12,
                  }}
                />
                <Legend wrapperStyle={{ fontSize: 11 }} />
                <Bar dataKey="referrals" fill="url(#referralsGradient)" name="Referrals" />
                <Bar dataKey="hires" fill="url(#hiresGradient)" name="Hires" />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex flex-col items-center justify-center h-[250px] text-center">
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-slate-500/20 to-zinc-500/20 flex items-center justify-center mb-4">
                <TrendingUp className="w-8 h-8 text-white/40" />
              </div>
              <h3 className="text-sm font-medium text-white/80 mb-2">No Job Performance Data</h3>
              <p className="text-xs text-white/60 max-w-[250px] leading-relaxed">
                Job performance metrics will appear here once you start referring candidates to
                specific positions
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
