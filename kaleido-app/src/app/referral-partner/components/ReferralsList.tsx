'use client';

import GenericTable from '@/components/Layouts/GenericTable';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, CheckCircle, Clock, DollarSign, Hash, TrendingUp } from 'lucide-react';
import React from 'react';
import { ReferralPartner, ReferralStatus } from '../types';

interface ReferralsListProps {
  partner: ReferralPartner;
  onRefresh?: () => void;
}

const statusColors: Record<ReferralStatus, string> = {
  [ReferralStatus.PENDING]: 'bg-gray-500',
  [ReferralStatus.CANDIDATE_APPLIED]: 'bg-blue-500',
  [ReferralStatus.CANDIDATE_INTERVIEWED]: 'bg-yellow-500',
  [ReferralStatus.CANDIDATE_HIRED]: 'bg-green-500',
  [ReferralStatus.BOUNTY_APPROVED]: 'bg-purple-500',
  [ReferralStatus.BOUNTY_PAID]: 'bg-green-700',
  [ReferralStatus.EXPIRED]: 'bg-red-500',
  [ReferralStatus.CANCELLED]: 'bg-red-700',
};

const formatStatus = (status: ReferralStatus): string => {
  return status
    .replace(/_/g, ' ')
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

export const ReferralsList: React.FC<ReferralsListProps> = ({ partner, onRefresh }) => {
  const referrals = partner.referrals || [];
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return '-';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const columns = [
    {
      key: 'referralCode',
      label: 'Referral Code',
      icon: Hash,
      render: (value: string) => (
        <span className="font-mono text-sm text-purple-600 dark:text-purple-400">{value}</span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      icon: TrendingUp,
      render: (value: ReferralStatus) => (
        <Badge className={`${statusColors[value]} text-white`}>{formatStatus(value)}</Badge>
      ),
    },
    {
      key: 'candidateAppliedAt',
      label: 'Applied Date',
      icon: Calendar,
      render: (value: string) => formatDate(value),
    },
    {
      key: 'candidateHiredAt',
      label: 'Hired Date',
      icon: CheckCircle,
      render: (value: string) => formatDate(value),
    },
    {
      key: 'bountyAmount',
      label: 'Bounty Amount',
      icon: DollarSign,
      render: (value: number) => (
        <span className="font-semibold text-green-600 dark:text-green-400">
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      key: 'createdAt',
      label: 'Created',
      icon: Clock,
      render: (value: string) => formatDate(value),
    },
  ];

  return (
    <div className="relative">
      <Card className="border-white/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-purple-600" />
            Your Referrals
          </CardTitle>
          <CardDescription>
            Track all your referral submissions and their current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <GenericTable
            data={referrals}
            columns={columns}
            itemsPerPage={10}
            emptyStateConfig={{
              type: 'generic',
              title: 'No referrals yet',
              description: 'Start sharing your referral link to earn commissions',
              showButton: false,
            }}
            searchPlaceholder="Search by referral code..."
            disablePagination={referrals.length <= 10}
          />
        </CardContent>
      </Card>
    </div>
  );
};
