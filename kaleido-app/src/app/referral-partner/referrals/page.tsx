'use client';

import ReferralPageHeader from '@/components/common/ReferralPageHeader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useUser } from '@auth0/nextjs-auth0/client';
import { format, parseISO } from 'date-fns';
import { motion } from 'framer-motion';
import {
  AlertCircle,
  CheckCircle,
  ChevronDown,
  Clock,
  FileText,
  Filter,
  XCircle,
} from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { ReferralPartnerTabs } from '../components/ReferralPartnerTabs';
import { referralApi } from '../services/referralApi';
import { Referral as APIReferral, ReferralPartner, ReferralStatus } from '../types';

// Local types for detailed referrals
interface LocalReferral {
  id: string;
  candidateName: string;
  candidateEmail: string;
  jobTitle: string;
  companyName: string;
  status: 'APPLIED' | 'INTERVIEWING' | 'HIRED' | 'REJECTED';
  appliedAt: string;
  hiredAt?: string;
  commission: number;
  isPaid: boolean;
  paidAt?: string;
}

export default function ReferralsPage() {
  const { user, isLoading: userLoading } = useUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [partner, setPartner] = useState<ReferralPartner | null>(null);
  const [referrals, setReferrals] = useState<LocalReferral[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedMonth, setSelectedMonth] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  const loadPartnerData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.sub) {
        setError('User not authenticated.');
        return;
      }

      const partnerResponse = await referralApi.getPartnerByClientId(user.sub);

      if (!partnerResponse) {
        setError('No referral partner profile found for your account.');
        return;
      }

      setPartner(partnerResponse);

      // Fetch detailed referrals
      const referralsResponse = await referralApi.getReferrals({
        referralPartnerId: partnerResponse.id,
      });

      // Process referrals into local format
      const processedReferrals: LocalReferral[] = referralsResponse.map((referral: APIReferral) => {
        let localStatus: 'APPLIED' | 'INTERVIEWING' | 'HIRED' | 'REJECTED' = 'APPLIED';

        switch (referral.status) {
          case ReferralStatus.CANDIDATE_APPLIED:
            localStatus = 'APPLIED';
            break;
          case ReferralStatus.CANDIDATE_INTERVIEWED:
            localStatus = 'INTERVIEWING';
            break;
          case ReferralStatus.CANDIDATE_HIRED:
          case ReferralStatus.BOUNTY_APPROVED:
          case ReferralStatus.BOUNTY_PAID:
            localStatus = 'HIRED';
            break;
          case ReferralStatus.CANCELLED:
          case ReferralStatus.EXPIRED:
            localStatus = 'REJECTED';
            break;
        }

        return {
          id: referral.id,
          candidateName: referral.candidate?.fullName || 'Unknown Candidate',
          candidateEmail: referral.candidate?.email || '',
          jobTitle: referral.job?.jobType || 'Position',
          companyName: referral.job?.companyName || 'Company',
          status: localStatus,
          appliedAt: referral.candidateAppliedAt || referral.createdAt,
          hiredAt: referral.candidateHiredAt,
          commission: referral.bountyAmount || 0,
          isPaid: referral.status === ReferralStatus.BOUNTY_PAID,
          paidAt: referral.bountyPaidAt,
        };
      });

      setReferrals(processedReferrals);
    } catch (err) {
      console.error('Error loading partner data:', err);
      setError('Failed to load partner data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [user?.sub]);

  useEffect(() => {
    if (user && !userLoading) {
      loadPartnerData();
    }
  }, [user, userLoading, loadPartnerData]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'HIRED':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">
            <CheckCircle className="w-3 h-3" />
            Hired
          </span>
        );
      case 'INTERVIEWING':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">
            <Clock className="w-3 h-3" />
            Interviewing
          </span>
        );
      case 'APPLIED':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-yellow-500/20 text-yellow-300 rounded-full text-xs">
            <AlertCircle className="w-3 h-3" />
            Applied
          </span>
        );
      case 'REJECTED':
        return (
          <span className="flex items-center gap-1 px-2 py-1 bg-red-500/20 text-red-300 rounded-full text-xs">
            <XCircle className="w-3 h-3" />
            Rejected
          </span>
        );
      default:
        return null;
    }
  };

  const getPaymentBadge = (isPaid: boolean) => {
    if (isPaid) {
      return <span className="text-xs text-green-300">Paid</span>;
    }
    return <span className="text-xs text-yellow-300">Pending</span>;
  };

  const filteredReferrals = referrals.filter(referral => {
    if (selectedStatus !== 'all' && referral.status !== selectedStatus) return false;
    if (selectedMonth !== 'all') {
      const referralMonth = format(parseISO(referral.appliedAt), 'yyyy-MM');
      if (referralMonth !== selectedMonth) return false;
    }
    return true;
  });

  // Get unique months from referrals for filter
  const getAvailableMonths = () => {
    const months = referrals.map(referral => format(parseISO(referral.appliedAt), 'yyyy-MM'));
    return [...new Set(months)].sort().reverse();
  };

  return (
    <AppLayout isLoading={loading || userLoading}>
      {error || !partner ? (
        <div className="relative flex h-full w-full items-center justify-center p-3">
          <Card className="max-w-md border-white/5 bg-red-900/10">
            <CardContent className="p-8 text-center">
              <p className="text-white/80">{error || 'Unable to load partner data.'}</p>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="h-full flex flex-col relative">
          {/* Hero Header */}
          <ReferralPageHeader
            title="Referral Details"
            description="Track and manage all your referral submissions"
            icon={FileText}
          />

          {/* Spacer for fixed header */}
          <div className="h-[320px] flex-shrink-0"></div>

          {/* Tab Navigation */}
          <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur h-12 sm:h-14">
            <div className="container mx-auto px-4 sm:px-6 h-full flex items-center justify-between">
              <ReferralPartnerTabs className="h-full flex-1" />
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-auto">
            <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
              <motion.div
                className="relative"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {/* Tab Body Title */}
                <div className="mb-8 relative">
                  {/* Animated background gradient */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-blue-600/20 blur-3xl opacity-50 animate-pulse"></div>

                  {/* Header content */}
                  <div className="relative">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-4 lg:mb-2 gap-4 lg:gap-0">
                      <div>
                        <h1 className="text-lg sm:text-xl font-bold mb-1 flex items-center gap-2 sm:gap-3">
                          <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
                            <FileText className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-white" />
                          </div>
                          <span className="bg-gradient-to-r from-white via-pink-100 to-white bg-clip-text text-transparent">
                            Referral Tracking
                          </span>
                        </h1>
                        <p className="text-xs sm:text-sm text-white/60">
                          Monitor status and progress of your referred candidates
                        </p>
                      </div>
                    </div>

                    {/* Quick stats ribbon */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-6 mt-4">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                        <span className="text-xs text-white/60">Live Updates</span>
                      </div>
                      <div className="hidden sm:block h-4 w-px bg-white/20"></div>
                      <span className="text-xs text-white/60">Track candidate progress</span>
                    </div>
                  </div>
                </div>

                {/* Page Header */}
                <div className="flex items-center justify-between mb-6">
                  <div></div>

                  {/* Filter Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-white/20 bg-white/5 hover:bg-white/10 text-white"
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Filters
                    <ChevronDown
                      className={`h-4 w-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`}
                    />
                  </Button>
                </div>

                {/* Filter Options */}
                {showFilters && (
                  <div className="flex gap-4 mb-6 p-4 bg-white/5 border border-white/10 rounded-lg backdrop-blur-md">
                    <select
                      value={selectedStatus}
                      onChange={e => setSelectedStatus(e.target.value)}
                      className="px-3 py-1.5 bg-black/30 border border-white/20 rounded-lg text-sm text-white backdrop-blur-md"
                    >
                      <option value="all">All Status</option>
                      <option value="HIRED">Hired</option>
                      <option value="INTERVIEWING">Interviewing</option>
                      <option value="APPLIED">Applied</option>
                      <option value="REJECTED">Rejected</option>
                    </select>

                    <select
                      value={selectedMonth}
                      onChange={e => setSelectedMonth(e.target.value)}
                      className="px-3 py-1.5 bg-black/30 border border-white/20 rounded-lg text-sm text-white backdrop-blur-md"
                    >
                      <option value="all">All Months</option>
                      {getAvailableMonths().map(month => (
                        <option key={month} value={month}>
                          {format(parseISO(month + '-01'), 'MMMM yyyy')}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                {/* Single Referrals Table */}
                <Card className="border-white/10 bg-black/5 backdrop-blur-lg">
                  <CardContent className="p-0">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-white/10 text-left">
                            <th className="px-6 py-4 text-sm font-medium text-white/70">
                              Candidate
                            </th>
                            <th className="px-6 py-4 text-sm font-medium text-white/70">
                              Position
                            </th>
                            <th className="px-6 py-4 text-sm font-medium text-white/70">Status</th>
                            <th className="px-6 py-4 text-sm font-medium text-white/70">Applied</th>
                            <th className="px-6 py-4 text-sm font-medium text-white/70">
                              Commission
                            </th>
                            <th className="px-6 py-4 text-sm font-medium text-white/70">Payment</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredReferrals.map(referral => (
                            <tr
                              key={referral.id}
                              className="border-b border-white/5 hover:bg-white/5 transition-colors"
                            >
                              <td className="px-6 py-4">
                                <div>
                                  <p className="text-sm font-medium text-white">
                                    {referral.candidateName}
                                  </p>
                                  <p className="text-xs text-white/60">{referral.candidateEmail}</p>
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <div>
                                  <p className="text-sm text-white">{referral.jobTitle}</p>
                                  <p className="text-xs text-white/60">{referral.companyName}</p>
                                </div>
                              </td>
                              <td className="px-6 py-4">{getStatusBadge(referral.status)}</td>
                              <td className="px-6 py-4">
                                <p className="text-sm text-white/80">
                                  {format(parseISO(referral.appliedAt), 'MMM d, yyyy')}
                                </p>
                              </td>
                              <td className="px-6 py-4">
                                <p className="text-sm font-medium text-white">
                                  {referral.commission > 0
                                    ? `$${referral.commission.toLocaleString()}`
                                    : '-'}
                                </p>
                              </td>
                              <td className="px-6 py-4">
                                {referral.commission > 0 && (
                                  <>
                                    {getPaymentBadge(referral.isPaid)}
                                    {referral.paidAt && (
                                      <p className="text-xs text-white/60 mt-1">
                                        {format(parseISO(referral.paidAt), 'MMM d')}
                                      </p>
                                    )}
                                  </>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {filteredReferrals.length === 0 && (
                      <div className="text-center py-12">
                        <FileText className="w-12 h-12 text-white/30 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-white mb-2">No referrals yet</h3>
                        <p className="text-white/60">
                          Start sharing your referral link to earn commissions
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      )}
    </AppLayout>
  );
}
