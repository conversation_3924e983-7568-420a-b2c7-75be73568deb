'use client';

import { ArrowLeft, Users } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { ATSManager } from '@/components/ATSManager';
import { FullPageBgLayout } from '@/components/common/FullPageBg';

import { NavigationButton } from '@/components/common/NavigationButton';
import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import { JobForm } from '@/components/MatchRank/components/JobForm';
import { createMatchRankConfig } from '@/components/shared/GenericStatusManager/configs/matchRankConfig';
import { createScoutConfig } from '@/components/shared/GenericStatusManager/configs/scoutConfig';
import { createUploadConfig } from '@/components/shared/GenericStatusManager/configs/uploadConfig';
import { GenericStatusManager } from '@/components/shared/GenericStatusManager/GenericStatusManager';
import { showToast } from '@/components/Toaster';
import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';
import { useScoutedCandidatesStore } from '@/stores/scoutedCandidatesStore';
import { useScoutJobsStore } from '@/stores/scoutJobsStore';
import { useJobStore, useUploadJobsStore } from '@/stores/unifiedJobStore';

const JobEditPageContent: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const jobId = params?.id as string;

  // Local loading state to prevent empty states from showing
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Use unified job store
  const {
    currentJob,
    formData,
    isLoading,
    isSaving,
    isProcessing,
    hasUnsavedChanges,
    validationErrors,
    stats,
    setSelectedJob,
    fetchJobById,
    fetchJobCriteria,
    updateFormData,
    saveForm,
    onWorkerComplete,
    reset,
  } = useJobStore();

  // Status manager stores
  const {
    jobs: matchRankJobs,
    activeJobs: activeMatchRankJobs,
    updateJobStatus: updateMatchRankJob,
    updateJobProgress: updateMatchRankJobProgress,
    removeJob: removeMatchRankJob,
  } = useMatchRankJobsStore();
  const {
    jobs: scoutJobs,
    activeJobs: activeScoutJobs,
    updateJobStatus: updateScoutJob,
    updateJobProgress: updateScoutJobProgress,
    removeJob: removeScoutJob,
  } = useScoutJobsStore();
  const {
    jobs: uploadJobs,
    activeJobs: activeUploadJobs,
    updateJobStatus: updateUploadJob,
    updateJobProgress: updateUploadJobProgress,
    removeJob: removeUploadJob,
  } = useUploadJobsStore();

  // Scouted candidates store for refreshing scout results
  const { fetchScoutedCandidates } = useScoutedCandidatesStore();

  // Convert job types to StatusJob format
  const convertedMatchRankJobs = Object.fromEntries(
    Object.entries(matchRankJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.error,
        message: job.message,
        createdAt: job.createdAt,
        updatedAt: job.createdAt, // Use createdAt as fallback since updatedAt may not exist
        notifiedCompletion: job.notifiedCompletion,
        errorCount: job.errorCount,
        metadata: job.metadata || {},
      },
    ])
  );

  const convertedScoutJobs = Object.fromEntries(
    Object.entries(scoutJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.message,
        message: job.message,
        createdAt: job.createdAt,
        updatedAt: job.createdAt, // Use createdAt as fallback since updatedAt may not exist
        totalProfiles: job.totalProfiles,
        processedProfiles: job.processedProfiles,
        successCount: job.successCount,
        failedCount: job.failedCount,
        duplicateCount: job.duplicateCount,
        results: job.results,
        duplicateInfo: job.duplicateInfo,
        data: job.data,
        metadata: job.metadata || {},
      },
    ])
  );

  // Debug scout jobs

  const convertedUploadJobs = Object.fromEntries(
    Object.entries(uploadJobs).map(([key, job]) => [
      key,
      {
        id: job.id || job.jobId, // Use job.id if available, fallback to jobId
        jobId: job.jobId || job.id, // Ensure jobId is set
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.message,
        message: job.message,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt || job.createdAt, // Use updatedAt if available
        totalFiles: job.totalFiles,
        processedFiles: job.processedFiles,
        failedCount: job.failedCount,
        relatedId: job.relatedId, // Pass through the actual job ID
        metadata: job.metadata || {},
        // If result exists and doesn't have jobId, add it from relatedId
        ...(job.result && !job.result.jobId && job.relatedId
          ? {
              result: { ...job.result, jobId: job.relatedId },
            }
          : {}),
      },
    ])
  );

  // Debug upload jobs

  // Initialize job selection and fetch data with loading state
  useEffect(() => {
    const loadData = async () => {
      if (jobId && !hasInitialized) {
        setHasInitialized(true);
        setSelectedJob(jobId);

        try {
          // Fetch job data to populate the form
          await fetchJobCriteria(jobId);
        } catch (error) {
          console.error('Error loading job data:', error);
          showToast({
            message: 'Failed to load job data',
            isSuccess: false,
          });
        } finally {
          setIsInitialLoading(false);
        }
      } else if (!jobId) {
        setIsInitialLoading(false);
      }
    };

    loadData();

    // Cleanup on unmount
    return () => {
      reset();
    };
  }, [jobId, hasInitialized, fetchJobCriteria, setSelectedJob, reset]);

  // Handle navigation back to jobs list
  const handleBackToJobs = useCallback(() => {
    router.push('/jobs');
  }, [router]);

  // Handle navigation to candidates page
  const handleViewCandidates = useCallback(() => {
    router.push(`/jobs/${jobId}/candidates`);
  }, [router, jobId]);

  // Handle upload completion
  const handleUploadComplete = useCallback(async (data: any) => {
    if (data?.candidates?.length) {
      showToast({
        message: `Candidates uploaded successfully`,
        isSuccess: true,
      });
    }

    // Worker completion will be handled by the status manager
    // No need for manual refresh here
  }, []);

  // Handle uploader close
  const handleUploaderClose = useCallback(() => {
    // No specific action needed
  }, []);

  // Handle form save completion with loading state
  const handleSaveComplete = useCallback(async () => {
    try {
      showToast({
        message: 'Job saved successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error saving job:', error);
      showToast({
        message: 'Failed to save job',
        isSuccess: false,
      });
    }
  }, [saveForm]);

  // Show ColourfulLoader while initial loading
  if (isInitialLoading) {
    return <ColourfulLoader text="Loading job data..." />;
  }

  // Show error if no jobId
  if (!jobId) {
    return (
      <div className="text-white text-lg backdrop-blur-xl px-6 py-3 rounded-lg bg-white/10 border border-white/10 shadow-xl">
        Invalid job ID
      </div>
    );
  }

  // Show error if no job data after loading
  if (!currentJob) {
    return (
      <div className="text-white text-lg backdrop-blur-xl px-6 py-3 rounded-lg bg-white/10 border border-white/10 shadow-xl">
        Job not found
      </div>
    );
  }

  return (
    <FullPageBgLayout>
      <div className="h-screen flex flex-col overflow-hidden">
        {/* Fixed Header with Actions */}
        <div className="flex-none bg-black/5 backdrop-blur-lg border-b border-white/10 shadow-md z-20">
          <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between py-4">
              <div className="flex items-center space-x-4">
                <button
                  type="button"
                  onClick={handleBackToJobs}
                  className="text-white hover:text-pink-400 transition-colors"
                  aria-label="Go back to jobs"
                >
                  <ArrowLeft className="w-6 h-6" />
                </button>
                <div>
                  <h1 className="text-xl font-semibold text-white">
                    {currentJob.jobType || 'Job Title'}
                  </h1>
                  <p className="text-sm text-pink-300/80 mt-1">Back to Jobs</p>
                </div>
              </div>

              {/* Stats and Actions */}
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-3">
                  <NavigationButton icon={Users} onClick={handleViewCandidates}>
                    View Ranked Candidates
                  </NavigationButton>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scrollable Main Content */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full px-4 sm:px-6 lg:px-8 py-6">
            <div className="max-w-9xl mx-auto h-full">
              <JobForm
                formData={formData}
                validationErrors={validationErrors}
                jobId={jobId}
                onUploaderClose={handleUploaderClose}
                onUploadComplete={handleUploadComplete}
                onSaveComplete={handleSaveComplete}
                isProcessing={isProcessing}
                hasUnsavedChanges={hasUnsavedChanges}
                isAtsJob={false}
                fullJobData={currentJob}
                useJobEditStore={false} // Use unified store instead
              />
            </div>
          </div>
        </div>

        {/* Status Managers - These will handle worker completion events */}
        <ATSManager
          onComplete={async (jobId: string) => {
            await onWorkerComplete(jobId, 'ats');
            // Additional refresh to ensure UI is updated
            await fetchJobCriteria(jobId, true);
          }}
        />
        <GenericStatusManager
          jobs={convertedMatchRankJobs}
          activeJobs={activeMatchRankJobs}
          config={createMatchRankConfig()}
          onUpdateJob={(jobId: string, updates: any) => {
            // Update progress if provided
            if (updates.progress !== undefined) {
              updateMatchRankJobProgress(jobId, updates.progress);
            }

            // Update status if provided
            if (updates.status !== undefined) {
              updateMatchRankJob(jobId, updates.status, updates);
            }
          }}
          onRemoveJob={removeMatchRankJob}
          onComplete={async (actualJobId: string) => {
            // actualJobId is the real job ID (UUID), not the worker job ID
            await onWorkerComplete(actualJobId, 'matchrank');
            // Additional refresh to ensure UI is updated
            await fetchJobCriteria(actualJobId, true);
          }}
        />
        <GenericStatusManager
          jobs={convertedScoutJobs}
          activeJobs={activeScoutJobs}
          config={createScoutConfig()}
          onUpdateJob={(jobId: string, updates: any) => {
            // Update progress if provided
            if (updates.progress !== undefined) {
              updateScoutJobProgress(jobId, updates.progress);
            }

            // Update status if provided
            if (updates.status !== undefined) {
              updateScoutJob(jobId, updates.status, updates);
            }
          }}
          onRemoveJob={removeScoutJob}
          onComplete={async (actualJobId: string) => {
            // actualJobId is the real job ID (UUID), not the worker job ID
            await onWorkerComplete(actualJobId, 'scout');
            // Additional refresh to ensure UI is updated
            await fetchJobCriteria(actualJobId, true);
            // Immediately refresh scouted candidates to update ScoutOnlineTable
            await fetchScoutedCandidates(actualJobId, true);
          }}
        />
        <GenericStatusManager
          jobs={convertedUploadJobs}
          activeJobs={activeUploadJobs}
          config={createUploadConfig()}
          onUpdateJob={(jobId: string, updates: any) => {
            // Update progress if provided
            if (updates.progress !== undefined) {
              updateUploadJobProgress(jobId, updates.progress);
            }

            // Update status if provided
            if (updates.status !== undefined) {
              updateUploadJob(jobId, updates.status, updates);
            }
          }}
          onRemoveJob={removeUploadJob}
          onComplete={async (actualJobId: string) => {
            // actualJobId is the real job ID (UUID), not the worker job ID
            await onWorkerComplete(actualJobId, 'upload');
            // Additional refresh to ensure UI is updated
            await fetchJobCriteria(actualJobId, true);
          }}
        />
      </div>
    </FullPageBgLayout>
  );
};

// Wrap the component with LoadingStateProvider
const JobEditPageComponent: React.FC = () => {
  return <JobEditPageContent />;
};

export default JobEditPageComponent;
