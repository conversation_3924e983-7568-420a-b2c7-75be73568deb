'use client';

import { motion, useScroll, useTransform } from 'framer-motion';

import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import { useUser } from '@/hooks/useUser';
import { UserRole } from '@/types/roles';
import { UserCircleIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import Link from 'next/link';

export const Header = () => {
  const { user, isLoading } = useUser();
  const { userData } = useEnhancedUserData();
  const { scrollY } = useScroll();
  const backgroundColor = useTransform(
    scrollY,
    [0, 100],
    ['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 1)']
  );
  const textColor = useTransform(
    scrollY,
    [0, 100],
    ['rgba(255, 255, 255, 1)', 'rgba(55, 65, 81, 1)']
  );
  const hoverColor = useTransform(
    scrollY,
    [0, 100],
    ['rgba(236, 72, 153, 1)', 'rgba(219, 39, 119, 1)'] // pink-300 to pink-600
  );
  const shadowOpacity = useTransform(scrollY, [0, 100], [0, 0.1]);

  // Determine dashboard URL based on authoritative backend user role
  const getDashboardUrl = () => {
    if (!user) return '/dashboard';

    // Use the authoritative role from backend instead of Auth0 user claims
    const role = userData?.userRole || user.role || user['https://headstart.com/roles']?.[0];

    if (role === UserRole.EMPLOYER || role === UserRole.ADMIN || role === UserRole.SUPER_ADMIN)
      return '/dashboard';
    if (role === UserRole.JOB_SEEKER) return '/dashboard';
    if (role === UserRole.GRADUATE) return '/graduate/dashboard';

    return '/dashboard';
  };

  return (
    <>
      <motion.header
        className="fixed top-0 left-0 right-0 z-50 backdrop-blur-xl border-b border-white/10"
        style={{
          backgroundColor,
          boxShadow: shadowOpacity.get()
            ? `0 1px 3px rgba(0, 0, 0, ${shadowOpacity.get()})`
            : 'none',
        }}
      >
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                >
                  <Image
                    src={'/images/logos/kaleido-logo-full.webp'}
                    alt={'Company logo'}
                    width={100}
                    height={100}
                    className="mr-2"
                  />
                </motion.div>
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              {!isLoading && user ? (
                <motion.div whileHover={{ scale: 1.05 }}>
                  <Link
                    href={getDashboardUrl()}
                    className="flex items-center transition-colors text-gray-800 hover:text-gray-600"
                  >
                    <UserCircleIcon className="h-5 w-5 mr-1" />
                    My Dashboard
                  </Link>
                </motion.div>
              ) : (
                <>
                  <motion.div whileHover={{ scale: 1.05 }}>
                    <motion.div style={{ color: textColor }}>
                      <Link
                        href="/job-seeker-holding?from=open-jobs"
                        className="font-semibold text-sm transition-colors hover:opacity-80"
                        style={{ color: 'inherit' }}
                      >
                        Log In
                      </Link>
                    </motion.div>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link
                      href="/job-seeker-holding?from=open-jobs"
                      className="text-white inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-pink-600 to-purple-600 rounded-xl font-semibold text-sm hover:from-pink-700 hover:to-purple-700 transition-all duration-300 shadow-lg"
                    >
                      Sign Up
                    </Link>
                  </motion.div>
                </>
              )}
            </div>
          </div>
        </nav>
      </motion.header>
    </>
  );
};
