'use client';

// Import polyfills for Turbopack compatibility
import '@/lib/turbopack-polyfills';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { JobSeekerSetupSlider } from '@/components/JobSeeker/JobSeekerSetupSlider';
import { showToast } from '@/components/Toaster';
import { JobSearchProvider, useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import apiHelper from '@/lib/apiHelper';
import { useJobSearchStore } from '@/stores/jobSearchStore';
import { UserRole } from '@/types/roles';
import { useUser } from '@/hooks/useUser';

import { HeroSection } from './components/HeroSection';
import { JobInfo } from './components/JobInfo';
import { JobSearchEmptyState } from './components/JobSearchEmptyState';
import { JobSkeleton } from './components/JobSkeleton';
import { OpenJobsList } from './components/OpenJobsList';
import { SearchFilters } from './components/SearchFilters';
// Import the dedicated styles for open jobs
import styles from './styles/openJobs.module.css';

// Safe router wrapper component
function SafeRouterComponent({ children }: { children: React.ReactNode }) {
  // No need to use router here, just checking if it's ready
  const [isRouterReady, setIsRouterReady] = useState(false);

  useEffect(() => {
    // Router is available when the component mounts
    setIsRouterReady(true);
  }, []);

  if (!isRouterReady) {
    return null; // Or a loading spinner
  }

  return <>{children}</>;
}

export default function OpenJobsPage() {
  // Auth state
  const { user } = useUser();
  const [userRole, setUserRole] = useState<string | null>(null);

  // Job seeker setup state
  const [showSetupSlider, setShowSetupSlider] = useState(false);
  const [profileValidation, setProfileValidation] = useState<any>(null);
  const [initialSliderData, setInitialSliderData] = useState<any>({});

  // Check user role and profile validation when user is loaded
  useEffect(() => {
    const checkUserRole = async () => {
      if (user) {
        try {
          const validationResponse = await apiHelper.post('/job-seekers/validate-profile', user);
          if (validationResponse) {
            setUserRole(validationResponse.role);
            setInitialSliderData(validationResponse);
            setProfileValidation(validationResponse);
          }

          // Only show setup slider for job seekers with invalid profiles
          if (!validationResponse.isValid && validationResponse.role === UserRole.JOB_SEEKER) {
            setShowSetupSlider(true);
          }
        } catch (error) {
          console.error('Error checking user role:', error);
          // Don't let profile validation errors prevent the page from loading
        }
      }
    };

    checkUserRole();
  }, [user]);

  // Handle job application
  const onJobApply = async (jobId: string) => {
    if (!user) {
      showToast({
        message: 'Please sign in to apply for jobs',
        type: 'info',
      });
      return;
    }

    if (userRole !== UserRole.JOB_SEEKER) {
      showToast({
        message: 'Only job seekers can apply for jobs',
        type: 'error',
      });
      return;
    }

    if (!profileValidation?.isValid) {
      setShowSetupSlider(true);
      return;
    }

    try {
      await apiHelper.post(`/api/jobs/${jobId}/apply`, {});
      showToast({
        message: 'Your application has been submitted successfully',
        type: 'success',
      });
    } catch (error) {
      console.error('Error applying for job:', error);
      showToast({
        message: 'Failed to submit your application. Please try again.',
        type: 'error',
      });
    }
  };

  // Handle profile setup completion
  const onSetupComplete = () => {
    setShowSetupSlider(false);
  };

  return (
    <JobSearchProvider initialPublic={true}>
      <SafeRouterComponent>
        <div className="min-h-screen">
          {showSetupSlider && (
            <JobSeekerSetupSlider
              onClose={() => setShowSetupSlider(false)}
              onComplete={onSetupComplete}
              initialData={initialSliderData}
              validationResponse={profileValidation}
            />
          )}
          <OpenJobsContent showFilters={!showSetupSlider} onJobApply={onJobApply} />
        </div>
      </SafeRouterComponent>
    </JobSearchProvider>
  );
}

// Content component that consumes the JobSearch context
function OpenJobsContent({
  // showFilters is not used but kept for API compatibility
  showFilters: _,
  onJobApply,
}: {
  showFilters: boolean;
  onJobApply: (jobId: string) => Promise<void>;
}) {
  const router = useRouter();
  const {
    searchTerm,
    filters,
    setFilters,
    isPublicView,
    switchToPrivateView,
    showAllJobs,
    setSearchTerm,
    setShowAllJobs,
  } = useJobSearch();
  const { user } = useUser();
  const {
    jobs,
    selectedJobId,
    isLoading,
    totalJobs,
    availableFilters,
    fetchJobs,
    refreshJobs,
    getSearchParamsFromUrl,
  } = useJobSearchStore();

  // Switch to private view when user logs in
  useEffect(() => {
    if (user && isPublicView) {
      switchToPrivateView();
    }
  }, [user, isPublicView, switchToPrivateView]);

  // Refresh job data when user auth state changes
  useEffect(() => {
    if (jobs.length > 0) {
      refreshJobs(user);
    }
  }, [user?.sub, refreshJobs, jobs.length]);

  // Initialize state from URL parameters on mount
  useEffect(() => {
    const {
      searchTerm: urlSearchTerm,
      showAllJobs: urlShowAllJobs,
      filters: urlFilters,
    } = getSearchParamsFromUrl();

    // Update context state from URL parameters
    if (urlSearchTerm) setSearchTerm(urlSearchTerm);
    setShowAllJobs(urlShowAllJobs);

    // Convert to the expected type structure before setting filters
    if (Object.keys(urlFilters).length > 0) {
      const typedFilters = {
        location: urlFilters.location || '',
        salary: urlFilters.salary || '',
        currency: urlFilters.currency || '',
        jobType: urlFilters.jobType || '',
      };
      setFilters(typedFilters);
    }
  }, [getSearchParamsFromUrl, setSearchTerm, setShowAllJobs, setFilters]);

  // Fetch jobs when filters or search term change
  useEffect(() => {
    fetchJobs({
      filters: filters as unknown as Record<string, string>, // Safe type assertion since we control the filter values
      searchTerm,
      showAllJobs,
      user,
    });
  }, [filters, searchTerm, showAllJobs, user, fetchJobs]);

  // Get the selected job from the jobs array
  const selectedJob = jobs.find(job => job.id === selectedJobId);

  return (
    <div className={styles.openJobsContent}>
      {/* Hero Section */}
      <HeroSection />

      {/* Search Filters Section */}
      <SearchFilters
        totalJobs={totalJobs}
        availableFilters={{
          ...availableFilters,
          industries: [], // Add required industries field
        }}
      />

      {/* Job Listings Section */}
      <div className="max-w-[100rem] mx-auto px-4 sm:px-6 lg:px-8">
        {jobs.length === 0 && !isLoading ? (
          <JobSearchEmptyState
            totalJobs={totalJobs}
            hasActiveFilters={Boolean(
              searchTerm ||
                filters.location ||
                filters.salary ||
                filters.currency ||
                filters.jobType
            )}
            onClearFilters={() => {
              setSearchTerm('');
              setFilters({
                location: '',
                salary: '',
                currency: '',
                jobType: '',
              });
            }}
          />
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Job List */}
            <div className="lg:col-span-4">
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <JobSkeleton key={i} type="list" />
                  ))}
                </div>
              ) : (
                <OpenJobsList
                  jobs={jobs.map(
                    job =>
                      ({
                        ...job,
                        createdAt: job.createdAt?.toString(),
                        matchDetails: job.matchDetails
                          ? {
                              skillsMatch: {
                                score: job.matchDetails.skillsMatch?.score || 0,
                                feedback: 'Skills match information',
                              },
                              experienceMatch: {
                                score: job.matchDetails.experienceMatch?.score || 0,
                                feedback: 'Experience match information',
                              },
                              locationMatch: {
                                score: job.matchDetails.locationMatch?.score || 0,
                                feedback: 'Location match information',
                              },
                              matchFeedback: job.matchDetails.matchFeedback || '',
                            }
                          : undefined,
                      }) as any
                  )}
                  selectedJob={
                    selectedJob &&
                    ({
                      ...selectedJob,
                      createdAt: selectedJob.createdAt?.toString(),
                      matchDetails: selectedJob.matchDetails
                        ? {
                            skillsMatch: {
                              score: selectedJob.matchDetails.skillsMatch?.score || 0,
                              feedback: 'Skills match information',
                            },
                            experienceMatch: {
                              score: selectedJob.matchDetails.experienceMatch?.score || 0,
                              feedback: 'Experience match information',
                            },
                            locationMatch: {
                              score: selectedJob.matchDetails.locationMatch?.score || 0,
                              feedback: 'Location match information',
                            },
                            matchFeedback: selectedJob.matchDetails.matchFeedback || '',
                          }
                        : undefined,
                    } as any)
                  }
                  setSelectedJob={job => {
                    if (job) {
                      router.push(`${job.id}`, { scroll: false });
                    }
                  }}
                  onJobApply={onJobApply}
                />
              )}
            </div>

            {/* Job Details - Only visible on desktop */}
            <div className="hidden lg:block lg:col-span-8" id="job-details">
              {selectedJob ? (
                <div className="sticky top-24 max-h-[calc(100vh-8rem)] overflow-y-auto pr-2">
                  <JobInfo
                    job={{
                      ...selectedJob,
                      createdAt: selectedJob.createdAt?.toString(),
                    }}
                    onJobApply={onJobApply}
                  />
                </div>
              ) : (
                <JobSkeleton type="details" />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
