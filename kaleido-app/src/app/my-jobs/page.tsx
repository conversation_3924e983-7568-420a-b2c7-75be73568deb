'use client';

// Import polyfills for Turbopack compatibility
import '@/lib/turbopack-polyfills';

import { useCallback, useEffect, useState } from 'react';

import EmployerLayout from '@/components/steps/layout/EmployerLayout';
import { showToast } from '@/components/Toaster';
import { JobSearchProvider, useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import apiHelper from '@/lib/apiHelper';
import { useJobSearchStore } from '@/stores/jobSearchStore';
import { useUser } from '@auth0/nextjs-auth0/client';

import { JobInfo } from '../open-jobs/components/JobInfo';
import { JobSkeleton } from '../open-jobs/components/JobSkeleton';
import { SearchFilters } from '../open-jobs/components/SearchFilters';
import { MyJobsHeroSection } from './components/MyJobsHeroSection';
import { MyJobsList } from './components/MyJobsList';
// Import the dedicated styles for open jobs

// Safe router wrapper component (reused from open-jobs)
function SafeRouterComponent({ children }: { children: React.ReactNode }) {
  const [isRouterReady, setIsRouterReady] = useState(false);

  useEffect(() => {
    setIsRouterReady(true);
  }, []);

  if (!isRouterReady) {
    return null;
  }

  return <>{children}</>;
}

export default function MyJobsPage() {
  // Set up styling similar to open-jobs
  useEffect(() => {
    document.documentElement.setAttribute('data-page', 'my-jobs');

    return () => {
      document.documentElement.removeAttribute('data-page');
    };
  }, []);

  useEffect(() => {
    // Keep dark theme but add page-specific styling
    document.documentElement.setAttribute('data-theme', 'dark');
    document.documentElement.style.colorScheme = 'dark';

    // Create and inject custom styles (similar to open-jobs)
    const styleElement = document.createElement('style');
    styleElement.id = 'my-jobs-theme-override';
    styleElement.innerHTML = `
      /* Light theme styling for my-jobs page */
      [data-page="my-jobs"],
      [data-page="my-jobs"] *,
      [data-page="my-jobs"] [data-theme="dark"],
      [data-page="my-jobs"] [data-theme="light"] {
        --foreground-rgb: 17, 24, 39 !important;
        --foreground-color: rgb(17, 24, 39) !important;
        --background-rgb: 249, 250, 251 !important;
        --background-start: #f9fafb !important;
        --background-middle: #f9fafb !important;
        --background-end: #f9fafb !important;
        --card-bg: rgba(255, 255, 255, 1) !important;
        --card-border: rgba(229, 231, 235, 1) !important;
      }

      /* First, apply general dark text to most elements */
      [data-page="my-jobs"] h1,
      [data-page="my-jobs"] h2,
      [data-page="my-jobs"] h3,
      [data-page="my-jobs"] h4,
      [data-page="my-jobs"] h5,
      [data-page="my-jobs"] h6,
      [data-page="my-jobs"] p,
      [data-page="my-jobs"] span,
      [data-page="my-jobs"] div,
      [data-page="my-jobs"] label,
      [data-page="my-jobs"] button {
        color: #111827;
      }

      /* Then override with specific color classes - these have higher specificity */
      [data-page="my-jobs"] .text-white,
      [data-page="my-jobs"] [class*="text-white"] {
        color: #ffffff;
      }

      /* Updated color palette for better visual appeal */
      [data-page="my-jobs"] .text-white {
        color: #ffffff;
      }

      [data-page="my-jobs"] .text-amber-400 {
        color: #fbbf24 !important;
      }

      [data-page="my-jobs"] .text-emerald-500 {
        color: #10b981 !important;
      }

      [data-page="my-jobs"] .text-purple-600 {
        color: #9333ea !important;
      }

      /* CAROUSEL SPECIFIC OVERRIDES - Preserve all inline styles and colors */
      [data-page="my-jobs"] .my-jobs-hero-carousel * {
        color: inherit !important;
      }

      /* Ensure carousel text elements use their inline styles */
      [data-page="my-jobs"] .my-jobs-hero-carousel .text-white {
        color: #ffffff;
      }

      [data-page="my-jobs"] .my-jobs-hero-carousel .text-gradient-accent {
        color: #fbbf24 !important;
      }

      /* Force all carousel elements to respect inline styles */
      [data-page="my-jobs"] .my-jobs-hero-carousel [style*="color"] {
        color: inherit !important;
      }

      [data-page="my-jobs"] {
        background-color: #f9fafb !important;
        color: #111827;
      }

      /* Completely exclude carousel from global color inheritance */
      [data-page="my-jobs"] .my-jobs-hero-carousel {
        color: initial !important;
      }

      [data-page="my-jobs"] input,
      [data-page="my-jobs"] select,
      [data-page="my-jobs"] textarea {
        background-color: #ffffff;
        color: #111827;
        border-color: #d1d5db !important;
      }

      [data-page="my-jobs"] .bg-white {
        background-color: #ffffff;
      }
    `;
    document.head.appendChild(styleElement);

    document.body.setAttribute('data-page', 'my-jobs');

    return () => {
      const styleEl = document.getElementById('my-jobs-theme-override');
      if (styleEl) {
        styleEl.remove();
      }
      document.body.removeAttribute('data-page');
      document.documentElement.setAttribute('data-theme', 'dark');
      document.documentElement.style.colorScheme = 'dark';
    };
  }, []);

  // Handle job application (not applicable for employers)
  const onJobApply = async () => {
    showToast({
      message: 'Employers cannot apply for jobs',
      type: 'error',
    });
  };

  return (
    <EmployerLayout>
      <JobSearchProvider>
        <SafeRouterComponent>
          <MyJobsContent onJobApply={onJobApply} />
        </SafeRouterComponent>
      </JobSearchProvider>
    </EmployerLayout>
  );
}

// Content component that uses the same layout as OpenJobsContent
function MyJobsContent({ onJobApply }: { onJobApply: () => Promise<void> }) {
  const { setFilters, setSearchTerm, setShowAllJobs } = useJobSearch();
  const { user } = useUser();
  const { jobs, selectedJobId, isLoading, totalJobs, availableFilters, getSearchParamsFromUrl } =
    useJobSearchStore();

  // Custom fetch function for my jobs
  const fetchMyJobs = useCallback(async () => {
    if (!user) {
      return;
    }

    // Set loading state
    useJobSearchStore.setState({ isLoading: true, error: null });

    try {
      const response = await apiHelper.get('/jobs/company/my-jobs?page=1&limit=50');

      // Update the job search store with our jobs
      // The backend returns JobsResponse { data: Job[], metadata: {...}, pagination?: {...} }
      useJobSearchStore.setState({
        jobs: response.data || [],
        totalJobs:
          response.pagination?.totalItems || response.metadata?.total || response.data?.length || 0,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error('Error fetching my jobs:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.status,
        response: error.response?.data,
      });

      showToast({
        message: 'Failed to fetch your jobs. Please try again.',
        type: 'error',
      });

      useJobSearchStore.setState({
        jobs: [],
        totalJobs: 0,
        isLoading: false,
        error: 'Failed to fetch jobs',
      });
    }
  }, [user]);

  // Fetch jobs when component mounts and user is available
  useEffect(() => {
    if (user) {
      fetchMyJobs();
    } else {
    }
  }, [user, fetchMyJobs]);

  // Initialize URL parameters
  useEffect(() => {
    const urlParams = getSearchParamsFromUrl();
    setSearchTerm(urlParams.searchTerm);
    setShowAllJobs(urlParams.showAllJobs);

    // Set filters from URL
    const urlFilters = new URLSearchParams(window.location.search);
    const filters: any = {};
    urlFilters.forEach((value, key) => {
      if (['location', 'salary', 'currency', 'jobType'].includes(key)) {
        filters[key] = value;
      }
    });

    if (Object.keys(filters).length > 0) {
      const typedFilters = {
        location: filters.location || '',
        salary: filters.salary || '',
        currency: filters.currency || '',
        jobType: filters.jobType || '',
      };
      setFilters(typedFilters);
    }
  }, [getSearchParamsFromUrl, setSearchTerm, setShowAllJobs, setFilters]);

  // Get the selected job from the jobs array
  const selectedJob = jobs.find(job => job.id === selectedJobId);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <MyJobsHeroSection totalJobs={totalJobs} />

      {/* Search Filters Section - Add top padding to account for fixed header */}
      <div className="pt-16 sm:pt-18 md:pt-20">
        <SearchFilters
          totalJobs={totalJobs}
          availableFilters={{
            ...availableFilters,
            industries: [], // Add required industries field
          }}
        />
      </div>

      {/* Job Listings Section - Following open-jobs pattern */}
      <div className="max-w-[100rem] mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Job List */}
          <div className="lg:col-span-4">
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <JobSkeleton key={i} type="list" />
                ))}
              </div>
            ) : jobs.length === 0 ? (
              <JobSkeleton
                type="empty"
                onClearFilters={() => {
                  setFilters({
                    location: '',
                    salary: '',
                    currency: '',
                    jobType: '',
                  });
                }}
              />
            ) : (
              <MyJobsList />
            )}
          </div>

          {/* Job Details - Only visible on desktop */}
          <div className="hidden lg:block lg:col-span-8" id="job-details">
            {selectedJob ? (
              <div className="sticky top-20 max-h-[calc(100vh-6rem)] overflow-y-auto pr-2">
                <JobInfo
                  job={{
                    ...selectedJob,
                    createdAt: selectedJob.createdAt?.toString(),
                  }}
                  onJobApply={onJobApply}
                />
              </div>
            ) : (
              <JobSkeleton type="details" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
