'use client';

import { motion } from 'framer-motion';
import {
  BarChart3,
  ClipboardCheck,
  Database,
  Flag,
  FlaskConical,
  Mail,
  Settings2,
  Users,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

import PageHeader from '@/components/common/PageHeader';
import { TabItem, TabNavigation } from '@/components/common/TabNavigation';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import AppLayout from '@/components/steps/layout/AppLayout';

// Import the admin components directly
import CompaniesTable from '@/components/admin/CompaniesTable';
import EmailConfigContent from '@/components/admin/EmailConfigContent';
import EntitiesContent from '@/components/admin/EntitiesContent';
import RegistrationContentWrapper from '@/components/admin/RegistrationContentWrapper';
import TestDashboardContent from '@/components/admin/TestDashboardContent';
import UsageDashboard from '@/components/admin/UsageDashboard';
import WaitlistsContent from '@/components/admin/WaitlistsContent';
import { apiClient as apiHelper } from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';

// Define interfaces for API responses
interface UsageStatsResponse {
  totalCompanies?: number;
  totalJobs?: number;
  totalVideoJDs?: number;
  totalCandidates?: number;
  activeCompanies?: number;
  industryDistribution?: any[];
  platformActivity?: any;
}

interface UsageStats {
  totalCompanies: number;
  totalJobs: number;
  totalVideoJDs: number;
  totalCandidates: number;
  activeCompanies: number;
  industryDistribution: any[];
  platformActivity?: any;
}

const adminTabs: TabItem[] = [
  {
    id: 'usage',
    label: 'Usage',
    icon: BarChart3,
  },
  {
    id: 'registration',
    label: 'Registration',
    icon: ClipboardCheck,
  },
  {
    id: 'entities',
    label: 'Entities',
    icon: Database,
  },
  {
    id: 'waitlists',
    label: 'Waitlists',
    icon: Users,
  },
  {
    id: 'feature-flags',
    label: 'Feature Flags',
    icon: Flag,
  },
  {
    id: 'test-dashboard',
    label: 'Test Dashboard',
    icon: FlaskConical,
  },
  {
    id: 'email-config',
    label: 'Email Config',
    icon: Mail,
  },
];

// Default stats data
const defaultStats: UsageStats = {
  totalCompanies: 0,
  totalJobs: 0,
  totalVideoJDs: 0,
  totalCandidates: 0,
  activeCompanies: 0,
  industryDistribution: [],
  platformActivity: {},
};

export default function AdminPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('usage');
  const [isLoading, setIsLoading] = useState(false);

  // Usage page state
  const [usageStats, setUsageStats] = useState(defaultStats);
  const [usageLoading, setUsageLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeFilter, setTimeFilter] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && adminTabs.some(t => t.id === tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  useEffect(() => {
    if (activeTab === 'usage') {
      fetchUsageStats();
    }
  }, [activeTab]);

  const fetchUsageStats = async () => {
    setUsageLoading(true);
    setError(null);
    try {
      // Fetch usage stats with pagination (first page, no search)
      const response = (await apiHelper.get(
        '/dashboard/usage-stats?page=1&limit=10'
      )) as UsageStatsResponse;
      if (response && typeof response === 'object') {
        // Extract the stats from the response
        const stats: UsageStats = {
          totalCompanies: response.totalCompanies || 0,
          totalJobs: response.totalJobs || 0,
          totalVideoJDs: response.totalVideoJDs || 0,
          totalCandidates: response.totalCandidates || 0,
          activeCompanies: response.activeCompanies || 0,
          industryDistribution: response.industryDistribution || [],
          platformActivity: response.platformActivity || {},
        };
        setUsageStats(stats);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error: any) {
      console.error('Error fetching usage stats:', error);
      setError(error.message || 'Failed to fetch usage statistics');
      setUsageStats(defaultStats);
    } finally {
      setUsageLoading(false);
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);

    // Update URL with new tab
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', tabId);
    router.push(`/admin?${params.toString()}`);
  };

  const handleTimeFilterChange = (value: 'daily' | 'weekly' | 'monthly' | 'yearly') => {
    setTimeFilter(value);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'usage':
        return (
          <div className="mx-auto w-full p-6">
            {usageLoading ? (
              <ColorfulSmokeyOrbLoader text="Loading usage data..." useModalBg={false} />
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="space-y-6">
                  {/* Time filter controls */}
                  <div className="flex justify-end mb-4">
                    <div className="flex items-center gap-2 bg-white/5 border border-white/10 p-1 rounded-md">
                      <button
                        onClick={() => handleTimeFilterChange('daily')}
                        className={`px-3 py-1 rounded-sm text-sm ${
                          timeFilter === 'daily' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                        }`}
                      >
                        Day
                      </button>
                      <button
                        onClick={() => handleTimeFilterChange('weekly')}
                        className={`px-3 py-1 rounded-sm text-sm ${
                          timeFilter === 'weekly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                        }`}
                      >
                        Week
                      </button>
                      <button
                        onClick={() => handleTimeFilterChange('monthly')}
                        className={`px-3 py-1 rounded-sm text-sm ${
                          timeFilter === 'monthly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                        }`}
                      >
                        Month
                      </button>
                      <button
                        onClick={() => handleTimeFilterChange('yearly')}
                        className={`px-3 py-1 rounded-sm text-sm ${
                          timeFilter === 'yearly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                        }`}
                      >
                        Year
                      </button>
                    </div>
                  </div>

                  {/* Error Display */}
                  {error && (
                    <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                      <p className="text-red-400">Error loading usage statistics: {error}</p>
                      <button
                        onClick={() => {
                          invalidateJobsCache();
                          refreshJobs(true);
                          setError(null);
                          setUsageLoading(true);
                          fetchUsageStats();
                        }}
                        className="mt-2 px-3 py-1 bg-red-500/20 text-red-300 rounded text-sm hover:bg-red-500/30 transition-colors"
                      >
                        Retry
                      </button>
                    </div>
                  )}

                  {/* Usage Dashboard Stats */}
                  {usageStats ? (
                    <UsageDashboard stats={usageStats} timeFilter={timeFilter} />
                  ) : (
                    !error && (
                      <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                        <p className="text-yellow-400">No usage stats available</p>
                      </div>
                    )
                  )}

                  {/* Companies Table */}
                  <div className="mt-8">
                    <h2 className="text-xl font-semibold text-white/90 mb-4">
                      Company Usage Details
                    </h2>
                    <CompaniesTable />
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        );

      case 'registration':
        return (
          <div className="mx-auto w-full p-6">
            <RegistrationContentWrapper />
          </div>
        );

      case 'entities':
        return (
          <div className="mx-auto w-full p-6">
            <EntitiesContent />
          </div>
        );

      case 'waitlists':
        return (
          <div className="mx-auto w-full p-6">
            <WaitlistsContent />
          </div>
        );

      case 'feature-flags':
        return (
          <div className="mx-auto w-full p-6">
            <div className="bg-white/5 border border-white/10 rounded-lg p-8 text-center">
              <Flag className="w-12 h-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Feature Flags</h3>
              <p className="text-white/60">Feature flags management coming soon</p>
            </div>
          </div>
        );

      case 'test-dashboard':
        return (
          <div className="mx-auto w-full">
            <TestDashboardContent />
          </div>
        );

      case 'email-config':
        return (
          <div className="mx-auto w-full p-6">
            <EmailConfigContent />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <AppLayout>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          variant="fullwidth"
          title="Admin Dashboard"
          description="Manage platform settings, users, and configuration"
          icon={Settings2}
          imageSrc="/images/insights/innovation_technology_revised.png"
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Tabs Navigation */}
        <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur h-12 sm:h-14">
          <div className="container mx-auto px-4 sm:px-6 h-full flex items-center">
            {isLoading ? (
              <ColorfulSmokeyOrbLoader text="Loading admin dashboard" />
            ) : (
              <TabNavigation
                tabs={adminTabs}
                activeTab={activeTab}
                onTabChange={handleTabChange}
                variant="gradient"
                gradientColors={{
                  from: 'from-purple-600',
                  to: 'to-pink-600',
                }}
                updateUrl={true}
                queryParamName="tab"
                showBorder={true}
                showSeparators={true}
                mobileCollapse={true}
                className="w-full h-full flex-1 text-white"
              />
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
            {!isLoading && <div>{renderTabContent()}</div>}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
