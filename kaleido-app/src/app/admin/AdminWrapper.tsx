'use client';

import { Settings2 } from 'lucide-react';
import React from 'react';

import PageHeader from '@/components/common/PageHeader';
import { TabItem, TabNavigation } from '@/components/common/TabNavigation';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import AppLayout from '@/components/steps/layout/AppLayout';

interface AdminWrapperProps {
  children: React.ReactNode;
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  isLoading?: boolean;
}

export const AdminWrapper: React.FC<AdminWrapperProps> = ({
  children,
  tabs,
  activeTab,
  onTabChange,
  isLoading = false,
}) => {
  return (
    <AppLayout>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          variant="fullwidth"
          title="Admin Dashboard"
          description="Manage platform settings, users, and configuration"
          icon={Settings2}
          imageSrc="/images/landing/open-jobs/open-jobs-8.webp"
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Tabs Navigation */}
        <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur h-12 sm:h-14">
          <div className="container mx-auto px-4 sm:px-6 h-full flex items-center">
            {isLoading ? (
              <ColorfulSmokeyOrbLoader text="Loading admin dashboard" />
            ) : (
              <TabNavigation
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={onTabChange}
                variant="gradient"
                gradientColors={{
                  from: 'from-purple-600',
                  to: 'to-pink-600',
                }}
                updateUrl={true}
                queryParamName="tab"
                showBorder={true}
                showSeparators={true}
                mobileCollapse={true}
                className="w-full h-full flex-1 text-white"
              />
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
            {!isLoading && <div>{children}</div>}
          </div>
        </div>
      </div>
    </AppLayout>
  );
};
