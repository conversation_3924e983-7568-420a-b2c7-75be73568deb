'use client';

import React, { useState } from 'react';

import { motion } from 'framer-motion';
import { ArrowLeft, Search, LogIn, UserPlus } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { UserRole } from '@/types/roles';

/**
 * Job Seeker Holding Page using Auth0 role management
 * This version uses Auth0's built-in capabilities with Next.js Auth0
 */
export default function JobSeekerHoldingPage() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogin = () => {
    // Check if user came from open jobs page and redirect back there
    const referrer = typeof window !== 'undefined' ? document.referrer : '';
    const searchParams = typeof window !== 'undefined' ? window.location.search || '' : '';
    const fromOpenJobs = referrer.includes('/open-jobs') || searchParams.includes('from=open-jobs');

    // Redirect to Auth0 login with job seeker role
    const params = new URLSearchParams({
      returnTo: fromOpenJobs ? '/open-jobs' : '/dashboard',
      role: UserRole.JOB_SEEKER,
      login_hint: JSON.stringify({
        role: UserRole.JOB_SEEKER,
        source: 'job-seeker-holding',
        action: 'login',
        timestamp: Date.now(),
        fromOpenJobs,
      }),
    });

    window.location.href = `/api/auth/login?${params.toString()}`;
  };

  const handleRegister = () => {
    // Redirect to Auth0 signup with job seeker role
    const params = new URLSearchParams({
      returnTo: '/jobseeker-onboarding',
      role: UserRole.JOB_SEEKER,
      screen_hint: 'signup',
      login_hint: JSON.stringify({
        role: UserRole.JOB_SEEKER,
        source: 'job-seeker-holding',
        action: 'signup',
        timestamp: Date.now(),
      }),
    });

    window.location.href = `/api/auth/login?${params.toString()}`;
  };

  const handleBackToLanding = () => {
    try {
      router.push('/');
    } catch (error) {
      console.error('Navigation failed:', error);
      // Handle navigation error gracefully - component doesn't crash
    }
  };

  if (!mounted) {
    return null;
  }

  return (
    <>
      <div className="min-h-screen bg-black relative overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="/images/landing/new/job-seekers.jpeg"
            alt="Job Seekers Background"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-[linear-gradient(to_right,rgba(236,72,153,0.95),rgba(236,72,153,0.7),transparent),linear-gradient(to_bottom,rgba(251,113,133,0.1),rgba(236,72,153,0.2))]" />
        </div>

        {/* Logo */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-6 left-6 z-10 flex items-center gap-3"
        >
          <Image
            src="/images/logos/kaleido-logo-only.webp"
            alt="Kaleido Logo"
            width={55}
            height={55}
            className="transition-all duration-300"
            priority
          />
          <Image
            src="/images/logos/kaleido-logo-without-logo-white.webp"
            alt="Kaleido Talent Logo"
            width={100}
            height={45}
            className="transition-all duration-300"
            priority
          />
        </motion.div>

        {/* Back button */}
        <motion.button
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          onClick={handleBackToLanding}
          className="absolute top-6 right-6 z-10 text-white/80 hover:text-white transition-colors duration-300 flex items-center gap-2 px-4 py-2 rounded-lg backdrop-blur-sm hover:bg-white/10"
        >
          <ArrowLeft size={20} />
          <span className="text-sm font-medium">Back to Landing</span>
        </motion.button>

        {/* Main Content */}
        <div className="relative z-10 h-screen flex items-center justify-center px-4">
          <div className="max-w-2xl mx-auto text-center text-white">
            {/* Icon */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="flex justify-center mb-8"
            >
              <div className="bg-white/10 backdrop-blur-md p-6 rounded-full">
                <Search size={64} className="text-white" />
              </div>
            </motion.div>

            {/* Heading */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-5xl md:text-6xl font-bold mb-6 leading-tight"
            >
              Find Your Dream Job
            </motion.h1>

            {/* Subheading */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-xl md:text-2xl text-white/90 mb-12 leading-relaxed max-w-xl mx-auto"
            >
              Discover opportunities that match your skills and aspirations. Let AI help you find
              the perfect role.
            </motion.p>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <button
                onClick={handleLogin}
                className="group bg-white/20 backdrop-blur-md border border-white/30 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/30 transition-all duration-300 flex items-center gap-3 min-w-[200px] justify-center"
              >
                <LogIn size={24} className="group-hover:scale-110 transition-transform" />
                Log In
              </button>

              <button
                onClick={handleRegister}
                className="group bg-white text-pink-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/90 transition-all duration-300 flex items-center gap-3 min-w-[200px] justify-center shadow-lg"
              >
                <UserPlus size={24} className="group-hover:scale-110 transition-transform" />
                Get Started
              </button>
            </motion.div>

            {/* Features */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center"
            >
              <div className="bg-white/10 backdrop-blur-md p-6 rounded-xl border border-white/20">
                <div className="text-3xl mb-3">🎯</div>
                <h3 className="text-lg font-semibold mb-2">AI-Powered Matching</h3>
                <p className="text-white/80 text-sm">
                  Our AI analyzes your skills and preferences to find the best job matches
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-md p-6 rounded-xl border border-white/20">
                <div className="text-3xl mb-3">🚀</div>
                <h3 className="text-lg font-semibold mb-2">Career Growth</h3>
                <p className="text-white/80 text-sm">
                  Access opportunities that help you advance your career and skills
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-md p-6 rounded-xl border border-white/20">
                <div className="text-3xl mb-3">⚡</div>
                <h3 className="text-lg font-semibold mb-2">Fast Applications</h3>
                <p className="text-white/80 text-sm">
                  Apply to multiple jobs quickly with our streamlined application process
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
}
