import { UserRole } from '@/types/roles';
import { getNavItemsByRole, getNavItemsByRoles } from '../navigation';

describe('Navigation with Dual-Role System', () => {
  describe('getNavItemsByRoles', () => {
    it('should return navigation items for a single role', () => {
      const primaryRole = UserRole.JOB_SEEKER;
      const items = getNavItemsByRoles(primaryRole);

      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Dashboard',
          roles: expect.arrayContaining([UserRole.JOB_SEEKER]),
        })
      );

      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'My Applications',
          roles: expect.arrayContaining([UserRole.JOB_SEEKER]),
        })
      );

      // Should not contain employer-only items
      expect(items).not.toContainEqual(
        expect.objectContaining({
          label: 'Talent Hub',
        })
      );

      // Should not contain referral partner items
      expect(items).not.toContainEqual(
        expect.objectContaining({
          label: 'Partner Dashboard',
        })
      );
    });

    it('should include referral partner menu items when user has REFERRAL_PARTNER as additional role', () => {
      const primaryRole = UserRole.JOB_SEEKER;
      const additionalRoles = [UserRole.REFERRAL_PARTNER];
      const items = getNavItemsByRoles(primaryRole, additionalRoles);

      // Should have both job seeker items
      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'My Applications',
          roles: expect.arrayContaining([UserRole.JOB_SEEKER]),
        })
      );

      // AND referral partner items
      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Partner Dashboard',
          href: '/referral-partner',
          roles: expect.arrayContaining([UserRole.REFERRAL_PARTNER]),
        })
      );

      // Earnings page has been moved to tabs within Partner Dashboard
      // No longer a separate navigation item
    });

    it('should handle employer with referral partner role', () => {
      const primaryRole = UserRole.EMPLOYER;
      const additionalRoles = [UserRole.REFERRAL_PARTNER];
      const items = getNavItemsByRoles(primaryRole, additionalRoles);

      // Should have employer items
      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Talent Hub',
          roles: expect.arrayContaining([UserRole.EMPLOYER]),
        })
      );

      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Jobs',
          roles: expect.arrayContaining([UserRole.EMPLOYER]),
        })
      );

      // AND referral partner items
      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Partner Dashboard',
          roles: expect.arrayContaining([UserRole.REFERRAL_PARTNER]),
        })
      );
    });

    it('should handle multiple additional roles', () => {
      const primaryRole = UserRole.JOB_SEEKER;
      const additionalRoles = [UserRole.REFERRAL_PARTNER, UserRole.ADMIN];
      const items = getNavItemsByRoles(primaryRole, additionalRoles);

      // Should have items from all roles
      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'My Applications', // Job seeker
        })
      );

      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Partner Dashboard', // Referral partner
        })
      );

      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Talent Hub', // Admin
        })
      );
    });

    it('should not duplicate items when roles overlap', () => {
      const primaryRole = UserRole.EMPLOYER;
      const additionalRoles = [UserRole.ADMIN]; // Both can access Talent Hub
      const items = getNavItemsByRoles(primaryRole, additionalRoles);

      const talentHubItems = items.filter(item => item.label === 'Talent Hub');
      expect(talentHubItems).toHaveLength(1);
    });

    it('should handle empty additional roles array', () => {
      const primaryRole = UserRole.EMPLOYER;
      const items = getNavItemsByRoles(primaryRole, []);

      // Should only have employer items
      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'Talent Hub',
        })
      );

      // Should not have referral partner items
      expect(items).not.toContainEqual(
        expect.objectContaining({
          label: 'Partner Dashboard',
        })
      );
    });
  });

  describe('getNavItemsByRole', () => {
    it('should return items for user with role when called', () => {
      const role = UserRole.JOB_SEEKER;
      const items = getNavItemsByRole(role);

      expect(items).toBeDefined();
      expect(items.length).toBeGreaterThan(0);
      expect(items).toContainEqual(
        expect.objectContaining({
          label: 'My Applications',
        })
      );
    });

    it('should filter out hidden items', () => {
      const items = getNavItemsByRole(UserRole.EMPLOYER);

      // Hidden items are still returned by getNavItemsByRole,
      // they are filtered in the component level
      const hiddenItems = items.filter(item => item.hidden);
      expect(hiddenItems.length).toBeGreaterThan(0);
    });
  });
});
