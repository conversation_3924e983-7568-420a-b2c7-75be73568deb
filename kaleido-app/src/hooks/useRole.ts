import { useEffect, useState } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { authService } from '@/services/auth.service';
import { UserRole } from '@/types/roles';

/**
 * Industry-standard hook for role-based access control
 * Gets role from JWT claims (server-verified)
 */
export const useRole = () => {
  const { user, isLoading: userLoading } = useUser();
  const [role, setRole] = useState<UserRole | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    const fetchRole = async () => {
      // Wait for user to load
      if (userLoading) return;

      // If we've already checked, don't check again
      if (hasChecked) return;

      // No user = no role
      if (!user) {
        setRole(null);
        setLoading(false);
        setHasChecked(true);
        return;
      }

      try {
        // Mark as checked early to prevent multiple calls
        setHasChecked(true);

        // Try to get role from JWT claims first
        const session = await authService.getSession();
        if (session?.role) {
          setRole(session.role);
          setLoading(false);
          return;
        }

        // Fallback: Check localStorage for role (temporary until JWT claims are configured)
        const roleData = localStorage.getItem(`userRole_${user.sub}`);
        if (roleData) {
          try {
            const parsed = JSON.parse(roleData);
            if (parsed.role && Object.values(UserRole).includes(parsed.role)) {
              setRole(parsed.role as UserRole);
              setLoading(false);
              return;
            }
          } catch (e) {
            // Silently ignore parse errors - don't log repeatedly
          }
        }

        // No role found
        setRole(null);
      } catch (error) {
        // Only log error once
        if (!hasChecked) {
          console.error('useRole: Failed to get role:', error);
        }
        setRole(null);
      } finally {
        setLoading(false);
      }
    };

    fetchRole();
  }, [user, userLoading, hasChecked]);

  return { role, loading: loading || userLoading };
};

/**
 * Check if user has specific role(s)
 */
export const useHasRole = (allowedRoles: UserRole | UserRole[]) => {
  const { role } = useRole();
  const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  return role ? roles.includes(role) : false;
};

/**
 * Check if user has specific permission(s)
 */
export const useHasPermission = (permission: string | string[]) => {
  const [hasPermission, setHasPermission] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        const permissions = Array.isArray(permission) ? permission : [permission];
        const results = await Promise.all(permissions.map(p => authService.hasPermission(p)));
        setHasPermission(results.every(r => r));
      } catch (error) {
        console.error('useHasPermission: Failed to check permission:', error);
        setHasPermission(false);
      } finally {
        setLoading(false);
      }
    };

    checkPermission();
  }, [permission]);

  return { hasPermission, loading };
};
