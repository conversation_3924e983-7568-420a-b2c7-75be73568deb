import useUser from '@/hooks/useUser';
import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';
import { useQuery } from '@tanstack/react-query';

import useFallbackUserData from './useFallbackUserData';

interface OnboardingSection {
  percentage: number;
  completed: boolean;
  completedAt: string | null;
  requiredFields?: string[];
  completedFields?: string[];
}

interface OnboardingStatus {
  overall: {
    percentage: number;
    completed: boolean;
    completedAt: string | null;
  };
  sections: {
    basicInfo: OnboardingSection;
    professionalInfo: OnboardingSection;
    preferences: OnboardingSection;
    additionalInfo: OnboardingSection;
  };
  lastUpdated: string | null;
  canApplyForJobs: boolean;
  hasCompletedOnboarding: boolean;
  isLinkedInImported?: boolean;
  linkedInImportDate?: string | null;
}

interface DashboardStats {
  userRole: UserRole;
  applications?: {
    total: number;
    statusBreakdown: Record<string, number>;
    recentApplications: any[];
  };
  matchedJobs?: {
    total: number;
    matchedThisWeek: number;
    byIndustry: Record<string, number>;
    recentMatches: any[];
  };
  notifications: {
    total: number;
    unread: number;
    recent: any[];
  };
  onboardingStatus?: OnboardingStatus;
  jobs?: any;
  candidates?: any;
  videoJDs?: any;
  culturalFit?: any;
  metrics?: any;
  subscription?: any;
  jobSeekerMetrics?: any;
  skills?: any;
}

interface CompanyData {
  id: string;
  clientId: string;
  companyName: string;
  companyWebsite: string;
  department: string;
  industry: string;
  size: string;
  location: string;
  contactEmail: string;
  phoneNumber: string;
  logo: string;
  description: string;
  preferences: any;
  layoutPreference: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  heroImage: string;
  featuredImages: string[];
  customCss: string;
  atsConfig: any;
  socialMediaConnectors: any;
  isPublished: boolean;
  jobCount: number;
  candidateUploadCount: number;
  companyValues: any[];
  isDemoMode: boolean;
  isApproved: boolean;
  declineReason: string | null;
  subscriptionPlan: string;
  subscriptionCredits: any;
  subscriptionStartDate: string;
  subscriptionEndDate: string;
  subscriptionAutoRenew: boolean;
  subscriptionPaymentMethod: string | null;
  subscriptionPaymentId: string | null;
  stripeCustomerId: string | null;
  stripeSubscriptionId: string | null;
  onboardingRequired: boolean;
}

interface JobSeekerProfile {
  id?: string;
  hasCompletedOnboarding?: boolean;
  onboardingRequired?: boolean;
}

interface EnhancedUserData {
  userRole: UserRole;
  dashboardStats: DashboardStats;
  company?: CompanyData | null;
  profile?: JobSeekerProfile | null;
}

interface UseEnhancedUserDataReturn {
  userData: EnhancedUserData | null;
  isLoading: boolean;
  error: unknown;
  refetch: () => void;
  isFallback: boolean;
}

/**
 * Enhanced hook that fetches comprehensive user data including dashboard stats and company data
 * This replaces the need for separate useDashboardStats and company data calls
 * The backend determines the authoritative user role, not URL parameters
 */
export function useEnhancedUserData(): UseEnhancedUserDataReturn {
  const { user } = useUser();
  const fallbackData = useFallbackUserData();
  const clientId = user?.sub;
  const isEnabled = !!clientId;

  const {
    data: userData,
    isLoading,
    error,
    refetch,
  } = useQuery<EnhancedUserData>({
    queryKey: ['enhancedUserData', clientId],
    queryFn: async () => {
      if (!clientId) {
        return null;
      }

      try {
        // Use the new enhanced stats endpoint that includes company data
        const response = await apiHelper.get('/dashboard/enhanced-stats');

        if (!response) {
          return null;
        }

        // Extract the user role from the response (this is authoritative from backend)
        const userRole = response.userRole as UserRole;

        // Structure the response for consistent usage
        return {
          userRole,
          dashboardStats: response,
          company: response.company || null,
          profile: response.profile || null,
        };
      } catch (error) {
        console.error('useEnhancedUserData: Error in queryFn:', error);
        throw error; // Re-throw to trigger retry logic
      }
    },
    enabled: isEnabled,
    staleTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
    // Retry configuration for better error handling
    retry: (failureCount, error: any) => {
      // Don't retry on 401/403 errors (authentication issues)
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      // Don't retry on 404 errors (user not found)
      if (error?.response?.status === 404) {
        return false;
      }
      // Don't retry on 429 errors (rate limit) - use fallback instead
      if (error?.response?.status === 429) {
        return false;
      }
      // Don't retry on circuit breaker errors
      if (
        error?.message?.includes('circuit breaker') ||
        error?.message?.includes('Circuit breaker')
      ) {
        return false;
      }
      // Retry up to 2 times for server errors (5xx) and network issues
      return failureCount < 2;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Determine if we should use fallback data
  // Be more aggressive about using fallback for rate limit and circuit breaker errors
  const isRateLimitError = (error as any)?.response?.status === 429;
  const isCircuitBreakerError =
    (error as any)?.message?.includes('circuit breaker') ||
    (error as any)?.message?.includes('Circuit breaker');
  const shouldUseFallback =
    !isLoading &&
    (error || !userData) &&
    !!fallbackData &&
    (isRateLimitError || isCircuitBreakerError || !userData);
  const finalUserData: EnhancedUserData | null =
    shouldUseFallback && fallbackData
      ? {
          userRole: fallbackData.userRole,
          dashboardStats: fallbackData.dashboardStats,
          company: fallbackData.company,
          profile: fallbackData.profile || null,
        }
      : userData || null;

  // Log errors for debugging (since onError is not available in newer TanStack Query)
  if (error) {
    const errorStatus = (error as any)?.response?.status;
    const errorMessage = (error as any)?.message;

    if (errorStatus === 429) {
      console.warn(
        '⚠️ Rate limit error for enhanced user data - using fallback data if available:',
        {
          status: errorStatus,
          message: errorMessage,
          clientId,
          hasFallback: !!fallbackData,
        }
      );
    } else if (errorMessage?.includes('circuit breaker')) {
      console.warn(
        '⚠️ Circuit breaker active for enhanced user data - using fallback data if available:',
        {
          message: errorMessage,
          clientId,
          hasFallback: !!fallbackData,
        }
      );
    } else {
      console.error('Enhanced user data fetch error:', {
        status: errorStatus,
        message: errorMessage,
        clientId,
        hasFallback: !!fallbackData,
      });
    }
  }

  // If we have an error and no fallback data, we should show the error
  // If we have fallback data, we should not show loading or error
  const finalError = error && !shouldUseFallback ? error : null;
  const finalIsLoading = isLoading && !shouldUseFallback;

  return {
    userData: finalUserData,
    isLoading: finalIsLoading,
    error: finalError,
    refetch,
    isFallback: shouldUseFallback,
  };
}

export default useEnhancedUserData;
