import React from 'react';

import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/shared/types';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';

import useEnhancedUserData from '../useEnhancedUserData';

// Mock dependencies
jest.mock('@/hooks/useUser', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('@/hooks/useFallbackUserData', () => ({
  __esModule: true,
  default: jest.fn(),
}));

jest.mock('@/lib/apiHelper', () => ({
  get: jest.fn(),
}));

const mockUseUser = require('@/hooks/useUser').default;
const mockUseFallbackUserData = require('@/hooks/useFallbackUserData').default;
const mockApiHelper = apiHelper as jest.Mocked<typeof apiHelper>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });

  return function Wrapper({ children }: { children: React.ReactNode }) {
    return React.createElement(QueryClientProvider, { client: queryClient }, children);
  };
};

describe('useEnhancedUserData', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementations
    mockUseUser.mockReturnValue({
      user: { sub: 'test-user-id' },
    });

    mockUseFallbackUserData.mockReturnValue(null);
  });

  it('should return loading state initially', () => {
    const { result } = renderHook(() => useEnhancedUserData(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isLoading).toBe(true);
    expect(result.current.userData).toBe(null);
    expect(result.current.isFallback).toBe(false);
  });

  it('should fetch and return enhanced user data successfully', async () => {
    const mockResponse = {
      userRole: UserRole.EMPLOYER,
      jobs: { total: 5 },
      candidates: { total: 10 },
      notifications: { total: 3, unread: 1 },
      subscription: { plan: 'premium' },
      company: { id: 'company-1', companyName: 'Test Company' },
    };

    mockApiHelper.get.mockResolvedValueOnce(mockResponse);

    const { result } = renderHook(() => useEnhancedUserData(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.userData).toEqual({
      userRole: UserRole.EMPLOYER,
      dashboardStats: mockResponse,
      company: mockResponse.company,
      profile: null,
    });
    expect(result.current.isFallback).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should use fallback data when API call fails', async () => {
    const mockFallbackData = {
      userRole: UserRole.JOB_SEEKER,
      dashboardStats: {
        userRole: UserRole.JOB_SEEKER,
        notifications: { total: 0, unread: 0, recent: [] },
        applications: { total: 0, statusBreakdown: {}, recentApplications: [] },
      },
      company: null,
      profile: null,
    };

    // Mock API to reject with network error (will trigger retries)
    mockApiHelper.get.mockRejectedValue(new Error('Network error'));
    mockUseFallbackUserData.mockReturnValue(mockFallbackData);

    const { result } = renderHook(() => useEnhancedUserData(), {
      wrapper: createWrapper(),
    });

    // Wait for the query to complete (including all retries - can take up to 7 seconds with exponential backoff)
    await waitFor(
      () => {
        expect(result.current.isLoading).toBe(false);
      },
      { timeout: 10000 }
    );

    // Should use fallback data when API fails
    expect(result.current.isFallback).toBe(true);
    expect(result.current.userData).toEqual(mockFallbackData);
    expect(result.current.error).toBeNull(); // Error should be null when using fallback
  }, 15000);

  it('should not retry on 401 authentication errors', async () => {
    const authError = {
      response: { status: 401 },
      message: 'Unauthorized',
    };

    mockApiHelper.get.mockRejectedValueOnce(authError);

    const { result } = renderHook(() => useEnhancedUserData(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should only call API once (no retries for auth errors)
    expect(mockApiHelper.get).toHaveBeenCalledTimes(1);
    expect(result.current.error).toBeTruthy();
  });

  it('should not retry on 404 user not found errors', async () => {
    const notFoundError = {
      response: { status: 404 },
      message: 'User not found',
    };

    mockApiHelper.get.mockRejectedValueOnce(notFoundError);

    const { result } = renderHook(() => useEnhancedUserData(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should only call API once (no retries for 404 errors)
    expect(mockApiHelper.get).toHaveBeenCalledTimes(1);
    expect(result.current.error).toBeTruthy();
  });

  it('should return null when user is not available', () => {
    mockUseUser.mockReturnValue({
      user: null,
    });

    const { result } = renderHook(() => useEnhancedUserData(), {
      wrapper: createWrapper(),
    });

    expect(result.current.userData).toBe(null);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isFallback).toBe(false);
  });

  it('should handle different user roles correctly', async () => {
    const testCases = [
      {
        role: UserRole.ADMIN,
        expectedData: { userRole: UserRole.ADMIN, hasCompany: true },
      },
      {
        role: UserRole.JOB_SEEKER,
        expectedData: { userRole: UserRole.JOB_SEEKER, hasCompany: false },
      },
      {
        role: UserRole.GRADUATE,
        expectedData: { userRole: UserRole.GRADUATE, hasCompany: false },
      },
    ];

    for (const testCase of testCases) {
      const mockResponse = {
        userRole: testCase.role,
        notifications: { total: 0, unread: 0, recent: [] },
        company: testCase.expectedData.hasCompany ? { id: 'company-1' } : null,
      };

      mockApiHelper.get.mockResolvedValueOnce(mockResponse);

      const { result } = renderHook(() => useEnhancedUserData(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.userData?.userRole).toBe(testCase.role);
      expect(!!result.current.userData?.company).toBe(testCase.expectedData.hasCompany);
    }
  });
});
