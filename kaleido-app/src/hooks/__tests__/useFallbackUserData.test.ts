import { renderHook } from '@testing-library/react';

import { UserRole } from '@/types/roles';

import useFallbackUserData from '../useFallbackUserData';

// Mock dependencies
jest.mock('@auth0/nextjs-auth0/client', () => ({
  useUser: jest.fn(),
}));

const mockUseUser = require('@auth0/nextjs-auth0/client').useUser;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('useFallbackUserData', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('should return null when user is not available', () => {
    mockUseUser.mockReturnValue({
      user: null,
    });

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current).toBe(null);
  });

  it('should return fallback data with cached role from localStorage', () => {
    const mockUser = { sub: 'test-user-id' };
    const cachedRoleData = JSON.stringify({ role: UserRole.EMPLOYER });

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue(cachedRoleData);

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current).toEqual({
      userRole: UserRole.EMPLOYER,
      dashboardStats: expect.objectContaining({
        userRole: UserRole.EMPLOYER,
        notifications: expect.any(Object),
        jobs: expect.any(Object),
        candidates: expect.any(Object),
        subscription: expect.any(Object),
      }),
      company: null,
      profile: null,
    });
  });

  it('should use Auth0 role when localStorage is empty', () => {
    const mockUser = {
      sub: 'test-user-id',
      role: UserRole.JOB_SEEKER,
    };

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current).toEqual({
      userRole: UserRole.JOB_SEEKER,
      dashboardStats: expect.objectContaining({
        userRole: UserRole.JOB_SEEKER,
        notifications: expect.any(Object),
        applications: expect.any(Object),
        matchedJobs: expect.any(Object),
      }),
      company: null,
      profile: null,
    });
  });

  it('should use role from custom Auth0 claims', () => {
    const mockUser = {
      sub: 'test-user-id',
      'https://headstart.com/roles': [UserRole.GRADUATE],
    };

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current?.userRole).toBe(UserRole.GRADUATE);
  });

  it('should default to JOB_SEEKER when no role is found', () => {
    const mockUser = { sub: 'test-user-id' };

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current?.userRole).toBe(UserRole.JOB_SEEKER);
  });

  it('should handle corrupted localStorage data gracefully', () => {
    const mockUser = { sub: 'test-user-id' };

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue('invalid-json');

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current?.userRole).toBe(UserRole.JOB_SEEKER);
  });

  it('should provide employer-specific stats for employer roles', () => {
    const mockUser = { sub: 'test-user-id' };
    const cachedRoleData = JSON.stringify({ role: UserRole.EMPLOYER });

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue(cachedRoleData);

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current?.dashboardStats).toEqual(
      expect.objectContaining({
        userRole: UserRole.EMPLOYER,
        jobs: expect.objectContaining({
          total: 0,
          statusBreakdown: {},
          recentJobs: [],
        }),
        candidates: expect.objectContaining({
          total: 0,
          matched: 0,
          recentMatches: [],
        }),
        subscription: expect.objectContaining({
          plan: 'free',
          credits: expect.objectContaining({
            usedCredits: 0,
            totalCredits: 0,
            remainingCredits: 0,
          }),
        }),
      })
    );
  });

  it('should provide job-seeker-specific stats for job seeker roles', () => {
    const mockUser = { sub: 'test-user-id' };
    const cachedRoleData = JSON.stringify({ role: UserRole.JOB_SEEKER });

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue(cachedRoleData);

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current?.dashboardStats).toEqual(
      expect.objectContaining({
        userRole: UserRole.JOB_SEEKER,
        applications: expect.objectContaining({
          total: 0,
          statusBreakdown: {},
          recentApplications: [],
        }),
        matchedJobs: expect.objectContaining({
          total: 0,
          matchedThisWeek: 0,
          recentMatches: [],
        }),
      })
    );
  });

  it('should provide admin stats similar to employer', () => {
    const mockUser = { sub: 'test-user-id' };
    const cachedRoleData = JSON.stringify({ role: UserRole.ADMIN });

    mockUseUser.mockReturnValue({ user: mockUser });
    mockLocalStorage.getItem.mockReturnValue(cachedRoleData);

    const { result } = renderHook(() => useFallbackUserData());

    expect(result.current?.dashboardStats).toEqual(
      expect.objectContaining({
        userRole: UserRole.ADMIN,
        jobs: expect.any(Object),
        candidates: expect.any(Object),
        subscription: expect.any(Object),
      })
    );
  });
});
