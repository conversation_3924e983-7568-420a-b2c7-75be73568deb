import { renderHook, waitFor } from '@testing-library/react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useRole, useHasRole } from '../useRole';
import { authService } from '@/services/auth.service';
import { UserRole } from '@/types/roles';

// Mock dependencies
jest.mock('@auth0/nextjs-auth0/client');
jest.mock('@/services/auth.service');

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockAuthService = authService as jest.Mocked<typeof authService>;

describe('useRole Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();

    // Reset the mock implementation
    mockAuthService.getSession = jest.fn();
  });

  describe('useRole', () => {
    it('should return null when no user is authenticated', async () => {
      mockUseUser.mockReturnValue({
        user: null,
        error: undefined,
        isLoading: false,
      });

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.role).toBeNull();
      expect(mockAuthService.getSession).not.toHaveBeenCalled();
    });

    it('should return role from JWT claims when available', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: 'auth0|123' },
        role: UserRole.JOB_SEEKER,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.role).toBe(UserRole.JOB_SEEKER);
      expect(mockAuthService.getSession).toHaveBeenCalledTimes(1);
    });

    it('should fallback to localStorage when JWT claims have no role', async () => {
      const userId = 'auth0|123';
      mockUseUser.mockReturnValue({
        user: { sub: userId, email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: userId },
        role: null,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      // Set role in localStorage
      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.EMPLOYER, clientId: userId })
      );

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.role).toBe(UserRole.EMPLOYER);
    });

    it('should handle invalid role data in localStorage gracefully', async () => {
      const userId = 'auth0|123';
      mockUseUser.mockReturnValue({
        user: { sub: userId, email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: userId },
        role: null,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      // Set invalid JSON in localStorage
      localStorage.setItem(`userRole_${userId}`, 'invalid-json');

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.role).toBeNull();
    });

    it('should handle auth service errors gracefully', async () => {
      const userId = 'auth0|123';
      mockUseUser.mockReturnValue({
        user: { sub: userId, email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockRejectedValue(new Error('Auth service error'));

      // Mock console.error for this test to suppress expected error output
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.role).toBeNull();

      // Verify error was logged and restore console.error
      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });

    it('should only check role once to prevent multiple API calls', async () => {
      const userId = 'auth0|123';
      mockUseUser.mockReturnValue({
        user: { sub: userId, email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: userId },
        role: UserRole.GRADUATE,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result, rerender } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.role).toBe(UserRole.GRADUATE);
      expect(mockAuthService.getSession).toHaveBeenCalledTimes(1);

      // Rerender the hook
      rerender();

      // Should not call getSession again
      expect(mockAuthService.getSession).toHaveBeenCalledTimes(1);
    });

    it('should show loading state while user is loading', () => {
      mockUseUser.mockReturnValue({
        user: null,
        error: undefined,
        isLoading: true,
      });

      const { result } = renderHook(() => useRole());

      expect(result.current.loading).toBe(true);
      expect(result.current.role).toBeNull();
      expect(mockAuthService.getSession).not.toHaveBeenCalled();
    });
  });

  describe('useHasRole', () => {
    it('should return true when user has the required role', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: 'auth0|123' },
        role: UserRole.ADMIN,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useHasRole(UserRole.ADMIN));

      await waitFor(() => {
        expect(result.current).toBe(true);
      });
    });

    it('should return true when user has one of the required roles', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: 'auth0|123' },
        role: UserRole.EMPLOYER,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useHasRole([UserRole.ADMIN, UserRole.EMPLOYER]));

      await waitFor(() => {
        expect(result.current).toBe(true);
      });
    });

    it('should return false when user does not have the required role', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: 'auth0|123' },
        role: UserRole.JOB_SEEKER,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useHasRole(UserRole.ADMIN));

      await waitFor(() => {
        expect(result.current).toBe(false);
      });
    });

    it('should return false when user has no role', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession.mockResolvedValue({
        user: { sub: 'auth0|123' },
        role: null,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useHasRole(UserRole.JOB_SEEKER));

      await waitFor(() => {
        expect(result.current).toBe(false);
      });
    });
  });
});
