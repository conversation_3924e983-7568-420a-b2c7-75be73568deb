import { useUser } from '@auth0/nextjs-auth0/client';

import { UserRole } from '@/types/roles';

interface FallbackUserData {
  userRole: UserRole;
  dashboardStats: any;
  company?: any;
  profile?: any;
}

/**
 * Fallback hook that provides basic user data when the backend is unavailable
 * This uses localStorage and Auth0 user data to determine a reasonable fallback role
 */
export function useFallbackUserData(): FallbackUserData | null {
  const { user } = useUser();

  if (!user?.sub) {
    return null;
  }

  // Try to get role from localStorage first
  const cachedRoleData = localStorage.getItem(`userRole_${user.sub}`);
  let fallbackRole: UserRole = UserRole.JOB_SEEKER; // Default fallback

  if (cachedRoleData) {
    try {
      const parsed = JSON.parse(cachedRoleData);
      fallbackRole = parsed.role || UserRole.JOB_SEEKER;
    } catch (error) {
      console.warn('Failed to parse cached role data:', error);
    }
  } else {
    // Try to determine role from Auth0 user data
    const auth0Role = user.role || user['https://headstart.com/roles']?.[0];
    if (auth0Role && Object.values(UserRole).includes(auth0Role as UserRole)) {
      fallbackRole = auth0Role as UserRole;
    }
  }

  // Create minimal dashboard stats for fallback
  const fallbackDashboardStats = {
    userRole: fallbackRole,
    notifications: {
      total: 0,
      unread: 0,
      recent: [],
    },
    // Add role-specific minimal stats
    ...(fallbackRole === UserRole.EMPLOYER || fallbackRole === UserRole.ADMIN
      ? {
          jobs: {
            total: 0,
            statusBreakdown: {},
            recentJobs: [],
          },
          candidates: {
            total: 0,
            matched: 0,
            recentMatches: [],
          },
          subscription: {
            plan: 'free',
            credits: {
              usedCredits: 0,
              totalCredits: 0,
              remainingCredits: 0,
            },
          },
        }
      : {
          applications: {
            total: 0,
            statusBreakdown: {},
            recentApplications: [],
          },
          matchedJobs: {
            total: 0,
            matchedThisWeek: 0,
            recentMatches: [],
          },
        }),
  };

  return {
    userRole: fallbackRole,
    dashboardStats: fallbackDashboardStats,
    company: null,
    profile: null,
  };
}

export default useFallbackUserData;
