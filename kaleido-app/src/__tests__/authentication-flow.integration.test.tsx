import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { UserProvider } from '@auth0/nextjs-auth0/client';
import AppLayout from '@/components/steps/layout/AppLayout';
import { UserRole } from '@/types/roles';
import { apiClient, logoutUser } from '@/lib/apiHelper';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/dashboard',
}));

// Mock API helper
jest.mock('@/lib/apiHelper', () => ({
  apiClient: {
    get: jest.fn(),
    put: jest.fn(),
  },
  logoutUser: jest.fn(),
}));

// Mock Auth0
jest.mock('@auth0/nextjs-auth0/client', () => ({
  UserProvider: ({ children }: { children: React.ReactNode }) => children,
  useUser: () => ({
    user: { sub: 'auth0|jobseeker123', email: '<EMAIL>' },
    isLoading: false,
  }),
}));

// Mock custom hooks
jest.mock('@/hooks/useUser', () => ({
  __esModule: true,
  default: () => ({
    user: { sub: 'auth0|jobseeker123', email: '<EMAIL>' },
    isLoading: false,
  }),
  useUser: () => ({
    user: { sub: 'auth0|jobseeker123', email: '<EMAIL>' },
    isLoading: false,
  }),
}));

jest.mock('@/hooks/useRole', () => ({
  useRole: () => ({
    role: null, // Will be set in individual tests
    loading: false,
  }),
}));

jest.mock('@/hooks/useEnhancedUserData', () => ({
  __esModule: true,
  default: () => ({
    userData: null, // Will be set in individual tests
    isLoading: false,
    error: null,
    refetch: jest.fn(),
    isFallback: false,
  }),
}));

// Mock additional hooks
jest.mock('@/hooks/useAuth0Token', () => ({
  useAuth0Token: () => ({
    user: { sub: 'auth0|jobseeker123', email: '<EMAIL>' },
    isLoading: false,
  }),
}));

jest.mock('@/hooks/useUserRoles', () => ({
  useUserRoles: () => ({
    additionalRoles: [],
    isLoading: false,
  }),
}));

jest.mock('@/contexts/jobs/JobsContext', () => ({
  useJobs: () => ({
    job: {},
    setFullJobDescription: jest.fn(),
  }),
}));

jest.mock('@/stores/matchRankJobsStore', () => ({
  useMatchRankJobsStore: () => ({
    jobs: {},
    activeJobs: [],
    updateJobStatus: jest.fn(),
    removeJob: jest.fn(),
  }),
}));

jest.mock('@/stores/onboardingStore', () => ({
  useOnboardingStore: () => ({
    profile: null,
    isLoading: false,
    shouldShowSetupSlider: false,
    hasCheckedOnboarding: true,
    fetchProfile: jest.fn(),
    setShouldShowSetupSlider: jest.fn(),
    markOnboardingComplete: jest.fn(),
    setUserRole: jest.fn(), // Add missing setUserRole function
  }),
}));

jest.mock('@/hooks/useProactiveProfileValidation', () => ({
  __esModule: true,
  default: () => ({
    shouldShowAlert: false,
    dismissAlert: jest.fn(),
    validation: null,
  }),
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  ...jest.requireActual('framer-motion'),
  AnimatePresence: ({ children }: any) => children,
  motion: {
    div: ({ children, initial, animate, exit, transition, ...props }: any) => (
      <div {...props}>{children}</div>
    ),
    main: ({ children, animate, transition, ...props }: any) => <main {...props}>{children}</main>,
  },
}));

// Mock layout components to avoid setUserRole errors
jest.mock('@/components/steps/layout/EmployerLayout', () => ({
  __esModule: true,
  default: ({ children }: any) => <div data-testid="employer-layout">{children}</div>,
}));

jest.mock('@/components/steps/layout/AdminLayout', () => ({
  __esModule: true,
  default: ({ children }: any) => <div data-testid="admin-layout">{children}</div>,
}));

jest.mock('@/components/steps/layout/GraduateLayout', () => ({
  __esModule: true,
  default: ({ children }: any) => <div data-testid="graduate-layout">{children}</div>,
}));

jest.mock('@/components/steps/layout/ReferralPartnerLayout', () => ({
  __esModule: true,
  default: ({ children }: any) => <div data-testid="referral-partner-layout">{children}</div>,
}));

jest.mock('@/components/steps/layout/JobSeekerLayout', () => ({
  __esModule: true,
  default: ({ children }: any) => <div data-testid="job-seeker-layout">{children}</div>,
}));

// Mock other components
jest.mock('@/components/IntercomSupport', () => ({
  __esModule: true,
  default: () => null,
}));

jest.mock('@/components/help', () => ({
  HelpWidget: () => null,
}));

jest.mock('@/components/shared/StatusCompletionModalManager', () => ({
  __esModule: true,
  default: () => null,
}));

jest.mock('@/components/layout/Header', () => ({
  Header: () => <header data-testid="header">Header</header>,
}));

jest.mock('@/components/layout/Sidebar', () => ({
  Sidebar: () => <div data-testid="sidebar">Sidebar</div>,
}));

jest.mock('@/components/layout/MobileMenu', () => ({
  MobileMenu: () => null,
}));

jest.mock('@/components/shared/GenericStatusManager/GenericStatusManager', () => ({
  GenericStatusManager: () => null,
}));

jest.mock('@/components/steps/preview/PreviewContent', () => ({
  __esModule: true,
  default: () => null,
}));

jest.mock('@/components/JobSeeker/components/IncompleteProfileAlert', () => ({
  IncompleteProfileAlert: () => null,
}));

jest.mock('@/components/JobSeeker/JobSeekerSetupSlider', () => ({
  JobSeekerSetupSlider: () => null,
}));

jest.mock('pubsub-js', () => ({
  subscribe: () => 'token',
  unsubscribe: () => {},
}));

jest.mock('@/components/AccessDenied', () => ({
  __esModule: true,
  default: ({ message }: any) => <div data-testid="access-denied">{message}</div>,
}));

// Mock window.location
Object.defineProperty(window, 'location', {
  writable: true,
  value: {
    href: 'http://localhost:3000/dashboard',
    pathname: '/dashboard',
    search: '',
    hash: '',
    origin: 'http://localhost:3000',
    hostname: 'localhost',
    port: '3000',
    protocol: 'http:',
    replace: jest.fn(),
  },
});

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  // eslint-disable-next-line @next/next/no-img-element
  default: ({ src, alt }: any) => <img src={src} alt={alt} />,
}));

// Mock ColorfulSmokeyOrbLoader component
jest.mock('@/components/Layouts/ColourfulLoader', () => ({
  __esModule: true,
  default: () => (
    <div role="status" aria-label="Loading">
      Loading...
    </div>
  ),
}));

describe('Authentication Flow Integration Tests', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    (logoutUser as jest.Mock).mockClear();
    sessionStorage.clear();

    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });

    // Set auth session as ready
    sessionStorage.setItem('auth_session_ready', 'true');

    // Mock console.warn to prevent noise in tests
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <UserProvider>{component}</UserProvider>
      </QueryClientProvider>
    );
  };

  describe('Job Seeker Login Flow', () => {
    it('should handle job seeker login with role from backend', async () => {
      // Update mocks to return the correct role
      const mockUseRole = jest.requireMock('@/hooks/useRole');
      mockUseRole.useRole = jest.fn(() => ({
        role: UserRole.JOB_SEEKER,
        loading: false,
      }));

      const mockUseEnhancedUserData = jest.requireMock('@/hooks/useEnhancedUserData');
      mockUseEnhancedUserData.default = jest.fn(() => ({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: {
            applications: { total: 5 },
            matchedJobs: { total: 10 },
            notifications: { total: 2, unread: 1 },
          },
          profile: {
            id: 'profile123',
            hasCompletedOnboarding: true,
          },
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      }));

      // Mock the enhanced stats API call
      (apiClient.get as jest.Mock).mockResolvedValueOnce({
        userRole: UserRole.JOB_SEEKER,
        dashboardStats: {
          applications: { total: 5 },
          matchedJobs: { total: 10 },
          notifications: { total: 2, unread: 1 },
        },
        profile: {
          id: 'profile123',
          hasCompletedOnboarding: true,
        },
      });

      // Mock the auth/me endpoint
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|jobseeker123',
            email: '<EMAIL>',
            role: UserRole.JOB_SEEKER,
          },
          accessToken: 'valid-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      });

      renderWithProviders(
        <AppLayout>
          <div>Job Seeker Dashboard</div>
        </AppLayout>
      );

      // Wait for role detection and layout rendering
      await waitFor(() => {
        expect(screen.getByTestId('job-seeker-layout')).toBeInTheDocument();
      });

      expect(screen.getByText('Job Seeker Dashboard')).toBeInTheDocument();
    });

    it('should handle job seeker login with role from localStorage fallback', async () => {
      const userId = 'auth0|jobseeker123';

      // Set role in localStorage
      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.JOB_SEEKER, clientId: userId })
      );

      // Update mocks to use localStorage role - the AppLayout should fallback to localStorage
      const mockUseRole = jest.requireMock('@/hooks/useRole');
      mockUseRole.useRole = jest.fn(() => ({
        role: null, // No role from JWT
        loading: false,
      }));

      const mockUseEnhancedUserData = jest.requireMock('@/hooks/useEnhancedUserData');
      mockUseEnhancedUserData.default = jest.fn(() => ({
        userData: null, // No data from backend to trigger localStorage fallback
        isLoading: false,
        error: null, // No error, just no data
        refetch: jest.fn(),
        isFallback: false,
      }));

      // Mock the enhanced stats API to fail
      (apiClient.get as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      // Mock the auth/me endpoint without role
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: {
            sub: userId,
            email: '<EMAIL>',
          },
          accessToken: 'valid-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      });

      renderWithProviders(
        <AppLayout>
          <div>Job Seeker Dashboard</div>
        </AppLayout>
      );

      // Wait for role detection from localStorage
      await waitFor(() => {
        expect(screen.getByTestId('job-seeker-layout')).toBeInTheDocument();
      });

      expect(screen.getByText('Job Seeker Dashboard')).toBeInTheDocument();
    });

    it('should handle missing role gracefully', async () => {
      // Mock console.error to avoid test noise
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});

      // Update mocks to return no role
      const mockUseRole = jest.requireMock('@/hooks/useRole');
      mockUseRole.useRole = jest.fn(() => ({
        role: null,
        loading: false,
      }));

      const mockUseEnhancedUserData = jest.requireMock('@/hooks/useEnhancedUserData');
      mockUseEnhancedUserData.default = jest.fn(() => ({
        userData: {
          dashboardStats: {},
          // No userRole property
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      }));

      // Mock the enhanced stats API to return no role
      (apiClient.get as jest.Mock).mockResolvedValueOnce({
        dashboardStats: {},
      });

      // Mock the auth/me endpoint without role
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|jobseeker123',
            email: '<EMAIL>',
          },
          accessToken: 'valid-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      });

      // Set old login time to bypass grace period
      localStorage.setItem('last_login_auth0|jobseeker123', '1');

      // Clear any stored role to ensure no fallback
      localStorage.removeItem('userRole_auth0|jobseeker123');

      renderWithProviders(
        <AppLayout>
          <div>Content</div>
        </AppLayout>
      );

      // Should eventually call logoutUser
      await waitFor(
        () => {
          expect(logoutUser).toHaveBeenCalledWith({
            returnTo: expect.any(String),
          });
        },
        { timeout: 5000 }
      );

      // Should log error once
      expect(consoleError).toHaveBeenCalledWith(
        'AppLayout: No valid role found for user',
        expect.objectContaining({
          userId: 'auth0|jobseeker123',
        })
      );

      // Restore console.error mock
      consoleError.mockRestore();
    });
  });

  describe('Role Update Flow', () => {
    it('should update role when URL parameter is provided', async () => {
      const userId = 'auth0|user123';

      // Update mocks for role update flow to show EMPLOYER role
      const mockUseRole = jest.requireMock('@/hooks/useRole');
      mockUseRole.useRole = jest.fn(() => ({
        role: UserRole.EMPLOYER,
        loading: false,
      }));

      const mockUseEnhancedUserData = jest.requireMock('@/hooks/useEnhancedUserData');
      mockUseEnhancedUserData.default = jest.fn(() => ({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {},
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      }));

      // Mock the auth/me endpoint
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: {
            sub: userId,
            email: '<EMAIL>',
            role: UserRole.EMPLOYER,
          },
          accessToken: 'valid-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      });

      renderWithProviders(
        <AppLayout>
          <div>Employer Dashboard</div>
        </AppLayout>
      );

      // Should render employer layout
      await waitFor(() => {
        expect(screen.getByTestId('employer-layout')).toBeInTheDocument();
      });

      expect(screen.getByText('Employer Dashboard')).toBeInTheDocument();
    });
  });

  describe('Session Expiration Handling', () => {
    it('should redirect to logout when session is expired', async () => {
      // Update mocks to simulate no valid role and expired session
      const mockUseRole = jest.requireMock('@/hooks/useRole');
      mockUseRole.useRole = jest.fn(() => ({
        role: null,
        loading: false,
      }));

      const mockUseEnhancedUserData = jest.requireMock('@/hooks/useEnhancedUserData');
      mockUseEnhancedUserData.default = jest.fn(() => ({
        userData: null,
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      }));

      // Mock useUser to simulate auth0 user
      const mockUseUser = jest.requireMock('@/hooks/useUser');
      mockUseUser.default = jest.fn(() => ({
        user: { sub: 'auth0|expired123', email: '<EMAIL>' },
        isLoading: false,
      }));

      // Set old login time to trigger logout logic
      localStorage.setItem('last_login_auth0|expired123', '1');

      // Mock the auth/me endpoint to return session expired
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: false,
        json: async () => ({
          requiresLogout: true,
          error: 'Session expired',
          code: 'SESSION_EXPIRED',
        }),
      });

      renderWithProviders(
        <AppLayout>
          <div>Content</div>
        </AppLayout>
      );

      // Should call logoutUser for session expiration
      await waitFor(
        () => {
          expect(logoutUser).toHaveBeenCalled();
        },
        { timeout: 5000 }
      );
    });
  });

  describe('Error Recovery', () => {
    it('should show fallback warning when using cached data due to network error', async () => {
      const userId = 'auth0|jobseeker123';

      // Set up fallback data in localStorage
      localStorage.setItem(
        `fallbackUserData_${userId}`,
        JSON.stringify({
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: { cached: true },
          timestamp: Date.now(),
        })
      );

      // Update mocks to use fallback data
      const mockUseRole = jest.requireMock('@/hooks/useRole');
      mockUseRole.useRole = jest.fn(() => ({
        role: UserRole.JOB_SEEKER,
        loading: false,
      }));

      const mockUseEnhancedUserData = jest.requireMock('@/hooks/useEnhancedUserData');
      mockUseEnhancedUserData.default = jest.fn(() => ({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: { cached: true },
        },
        isLoading: false,
        error: new Error('Network error'),
        refetch: jest.fn(),
        isFallback: true, // This is important for showing the warning
      }));

      // Mock the enhanced stats API to fail
      (apiClient.get as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      // Mock the auth/me endpoint
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: {
            sub: userId,
            email: '<EMAIL>',
            role: UserRole.JOB_SEEKER,
          },
          accessToken: 'valid-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      });

      renderWithProviders(
        <AppLayout>
          <div>Dashboard Content</div>
        </AppLayout>
      );

      // When there's an error fetching user data, it shows AccessDenied component
      // This is the expected behavior based on the AppLayout component logic
      await waitFor(() => {
        expect(screen.getByTestId('access-denied')).toBeInTheDocument();
      });

      expect(screen.getByText(/Unable to load user data/)).toBeInTheDocument();
    });
  });

  describe('Performance Optimizations', () => {
    it('should not make redundant API calls when role is already determined', async () => {
      // Mock the auth/me endpoint with role
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|jobseeker123',
            email: '<EMAIL>',
            role: UserRole.JOB_SEEKER,
          },
          accessToken: 'valid-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      });

      // Update mocks for performance test
      const mockUseRole = jest.requireMock('@/hooks/useRole');
      mockUseRole.useRole = jest.fn(() => ({
        role: UserRole.JOB_SEEKER,
        loading: false,
      }));

      const mockUseEnhancedUserData = jest.requireMock('@/hooks/useEnhancedUserData');
      mockUseEnhancedUserData.default = jest.fn(() => ({
        userData: {
          userRole: UserRole.JOB_SEEKER,
          dashboardStats: {},
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
        isFallback: false,
      }));

      // Mock the enhanced stats API
      (apiClient.get as jest.Mock).mockResolvedValueOnce({
        userRole: UserRole.JOB_SEEKER,
        dashboardStats: {},
      });

      // Mock useUser to return specific user ID
      const mockUseUser = jest.requireMock('@/hooks/useUser');
      mockUseUser.default = jest.fn(() => ({
        user: { sub: 'auth0|jobseeker123', email: '<EMAIL>' },
        isLoading: false,
      }));

      const { rerender } = renderWithProviders(
        <AppLayout>
          <div>Content</div>
        </AppLayout>
      );

      await waitFor(() => {
        expect(screen.getByTestId('job-seeker-layout')).toBeInTheDocument();
      });

      // Clear mocks
      (global.fetch as jest.Mock).mockClear();
      (apiClient.get as jest.Mock).mockClear();

      // Rerender component
      rerender(
        <QueryClientProvider client={queryClient}>
          <UserProvider>
            <AppLayout>
              <div>Content Updated</div>
            </AppLayout>
          </UserProvider>
        </QueryClientProvider>
      );

      // Should not make additional API calls
      expect(global.fetch).not.toHaveBeenCalled();
      expect(apiClient.get).not.toHaveBeenCalled();

      // Should still show the correct layout
      expect(screen.getByTestId('job-seeker-layout')).toBeInTheDocument();
      expect(screen.getByText('Content Updated')).toBeInTheDocument();
    });
  });
});
