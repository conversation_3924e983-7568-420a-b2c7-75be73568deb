/**
 * Tests for authentication fixes
 * Focus: Real-world usage scenarios and practical UX issues
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/hooks/useUser';
import JobSeekerHoldingPage from '@/app/job-seeker-holding/page';
import { UserRole } from '@/types/roles';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/hooks/useUser', () => ({
  useUser: jest.fn(),
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height, priority, className }: any) => (
    // eslint-disable-next-line @next/next/no-img-element
    <img src={src} alt={alt} width={width} height={height} className={className} />
  ),
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
}));

// Mock window.location.href
Object.defineProperty(window, 'location', {
  value: {
    href: '',
  },
  writable: true,
});

describe('Job Seeker Holding Page Authentication', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
    (useUser as jest.Mock).mockReturnValue({
      user: null,
      isLoading: false,
    });
    window.location.href = '';
  });

  describe('App Router Implementation', () => {
    it('renders holding page without errors', () => {
      render(<JobSeekerHoldingPage />);

      expect(screen.getByText('Find Your Dream Job')).toBeInTheDocument();
      expect(screen.getByText('Log In')).toBeInTheDocument();
      expect(screen.getByText('Get Started')).toBeInTheDocument();
    });

    it('handles login button click correctly', () => {
      render(<JobSeekerHoldingPage />);

      const loginButton = screen.getByText('Log In');
      fireEvent.click(loginButton);

      expect(window.location.href).toContain('/api/auth/login');
      expect(window.location.href).toContain(`role=${UserRole.JOB_SEEKER}`);
      expect(window.location.href).toContain('returnTo=%2Fdashboard');
    });

    it('handles register button click correctly', () => {
      render(<JobSeekerHoldingPage />);

      const registerButton = screen.getByText('Get Started');
      fireEvent.click(registerButton);

      expect(window.location.href).toContain('/api/auth/login');
      expect(window.location.href).toContain(`role=${UserRole.JOB_SEEKER}`);
      expect(window.location.href).toContain('returnTo=%2Fjobseeker-onboarding');
      expect(window.location.href).toContain('screen_hint=signup');
    });

    it('handles back to landing navigation', () => {
      render(<JobSeekerHoldingPage />);

      const backButton = screen.getByText('Back to Landing');
      fireEvent.click(backButton);

      expect(mockPush).toHaveBeenCalledWith('/');
    });

    it('includes proper login_hint for tracking', () => {
      render(<JobSeekerHoldingPage />);

      const loginButton = screen.getByText('Log In');
      fireEvent.click(loginButton);

      expect(window.location.href).toContain('login_hint=');
      const urlParams = new URLSearchParams(window.location.href.split('?')[1]);
      const loginHint = JSON.parse(decodeURIComponent(urlParams.get('login_hint') || '{}'));

      expect(loginHint.role).toBe(UserRole.JOB_SEEKER);
      expect(loginHint.source).toBe('job-seeker-holding');
      expect(loginHint.action).toBe('login');
    });
  });

  describe('User Experience Scenarios', () => {
    it('prevents multiple rapid clicks on login button', async () => {
      render(<JobSeekerHoldingPage />);

      const loginButton = screen.getByText('Log In');

      // Simulate rapid clicking
      fireEvent.click(loginButton);
      const firstHref = window.location.href;

      // Reset href to test that second click doesn't change it
      window.location.href = '';
      fireEvent.click(loginButton);

      // Should still redirect to auth (this tests the button works)
      expect(window.location.href).toContain('/api/auth/login');
      expect(window.location.href).toContain('role=job-seeker');
    });

    it('displays all key features for job seekers', () => {
      render(<JobSeekerHoldingPage />);

      expect(screen.getByText('AI-Powered Matching')).toBeInTheDocument();
      expect(screen.getByText('Career Growth')).toBeInTheDocument();
      expect(screen.getByText('Fast Applications')).toBeInTheDocument();
    });

    it('shows appropriate messaging for job seekers', () => {
      render(<JobSeekerHoldingPage />);

      expect(screen.getByText(/Discover opportunities that match your skills/)).toBeInTheDocument();
      expect(screen.getByText(/Let AI help you find the perfect role/)).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing images gracefully', () => {
      render(<JobSeekerHoldingPage />);

      const images = screen.getAllByRole('img');
      images.forEach(img => {
        // Simulate image load error
        fireEvent.error(img);
        // Should not crash the component
        expect(screen.getByText('Find Your Dream Job')).toBeInTheDocument();
      });
    });

    it('handles router errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      (useRouter as jest.Mock).mockReturnValue({
        push: jest.fn().mockImplementation(() => {
          throw new Error('Navigation failed');
        }),
      });

      render(<JobSeekerHoldingPage />);

      const backButton = screen.getByText('Back to Landing');

      // Should not crash when navigation fails
      fireEvent.click(backButton);

      // Should still render the component
      expect(screen.getByText('Find Your Dream Job')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });
});
