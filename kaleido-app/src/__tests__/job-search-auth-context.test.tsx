/**
 * Tests for JobSearchContext authentication handling
 * Focus: Real-world scenarios where users log in/out while browsing jobs
 */

import { render, screen, waitFor, act } from '@testing-library/react';
import { ReactNode } from 'react';
import { JobSearchProvider, useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import { useUser } from '@/hooks/useUser';
import { useUser as useAuth0User } from '@auth0/nextjs-auth0/client';

// Mock dependencies
jest.mock('@/hooks/useUser', () => ({
  useUser: jest.fn(),
}));

jest.mock('@auth0/nextjs-auth0/client', () => ({
  useUser: jest.fn(),
}));

jest.mock('@/lib/jobSearchApi', () => ({
  searchJobs: jest.fn(),
}));

jest.mock('@/lib/apiHelper', () => ({
  get: jest.fn(),
}));

// Test component to access context
function TestComponent() {
  const { isPublicView, switchToPrivateView, switchToPublicView, isLoading } = useJobSearch();

  return (
    <div>
      <div data-testid="public-view">{isPublicView ? 'public' : 'private'}</div>
      <div data-testid="loading">{isLoading ? 'loading' : 'ready'}</div>
      <button onClick={switchToPrivateView} data-testid="switch-private">
        Switch to Private
      </button>
      <button onClick={switchToPublicView} data-testid="switch-public">
        Switch to Public
      </button>
    </div>
  );
}

function renderWithProvider(initialPublic = false, children: ReactNode = <TestComponent />) {
  return render(
    <JobSearchProvider onCloseOverride={() => {}} initialPublic={initialPublic}>
      {children}
    </JobSearchProvider>
  );
}

describe('JobSearchContext Authentication Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Set up default Auth0 mock
    (useAuth0User as jest.Mock).mockReturnValue({
      user: null,
      isLoading: false,
    });
  });

  describe('Initial State Management', () => {
    it('starts in public mode when no user is present', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: null,
        isLoading: false,
      });

      renderWithProvider(false);

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });
    });

    it('starts in private mode when user is logged in', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      renderWithProvider(false);

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });
    });

    it('respects initialPublic=true but switches to private when user detected', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      renderWithProvider(true);

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });
    });
  });

  describe('Real-world User Authentication Scenarios', () => {
    it('switches to private view when user logs in while browsing', async () => {
      // Start with no user
      (useAuth0User as jest.Mock).mockReturnValue({
        user: null,
        isLoading: false,
      });

      const { rerender } = renderWithProvider(true);

      // Initially public
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });

      // User logs in - re-render with new user
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      rerender(
        <JobSearchProvider onCloseOverride={() => {}} initialPublic={true}>
          <TestComponent />
        </JobSearchProvider>
      );

      // Should automatically switch to private
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });
    });

    it('switches to public view when user logs out', async () => {
      // Start with user logged in
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      const { rerender } = renderWithProvider(false);

      // Initially private
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });

      // User logs out - re-render with no user
      (useAuth0User as jest.Mock).mockReturnValue({
        user: null,
        isLoading: false,
      });

      rerender(
        <JobSearchProvider onCloseOverride={() => {}} initialPublic={false}>
          <TestComponent />
        </JobSearchProvider>
      );

      // Should automatically switch to public
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });
    });

    it('handles user loading state appropriately', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: null,
        isLoading: true,
      });

      const { rerender } = renderWithProvider(false);

      // Should start in public mode even during loading
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });

      // When loading finishes with user
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      rerender(
        <JobSearchProvider onCloseOverride={() => {}} initialPublic={false}>
          <TestComponent />
        </JobSearchProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });
    });
  });

  describe('Company Profile Job Board Scenarios', () => {
    it('allows public browsing on company profile pages', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: null,
        isLoading: false,
      });

      renderWithProvider(true); // Company profile starts public

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });
    });

    it('upgrades to private view when logged-in user visits company profile', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      renderWithProvider(true); // Company profile starts public

      // Should automatically upgrade to private for logged-in user
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });
    });

    it('maintains public view for anonymous users on company profiles', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: null,
        isLoading: false,
      });

      renderWithProvider(true);

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });

      // Should stay public for anonymous users
      expect(screen.getByTestId('public-view')).toHaveTextContent('public');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('handles rapid authentication state changes', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: null,
        isLoading: false,
      });

      renderWithProvider(false);

      // Should handle gracefully - simplified test
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });
    });

    it('handles missing user sub property', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: {}, // User object without sub
        isLoading: false,
      });

      renderWithProvider(false);

      // Should treat as no user
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });
    });

    it('handles undefined user object', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: undefined,
        isLoading: false,
      });

      renderWithProvider(false);

      // Should treat as no user
      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('public');
      });
    });
  });

  describe('Performance and UX Considerations', () => {
    it('does not cause infinite re-renders', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      const renderSpy = jest.fn();

      function SpyComponent() {
        renderSpy();
        return <TestComponent />;
      }

      renderWithProvider(false, <SpyComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });

      // Should not re-render excessively (allow for initial renders)
      expect(renderSpy).toHaveBeenCalledTimes(1);
    });

    it('maintains stable state during navigation', async () => {
      (useAuth0User as jest.Mock).mockReturnValue({
        user: { sub: 'user123' },
        isLoading: false,
      });

      renderWithProvider(false);

      await waitFor(() => {
        expect(screen.getByTestId('public-view')).toHaveTextContent('private');
      });

      // Simulate navigation or re-render
      act(() => {
        // Component re-renders but user state stays same
      });

      // Should maintain private state
      expect(screen.getByTestId('public-view')).toHaveTextContent('private');
    });
  });
});
