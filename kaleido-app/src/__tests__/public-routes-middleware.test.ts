/**
 * Tests for public routes and middleware configuration
 * Focus: Ensuring critical UX flows don't break due to authentication
 */

import {
  isPublicRoute,
  getMiddlewareIgnorePaths,
  getClientPublicRoutes,
  getAuthStateHandlerPublicPaths,
  PUBLIC_ROUTES,
} from '@/types/publicRoutes';

describe('Public Routes Configuration', () => {
  describe('Critical User Flows', () => {
    it('allows access to job seeker holding page', () => {
      expect(isPublicRoute('/job-seeker-holding')).toBe(true);
    });

    it('allows access to employer holding page', () => {
      expect(isPublicRoute('/employer-holding')).toBe(true);
    });

    it('allows access to company profile pages', () => {
      expect(isPublicRoute('/company-profile')).toBe(true);
      expect(isPublicRoute('/company-profile/some-company')).toBe(true);
      expect(isPublicRoute('/company-profile/some-company/jobs')).toBe(true);
    });

    it('allows access to open jobs pages', () => {
      expect(isPublicRoute('/open-jobs')).toBe(true);
      expect(isPublicRoute('/open-jobs/some-job-id')).toBe(true);
    });

    it('allows access to landing and root pages', () => {
      expect(isPublicRoute('/')).toBe(true);
      expect(isPublicRoute('/landing')).toBe(true);
    });

    it('allows access to auth-related routes', () => {
      expect(isPublicRoute('/api/auth/login')).toBe(true);
      expect(isPublicRoute('/api/auth/callback')).toBe(true);
      expect(isPublicRoute('/api/auth/logout')).toBe(true);
    });
  });

  describe('Middleware Configuration', () => {
    const middlewareIgnorePaths = getMiddlewareIgnorePaths();

    it('excludes holding pages from middleware auth checks', () => {
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.JOB_SEEKER_HOLDING);
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.EMPLOYER_HOLDING);
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.GRADUATE_HOLDING);
    });

    it('excludes company profile and job pages from middleware auth checks', () => {
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.COMPANY_PROFILE);
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.OPEN_JOBS);
    });

    it('excludes essential system routes from middleware auth checks', () => {
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.API_AUTH);
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.NEXT_STATIC);
      expect(middlewareIgnorePaths).toContain(PUBLIC_ROUTES.FAVICON);
    });

    it('processes landing pages through middleware but keeps them public', () => {
      // Root and landing pages should be processed by middleware (not ignored)
      // but should still be accessible to anonymous users
      expect(middlewareIgnorePaths).not.toContain(PUBLIC_ROUTES.ROOT);
      expect(middlewareIgnorePaths).not.toContain(PUBLIC_ROUTES.LANDING);

      // But they should still be in client public routes
      const clientRoutes = getClientPublicRoutes();
      expect(clientRoutes).toContain(PUBLIC_ROUTES.ROOT);
      expect(clientRoutes).toContain(PUBLIC_ROUTES.LANDING);
    });
  });

  describe('Client-side Authentication Guards', () => {
    const clientPublicRoutes = getClientPublicRoutes();

    it('includes all user-facing public routes', () => {
      expect(clientPublicRoutes).toContain(PUBLIC_ROUTES.JOB_SEEKER_HOLDING);
      expect(clientPublicRoutes).toContain(PUBLIC_ROUTES.EMPLOYER_HOLDING);
      expect(clientPublicRoutes).toContain(PUBLIC_ROUTES.COMPANY_PROFILE);
      expect(clientPublicRoutes).toContain(PUBLIC_ROUTES.OPEN_JOBS);
    });

    it('includes landing and root pages', () => {
      expect(clientPublicRoutes).toContain(PUBLIC_ROUTES.ROOT);
      expect(clientPublicRoutes).toContain(PUBLIC_ROUTES.LANDING);
    });
  });

  describe('AuthStateHandler Configuration', () => {
    const authStateHandlerPaths = getAuthStateHandlerPublicPaths();

    it('prevents auth redirects on holding pages', () => {
      expect(authStateHandlerPaths).toContain(PUBLIC_ROUTES.JOB_SEEKER_HOLDING);
      expect(authStateHandlerPaths).toContain(PUBLIC_ROUTES.EMPLOYER_HOLDING);
    });

    it('prevents auth redirects on public job and company pages', () => {
      expect(authStateHandlerPaths).toContain(PUBLIC_ROUTES.COMPANY_PROFILE);
      expect(authStateHandlerPaths).toContain(PUBLIC_ROUTES.OPEN_JOBS);
    });
  });

  describe('Edge Cases and Error Prevention', () => {
    it('handles empty string paths', () => {
      expect(isPublicRoute('')).toBe(false);
    });

    it('handles null/undefined paths', () => {
      expect(isPublicRoute(null as any)).toBe(false);
      expect(isPublicRoute(undefined as any)).toBe(false);
    });

    it('handles paths with query parameters', () => {
      expect(isPublicRoute('/open-jobs?search=developer')).toBe(true);
      expect(isPublicRoute('/company-profile/test?tab=jobs')).toBe(true);
    });

    it('handles paths with hash fragments', () => {
      expect(isPublicRoute('/open-jobs#section')).toBe(true);
      expect(isPublicRoute('/company-profile/test#about')).toBe(true);
    });

    it('properly distinguishes between similar paths', () => {
      // Public
      expect(isPublicRoute('/open-jobs')).toBe(true);
      expect(isPublicRoute('/company-profile')).toBe(true);

      // Should be protected (not in public routes)
      expect(isPublicRoute('/dashboard')).toBe(false);
      expect(isPublicRoute('/my-jobs')).toBe(false);
      expect(isPublicRoute('/candidates')).toBe(false);
    });
  });

  describe('Real-world Navigation Scenarios', () => {
    it('supports anonymous user browsing company jobs', () => {
      // Anonymous user clicks on company link
      expect(isPublicRoute('/company-profile/tech-startup')).toBe(true);

      // Views their jobs page
      expect(isPublicRoute('/company-profile/tech-startup/jobs')).toBe(true);

      // Views specific job
      expect(isPublicRoute('/open-jobs/software-engineer-123')).toBe(true);
    });

    it('supports user registration flow', () => {
      // User lands on root
      expect(isPublicRoute('/')).toBe(true);

      // Clicks "For Job Seekers"
      expect(isPublicRoute('/job-seeker-holding')).toBe(true);

      // Auth flow starts (handled by API routes)
      expect(isPublicRoute('/api/auth/login')).toBe(true);
      expect(isPublicRoute('/api/auth/callback')).toBe(true);
    });

    it('supports shared content access', () => {
      // Shared job links
      expect(isPublicRoute('/shared/job/123')).toBe(true);

      // Public candidate profiles
      expect(isPublicRoute('/public/candidate/456')).toBe(true);

      // Video capture for applications
      expect(isPublicRoute('/video-capture')).toBe(true);
    });
  });

  describe('Security Considerations', () => {
    it('does not expose sensitive routes as public', () => {
      // These should NOT be public
      expect(isPublicRoute('/dashboard')).toBe(false);
      expect(isPublicRoute('/admin')).toBe(false);
      expect(isPublicRoute('/candidates')).toBe(false);
      expect(isPublicRoute('/my-jobs')).toBe(false);
      expect(isPublicRoute('/company-settings')).toBe(false);
      expect(isPublicRoute('/profile')).toBe(false);
    });

    it('properly handles case sensitivity', () => {
      // Should be case sensitive for security
      expect(isPublicRoute('/Open-Jobs')).toBe(false);
      expect(isPublicRoute('/COMPANY-PROFILE')).toBe(false);
    });

    it('does not allow access to internal API routes', () => {
      // Only auth API routes should be public
      expect(isPublicRoute('/api/jobs')).toBe(false);
      expect(isPublicRoute('/api/companies')).toBe(false);
      expect(isPublicRoute('/api/candidates')).toBe(false);

      // Auth routes should be public
      expect(isPublicRoute('/api/auth/login')).toBe(true);
    });
  });

  describe('Performance and Consistency', () => {
    it('has consistent arrays across different functions', () => {
      const middleware = getMiddlewareIgnorePaths();
      const client = getClientPublicRoutes();
      const authHandler = getAuthStateHandlerPublicPaths();

      // All should include core public routes
      const coreRoutes = [
        PUBLIC_ROUTES.COMPANY_PROFILE,
        PUBLIC_ROUTES.OPEN_JOBS,
        PUBLIC_ROUTES.JOB_SEEKER_HOLDING,
      ];

      coreRoutes.forEach(route => {
        expect(client).toContain(route);
        expect(authHandler).toContain(route);
        // Middleware may not include all client routes, but should include core ones
      });
    });

    it('does not have duplicate entries', () => {
      const middleware = getMiddlewareIgnorePaths();
      const client = getClientPublicRoutes();
      const authHandler = getAuthStateHandlerPublicPaths();

      expect(new Set(middleware).size).toBe(middleware.length);
      expect(new Set(client).size).toBe(client.length);
      expect(new Set(authHandler).size).toBe(authHandler.length);
    });
  });
});
