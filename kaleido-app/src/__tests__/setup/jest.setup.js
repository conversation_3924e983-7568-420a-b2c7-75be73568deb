import '@testing-library/jest-dom';
import { setupTests } from './test-utils';

// Setup common test utilities
setupTests();

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }) {
    // eslint-disable-next-line @next/next/no-img-element
    return <img src={src} alt={alt} {...props} />;
  };
});

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }) {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    );
  };
});

// Mock React PDF components
jest.mock('@react-pdf/renderer', () => ({
  PDFDownloadLink: ({ children, fileName }) => (
    <div data-testid="pdf-download" data-filename={fileName}>
      {typeof children === 'function' ? children({ loading: false }) : children}
    </div>
  ),
  Document: ({ children }) => <div data-testid="pdf-document">{children}</div>,
  Page: ({ children }) => <div data-testid="pdf-page">{children}</div>,
  Text: ({ children }) => <span data-testid="pdf-text">{children}</span>,
  View: ({ children }) => <div data-testid="pdf-view">{children}</div>,
}));

// Global test configuration
global.console = {
  ...console,
  // Suppress specific console methods during tests
  warn: jest.fn(),
  error: jest.fn(),
  log: jest.fn(),
};

// Setup fake timers for tests that need them
beforeAll(() => {
  jest.useFakeTimers();
});

afterAll(() => {
  jest.runOnlyPendingTimers();
  jest.useRealTimers();
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
  jest.clearAllTimers();
});

// Increase test timeout for slower tests
jest.setTimeout(10000);
