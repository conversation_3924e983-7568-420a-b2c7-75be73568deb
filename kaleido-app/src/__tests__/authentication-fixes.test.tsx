import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useRole } from '@/hooks/useRole';
import { authService } from '@/services/auth.service';
import { UserRole } from '@/types/roles';

// Mock dependencies
jest.mock('@auth0/nextjs-auth0/client');
jest.mock('@/services/auth.service');

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;
const mockAuthService = authService as jest.Mocked<typeof authService>;

describe('Authentication Fixes Validation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();

    // Mock window.location
    Object.defineProperty(window, 'location', {
      writable: true,
      value: {
        href: 'http://localhost:3000/dashboard',
        pathname: '/dashboard',
        search: '',
        hash: '',
      },
    });
  });

  describe('Console Error Prevention', () => {
    it('should not log multiple errors during role detection', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});

      // Mock user but no role initially
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      // Mock auth service to return no role
      mockAuthService.getSession = jest.fn().mockResolvedValue({
        user: { sub: 'auth0|123' },
        role: null,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      // Render the useRole hook multiple times (simulating re-renders)
      const { result, rerender } = renderHook(() => useRole());

      // Wait for initial load
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Simulate multiple re-renders
      rerender();
      rerender();
      rerender();

      // Should only call getSession once due to hasChecked flag
      expect(mockAuthService.getSession).toHaveBeenCalledTimes(1);

      consoleError.mockRestore();
    });

    it('should only log error once when no role is found', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => {});

      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession = jest
        .fn()
        .mockResolvedValueOnce({
          user: { sub: 'auth0|123' },
          role: null,
          accessToken: 'token',
          accessTokenScope: '',
          accessTokenExpiresAt: 0,
          idToken: '',
        })
        .mockRejectedValueOnce(new Error('Network error'));

      const { result, rerender } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Force another check by clearing hasChecked
      rerender();

      // Error should only be logged once
      expect(consoleError).toHaveBeenCalledTimes(0); // No error because hasChecked prevents re-execution

      consoleError.mockRestore();
    });
  });

  describe('Role Fallback Logic', () => {
    it('should use localStorage role when JWT has no role', async () => {
      const userId = 'auth0|123';

      // Set role in localStorage
      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.JOB_SEEKER, clientId: userId })
      );

      mockUseUser.mockReturnValue({
        user: { sub: userId, email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      // Mock auth service to return no role in JWT
      mockAuthService.getSession = jest.fn().mockResolvedValue({
        user: { sub: userId },
        role: null,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.role).toBe(UserRole.JOB_SEEKER);
    });

    it('should handle multiple role sources in priority order', async () => {
      const userId = 'auth0|123';

      // Set role in localStorage (lowest priority)
      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.GRADUATE, clientId: userId })
      );

      mockUseUser.mockReturnValue({
        user: { sub: userId, email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      // Mock auth service to return role in JWT (highest priority)
      mockAuthService.getSession = jest.fn().mockResolvedValue({
        user: { sub: userId },
        role: UserRole.EMPLOYER, // This should take priority
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should use JWT role, not localStorage
      expect(result.current.role).toBe(UserRole.EMPLOYER);
    });

    it('should extract role from various JWT claim fields', async () => {
      const testCases = [
        { field: 'role', value: UserRole.JOB_SEEKER },
        { field: 'userRole', value: UserRole.EMPLOYER },
        { field: 'pendingRole', value: UserRole.GRADUATE },
        { field: 'https://kaleidotalent.com/role', value: UserRole.ADMIN },
      ];

      for (const testCase of testCases) {
        // Clear previous state
        jest.clearAllMocks();
        localStorage.clear();

        // Reset the auth service singleton
        (authService as any).constructor.instance = null;

        // Create user object with role in specific field
        const user = {
          sub: 'auth0|123',
          [testCase.field]: testCase.value,
        };

        // Mock the getSession method directly
        mockAuthService.getSession = jest.fn().mockResolvedValue({
          user,
          role: testCase.value,
          accessToken: 'token',
          accessTokenScope: '',
          accessTokenExpiresAt: Date.now() + 3600000,
          idToken: '',
        });

        // Test that the role is extracted correctly
        const result = await mockAuthService.getSession();

        expect(result?.role).toBe(testCase.value);
      }
    });
  });

  describe('Performance Optimizations', () => {
    it('should not make redundant API calls', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession = jest.fn().mockResolvedValue({
        user: { sub: 'auth0|123' },
        role: UserRole.JOB_SEEKER,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result, rerender } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockAuthService.getSession).toHaveBeenCalledTimes(1);

      // Multiple re-renders should not cause additional API calls
      rerender();
      rerender();
      rerender();

      expect(mockAuthService.getSession).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Handling', () => {
    it('should gracefully handle invalid localStorage data', async () => {
      const userId = 'auth0|123';

      // Set invalid JSON in localStorage
      localStorage.setItem(`userRole_${userId}`, 'invalid-json');

      mockUseUser.mockReturnValue({
        user: { sub: userId, email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession = jest.fn().mockResolvedValue({
        user: { sub: userId },
        role: null,
        accessToken: 'token',
        accessTokenScope: '',
        accessTokenExpiresAt: 0,
        idToken: '',
      });

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should return null instead of throwing error
      expect(result.current.role).toBeNull();
    });

    it('should handle auth service errors without crashing', async () => {
      mockUseUser.mockReturnValue({
        user: { sub: 'auth0|123', email: '<EMAIL>' },
        error: undefined,
        isLoading: false,
      });

      mockAuthService.getSession = jest.fn().mockRejectedValue(new Error('Auth service error'));

      // Mock console.error for this test to suppress expected error output
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useRole());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Should return null instead of throwing
      expect(result.current.role).toBeNull();

      // Verify error was logged and restore console.error
      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });
  });
});
