/**
 * Integration tests for company profile job pages authentication
 * Focus: Real-world scenarios where users browse company jobs
 */

import { render, screen, waitFor } from '@testing-library/react';
import { use } from 'react';
import CompanyJobsPage from '@/app/company-profile/[slug]/jobs/page';
import { useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import { useCompanyProfile } from '@/contexts/companyProfile/CompanyProfileContext';

// Mock the use() hook from React
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  use: jest.fn(),
}));

// Mock contexts
jest.mock('@/contexts/jobSearch/JobSearchContext', () => ({
  JobSearchProvider: ({ children }: any) => <div data-testid="job-search-provider">{children}</div>,
  useJobSearch: jest.fn(),
}));

jest.mock('@/contexts/companyProfile/CompanyProfileContext', () => ({
  useCompanyProfile: jest.fn(),
}));

// Mock other dependencies
jest.mock('date-fns', () => ({
  formatDistanceToNow: jest.fn(() => '2 days'),
}));

jest.mock('next/image', () => ({
  __esModule: true,
  // eslint-disable-next-line @next/next/no-img-element
  default: ({ src, alt, ...props }: any) => <img src={src} alt={alt} {...props} />,
}));

jest.mock('next/navigation', () => ({
  notFound: jest.fn(),
}));

jest.mock('@heroicons/react/24/outline', () => ({
  BriefcaseIcon: () => <div data-testid="briefcase-icon" />,
  ChevronDownIcon: () => <div data-testid="chevron-icon" />,
  CurrencyDollarIcon: () => <div data-testid="currency-icon" />,
  ListBulletIcon: () => <div data-testid="list-icon" />,
  MagnifyingGlassIcon: () => <div data-testid="search-icon" />,
  MapPinIcon: () => <div data-testid="map-icon" />,
  Squares2X2Icon: () => <div data-testid="grid-icon" />,
}));

// Mock job components
jest.mock('@/app/company-profile/[slug]/jobs/components', () => ({
  JobCard: ({ job, onSelect }: any) => (
    <div data-testid={`job-card-${job.id}`} onClick={() => onSelect(job)}>
      {job.title}
    </div>
  ),
  JobPreview: ({ job }: any) => (
    <div data-testid={`job-preview-${job.id}`}>{job.title} Preview</div>
  ),
}));

describe('Company Jobs Page Authentication Integration', () => {
  const mockCompany = {
    id: 'company-123',
    companyName: 'Test Company',
    logo: '/logo.png',
    primaryColor: '#3d1f47',
    secondaryColor: '#4a2654',
    accentColor: '#5c3161',
    layoutPreference: 'modern',
  };

  const mockJobs = {
    data: [
      {
        id: 'job-1',
        jobType: 'Software Engineer',
        location: ['Remote'],
        department: 'Engineering',
        salaryRange: '$100k-150k',
        createdAt: '2024-01-01T00:00:00Z',
      },
      {
        id: 'job-2',
        jobType: 'Product Manager',
        location: ['San Francisco'],
        department: 'Product',
        salaryRange: '$120k-180k',
        createdAt: '2024-01-02T00:00:00Z',
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the Promise params
    (use as jest.Mock).mockReturnValue({ slug: 'test-company' });

    // Default mock implementations
    (useJobSearch as jest.Mock).mockReturnValue({
      jobs: mockJobs,
      isLoading: false,
    });

    (useCompanyProfile as jest.Mock).mockReturnValue({
      company: mockCompany,
      isLoading: false,
    });
  });

  describe('Public Access Scenarios', () => {
    it('renders company jobs page for anonymous users', async () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByTestId('job-search-provider')).toBeInTheDocument();
      });
    });

    it('initializes JobSearchProvider with public mode', () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      // Verify JobSearchProvider is rendered with correct props
      expect(screen.getByTestId('job-search-provider')).toBeInTheDocument();
    });

    it('displays job search interface without authentication required', async () => {
      (useJobSearch as jest.Mock).mockReturnValue({
        jobs: mockJobs,
        isLoading: false,
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('Job title or keyword')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Country or time zone')).toBeInTheDocument();
        expect(screen.getByText('Search')).toBeInTheDocument();
      });
    });
  });

  describe('Authenticated User Scenarios', () => {
    it('shows enhanced features for logged-in users', async () => {
      (useJobSearch as jest.Mock).mockReturnValue({
        jobs: mockJobs,
        isLoading: false,
        isPublicView: false, // User is logged in
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByTestId('job-search-provider')).toBeInTheDocument();
      });
    });

    it('maintains job list functionality for authenticated users', async () => {
      (useJobSearch as jest.Mock).mockReturnValue({
        jobs: mockJobs,
        isLoading: false,
        isPublicView: false,
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByText('2 jobs')).toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading skeleton while jobs are loading', async () => {
      (useJobSearch as jest.Mock).mockReturnValue({
        jobs: null,
        isLoading: true,
      });

      (useCompanyProfile as jest.Mock).mockReturnValue({
        company: null,
        isLoading: true,
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      // Should show loading skeletons
      await waitFor(() => {
        const loadingElement = document.querySelector('.animate-pulse');
        expect(loadingElement).toBeInTheDocument();
      });
    });

    it('handles company loading independently of jobs loading', async () => {
      (useJobSearch as jest.Mock).mockReturnValue({
        jobs: mockJobs,
        isLoading: false,
      });

      (useCompanyProfile as jest.Mock).mockReturnValue({
        company: null,
        isLoading: true,
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      // Should still show loading state
      await waitFor(() => {
        const loadingElement = document.querySelector('.animate-pulse');
        expect(loadingElement).toBeInTheDocument();
      });
    });
  });

  describe('Job Filtering and Search', () => {
    it('provides search functionality without authentication', async () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByPlaceholderText('Job title or keyword')).toBeInTheDocument();
      });

      // Filter dropdowns should be present
      expect(screen.getByText('Experience Level')).toBeInTheDocument();
      expect(screen.getByText('Job types')).toBeInTheDocument();
      expect(screen.getByText('Salary')).toBeInTheDocument();
    });

    it('displays job count correctly', async () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByText('2 jobs')).toBeInTheDocument();
      });
    });

    it('shows empty state when no jobs found', async () => {
      (useJobSearch as jest.Mock).mockReturnValue({
        jobs: { data: [] },
        isLoading: false,
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByText('No jobs found')).toBeInTheDocument();
        expect(
          screen.getByText('No matching jobs were found. Try adjusting your search criteria.')
        ).toBeInTheDocument();
      });
    });
  });

  describe('Company Branding Integration', () => {
    it('applies company branding colors', async () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        const headerElement = screen.getByText("Let's find your dream job");
        expect(headerElement).toBeInTheDocument();
      });
    });

    it('displays company logo when available', async () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        const logo = screen.getByAltText('Test Company');
        expect(logo).toBeInTheDocument();
        expect(logo).toHaveAttribute('src', '/logo.png');
      });
    });

    it('handles missing company logo gracefully', async () => {
      (useCompanyProfile as jest.Mock).mockReturnValue({
        company: { ...mockCompany, logo: null },
        isLoading: false,
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      // Should not crash when logo is missing
      await waitFor(() => {
        expect(screen.getByText("Let's find your dream job")).toBeInTheDocument();
      });
    });
  });

  describe('View Mode Switching', () => {
    it('provides card and list view options', async () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByTestId('grid-icon')).toBeInTheDocument();
        expect(screen.getByTestId('list-icon')).toBeInTheDocument();
      });
    });

    it('displays jobs in card view by default', async () => {
      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      await waitFor(() => {
        expect(screen.getByTestId('job-card-job-1')).toBeInTheDocument();
        expect(screen.getByTestId('job-card-job-2')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles missing company gracefully', async () => {
      (useCompanyProfile as jest.Mock).mockReturnValue({
        company: null,
        isLoading: false,
      });

      const { notFound } = require('next/navigation');

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'non-existent' })} />);

      await waitFor(() => {
        expect(notFound).toHaveBeenCalled();
      });
    });

    it('handles job loading errors gracefully', async () => {
      (useJobSearch as jest.Mock).mockReturnValue({
        jobs: null,
        isLoading: false,
        error: new Error('Failed to load jobs'),
      });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'test-company' })} />);

      // Should not crash the application
      await waitFor(() => {
        expect(screen.getByTestId('job-search-provider')).toBeInTheDocument();
      });
    });
  });

  describe('URL Parameter Handling', () => {
    it('handles slug parameter correctly', () => {
      (use as jest.Mock).mockReturnValue({ slug: 'special-company-123' });

      render(<CompanyJobsPage params={Promise.resolve({ slug: 'special-company-123' })} />);

      expect(use).toHaveBeenCalledWith(expect.any(Promise));
    });

    it('handles invalid slug parameters', () => {
      (use as jest.Mock).mockReturnValue({ slug: '' });

      render(<CompanyJobsPage params={Promise.resolve({ slug: '' })} />);

      // Should not crash with empty slug
      expect(screen.getByTestId('job-search-provider')).toBeInTheDocument();
    });
  });
});
