export enum RemotePreference {
  REMOTE_ONLY = 'REMOTE_ONLY',
  HYBRID = 'HYBRID',
  ONSITE = 'ONSITE',
  FLEXIBLE = 'FLEXIBLE',
}

export enum ApplicationStatus {
  PENDING = 'PENDING',
  REVIEWED = 'REVIEWED',
  SHORTLISTED = 'SHORTLISTED',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN',
}

export enum CandidateStatus {
  NEW = 'NEW',
  IN_REVIEW = 'IN_REVIEW',
  SHORTLISTED = 'SHORTLISTED',
  REJECTED = 'REJECTED',
  WITHDRAWN = 'WITHDRAWN',
  APPLIED = 'APPLIED',
}

export enum UserRole {
  JOB_SEEKER = 'job-seeker',
  EMPLOYER = 'employer',
  GRADUATE = 'graduate',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super-admin',
  REFERRAL_PARTNER = 'referral-partner',
}

export interface SalaryRange {
  min: number;
  max: number;
  currency: string;
}

export interface CandidateExperience {
  achievements: any;
  company: string;
  title: string;
  location?: string;
  startDate: Date;
  endDate?: Date;
  current: boolean;
  description?: string;
  highlights?: string[];
  skills?: string[];
}

export interface Education {
  institution: string;
  degree: string;
  field: string;
  startDate: Date;
  endDate?: Date;
  description?: string;
  gpa?: number;
  achievements?: string[];
}

export interface Certification {
  name: string;
  issuer: string;
  issueDate: Date;
  expiryDate?: Date;
  credentialId?: string;
  credentialUrl?: string;
  skills?: string[];
}

export interface Project {
  name: string;
  description?: string;
  startDate?: Date | null;
  endDate?: Date | null;
  url?: string;
  technologies?: string[];
  mediaUrls?: string[];
  collaborators?: string[];
}

export interface JobPreferences {
  desiredSalary?: {
    min: number;
    max: number;
    currency: string;
    period: 'daily' | 'weekly' | 'bi-weekly' | 'monthly' | 'yearly';
  };
  jobTypes?: string[];
  locations?: string[];
  remotePreference?: 'remote' | 'hybrid' | 'onsite' | 'flexible';
  industries?: string[];
}

export interface Verifications {
  email?: boolean;
  phone?: boolean;
  education?: boolean;
  video?: any;
  employment?: boolean;
  identity?: any;
}

export interface Achievement {
  title: string;
  date: Date;
  description: string;
  url?: string;
}

export interface Recommendation {
  from: string;
  title: string;
  company: string;
  date: Date;
  content: string;
}

export interface WorkAvailability {
  immediatelyAvailable: boolean;
  noticePeriod?: number;
  availableFrom?: Date;
  preferredWorkHours?: string[];
}

export interface Compensation {
  current?: SalaryRange;
  expected?: SalaryRange;
  benefits?: string[];
}

export interface PrivacySettings {
  profileVisibility: 'PUBLIC' | 'PRIVATE' | 'CONNECTIONS_ONLY';
  showContact: boolean;
  showSalary: boolean;
  allowMessages: boolean;
}

export interface SocialProfile {
  platform: string;
  url: string;
  username?: string;
}

export interface JobSeekerMetadata {
  lastActive?: Date;
  profileCompletion?: number;
  searchKeywords?: string[];
  tags?: string[];
  lastVerified: any;
  lastUpdated?: any;
}

export interface JobSeeker {
  id: string;
  clientId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  summary?: string;
  skills: string[];
  experience?: CandidateExperience[];
  resumeUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  role: UserRole;
  education?: Education[];
  certifications?: Certification[];
  projects?: Project[];
  languages: string[];
  portfolioUrl?: string;
  videoIntroUrl?: string;
  preferences?: JobPreferences;
  passportId: string;
  verifications?: Verifications;
  achievements?: Achievement[];
  recommendations?: Recommendation[];
  workAvailability?: WorkAvailability;
  compensation?: Compensation;
  privacySettings?: PrivacySettings;
  socialProfiles?: SocialProfile[];
  metadata?: JobSeekerMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface JobApplication {
  id: string;
  jobSeekerId: string;
  jobId: string;
  coverLetter?: string;
  status: ApplicationStatus;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface ResumeUploadResponse {
  workAvailability: any;
  privacySettings: any;
  preferences: any;
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  summary?: string;
  skills: string[];
  experience: {
    title: string;
    company: string;
    location?: string;
    startDate: string;
    endDate?: string;
    description: string;
    achievements?: string[];
    duration: number;
  }[];
  education: {
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate?: string;
    description?: string;
  }[];
  certifications?: {
    name: string;
    issuer: string;
    issueDate: string;
    expiryDate?: string;
    credentialId?: string;
    credentialUrl?: string;
  }[];
  projects?: {
    name: string;
    description: string;
    startDate: string;
    endDate?: string;
    url?: string;
    technologies: string[];
  }[];
  languages: string[];
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  resumeUrl: string;
}
