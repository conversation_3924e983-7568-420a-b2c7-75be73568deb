import { AuthService } from '../auth.service';
import { UserRole } from '@/types/roles';
import { AuthCacheManager } from '@/lib/auth-cache-manager';

// Mock dependencies
jest.mock('@/lib/auth-cache-manager');

// Mock fetch
global.fetch = jest.fn();

describe('AuthService', () => {
  let authService: AuthService;
  let mockCacheManager: jest.Mocked<AuthCacheManager>;

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();

    // Reset singleton instance
    (AuthService as any).instance = null;

    // Mock cache manager
    mockCacheManager = {
      getCachedSession: jest.fn(),
      shouldRefresh: jest.fn(),
      cacheSession: jest.fn(),
      clearCache: jest.fn(),
    } as any;

    (AuthCacheManager.getInstance as jest.Mock).mockReturnValue(mockCacheManager);

    authService = AuthService.getInstance();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Role Extraction through getSession', () => {
    beforeEach(() => {
      mockCacheManager.getCachedSession.mockReturnValue(null);
    });

    it('should extract role from namespaced claim', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            'https://kaleidotalent.com/role': UserRole.JOB_SEEKER,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBe(UserRole.JOB_SEEKER);
    });

    it('should extract role from direct role field', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            role: UserRole.EMPLOYER,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBe(UserRole.EMPLOYER);
    });

    it('should extract role from pendingRole field', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            pendingRole: UserRole.GRADUATE,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBe(UserRole.GRADUATE);
    });

    it('should extract role from userRole field', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            userRole: UserRole.ADMIN,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBe(UserRole.ADMIN);
    });

    it('should fallback to localStorage when no role in claims', async () => {
      const userId = 'auth0|123';

      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.REFERRAL_PARTNER, clientId: userId })
      );

      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: userId,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBe(UserRole.REFERRAL_PARTNER);
    });

    it('should return null when no valid role found', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBeNull();
    });

    it('should validate role is in UserRole enum', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            role: 'INVALID_ROLE',
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBeNull();
    });

    it('should handle invalid JSON in localStorage gracefully', async () => {
      const userId = 'auth0|123';

      localStorage.setItem(`userRole_${userId}`, 'invalid-json');

      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: userId,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBeNull();
    });

    it('should prioritize JWT claims over localStorage', async () => {
      const userId = 'auth0|123';

      // Set different role in localStorage
      localStorage.setItem(
        `userRole_${userId}`,
        JSON.stringify({ role: UserRole.JOB_SEEKER, clientId: userId })
      );

      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: userId,
            role: UserRole.EMPLOYER,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();
      expect(session?.role).toBe(UserRole.EMPLOYER); // Should use JWT claim, not localStorage
    });
  });

  describe('getSession', () => {
    it('should return cached session when available and not expired', async () => {
      const cachedSession = {
        user: { sub: 'auth0|123' },
        role: UserRole.JOB_SEEKER,
        accessToken: 'cached-token',
        expiresAt: Date.now() + 3600000, // 1 hour from now
      };

      mockCacheManager.getCachedSession.mockReturnValue(cachedSession);
      mockCacheManager.shouldRefresh.mockReturnValue(false);

      const session = await authService.getSession();

      expect(session).toEqual({
        user: cachedSession.user,
        accessToken: 'cached-token',
        accessTokenScope: '',
        accessTokenExpiresAt: cachedSession.expiresAt,
        idToken: '',
        refreshToken: '',
        role: UserRole.JOB_SEEKER,
      });

      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('should fetch new session when cache is expired', async () => {
      mockCacheManager.getCachedSession.mockReturnValue(null);

      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            role: UserRole.EMPLOYER,
          },
          accessToken: 'new-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const session = await authService.getSession();

      expect(session).toEqual({
        user: { sub: 'auth0|123', role: UserRole.EMPLOYER },
        accessToken: 'new-token',
        accessTokenScope: 'openid profile',
        accessTokenExpiresAt: expect.any(Number),
        idToken: 'id-token',
        role: UserRole.EMPLOYER,
      });

      expect(mockCacheManager.cacheSession).toHaveBeenCalledWith(
        { sub: 'auth0|123', role: UserRole.EMPLOYER },
        UserRole.EMPLOYER,
        'new-token'
      );
    });

    it('should handle session expiration and redirect to logout', async () => {
      mockCacheManager.getCachedSession.mockReturnValue(null);

      const mockResponse = {
        ok: false,
        json: async () => ({
          requiresLogout: true,
          error: 'Session expired',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      // Mock window.location with all required properties
      delete (window as any).location;
      window.location = {
        href: '',
        hostname: 'localhost',
        replace: jest.fn(),
      } as any;

      const session = await authService.getSession();

      expect(session).toBeNull();
      expect(mockCacheManager.clearCache).toHaveBeenCalled();
      expect(window.location.href).toBe('/api/auth/logout');
    });

    it('should handle fetch errors gracefully', async () => {
      mockCacheManager.getCachedSession.mockReturnValue(null);
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const session = await authService.getSession();

      expect(session).toBeNull();
      expect(mockCacheManager.clearCache).toHaveBeenCalled();
    });
  });

  describe('hasRole', () => {
    it('should return true when user has required role', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            role: UserRole.ADMIN,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      mockCacheManager.getCachedSession.mockReturnValue(null);

      const hasRole = await authService.hasRole(UserRole.ADMIN);
      expect(hasRole).toBe(true);
    });

    it('should return true when user has one of multiple required roles', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            role: UserRole.EMPLOYER,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      mockCacheManager.getCachedSession.mockReturnValue(null);

      const hasRole = await authService.hasRole([UserRole.ADMIN, UserRole.EMPLOYER]);
      expect(hasRole).toBe(true);
    });

    it('should return false when user does not have required role', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
            role: UserRole.JOB_SEEKER,
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      mockCacheManager.getCachedSession.mockReturnValue(null);

      const hasRole = await authService.hasRole(UserRole.ADMIN);
      expect(hasRole).toBe(false);
    });

    it('should return false when user has no role', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: {
            sub: 'auth0|123',
          },
          accessToken: 'token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      mockCacheManager.getCachedSession.mockReturnValue(null);

      const hasRole = await authService.hasRole(UserRole.JOB_SEEKER);
      expect(hasRole).toBe(false);
    });
  });

  describe('logout', () => {
    it('should clear all authentication data and redirect', async () => {
      // Set up some auth data
      localStorage.setItem('auth_token', 'token123');
      localStorage.setItem('userRole_auth0|123', '{"role":"JOB_SEEKER"}');
      localStorage.setItem('@@auth0spajs@@::client_id::default::user', '{}');
      localStorage.setItem('other_data', 'keep-this');

      sessionStorage.setItem('auth_session_ready', 'true');
      sessionStorage.setItem('other_session', 'data');

      // Mock window.location with all required properties
      delete (window as any).location;
      window.location = {
        href: '',
        hostname: 'localhost',
        replace: jest.fn(),
      } as any;

      // Mock document.cookie
      Object.defineProperty(document, 'cookie', {
        writable: true,
        value: 'auth_cookie=value; other_cookie=value2',
      });

      // Mock fetch for clear-session endpoint
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      await authService.logout();

      // Check localStorage - ALL items should be removed (new behavior)
      expect(localStorage.length).toBe(0);
      expect(localStorage.getItem('auth_token')).toBeNull();
      expect(localStorage.getItem('userRole_auth0|123')).toBeNull();
      expect(localStorage.getItem('@@auth0spajs@@::client_id::default::user')).toBeNull();
      expect(localStorage.getItem('other_data')).toBeNull(); // This should also be cleared now

      // Check sessionStorage - should be completely cleared
      expect(sessionStorage.length).toBe(0);

      // Check cache manager was cleared
      expect(mockCacheManager.clearCache).toHaveBeenCalled();

      // Check that clear-session endpoint was called
      expect(global.fetch).toHaveBeenCalledWith('/api/auth/clear-session', {
        method: 'POST',
        credentials: 'include',
      });

      // Check redirect - in test mode with replace function available
      if (typeof window.location.replace === 'function') {
        expect(window.location.replace).toHaveBeenCalled();
        const callArg = (window.location.replace as jest.Mock).mock.calls[0][0];
        expect(callArg).toContain('/api/auth/logout');
        // Should not contain federated=true by default
        expect(callArg).not.toContain('federated=true');
      } else {
        // Fallback check for href
        expect(window.location.href).toContain('/api/auth/logout');
      }
    });
  });

  describe('getValidAccessToken', () => {
    it('should return access token from session', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({
          user: { sub: 'auth0|123' },
          accessToken: 'valid-token',
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const token = await authService.getValidAccessToken();
      expect(token).toBe('valid-token');
    });

    it('should refresh token when session has no access token', async () => {
      // First call returns session without token
      const sessionResponse = {
        ok: true,
        json: async () => ({
          user: { sub: 'auth0|123' },
          scope: 'openid profile',
          expiresAt: Date.now() + 3600000,
          idToken: 'id-token',
        }),
      };

      // Second call returns refreshed token
      const refreshResponse = {
        ok: true,
        json: async () => ({
          accessToken: 'refreshed-token',
        }),
      };

      (global.fetch as jest.Mock)
        .mockResolvedValueOnce(sessionResponse)
        .mockResolvedValueOnce(refreshResponse);

      const token = await authService.getValidAccessToken();
      expect(token).toBe('refreshed-token');
      expect(global.fetch).toHaveBeenCalledTimes(2);
      expect(global.fetch).toHaveBeenNthCalledWith(2, '/api/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });
    });
  });
});
