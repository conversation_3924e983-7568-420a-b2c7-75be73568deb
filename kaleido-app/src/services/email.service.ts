import apiHelper from '@/lib/apiHelper';

/**
 * Email service for client-side use
 */
export class EmailService {
  /**
   * Send status update email to a candidate
   */
  async sendStatusUpdateEmail(
    to: string,
    candidateName: string,
    jobTitle: string,
    statusType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status',
    message?: string,
    companyName?: string,
    additionalData?: {
      interviewDate?: string;
      meetingLink?: string;
      startDate?: string;
      onboardingLink?: string;
      expirationDate?: string;
      responseLink?: string;
    }
  ) {
    try {
      const response = await apiHelper.post('/email/status-update', {
        to,
        candidateName,
        jobTitle,
        companyName,
        message,
        statusType,
        ...additionalData,
      });

      return response;
    } catch (error) {
      console.error('Error sending status update email:', error);
      throw error;
    }
  }

  /**
   * Send post-onboarding email to company
   */
  async sendPostOnboardingEmail(
    to: string,
    companyName: string,
    contactName: string,
    contactEmail: string,
    contactPhone?: string,
    dashboardUrl?: string,
    demoVideoUrl?: string,
    supportUrl?: string,
    roadmapUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/post-onboarding', {
        to,
        companyName,
        contactName,
        contactEmail,
        contactPhone,
        dashboardUrl,
        demoVideoUrl,
        supportUrl,
        roadmapUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending post-onboarding email:', error);
      throw error;
    }
  }

  /**
   * Send mid-trial check-in email to company
   */
  async sendMidTrialCheckInEmail(
    to: string,
    contactName: string,
    contactEmail: string,
    companyName: string,
    contactPhone?: string,
    bookingUrl?: string,
    roadmapUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/mid-trial-checkin', {
        to,
        contactName,
        contactEmail,
        companyName,
        contactPhone,
        bookingUrl,
        roadmapUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending mid-trial check-in email:', error);
      throw error;
    }
  }

  /**
   * Send first job posted email to company
   */
  async sendJobPostedFirstEmail(
    to: string,
    contactName: string,
    contactEmail: string,
    companyName: string,
    jobTitle: string,
    contactPhone?: string,
    dashboardUrl?: string,
    bookingUrl?: string,
    supportUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/job-posted-first', {
        to,
        contactName,
        contactEmail,
        companyName,
        jobTitle,
        contactPhone,
        dashboardUrl,
        bookingUrl,
        supportUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending job posted first email:', error);
      throw error;
    }
  }

  /**
   * Send profile approved email to talent
   */
  async sendProfileApprovedEmail(
    to: string,
    talentName: string,
    topSkills?: string[],
    yearsExperience?: number,
    industry?: string,
    dashboardUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/profile-approved', {
        to,
        talentName,
        topSkills,
        yearsExperience,
        industry,
        dashboardUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending profile approved email:', error);
      throw error;
    }
  }

  /**
   * Send profile rejected email to talent
   */
  async sendProfileRejectedEmail(to: string, talentName: string) {
    try {
      const response = await apiHelper.post('/email/profile-rejected', {
        to,
        talentName,
      });

      return response;
    } catch (error) {
      console.error('Error sending profile rejected email:', error);
      throw error;
    }
  }

  /**
   * Send re-engagement email to talent
   */
  async sendReEngagementEmail(
    to: string,
    talentName: string,
    profileUrl?: string,
    dashboardUrl?: string,
    jobBoardUrl?: string,
    productRoadmapUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/re-engagement', {
        to,
        talentName,
        profileUrl,
        dashboardUrl,
        jobBoardUrl,
        productRoadmapUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending re-engagement email:', error);
      throw error;
    }
  }

  /**
   * Send job application submitted automated email to talent
   */
  async sendJobApplicationSubmittedEmail(
    to: string,
    talentName: string,
    jobTitle: string,
    companyName: string,
    dashboardUrl?: string,
    companyProfileUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/job-application-submitted', {
        to,
        talentName,
        jobTitle,
        companyName,
        dashboardUrl,
        companyProfileUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending job application submitted email:', error);
      throw error;
    }
  }

  /**
   * Send video introduction email for active talent
   */
  async sendVideoIntroductionActiveEmail(
    to: string,
    candidateName: string,
    roleName: string,
    companyName: string,
    contactName: string,
    contactEmail: string,
    recordingUrl: string,
    deadline: string,
    deadlineTime: string,
    contactPhone?: string,
    jobDescriptionUrl?: string,
    companyProfileUrl?: string,
    loginUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/video-introduction-active', {
        to,
        candidateName,
        roleName,
        companyName,
        contactName,
        contactEmail,
        contactPhone,
        recordingUrl,
        deadline,
        deadlineTime,
        jobDescriptionUrl,
        companyProfileUrl,
        loginUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending video introduction active email:', error);
      throw error;
    }
  }

  /**
   * Send video introduction email for passive talent
   */
  async sendVideoIntroductionPassiveEmail(
    to: string,
    candidateName: string,
    jobTitle: string,
    roleName: string,
    companyName: string,
    yourName: string,
    yourTitle: string,
    recordingUrl: string,
    deadline: string,
    deadlineTime: string,
    roleDetailsUrl?: string,
    companyProfileUrl?: string,
    loginUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/video-introduction-passive', {
        to,
        candidateName,
        jobTitle,
        roleName,
        companyName,
        yourName,
        yourTitle,
        recordingUrl,
        deadline,
        deadlineTime,
        roleDetailsUrl,
        companyProfileUrl,
        loginUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending video introduction passive email:', error);
      throw error;
    }
  }

  /**
   * Send interview invitation email to candidate
   */
  async sendInterviewInvitationEmail(
    to: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    contactName: string,
    contactEmail: string,
    proposedDate: string,
    proposedTime: string,
    format: string,
    locationOrLink: string,
    yourName: string,
    yourTitle: string,
    contactPhone?: string,
    contactTitle?: string,
    companyWebsiteUrl?: string,
    companyProfileUrl?: string,
    jobDescriptionUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/interview-invitation', {
        to,
        candidateName,
        jobTitle,
        companyName,
        contactName,
        contactEmail,
        contactPhone,
        contactTitle,
        proposedDate,
        proposedTime,
        format,
        locationOrLink,
        yourName,
        yourTitle,
        companyWebsiteUrl,
        companyProfileUrl,
        jobDescriptionUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending interview invitation email:', error);
      throw error;
    }
  }

  /**
   * Send application rejection email to candidate
   */
  async sendApplicationRejectionEmail(
    to: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    yourName: string,
    yourTitle: string
  ) {
    try {
      const response = await apiHelper.post('/email/application-rejection', {
        to,
        candidateName,
        jobTitle,
        companyName,
        yourName,
        yourTitle,
      });

      return response;
    } catch (error) {
      console.error('Error sending application rejection email:', error);
      throw error;
    }
  }

  /**
   * Send job application was submitted email to company
   */
  async sendJobApplicationWasSubmittedEmail(
    to: string,
    companyName: string,
    jobTitle: string,
    candidateFullName: string,
    applicationDate: string,
    viewApplicationUrl: string,
    recruiterName?: string,
    jobPostingUrl?: string,
    matchRankUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/job-application-was-submitted', {
        to,
        companyName,
        recruiterName,
        jobTitle,
        candidateFullName,
        applicationDate,
        jobPostingUrl,
        viewApplicationUrl,
        matchRankUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending job application was submitted email:', error);
      throw error;
    }
  }

  /**
   * Send company re-engagement email
   */
  async sendCompanyReEngagementEmail(
    to: string,
    companyName: string,
    jobTitle: string,
    loginUrl?: string,
    profileUrl?: string,
    dashboardUrl?: string
  ) {
    try {
      const response = await apiHelper.post('/email/company-re-engagement', {
        to,
        companyName,
        jobTitle,
        loginUrl,
        profileUrl,
        dashboardUrl,
      });

      return response;
    } catch (error) {
      console.error('Error sending company re-engagement email:', error);
      throw error;
    }
  }
}
