import { create } from 'zustand';

import apiHelper from '@/lib/apiHelper';

export interface ScoutedCandidate {
  id: string;
  fullName: string;
  jobTitle?: string;
  location?: string;
  currentCompany?: string;
  skills?: string[];
  experience?: Array<{
    title: string;
    company: string;
    description?: string;
    startDate?: string;
    endDate?: string;
    duration?: string;
  }>;
  education?: Array<{
    school: string;
    degree?: string;
    fieldOfStudy?: string;
    startDate?: string;
    endDate?: string;
  }>;
  summary?: string;
  linkedinUrl?: string;
  profileUrl?: string;
  isAdded: boolean;
  selected?: boolean; // For UI selection
}

interface ScoutedCandidatesState {
  // State
  candidatesByJobId: Record<string, ScoutedCandidate[]>;
  isLoading: Record<string, boolean>;
  lastFetchTimestamp: Record<string, number>;

  // Actions
  fetchScoutedCandidates: (jobId: string, forceRefresh?: boolean) => Promise<ScoutedCandidate[]>;
  updateScoutedCandidates: (jobId: string, candidates: ScoutedCandidate[]) => void;
  markCandidateAsAdded: (candidateId: string, jobId: string) => void;
  removeCandidateFromList: (candidateId: string, jobId: string) => void;
  clearScoutedCandidates: (jobId: string) => void;
}

// Create the store
export const useScoutedCandidatesStore = create<ScoutedCandidatesState>((set, get) => ({
  // Initial state
  candidatesByJobId: {},
  isLoading: {},
  lastFetchTimestamp: {},

  // Actions
  fetchScoutedCandidates: async (jobId: string, forceRefresh = false) => {
    const now = Date.now();
    const lastFetch = get().lastFetchTimestamp[jobId] || 0;
    const candidates = get().candidatesByJobId[jobId] || [];
    const isCurrentlyLoading = get().isLoading[jobId] || false;

    // If we're currently loading, don't make another request
    if (isCurrentlyLoading) {
      return candidates;
    }

    // If we fetched recently (within 5 seconds), return cached data unless force refresh
    if (now - lastFetch < 5000 && !forceRefresh) {
      return candidates;
    }

    // Set loading state and update timestamp to prevent duplicate calls
    set(state => ({
      isLoading: { ...state.isLoading, [jobId]: true },
      lastFetchTimestamp: { ...state.lastFetchTimestamp, [jobId]: now },
    }));

    try {
      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await apiHelper.get<ScoutedCandidate[]>(
        `/scouted-candidates?jobId=${jobId}&_t=${timestamp}`
      );

      if (response) {
        // Update the store with the new data
        set(state => ({
          candidatesByJobId: {
            ...state.candidatesByJobId,
            [jobId]: response,
          },
          isLoading: {
            ...state.isLoading,
            [jobId]: false,
          },
        }));

        return response;
      }

      // If no response, return current data
      return candidates;
    } catch (error) {
      console.error('Error fetching scouted candidates:', error);

      // Reset loading state
      set(state => ({
        isLoading: { ...state.isLoading, [jobId]: false },
      }));

      return candidates;
    }
  },

  updateScoutedCandidates: (jobId: string, candidates: ScoutedCandidate[]) => {
    set(state => ({
      candidatesByJobId: {
        ...state.candidatesByJobId,
        [jobId]: candidates,
      },
      lastFetchTimestamp: {
        ...state.lastFetchTimestamp,
        [jobId]: Date.now(),
      },
    }));
  },

  markCandidateAsAdded: (candidateId: string, jobId: string) => {
    set(state => {
      const jobCandidates = state.candidatesByJobId[jobId] || [];
      const updatedCandidates = jobCandidates.map(candidate =>
        candidate.id === candidateId ? { ...candidate, isAdded: true } : candidate
      );

      return {
        candidatesByJobId: {
          ...state.candidatesByJobId,
          [jobId]: updatedCandidates,
        },
      };
    });
  },

  removeCandidateFromList: (candidateId: string, jobId: string) => {
    set(state => {
      const jobCandidates = state.candidatesByJobId[jobId] || [];
      const updatedCandidates = jobCandidates.filter(candidate => candidate.id !== candidateId);

      return {
        candidatesByJobId: {
          ...state.candidatesByJobId,
          [jobId]: updatedCandidates,
        },
      };
    });
  },

  clearScoutedCandidates: (jobId: string) => {
    set(state => {
      const { [jobId]: _, ...restCandidates } = state.candidatesByJobId;
      const { [jobId]: __, ...restLoading } = state.isLoading;
      const { [jobId]: ___, ...restTimestamps } = state.lastFetchTimestamp;

      return {
        candidatesByJobId: restCandidates,
        isLoading: restLoading,
        lastFetchTimestamp: restTimestamps,
      };
    });
  },
}));
