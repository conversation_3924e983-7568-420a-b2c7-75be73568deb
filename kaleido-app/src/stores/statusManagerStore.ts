import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface StatusManagerJob {
  id: string;
  type: string;
  status: string;
  progress?: number;
  message?: string;
  error?: string;
  result?: any;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface StatusManagerInstance {
  id: string;
  type: string;
  jobs: StatusManagerJob[];
  isActive: boolean;
  isMinimized: boolean;
  isCollapsed: boolean;
  createdAt: string;
}

export interface StatusManagerModalState {
  isOpen: boolean;
  jobId?: string;
  jobType?: string;
  result?: any;
  navigateUrl?: string;
  isSameUrl: boolean;
}

interface StatusManagerStoreState {
  // ========== CORE DATA ==========
  instances: Record<string, StatusManagerInstance>;
  activeInstanceId: string | null;

  // ========== MODAL STATE ==========
  modalState: StatusManagerModalState;

  // ========== UI PREFERENCES ==========
  defaultMinimized: boolean;
  defaultCollapsed: boolean;
  closeOthersOnComplete: boolean;

  // ========== COMPLETION CACHE ==========
  completedJobs: string[];
  lastCompletionMessage: string | null;
  lastCompletionTime: number | null;
}

interface StatusManagerStoreActions {
  // ========== INSTANCE MANAGEMENT ==========
  createInstance: (id: string, type: string, jobs: StatusManagerJob[]) => void;
  updateInstance: (id: string, updates: Partial<StatusManagerInstance>) => void;
  removeInstance: (id: string) => void;
  setActiveInstance: (id: string | null) => void;
  ensureSingleInstance: (id: string) => void;

  // ========== JOB MANAGEMENT ==========
  addJob: (instanceId: string, job: StatusManagerJob) => void;
  updateJob: (instanceId: string, jobId: string, updates: Partial<StatusManagerJob>) => void;
  removeJob: (instanceId: string, jobId: string) => void;
  markJobCompleted: (instanceId: string, jobId: string, result?: any) => void;

  // ========== MODAL MANAGEMENT ==========
  showCompletionModal: (jobId: string, jobType: string, result: any, navigateUrl?: string) => void;
  hideCompletionModal: () => void;

  // ========== UI STATE ==========
  setInstanceMinimized: (id: string, minimized: boolean) => void;
  setInstanceCollapsed: (id: string, collapsed: boolean) => void;

  // ========== PREFERENCES ==========
  setDefaultMinimized: (minimized: boolean) => void;
  setDefaultCollapsed: (collapsed: boolean) => void;
  setCloseOthersOnComplete: (closeOthers: boolean) => void;

  // ========== COMPLETION TRACKING ==========
  isJobCompleted: (jobId: string) => boolean;
  clearCompletionCache: () => void;
  clearStaleCompletions: (maxAgeMs?: number) => void;

  // ========== UTILITIES ==========
  getActiveInstance: () => StatusManagerInstance | null;
  getAllActiveJobs: () => StatusManagerJob[];
  hasActiveJobs: () => boolean;
  reset: () => void;
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: StatusManagerStoreState = {
  instances: {},
  activeInstanceId: null,
  modalState: {
    isOpen: false,
    isSameUrl: false,
  },
  defaultMinimized: false,
  defaultCollapsed: false,
  closeOthersOnComplete: true,
  completedJobs: [],
  lastCompletionMessage: null,
  lastCompletionTime: null,
};

// ============================================================================
// ZUSTAND STORE
// ============================================================================

export const useStatusManagerStore = create<StatusManagerStoreState & StatusManagerStoreActions>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialState,

      // ========== INSTANCE MANAGEMENT ==========
      createInstance: (id: string, type: string, jobs: StatusManagerJob[]) => {
        set(state => {
          // Ensure single instance if needed
          if (get().closeOthersOnComplete) {
            // Remove other instances of the same type
            Object.keys(state.instances).forEach(instanceId => {
              if (state.instances[instanceId].type === type && instanceId !== id) {
                delete state.instances[instanceId];
              }
            });
          }

          state.instances[id] = {
            id,
            type,
            jobs,
            isActive: true,
            isMinimized: state.defaultMinimized,
            isCollapsed: state.defaultCollapsed,
            createdAt: new Date().toISOString(),
          };

          state.activeInstanceId = id;
        });
      },

      updateInstance: (id: string, updates: Partial<StatusManagerInstance>) => {
        set(state => {
          if (state.instances[id]) {
            Object.assign(state.instances[id], updates);
          }
        });
      },

      removeInstance: (id: string) => {
        set(state => {
          delete state.instances[id];
          if (state.activeInstanceId === id) {
            state.activeInstanceId = null;
          }
        });
      },

      setActiveInstance: (id: string | null) => {
        set(state => {
          state.activeInstanceId = id;
        });
      },

      ensureSingleInstance: (id: string) => {
        set(state => {
          // Close all other instances
          Object.keys(state.instances).forEach(instanceId => {
            if (instanceId !== id) {
              delete state.instances[instanceId];
            }
          });
          state.activeInstanceId = id;
        });
      },

      // ========== JOB MANAGEMENT ==========
      addJob: (instanceId: string, job: StatusManagerJob) => {
        set(state => {
          if (state.instances[instanceId]) {
            state.instances[instanceId].jobs.push(job);
          }
        });
      },

      updateJob: (instanceId: string, jobId: string, updates: Partial<StatusManagerJob>) => {
        set(state => {
          const instance = state.instances[instanceId];
          if (instance) {
            const jobIndex = instance.jobs.findIndex(j => j.id === jobId);
            if (jobIndex !== -1) {
              Object.assign(instance.jobs[jobIndex], updates);
              instance.jobs[jobIndex].updatedAt = new Date().toISOString();
            }
          }
        });
      },

      removeJob: (instanceId: string, jobId: string) => {
        set(state => {
          const instance = state.instances[instanceId];
          if (instance) {
            instance.jobs = instance.jobs.filter(j => j.id !== jobId);
          }
        });
      },

      markJobCompleted: (instanceId: string, jobId: string, result?: any) => {
        set(state => {
          const instance = state.instances[instanceId];
          if (instance) {
            const job = instance.jobs.find(j => j.id === jobId);
            if (job) {
              job.status = 'completed';
              job.result = result;
              job.completedAt = new Date().toISOString();
              job.updatedAt = new Date().toISOString();

              // Track completion
              if (!state.completedJobs.includes(jobId)) {
                state.completedJobs.push(jobId);
              }
              state.lastCompletionMessage = job.message || 'Job completed';
              state.lastCompletionTime = Date.now();
            }
          }
        });
      },

      // ========== MODAL MANAGEMENT ==========
      showCompletionModal: (jobId: string, jobType: string, result: any, navigateUrl?: string) => {
        const currentUrl = window.location.pathname;
        const isSameUrl = !navigateUrl || navigateUrl === currentUrl;

        set(state => {
          state.modalState = {
            isOpen: true,
            jobId,
            jobType,
            result,
            navigateUrl,
            isSameUrl,
          };
        });

        // If same URL, refresh the unifiedJobStore immediately
        if (isSameUrl) {
          // Dynamically import unifiedJobStore to avoid circular dependencies
          import('./unifiedJobStore')
            .then(async ({ useUnifiedJobStore }) => {
              const jobStore = useUnifiedJobStore.getState();

              // For matchrank jobs, trigger proper refresh
              if (jobType === 'matchRank' && jobStore.selectedJobId) {
                // Trigger the worker complete handler which will refresh data
                await jobStore.onWorkerComplete(jobStore.selectedJobId, 'matchrank');
              } else if (jobStore.selectedJobId) {
                // For other job types, still call onWorkerComplete
                await jobStore.onWorkerComplete(jobStore.selectedJobId, jobType as any);
              }
            })
            .catch(error => {
              console.error('[StatusManagerStore] Error refreshing job store:', error);
            });
        }
      },

      hideCompletionModal: () => {
        set(state => {
          state.modalState = {
            isOpen: false,
            isSameUrl: false,
            jobId: undefined,
            jobType: undefined,
            result: undefined,
            navigateUrl: undefined,
          };
        });
      },

      // ========== UI STATE ==========
      setInstanceMinimized: (id: string, minimized: boolean) => {
        set(state => {
          if (state.instances[id]) {
            state.instances[id].isMinimized = minimized;
          }
        });
      },

      setInstanceCollapsed: (id: string, collapsed: boolean) => {
        set(state => {
          if (state.instances[id]) {
            state.instances[id].isCollapsed = collapsed;
          }
        });
      },

      // ========== PREFERENCES ==========
      setDefaultMinimized: (minimized: boolean) => {
        set(state => {
          state.defaultMinimized = minimized;
          // Also save to localStorage for persistence
          localStorage.setItem('statusManager_defaultMinimized', String(minimized));
        });
      },

      setDefaultCollapsed: (collapsed: boolean) => {
        set(state => {
          state.defaultCollapsed = collapsed;
          // Also save to localStorage for persistence
          localStorage.setItem('statusManager_defaultCollapsed', String(collapsed));
        });
      },

      setCloseOthersOnComplete: (closeOthers: boolean) => {
        set(state => {
          state.closeOthersOnComplete = closeOthers;
          // Also save to localStorage for persistence
          localStorage.setItem('statusManager_closeOthersOnComplete', String(closeOthers));
        });
      },

      // ========== COMPLETION TRACKING ==========
      isJobCompleted: (jobId: string) => {
        return get().completedJobs.includes(jobId);
      },

      clearCompletionCache: () => {
        set(state => {
          state.completedJobs = [];
          state.lastCompletionMessage = null;
          state.lastCompletionTime = null;
          // Also clear modal state
          state.modalState = {
            isOpen: false,
            isSameUrl: false,
            jobId: undefined,
            jobType: undefined,
            result: undefined,
            navigateUrl: undefined,
          };
        });
      },

      clearStaleCompletions: (maxAgeMs: number = 30000) => {
        // Default 30 seconds
        const now = Date.now();
        const state = get();

        // Clear if completion is older than maxAge
        if (state.lastCompletionTime && now - state.lastCompletionTime > maxAgeMs) {
          get().clearCompletionCache();
        }

        // Clear stale instances that have no active jobs
        set(state => {
          Object.keys(state.instances).forEach(instanceId => {
            const instance = state.instances[instanceId];
            const hasActiveJobs = instance.jobs.some(
              job =>
                job.status !== 'completed' && job.status !== 'failed' && job.status !== 'cancelled'
            );

            if (!hasActiveJobs) {
              // Check if all jobs have been completed for more than maxAge
              const allJobsStale = instance.jobs.every(job => {
                if (job.completedAt) {
                  const completedTime = new Date(job.completedAt).getTime();
                  return now - completedTime > maxAgeMs;
                }
                return false;
              });

              if (allJobsStale) {
                delete state.instances[instanceId];
              }
            }
          });
        });
      },

      // ========== UTILITIES ==========
      getActiveInstance: () => {
        const state = get();
        return state.activeInstanceId ? state.instances[state.activeInstanceId] : null;
      },

      getAllActiveJobs: () => {
        const state = get();
        const allJobs: StatusManagerJob[] = [];

        Object.values(state.instances).forEach(instance => {
          if (instance.isActive) {
            allJobs.push(
              ...instance.jobs.filter(
                job =>
                  job.status !== 'completed' &&
                  job.status !== 'failed' &&
                  job.status !== 'cancelled'
              )
            );
          }
        });

        return allJobs;
      },

      hasActiveJobs: () => {
        return get().getAllActiveJobs().length > 0;
      },

      reset: () => {
        set(() => ({
          ...initialState,
          completedJobs: [],
        }));
      },
    }))
  )
);

// ============================================================================
// INITIALIZE FROM LOCALSTORAGE
// ============================================================================

// Load preferences from localStorage on module load
if (typeof window !== 'undefined') {
  const store = useStatusManagerStore.getState();

  const defaultMinimized = localStorage.getItem('statusManager_defaultMinimized');
  if (defaultMinimized !== null) {
    store.setDefaultMinimized(defaultMinimized === 'true');
  }

  const defaultCollapsed = localStorage.getItem('statusManager_defaultCollapsed');
  if (defaultCollapsed !== null) {
    store.setDefaultCollapsed(defaultCollapsed === 'true');
  }

  const closeOthersOnComplete = localStorage.getItem('statusManager_closeOthersOnComplete');
  if (closeOthersOnComplete !== null) {
    store.setCloseOthersOnComplete(closeOthersOnComplete === 'true');
  }
}

// Export types
export type { StatusManagerStoreState, StatusManagerStoreActions };
