import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

import { IJobSeekerProfile } from '@/components/JobSeeker/types';
import { showToast } from '@/components/Toaster';
import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/shared/types';
import { isPlaceholderEmail } from '@/utils/emailUtils';

interface JobSeekerState {
  formData: IJobSeekerProfile;
  isLoading: boolean;
  error: string | null;
  linkedInData: any;

  // Actions
  updateFormData: (data: Partial<IJobSeekerProfile>) => void;
  setFormData: (data: IJobSeekerProfile) => void;
  saveStep: (stepId: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setLinkedInData: (data: any) => void;
  resetStore: () => void;
}

const initialFormData: IJobSeekerProfile = {
  clientId: '',
  role: UserRole.JOB_SEEKER,
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  location: '',
  summary: '',
  skills: [],
  experience: [],
  education: [],
  certifications: [],
  languages: [],
  linkedinUrl: '',
  githubUrl: '',
  portfolioUrl: '',
  videoIntroUrl: null,
  myValues: [],
  preferences: {
    jobTypes: [],
    locations: [],
    industries: [],
    remotePreference: 'onsite',
    desiredSalary: {
      min: 0,
      max: 0,
      currency: 'USD',
      period: 'yearly',
    },
  },
  passportId: '',
  resumeUrl: '',
  verifications: {
    email: false,
    phone: false,
    education: false,
    employment: false,
  },
  achievements: [],
  recommendations: [],
  workAvailability: {
    immediatelyAvailable: false,
    noticePeriod: 0,
  },
  privacySettings: {
    profileVisibility: 'PRIVATE',
    allowMessages: true,
    showSalary: false,
    showContact: false,
  },
  projects: [],
  compensation: null,
  socialProfiles: [],
  metadata: {
    lastVerified: null,
    lastUpdated: null,
  },
  hasCompletedOnboarding: false,
};

export const useJobSeekerStore = create<JobSeekerState>()(
  devtools(
    (set, get) => ({
      formData: initialFormData,
      isLoading: false,
      error: null,
      linkedInData: null,

      updateFormData: (data: Partial<IJobSeekerProfile>) => {
        set(state => {
          const newData = { ...state.formData, ...data };

          // Ensure education dates are Date objects
          if (data.education) {
            newData.education = data.education.map(edu => ({
              ...edu,
              startDate: edu.startDate ? new Date(edu.startDate) : null,
              endDate: edu.endDate ? new Date(edu.endDate) : null,
            }));
          }

          // Validate remotePreference
          if (data.preferences?.remotePreference) {
            const validPreferences = ['remote', 'hybrid', 'onsite'];
            if (!validPreferences.includes(data.preferences.remotePreference)) {
              newData.preferences = {
                ...newData.preferences,
                remotePreference: 'onsite',
              };
            }
          }
          
          // Fix location if it's an object
          if (typeof newData.location === 'object' && newData.location !== null) {
            const loc = newData.location as any;
            newData.location = loc.country || '';
          }

          return { formData: newData };
        });
      },

      setFormData: (data: IJobSeekerProfile) => {
        set(state => {
          let newFormData = { ...state.formData, ...data };
          
          // Fix location if it's an object before setting
          if (typeof newFormData.location === 'object' && newFormData.location !== null) {
            const loc = newFormData.location as any;
            newFormData.location = loc.country || '';
          }
          
          return { formData: newFormData };
        });
      },

      saveStep: async (stepId: string) => {
        const { formData, linkedInData } = get();

        set({ isLoading: true, error: null });

        try {
          // Check localStorage for any values that might be missing in the form state
          const updatedFormData = { ...formData };

          // Check if the email is a placeholder and we have a valid email from Auth0 or LinkedIn
          if (isPlaceholderEmail(updatedFormData.email)) {
            // Try to get a valid email from LinkedIn data
            if (linkedInData?.email) {
              updatedFormData.email = linkedInData.email;
            }
          }

          // Filter out empty basic fields to prevent validation errors
          // Only include basic fields if they have valid values
          const filteredFormData = { ...updatedFormData };

          // Remove empty basic fields that would cause validation errors
          if (!filteredFormData.firstName?.trim()) {
            delete filteredFormData.firstName;
          }
          if (!filteredFormData.lastName?.trim()) {
            delete filteredFormData.lastName;
          }
          if (!filteredFormData.email?.trim() || !filteredFormData.email.includes('@')) {
            delete filteredFormData.email;
          }
          if (!filteredFormData.phone?.trim()) {
            delete filteredFormData.phone;
          }
          
          // Fix location field - convert object to string if needed
          if (typeof filteredFormData.location === 'object' && filteredFormData.location !== null) {
            // If location is an object with country/language, convert to string
            const loc = filteredFormData.location as any;
            if (loc.country) {
              filteredFormData.location = loc.country;
            } else {
              delete filteredFormData.location;
            }
          } else if (!filteredFormData.location?.trim()) {
            delete filteredFormData.location;
          }

          // Prepare final data for saving
          const dataToSave = {
            ...filteredFormData,
            role: UserRole.JOB_SEEKER, // Ensure role is set to the proper enum value
            lastCompletedStep: stepId,
            updatedAt: new Date().toISOString(),
            myValues: filteredFormData.myValues || [],
            linkedInProfile: linkedInData, // Save LinkedIn data to backend
          };

          // Ensure clientId is a string
          if (!dataToSave.clientId || typeof dataToSave.clientId !== 'string') {
            throw new Error('Invalid clientId');
          }

          let response;
          if (formData.id) {
            // Update existing profile
            response = await apiHelper.put(`/job-seekers/${formData.id}`, dataToSave);
          } else {
            // Only create a new profile if we have essential data
            const hasEssentialData = dataToSave.firstName && dataToSave.lastName && dataToSave.email;
            if (!hasEssentialData) {
              // Don't create a profile without essential data, just save locally
              console.log('Skipping profile creation - missing essential data');
              return;
            }
            
            // Check if profile already exists before creating
            try {
              const existingProfile = await apiHelper.get('/job-seekers');
              if (existingProfile && existingProfile.id) {
                // Profile exists, update it instead
                response = await apiHelper.put(`/job-seekers/${existingProfile.id}`, dataToSave);
                // Update local state with the ID
                set(state => ({
                  formData: {
                    ...state.formData,
                    id: existingProfile.id
                  }
                }));
              } else {
                // No existing profile, create new one
                response = await apiHelper.post('/job-seekers', dataToSave);
              }
            } catch (error) {
              // If GET fails, try to create a new profile
              console.log('No existing profile found, creating new one');
              response = await apiHelper.post('/job-seekers', dataToSave);
            }
          }

          // Update the store with the response to ensure we have the latest data
          if (response) {
            set(state => ({
              formData: {
                ...state.formData,
                ...response,
                // Preserve any fields that might have been updated locally but not saved
                lastCompletedStep: stepId,
                updatedAt: new Date().toISOString(),
              },
            }));
          }

          showToast({
            message: 'Progress saved successfully',
            isSuccess: true,
          });
        } catch (err) {
          console.error('Error in saveStep:', err);
          const errorMessage = 'Failed to save progress. Please try again.';
          set({ error: errorMessage });
          showToast({
            message: errorMessage,
            isSuccess: false,
          });
          throw err;
        } finally {
          set({ isLoading: false });
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      setLinkedInData: (data: any) => {
        set({ linkedInData: data });
      },

      resetStore: () => {
        set({
          formData: initialFormData,
          isLoading: false,
          error: null,
          linkedInData: null,
        });
      },
    }),
    {
      name: 'job-seeker-store',
    }
  )
);
