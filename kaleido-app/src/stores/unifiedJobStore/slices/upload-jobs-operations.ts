// ============================================================================
// UPLOAD JOBS OPERATIONS SLICE - Worker Job Tracking
// ============================================================================
// This slice handles uploadJobsStore compatibility for worker job management

import type { WorkerJob } from '../types';

// ========== LocalStorage Helpers ==========
const getInitialUploadJobsState = () => {
  if (typeof window === 'undefined') return { jobs: {}, activeJobs: [] };

  try {
    const persistedJobs = localStorage.getItem('uploadJobs');
    const persistedActiveJobs = localStorage.getItem('uploadActiveJobs');

    return {
      jobs: persistedJobs ? JSON.parse(persistedJobs) : {},
      activeJobs: persistedActiveJobs ? JSON.parse(persistedActiveJobs) : [],
    };
  } catch (error) {
    console.error('Error loading upload jobs from localStorage:', error);
    return { jobs: {}, activeJobs: [] };
  }
};

const persistUploadJobsState = (jobs: Record<string, WorkerJob>, activeJobs: string[]) => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem('uploadJobs', JSON.stringify(jobs));
    localStorage.setItem('uploadActiveJobs', JSON.stringify(activeJobs));
  } catch (error) {
    console.error('Error persisting upload jobs to localStorage:', error);
  }
};

export interface UploadJobsOperationsSlice {
  // ========== UploadJobsStore Compatibility Methods ==========
  addJob: (job: Omit<WorkerJob, 'createdAt' | 'progress' | 'processedFiles'>) => void;
  removeJob: (jobId: string) => void;
  getJobsByRelatedId: (relatedId: string) => WorkerJob[];
  clearCompletedJobs: () => void;
  clearCompletedWorkerJobs: () => void; // Alias

  // ========== Worker Job Management ==========
  updateWorkerJob: (jobId: string, updates: Partial<WorkerJob>) => void;
  getWorkerJobsByJobId: (jobId: string) => WorkerJob[];
  getActiveJobsByType: (jobType: string) => WorkerJob[];
  onWorkerComplete: (
    jobId: string,
    workerType: 'upload' | 'scout' | 'matchrank' | 'ats'
  ) => Promise<void>;

  // ========== Job Progress Tracking ==========
  updateJobStatus: (jobId: string, status: string, result?: any) => void;
  updateJobProgress: (jobId: string, progress: number) => void;

  // ========== Service Integration ==========
  initializeUploadJobsService: () => void;
}

export const createUploadJobsOperationsSlice = (set: any, get: any): UploadJobsOperationsSlice => ({
  // ========== UploadJobsStore Compatibility Methods ==========
  addJob: (job: Omit<WorkerJob, 'createdAt' | 'progress' | 'processedFiles'>) => {
    // Handle both 'id' and 'jobId' properties for backward compatibility
    const jobIdToUse = (job as any).jobId || job.id;

    // Map jobType to type if needed
    const jobType = (job as any).jobType || job.type;
    let type: WorkerJob['type'] = 'upload';
    if (jobType === 'file-upload' || jobType === 'resume_upload') {
      type = 'upload';
    } else if (jobType === 'scout' || jobType === 'scout-candidates') {
      type = 'scout';
    } else if (jobType === 'matchrank' || jobType === 'match-rank') {
      type = 'matchrank';
    } else if (jobType === 'ats') {
      type = 'ats';
    }

    const newJob: WorkerJob = {
      ...job,
      id: jobIdToUse, // Ensure the id is set correctly
      jobId: jobIdToUse, // Also set jobId for compatibility
      type, // Ensure type is set correctly
      progress: 0,
      processedFiles: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    set((state: any) => {
      const updatedWorkerJobs = { ...state.workerJobs, [jobIdToUse]: newJob };
      const updatedActiveJobs = [...state.activeJobs, jobIdToUse];

      // Persist to localStorage
      persistUploadJobsState(updatedWorkerJobs, updatedActiveJobs);

      return {
        workerJobs: updatedWorkerJobs,
        activeJobs: updatedActiveJobs,
      };
    });
  },

  removeJob: (jobId: string) => {
    set((state: any) => {
      const { [jobId]: removedJob, ...remainingJobs } = state.workerJobs;
      const activeJobs = state.activeJobs.filter((id: string) => id !== jobId);

      // Persist to localStorage
      persistUploadJobsState(remainingJobs, activeJobs);

      return {
        workerJobs: remainingJobs,
        activeJobs,
      };
    });
  },

  getJobsByRelatedId: (relatedId: string) => {
    return Object.values(get().workerJobs).filter(
      (job: any) => job.relatedId === relatedId
    ) as WorkerJob[];
  },

  clearCompletedJobs: () => {
    set((state: any) => {
      const activeWorkerJobs: any = {};
      Object.entries(state.workerJobs).forEach(([id, job]: [string, any]) => {
        if (job.status === 'queued' || job.status === 'active' || job.status === 'processing') {
          activeWorkerJobs[id] = job;
        }
      });

      // Persist to localStorage
      persistUploadJobsState(activeWorkerJobs, state.activeJobs);

      return { workerJobs: activeWorkerJobs };
    });
  },

  clearCompletedWorkerJobs: () => {
    get().clearCompletedJobs();
  },

  // ========== Worker Job Management ==========
  updateWorkerJob: (jobId: string, updates: Partial<WorkerJob>) => {
    set((state: any) => {
      if (state.workerJobs[jobId]) {
        const updatedJob = {
          ...state.workerJobs[jobId],
          ...updates,
          updatedAt: new Date().toISOString(),
        };

        return {
          workerJobs: {
            ...state.workerJobs,
            [jobId]: updatedJob,
          },
        };
      }
      return state;
    });
  },

  getWorkerJobsByJobId: (jobId: string) => {
    return Object.values(get().workerJobs).filter((job: any) => job.jobId === jobId) as WorkerJob[];
  },

  getActiveJobsByType: (jobType: string) => {
    return Object.values(get().workerJobs).filter(
      (job: any) =>
        job.type === jobType &&
        (job.status === 'queued' || job.status === 'active' || job.status === 'processing')
    ) as WorkerJob[];
  },

  onWorkerComplete: async (jobId: string, workerType: 'upload' | 'scout' | 'matchrank' | 'ats') => {
    // Handle different worker completion types
    switch (workerType) {
      case 'upload':
        // Update upload tracking state
        set((state: any) => {
          const activeJobs = state.activeJobs.filter((id: string) => id !== jobId);
          return {
            activeJobs,
            isResumeUploadActive: activeJobs.length > 0,
          };
        });

        // Refresh job data to update candidate count and credit consumption
        const state = get() as any;
        if (state.fetchJobCriteria) {
          await state.fetchJobCriteria(jobId, true);
        }
        break;

      case 'scout':
      case 'ats':
        // Handle other worker types as needed
        break;

      case 'matchrank':
        // Refresh candidates for the current job after matchrank completes
        const currentJobId = get().selectedJobId;
        if (currentJobId) {
          // Clear cache to force fresh data
          set((state: any) => ({
            lastFetchTime: {
              ...state.lastFetchTime,
              [`candidates-${currentJobId}`]: 0,
              [`job-details-${currentJobId}`]: 0,
            },
          }));

          // Fetch fresh candidates and job details
          try {
            await get().fetchCandidates(currentJobId, get().currentPage || 1, get().filters || {});
            await get().fetchJobDetails(currentJobId, true);

            // Dispatch refresh event for components
            window.dispatchEvent(
              new CustomEvent('candidatesRefreshed', {
                detail: { jobId: currentJobId, timestamp: Date.now() },
              })
            );
          } catch (error) {
            console.error('[onWorkerComplete] Error refreshing after matchrank:', error);
          }
        }
        break;
    }

    // Emit completion event for other components
    if (typeof window !== 'undefined') {
      window.dispatchEvent(
        new CustomEvent('workerComplete', {
          detail: { jobId, workerType, timestamp: Date.now() },
        })
      );
    }
  },

  // ========== Job Progress Tracking ==========
  updateJobStatus: (jobId: string, status: string, result?: any) => {
    set((state: any) => {
      if (!state.workerJobs[jobId]) return state;

      const updatedJob = {
        ...state.workerJobs[jobId],
        status,
        result,
        updatedAt: new Date().toISOString(),
        // If completed or failed, set progress to 100%
        ...(status === 'completed' || status === 'completed_with_errors'
          ? { progress: 100, processedFiles: state.workerJobs[jobId].totalFiles }
          : {}),
      };

      // Update activeJobs if the job is complete or failed
      let activeJobs = [...state.activeJobs];
      if (status === 'completed' || status === 'failed' || status === 'cancelled') {
        activeJobs = activeJobs.filter((id: string) => id !== jobId);
      }

      const updatedWorkerJobs = {
        ...state.workerJobs,
        [jobId]: updatedJob,
      };

      // Persist to localStorage
      persistUploadJobsState(updatedWorkerJobs, activeJobs);

      return {
        workerJobs: updatedWorkerJobs,
        activeJobs,
      };
    });

    // Publish a custom event when job status changes
    if (typeof window !== 'undefined') {
      window.dispatchEvent(
        new CustomEvent('uploadJobStatusChanged', {
          detail: {
            jobId,
            status,
            result,
          },
        })
      );
    }
  },

  updateJobProgress: (jobId: string, progress: number) => {
    set((state: any) => {
      if (!state.workerJobs[jobId]) return state;

      const job = state.workerJobs[jobId];
      const processedFiles = Math.floor((progress / 100) * job.totalFiles);

      const updatedJob = {
        ...job,
        progress,
        processedFiles,
        updatedAt: new Date().toISOString(),
      };

      const updatedWorkerJobs = {
        ...state.workerJobs,
        [jobId]: updatedJob,
      };

      // Persist to localStorage
      persistUploadJobsState(updatedWorkerJobs, state.activeJobs);

      return { workerJobs: updatedWorkerJobs };
    });
  },

  // ========== Service Integration ==========
  initializeUploadJobsService: () => {
    if (typeof window === 'undefined') return;

    // Set up event listener for upload completion with credit data
    window.addEventListener('uploadJobCompleted', (event: any) => {
      const { jobId, result } = event.detail || {};
      if (jobId && result?.creditData) {
        const creditData = result.creditData;
        set((state: any) => ({
          matchRankCost: {
            success: true,
            creditCost: creditData.estimatedCreditCost || 0,
            unevaluatedCandidatesCount: creditData.unevaluatedCandidatesCount || 0,
            isValid: true,
            message: `${creditData.totalCandidates} candidates ready for ranking`,
            availableCredits: creditData.availableCredits || 0,
          },
          stats: {
            ...state.stats,
            totalCandidates: creditData.totalCandidates || 0,
          },
        }));
      }
    });

    // Dynamically import the service to avoid SSR issues
    import('@/services/uploadJobsService')
      .then(({ uploadJobsService }) => {
        // Set up global callback for service integration
        uploadJobsService.setGlobalCallback((jobId: string, statusUpdate: any) => {
          const store = get();

          // Update job progress if available
          if (statusUpdate.progress !== undefined) {
            store.updateJobProgress(jobId, statusUpdate.progress);
          }

          // Update status if it's a terminal status
          if (
            statusUpdate.status === 'completed' ||
            statusUpdate.status === 'failed' ||
            statusUpdate.status === 'cancelled'
          ) {
            store.updateJobStatus(jobId, statusUpdate.status, statusUpdate);
          }
        });
      })
      .catch(error => {
        console.warn('Upload jobs service not available:', error);
      });
  },
});
