// ============================================================================
// JOB OPERATIONS SLICE - Core CRUD and Selection
// ============================================================================
// This slice handles core job operations, form management, and job selection

import { JobStatus } from '@/entities';
import apiHelper from '@/lib/apiHelper';
import type { JobFormData, PublishPlatform } from '../types';

export interface JobOperationsSlice {
  // ========== Job CRUD Operations ==========
  fetchJobs: () => Promise<any[]>;
  fetchJob: (jobId: string) => Promise<any | null>;
  fetchJobById: (jobId: string, force?: boolean) => Promise<void>;
  fetchJobDetails: (jobId: string, force?: boolean) => Promise<void>;
  fetchJobCriteria: (jobId: string, force?: boolean) => Promise<any>;
  createJob: (jobData: any) => Promise<any>;
  updateJobAsync: (jobId: string, updates: any) => Promise<any>;
  deleteJob: (jobId: string) => Promise<void>;
  refreshJobs: () => Promise<any[]>;
  reset: () => void;

  // ========== Candidates Management ==========
  fetchCandidates: (jobId: string, page?: number, filters?: any) => Promise<void>;
  fetchCandidateById: (jobId: string, candidateId: string) => Promise<any | null>;
  updateCandidateStatus: (jobId: string, candidateId: string, status: string) => Promise<void>;
  updateCandidateStatusWithMessage: (
    jobId: string,
    candidateId: string,
    status: string,
    message?: string,
    additionalData?: any
  ) => Promise<any>;
  onCandidateStatusChange: (jobId: string, candidateId: string) => Promise<void>;

  // ========== Optimistic Updates ==========
  updateCandidateStatusOptimistic: (jobId: string, candidateId: string, status: string) => void;
  revertOptimisticUpdate: (key: string) => void;
  silentDataSync: (jobId: string) => Promise<void>;

  // ========== Job Selection ==========
  setSelectedJob: (jobId: string | null) => void;
  setSelectedJobId: (jobId: string | null) => void;
  getSelectedJob: () => any;

  // ========== Form Management ==========
  updateFormData: (updates: any) => void;
  resetForm: () => void;
  saveForm: () => Promise<void>;

  // ========== Publishing ==========
  fetchPlatforms: () => Promise<PublishPlatform[]>;
  togglePlatformSelection: (platformId: string) => void;
  publishJob: (jobId: string, platforms: string[]) => Promise<any>;
  unpublishJob: (jobId: string, platforms?: string[]) => Promise<void>;
  getEffectivePublishedState: (job?: any) => boolean;

  // ========== UI State Management ==========
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setProcessing: (processing: boolean) => void;
  setError: (error: string | null) => void;

  // ========== Validation ==========
  setValidationErrors: (errors: any) => void;
  clearValidationErrors: () => void;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  hasPendingChanges: () => boolean;
  hasThresholdChanges: () => boolean;

  // ========== Pagination & Filtering ==========
  setCurrentPage: (page: number) => void;
  setFilters: (filters: any) => void;
}

export const createJobOperationsSlice = (set: any, get: any): JobOperationsSlice => ({
  // ========== Job CRUD Operations ==========
  fetchJobs: async () => {
    try {
      set({ isLoading: true });

      const response = await apiHelper.get('/jobs');

      if (response) {
        set((state: any) => ({
          jobs: response,
          isLoading: false,
          lastFetchTime: {
            ...state.lastFetchTime,
            jobs: Date.now(),
          },
        }));
        return response;
      }
      return [];
    } catch (error) {
      console.error('Error fetching jobs:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch jobs',
      });
      throw error;
    }
  },

  fetchJob: async (jobId: string) => {
    try {
      const response = await apiHelper.get(`/jobs/${jobId}`);

      if (response) {
        const job = response.job || response;
        set((state: any) => ({
          jobsById: {
            ...state.jobsById,
            [jobId]: job,
          },
          lastFetchTime: {
            ...state.lastFetchTime,
            [`job-${jobId}`]: Date.now(),
          },
        }));
        return job;
      }
      return null;
    } catch (error) {
      console.error('Error fetching job:', error);
      throw error;
    }
  },

  fetchJobById: async (jobId: string, force?: boolean) => {
    try {
      const response = await apiHelper.get(`/jobs/${jobId}`);

      if (response.success) {
        const job = response.job || response.data || response;

        // Update the store with the fetched job
        set((state: any) => ({
          job: job,
          selectedJob: job,
          selectedJobId: jobId,
          currentJob: job,
          jobsById: {
            ...state.jobsById,
            [jobId]: job,
          },
          jobs: state.jobs.map((j: any) => (j.id === jobId ? job : j)),
        }));
      }
    } catch (error) {
      console.error('Error fetching job:', error);
    }
  },

  fetchJobDetails: async (jobId: string, force?: boolean) => {
    const cacheKey = `job-details-${jobId}`;

    // Check cache if not forcing refresh
    if (!force && get().lastFetchTime?.[cacheKey]) {
      const now = Date.now();
      const lastFetch = get().lastFetchTime[cacheKey];
      const cacheTimeout = get().cacheTimeout || 5 * 60 * 1000; // 5 minutes default

      if (now - lastFetch < cacheTimeout) {
        return;
      }
    }

    try {
      set({ isLoading: true });

      // Import the utility function dynamically to avoid circular dependencies
      const { fetchJobDetails } = await import('@/utils/jobDetailsUtils');

      // Always use the details endpoint for this method
      const jobData = await fetchJobDetails(jobId, true);

      set((state: any) => ({
        currentJobDetails: jobData,
        selectedJobId: jobId,
        // Also update currentJob if it matches
        currentJob:
          state.currentJob?.id === jobId
            ? {
                ...state.currentJob,
                isPublished: jobData.isPublished,
                publishedPlatforms: jobData.publishedPlatforms,
                status: jobData.status,
              }
            : state.currentJob,
        // Update in jobs array too
        jobs: state.jobs.map((job: any) =>
          job.id === jobId
            ? {
                ...job,
                isPublished: jobData.isPublished,
                publishedPlatforms: jobData.publishedPlatforms,
                status: jobData.status,
              }
            : job
        ),
        jobsById: {
          ...state.jobsById,
          [jobId]: {
            ...state.jobsById[jobId],
            isPublished: jobData.isPublished,
            publishedPlatforms: jobData.publishedPlatforms,
            status: jobData.status,
          },
        },
        isLoading: false,
        lastFetchTime: {
          ...state.lastFetchTime,
          [cacheKey]: Date.now(),
        },
      }));
    } catch (error) {
      console.error('[fetchJobDetails] Error fetching job details:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch job details',
      });
      throw error;
    }
  },

  fetchJobCriteria: async (jobId: string, force?: boolean) => {
    const cacheKey = `criteria-${jobId}`;

    // Check cache if not forcing refresh
    if (!force && get().lastFetchTime?.[cacheKey]) {
      const now = Date.now();
      const lastFetch = get().lastFetchTime[cacheKey];
      const cacheTimeout = get().cacheTimeout || 5 * 60 * 1000; // 5 minutes default

      if (now - lastFetch < cacheTimeout) {
        // Return cached data instead of undefined
        return get().currentJob;
      }
    }

    try {
      set({ isLoading: true });

      const response = await apiHelper.get(`/jobs/${jobId}/criteria`);

      if (response) {
        // The criteria API returns additional data like candidates, stats, etc.
        // Extract the job data - it could be nested under 'job' property or at root level
        const job = response.job || response;
        const stats = response.stats || {};

        // Extract additional data that TotalCandidatesSection needs
        const candidatesPoolData = {
          candidatesPool: response.candidatesPool || { total: 0, candidates: [] },
          candidatesPoolStats: response.candidatesPoolStats || {},
        };

        // Extract matchRankCost from root level of response
        const matchRankCost = response.matchRankCost || null;

        // Extract recentCandidates from root level of response
        const recentCandidates = response.recentCandidates || [];

        // Extract totalCandidates from root level of response (could be in multiple places)
        const totalCandidates =
          response.totalCandidates || stats?.totalCandidates || response.candidatesPool?.total || 0;

        // Merge job data with additional fields from response
        const jobWithCandidates = {
          ...job,
          recentCandidates: recentCandidates,
          totalCandidates: totalCandidates,
        };

        // Convert job data to form data for editing
        const formData = {
          jobTitle: job.jobTitle || job.jobType || '',
          jobType: job.jobType || '',
          jobDescription: job.jobDescription || '',
          department: job.department || '',
          topCandidateThreshold: job.topCandidateThreshold || 0,
          secondTierCandidateThreshold: job.secondTierCandidateThreshold || 0,
          requirements: job.requirements || '',
          status: job.status || JobStatus.NEW,
          location: job.location || '',
          employmentType: job.employmentType || 'FULL_TIME',
          workMode: job.workMode || 'HYBRID',
          benefits: job.benefits || [],
          skills: job.skills || [],
          jobResponsibilities: job.jobResponsibilities || [],
          experience: job.experience || '',
          typeOfHiring: job.typeOfHiring || '',
          typeOfJob: job.typeOfJob || '',
        };

        // Update stats with totalCandidates if available
        const updatedStats = {
          ...stats,
          totalCandidates: totalCandidates || stats.totalCandidates || 0,
        };

        set((state: any) => ({
          currentJob: jobWithCandidates,
          formData: formData,
          originalFormData: formData,
          stats: updatedStats,
          hasUnsavedChanges: false,
          ...candidatesPoolData,
          matchRankCost: matchRankCost, // Store matchRankCost at root level
          isLoading: false,
          lastFetchTime: {
            ...state.lastFetchTime,
            [cacheKey]: Date.now(),
          },
        }));

        return jobWithCandidates;
      }
    } catch (error) {
      console.error('Error fetching job criteria:', error);
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch job criteria',
      });
      throw error;
    }
  },

  createJob: async (jobData: any) => {
    try {
      set({ isSaving: true });

      const response = await apiHelper.post('/jobs', jobData);

      if (response) {
        set((state: any) => ({
          jobs: [...state.jobs, response],
          currentJob: response,
          selectedJobId: response.id,
          selectedJob: response,
          jobsById: {
            ...state.jobsById,
            [response.id]: response,
          },
          isSaving: false,
          lastFetchTime: {
            ...state.lastFetchTime,
            jobs: 0, // Invalidate jobs cache
          },
        }));

        return response;
      }

      throw new Error('Failed to create job');
    } catch (error) {
      console.error('Error creating job:', error);
      set({
        isSaving: false,
        error: error instanceof Error ? error.message : 'Failed to create job',
      });
      throw error;
    }
  },

  updateJobAsync: async (jobId: string, updates: any) => {
    try {
      set({ isSaving: true });

      const response = await apiHelper.put(`/jobs/${jobId}`, updates);

      if (response) {
        set((state: any) => ({
          // Update current job
          currentJob:
            state.currentJob?.id === jobId
              ? { ...state.currentJob, ...response }
              : state.currentJob,

          // Update job in jobs array
          jobs: state.jobs.map((job: any) => (job.id === jobId ? { ...job, ...response } : job)),

          // Update jobsById
          jobsById: {
            ...state.jobsById,
            [jobId]: state.jobsById[jobId] ? { ...state.jobsById[jobId], ...response } : response,
          },

          originalFormData: { ...state.formData },
          hasUnsavedChanges: false,
          isSaving: false,
          lastFetchTime: {
            ...state.lastFetchTime,
            [`job-${jobId}`]: Date.now(),
          },
        }));

        return response;
      }
    } catch (error) {
      console.error('Error updating job:', error);
      set({
        isSaving: false,
        error: error instanceof Error ? error.message : 'Failed to update job',
      });
      throw error;
    }
  },

  deleteJob: async (jobId: string) => {
    try {
      set({ isProcessing: true });

      await apiHelper.delete(`/jobs/${jobId}`);

      set((state: any) => {
        const { [jobId]: removed, ...restJobsById } = state.jobsById;
        return {
          jobs: state.jobs.filter((job: any) => job.id !== jobId),
          jobsById: restJobsById,
          currentJob: state.currentJob?.id === jobId ? null : state.currentJob,
          selectedJobId: state.selectedJobId === jobId ? null : state.selectedJobId,
          selectedJob: state.selectedJob?.id === jobId ? null : state.selectedJob,
          isProcessing: false,
          lastFetchTime: {
            ...state.lastFetchTime,
            jobs: 0, // Invalidate jobs cache
          },
        };
      });
    } catch (error) {
      console.error('Error deleting job:', error);
      set({
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Failed to delete job',
      });
      throw error;
    }
  },

  refreshJobs: async () => {
    // Invalidate cache and fetch fresh data
    set((state: any) => ({
      lastFetchTime: {
        ...state.lastFetchTime,
        jobs: 0,
      },
    }));
    return get().fetchJobs();
  },

  // ========== Job Selection ==========
  setSelectedJob: (jobId: string | null) => {
    set((state: any) => {
      // Try to find job in multiple places
      let job = null;
      if (jobId) {
        // Check jobsById first
        job = state.jobsById[jobId];

        // If not found, check jobs array
        if (!job && Array.isArray(state.jobs)) {
          job = state.jobs.find((j: any) => j.id === jobId);
        }

        // If still not found, check jobsByStatus
        if (!job && state.jobsByStatus?.ALL?.jobs) {
          job = state.jobsByStatus.ALL.jobs.find((j: any) => j.id === jobId);
        }
      }

      return {
        selectedJobId: jobId,
        currentJob: job,
        selectedJob: job,
      };
    });
  },

  setSelectedJobId: (jobId: string | null) => {
    get().setSelectedJob(jobId);
  },

  getSelectedJob: () => {
    return get().selectedJob;
  },

  // ========== Form Management ==========
  updateFormData: (updates: any) => {
    set((state: any) => ({
      formData: { ...state.formData, ...updates },
      hasUnsavedChanges: true,
    }));
  },

  resetForm: () => {
    const initialFormData: JobFormData = {
      jobTitle: '',
      jobType: '',
      jobDescription: '',
      department: '',
      topCandidateThreshold: 0,
      secondTierCandidateThreshold: 0,
      requirements: '',
      status: JobStatus.DRAFT,
      location: '',
      employmentType: 'FULL_TIME',
      workMode: 'HYBRID',
      benefits: [],
    };

    set({
      formData: initialFormData,
      originalFormData: null,
      hasUnsavedChanges: false,
      validationErrors: {},
    });
  },

  saveForm: async () => {
    const { selectedJobId, formData, updateJobAsync } = get();
    if (selectedJobId && formData) {
      await updateJobAsync(selectedJobId, formData);
    }
  },

  // ========== Publishing ==========
  fetchPlatforms: async () => {
    try {
      const response = await apiHelper.get('/publishing/platforms');

      if (response) {
        set({ availablePlatforms: response });
        return response;
      }
      return [];
    } catch (error) {
      console.error('Error fetching platforms:', error);
      return [];
    }
  },

  togglePlatformSelection: (platformId: string) => {
    set((state: any) => {
      const platforms = [...state.selectedPlatforms];
      const index = platforms.indexOf(platformId);
      if (index > -1) {
        platforms.splice(index, 1);
      } else {
        platforms.push(platformId);
      }
      return { selectedPlatforms: platforms };
    });
  },

  publishJob: async (jobId: string, platforms: string[]) => {
    try {
      // Optimistically update the state immediately
      set((state: any) => ({
        isPublishing: true,
        // Optimistically update current job
        currentJob:
          state.currentJob?.id === jobId
            ? {
                ...state.currentJob,
                isPublished: true,
                publishedPlatforms: platforms,
                publishedAt: new Date().toISOString(),
                status: 'PUBLISHED',
              }
            : state.currentJob,
        // Also update currentJobDetails if it matches
        currentJobDetails:
          state.currentJobDetails?.id === jobId
            ? {
                ...state.currentJobDetails,
                isPublished: true,
                publishedPlatforms: platforms,
                publishedAt: new Date().toISOString(),
                status: 'PUBLISHED',
              }
            : state.currentJobDetails,
        // Optimistically update job in jobs array
        jobs: state.jobs.map((job: any) =>
          job.id === jobId
            ? {
                ...job,
                isPublished: true,
                publishedPlatforms: platforms,
                publishedAt: new Date().toISOString(),
                status: 'PUBLISHED',
              }
            : job
        ),
        jobsById: {
          ...state.jobsById,
          [jobId]: state.jobsById[jobId]
            ? {
                ...state.jobsById[jobId],
                isPublished: true,
                publishedPlatforms: platforms,
                publishedAt: new Date().toISOString(),
                status: 'PUBLISHED',
              }
            : state.jobsById[jobId],
        },
      }));

      const response = await apiHelper.put(`/jobs/${jobId}/publish`, { platforms });

      // Extract the publication status from the response
      const jobboardResult = response.find((result: any) => result.platform === 'jobboard');
      const isPublished = jobboardResult?.isPublished ?? true;

      set({ isPublishing: false, selectedPlatforms: [] });
      return response;
    } catch (error) {
      console.error('Error publishing job:', error);
      // Revert optimistic updates on error
      set({ isPublishing: false });
      throw error;
    }
  },

  unpublishJob: async (jobId: string, platforms?: string[]) => {
    try {
      // Optimistically update the state immediately
      set((state: any) => ({
        isUnpublishing: true,
        // Optimistically update current job
        currentJob:
          state.currentJob?.id === jobId
            ? {
                ...state.currentJob,
                isPublished: false,
                publishedPlatforms: [],
                status: 'ACTIVE',
              }
            : state.currentJob,
        // Also update currentJobDetails if it matches
        currentJobDetails:
          state.currentJobDetails?.id === jobId
            ? {
                ...state.currentJobDetails,
                isPublished: false,
                publishedPlatforms: [],
                status: 'ACTIVE',
              }
            : state.currentJobDetails,
        // Optimistically update job in jobs array
        jobs: state.jobs.map((job: any) =>
          job.id === jobId
            ? {
                ...job,
                isPublished: false,
                publishedPlatforms: [],
                status: 'ACTIVE',
              }
            : job
        ),
        jobsById: {
          ...state.jobsById,
          [jobId]: state.jobsById[jobId]
            ? {
                ...state.jobsById[jobId],
                isPublished: false,
                publishedPlatforms: [],
                status: 'ACTIVE',
              }
            : state.jobsById[jobId],
        },
        selectedPlatforms: [],
      }));

      // Use the same toggle endpoint as publishJob
      const response = await apiHelper.put(`/jobs/${jobId}/publish`, {
        platforms: platforms || ['jobboard'],
      });

      set({ isUnpublishing: false });
      return response;
    } catch (error) {
      console.error('Error unpublishing job:', error);
      // Revert optimistic updates on error
      set({ isUnpublishing: false });
      throw error;
    }
  },

  getEffectivePublishedState: (job?: any) => {
    return job?.isPublished === true;
  },

  // ========== UI State Management ==========
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setSaving: (saving: boolean) => {
    set({ isSaving: saving });
  },

  setProcessing: (processing: boolean) => {
    set({ isProcessing: processing });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  // ========== Validation ==========
  setValidationErrors: (errors: any) => {
    set({ validationErrors: errors });
  },

  clearValidationErrors: () => {
    set({ validationErrors: {} });
  },

  setHasUnsavedChanges: (hasChanges: boolean) => {
    set({ hasUnsavedChanges: hasChanges });
  },

  hasPendingChanges: () => {
    return get().hasUnsavedChanges;
  },

  hasThresholdChanges: () => {
    const { formData, originalFormData } = get();
    if (!originalFormData) return false;
    return (
      formData.topCandidateThreshold !== originalFormData.topCandidateThreshold ||
      formData.secondTierCandidateThreshold !== originalFormData.secondTierCandidateThreshold
    );
  },

  // ========== Pagination & Filtering ==========
  setCurrentPage: (page: number) => {
    set({ currentPage: page });
  },

  setFilters: (filters: any) => {
    set({ filters });
  },

  // ========== Candidates Management Implementation ==========
  fetchCandidates: async (jobId: string, page = 1, filters = {}) => {
    try {
      set({ isLoading: true });

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10', // Using default pageSize
        ...filters,
      });

      // Use regular endpoint which now returns minimal data by default
      const response = await apiHelper.get(`/jobs/${jobId}/candidates?${params}`);

      if (response) {
        const { job, candidates, stats, pagination } = response;

        set((state: any) => ({
          // Store candidates under the jobId in the grouped structure
          candidates: {
            ...state.candidates,
            [jobId]: candidates,
          },
          stats: stats,
          currentPage: page,
          totalPages: pagination?.totalPages || 1,
          filters: filters,
          isLoading: false,
          // Update current job if provided
          currentJob: job || state.currentJob,
          selectedJob: job || state.selectedJob,
        }));
      } else {
        // If no response, still reset loading state
        set({ isLoading: false });
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  fetchCandidateById: async (jobId: string, candidateId: string) => {
    try {
      const response = await apiHelper.get(`/candidates/${candidateId}`);

      if (response) {
        // Update the candidate in the store if it exists
        set((state: any) => {
          if (state.candidates && state.candidates[jobId]) {
            const candidates = state.candidates[jobId];
            const index = candidates.findIndex((c: any) => c.id === candidateId);
            if (index !== -1) {
              candidates[index] = {
                ...candidates[index],
                ...response,
              };
            }
          }

          // Also update in allCandidatesFlat
          const flatIndex = state.allCandidatesFlat.findIndex((c: any) => c.id === candidateId);
          if (flatIndex !== -1) {
            state.allCandidatesFlat[flatIndex] = {
              ...state.allCandidatesFlat[flatIndex],
              ...response,
            };
          }

          return state;
        });

        return response;
      }
      return null;
    } catch (error) {
      console.error('Error fetching candidate:', error);
      throw error;
    }
  },

  updateCandidateStatus: async (jobId: string, candidateId: string, status: string) => {
    try {
      // Apply optimistic update
      get().updateCandidateStatusOptimistic(jobId, candidateId, status);

      const response = await apiHelper.put(`/candidates/${candidateId}/status`, { status });

      if (response) {
        // Update was successful, refresh candidates to get accurate data
        await get().fetchCandidates(jobId, get().currentPage, get().filters);
      }

      // Remove optimistic update
      get().revertOptimisticUpdate(`${jobId}_${candidateId}`);
    } catch (error) {
      console.error('Error updating candidate status:', error);
      // Revert optimistic update on error
      get().revertOptimisticUpdate(`${jobId}_${candidateId}`);
      throw error;
    }
  },

  updateCandidateStatusWithMessage: async (
    jobId: string,
    candidateId: string,
    status: string,
    message?: string,
    additionalData?: any
  ) => {
    try {
      // Apply optimistic update
      get().updateCandidateStatusOptimistic(jobId, candidateId, status);

      const payload = {
        status,
        ...(message && { message }),
        ...additionalData,
      };

      const response = await apiHelper.put(`/candidates/${candidateId}/status`, payload);

      if (response) {
        // Update was successful, refresh candidates to get accurate data
        await get().fetchCandidates(jobId, get().currentPage, get().filters);
      }

      // Remove optimistic update
      get().revertOptimisticUpdate(`${jobId}_${candidateId}`);

      return response;
    } catch (error) {
      console.error('Error updating candidate status with message:', error);
      // Revert optimistic update on error
      get().revertOptimisticUpdate(`${jobId}_${candidateId}`);
      throw error;
    }
  },

  onCandidateStatusChange: async (jobId: string, candidateId: string) => {
    try {
      // Refresh candidates data to get updated stats
      await get().fetchCandidates(jobId, get().currentPage, get().filters);
    } catch (error) {
      console.error('Error refreshing candidates data after status change:', error);
    }
  },

  // ========== Optimistic Updates Implementation ==========
  updateCandidateStatusOptimistic: (jobId: string, candidateId: string, status: string) => {
    set((state: any) => ({
      optimisticUpdates: {
        ...state.optimisticUpdates,
        [`${jobId}_${candidateId}`]: { status, timestamp: Date.now() },
      },
    }));
  },

  revertOptimisticUpdate: (key: string) => {
    set((state: any) => {
      const { [key]: removed, ...optimisticUpdates } = state.optimisticUpdates;
      return { optimisticUpdates };
    });
  },

  silentDataSync: async (jobId: string) => {
    set({ silentUpdateInProgress: true });
    try {
      // Silently fetch latest candidates data without showing loading state
      const params = new URLSearchParams({
        page: get().currentPage?.toString() || '1',
        limit: '10',
        ...get().filters,
      });

      const response = await apiHelper.get(`/jobs/${jobId}/candidates?${params}`);

      if (response) {
        const { candidates, stats, pagination } = response;

        set((state: any) => ({
          // Update candidates without affecting loading state
          candidates: {
            ...state.candidates,
            [jobId]: candidates,
          },
          stats: stats || state.stats,
          totalPages: pagination?.totalPages || state.totalPages,
        }));
      }
    } catch (error) {
      console.error('Error in silent data sync:', error);
    } finally {
      set({ silentUpdateInProgress: false });
    }
  },

  reset: () => {
    const initialFormData: JobFormData = {
      jobTitle: '',
      jobType: '',
      jobDescription: '',
      department: '',
      topCandidateThreshold: 0,
      secondTierCandidateThreshold: 0,
      requirements: '',
      status: JobStatus.NEW,
      location: '',
      employmentType: 'FULL_TIME',
      workMode: 'HYBRID',
      benefits: [],
    };

    set({
      jobs: [],
      currentJob: null,
      currentJobDetails: null,
      selectedJobId: null,
      selectedJob: null,
      candidates: {},
      allCandidatesFlat: [],
      stats: null,
      formData: initialFormData,
      originalFormData: null,
      hasUnsavedChanges: false,
      isLoading: false,
      isSaving: false,
      isProcessing: false,
      error: null,
      validationErrors: {},
    });
  },
});
