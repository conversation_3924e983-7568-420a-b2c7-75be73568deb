import { NextApiRequest, NextApiResponse } from 'next';

/**
 * API endpoint to clear all session data before logout
 * This ensures complete cache clearing to prevent cross-login issues
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Clear all cookies from the response
    const cookies = req.cookies;
    const cookieHeaders: string[] = [];

    // Clear each cookie with all possible domain/path combinations
    Object.keys(cookies).forEach(cookieName => {
      // Clear for current domain
      cookieHeaders.push(
        `${cookieName}=; Max-Age=0; Path=/; HttpOnly=false; SameSite=Lax`,
        `${cookieName}=; Max-Age=0; Path=/; Domain=${req.headers.host}; HttpOnly=false; SameSite=Lax`,
        `${cookieName}=; Max-Age=0; Path=/api; HttpOnly=false; SameSite=Lax`,
        `${cookieName}=; Max-Age=0; Path=/api/auth; HttpOnly=false; SameSite=Lax`,
        // Also clear with expires in the past
        `${cookieName}=; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Path=/; HttpOnly=false; SameSite=Lax`,
        `${cookieName}=; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Path=/; Domain=${req.headers.host}; HttpOnly=false; SameSite=Lax`
      );

      // Clear for parent domain if subdomain
      if (req.headers.host && req.headers.host.includes('.')) {
        const parentDomain = '.' + req.headers.host.split('.').slice(-2).join('.');
        cookieHeaders.push(
          `${cookieName}=; Max-Age=0; Path=/; Domain=${parentDomain}; HttpOnly=false; SameSite=Lax`,
          `${cookieName}=; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Path=/; Domain=${parentDomain}; HttpOnly=false; SameSite=Lax`
        );
      }
    });

    // Specifically clear Auth0 cookies
    const auth0Cookies = [
      'appSession',
      'appSession.0',
      'appSession.1',
      'appSession.2',
      'auth0',
      'auth0.is',
      'auth0.is.authenticated',
      'auth_session',
      'a0.sso',
      '_auth0',
      'auth0_compat',
    ];

    auth0Cookies.forEach(cookieName => {
      cookieHeaders.push(
        `${cookieName}=; Max-Age=0; Path=/; HttpOnly=false; SameSite=Lax`,
        `${cookieName}=; Max-Age=0; Path=/; Domain=${req.headers.host}; HttpOnly=false; SameSite=Lax`,
        `${cookieName}=; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Path=/; HttpOnly=false; SameSite=Lax`
      );
    });

    // Clear role-related cookies
    const rolePatterns = ['pendingRole_', 'userRole_', 'role_', 'session_'];
    Object.keys(cookies).forEach(cookieName => {
      if (rolePatterns.some(pattern => cookieName.startsWith(pattern))) {
        cookieHeaders.push(
          `${cookieName}=; Max-Age=0; Path=/; HttpOnly=false; SameSite=Lax`,
          `${cookieName}=; Expires=Thu, 01 Jan 1970 00:00:00 GMT; Path=/; HttpOnly=false; SameSite=Lax`
        );
      }
    });

    // Set all cookie clearing headers
    res.setHeader('Set-Cookie', cookieHeaders);

    // Add cache control headers to prevent caching of this response
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    return res.status(200).json({
      success: true,
      message: 'Session cleared successfully',
      clearedCookies: Object.keys(cookies).length,
    });
  } catch (error) {
    console.error('Error clearing session:', error);
    return res.status(500).json({
      error: 'Failed to clear session',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
