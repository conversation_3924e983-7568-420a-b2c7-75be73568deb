'use client';

import '@/styles/hide-scrollbar.css';

import React, { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import {
  Award,
  Briefcase,
  Calendar,
  CheckCircle,
  CheckCircle2,
  ChevronDown,
  Clock,
  FileText,
  Filter,
  MapPin,
  RefreshCw,
  Search,
  Users,
  X,
} from 'lucide-react';
import { useRouter } from 'next/router';

import { AdvancedFilterPanel } from '@/components/CandidateFilters';
import CandidateFullProfileStandard from '@/components/Candidates/CandidateFullProfileStandard';
import PageHeader from '@/components/common/PageHeader';
import CandidateStatusDrawer from '@/components/JobSeeker/components/ProfileModal/CandidateStatusDrawer';
import CandidateTable from '@/components/Layouts/CandidateTable';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import { ICandidate, useTalentHubStore } from '@/stores/talentHubStore';
import { CandidateStatus } from '@/types/candidate.types';

interface Job {
  id: string;
  title: string;
  department?: string;
  location?: string;
  company?: string;
}

// Status display names and their order
const CANDIDATE_STATUSES = [
  {
    id: 'ALL',
    label: 'All',
    icon: <Users className="w-5 h-5" />,
    tooltip: 'Show all candidates regardless of status',
  },
  {
    id: CandidateStatus.NEW,
    label: 'New',
    icon: <CheckCircle className="w-5 h-5" />,
    tooltip: 'New candidates who have not been evaluated yet',
  },
  {
    id: CandidateStatus.INTERVIEWING,
    label: 'Interviewing',
    icon: <Calendar className="w-5 h-5" />,
    tooltip: 'Candidates in the interview process',
  },
  {
    id: CandidateStatus.OFFER_PENDING_APPROVAL,
    label: 'Offer Pending',
    icon: <Clock className="w-5 h-5" />,
    tooltip: 'Candidates with pending offer approvals',
  },
  {
    id: CandidateStatus.OFFER_EXTENDED,
    label: 'Offer Extended',
    icon: <CheckCircle2 className="w-5 h-5" />,
    tooltip: 'Candidates with extended offers',
  },
  {
    id: CandidateStatus.OFFER_ACCEPTED,
    label: 'Offer Accepted',
    icon: <CheckCircle className="w-5 h-5" />,
    tooltip: 'Candidates who have accepted offers',
  },
  {
    id: CandidateStatus.HIRED,
    label: 'Hired',
    icon: <Briefcase className="w-5 h-5" />,
    tooltip: 'Candidates who have been hired',
  },
  {
    id: CandidateStatus.REJECTED,
    label: 'Rejected',
    icon: <X className="w-5 h-5" />,
    tooltip: 'Candidates who have been rejected',
  },
];

// Convert candidate statuses to options for the dropdown
const STATUS_OPTIONS = CANDIDATE_STATUSES.map(status => ({
  value: status.id,
  label: status.label,
  icon: status.icon,
}));

const CandidateTrackingPage: React.FC = () => {
  const router = useRouter();
  const { jobId } = router.query;
  const [selectedJobId, setSelectedJobId] = useState<string | undefined>(undefined);
  const [isRouterReady, setIsRouterReady] = useState(false);
  // Use Zustand store for state management
  const {
    candidates,
    selectedCandidate,
    loading,
    isModalOpen,
    filters: storeFilters,
    pagination,
    fetchCandidates,
    fetchCandidateDetails,
    setFilters: setStoreFilters,
    setSelectedCandidate,
    setModalOpen,
    setPage,
  } = useTalentHubStore();

  // Local state for UI-specific functionality
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [activeFilterCount, setActiveFilterCount] = useState(0);
  const [stats, setStats] = useState<any>({
    totalCandidates: 0,
    inInterview: 0,
    pendingApproval: 0,
    hired: 0,
    byStatus: {},
    matchScoreDistribution: {
      excellent: 0,
      veryGood: 0,
      good: 0,
      fair: 0,
      poor: 0,
    },
    tierDistribution: {
      TOP: 0,
      SECOND: 0,
      OTHER: 0,
    },
    thresholdStats: {
      aboveTopThreshold: 0,
      aboveSecondThreshold: 0,
      belowThresholds: 0,
    },
    averageMatchScore: 0,
    thresholds: {
      topCandidateThreshold: 70,
      secondTierCandidateThreshold: 50,
    },
  });

  // Map store filters to local filter format for compatibility
  const filters = {
    status: storeFilters.status || '',
    jobId: storeFilters.jobId || '',
    searchTerm: storeFilters.searchTerm || '',
  };
  const [viewType, setViewType] = useState<'detailed' | 'status'>('detailed');

  // Get counts from stats for status display
  const statusCounts = {
    ALL: stats.totalCandidates,
    [CandidateStatus.NEW]:
      stats.totalCandidates - stats.inInterview - stats.pendingApproval - stats.hired,
    [CandidateStatus.INTERVIEWING]: stats.inInterview,
    [CandidateStatus.OFFER_PENDING_APPROVAL]: stats.pendingApproval,
    [CandidateStatus.OFFER_EXTENDED]: 0, // Add this to stats if needed
    [CandidateStatus.OFFER_ACCEPTED]: 0, // Add this to stats if needed
    [CandidateStatus.HIRED]: stats.hired,
    [CandidateStatus.REJECTED]: 0, // Add this to stats if needed
  };

  // Update status options with counts
  const statusOptionsWithCounts = STATUS_OPTIONS.map(option => ({
    ...option,
    label: `${option.label} (${statusCounts[option.value] || 0})`,
  }));

  // Handle status change
  const handleStatusChange = async (jobId: string, newStatus: CandidateStatus) => {
    try {
      await fetchCandidates(pagination.currentPage, pagination.itemsPerPage);
    } catch (error) {
      console.error('Error handling status change:', error);
    }
  };

  // Function to refresh candidate data after status changes
  const refreshCandidate = async () => {
    try {
      await fetchCandidates(pagination.currentPage, pagination.itemsPerPage);
    } catch (error) {
      console.error('Error refreshing candidate data:', error);
    }
  };

  useEffect(() => {
    if (router.isReady) {
      setIsRouterReady(true);

      // Set job ID from URL
      if (jobId && typeof jobId === 'string') {
        setSelectedJobId(jobId);
      } else {
        setSelectedJobId(undefined);
      }
    }
  }, [router.isReady, jobId, router.query.page, router.query.searchTerm, router.query.status]);

  // Use effect to fetch data when component mounts and router is ready
  useEffect(() => {
    if (isRouterReady) {
      // Set initial filters from URL params
      const urlParams = new URLSearchParams(window.location.search);
      const initialFilters = {
        jobId: (jobId as string) || urlParams.get('jobId') || '',
        status: urlParams.get('status') || '',
        searchTerm: urlParams.get('searchTerm') || '',
        tier: urlParams.get('tier') || '',
        typeOfJob: urlParams.get('typeOfJob') || '',
        department: urlParams.get('department') || '',
        experienceLevel: urlParams.get('experienceLevel') || '',
        skills: urlParams.get('skills') || '',
        location: urlParams.get('location') || '',
      };

      setStoreFilters(initialFilters);
      fetchCandidates(1, 10);
    }
  }, [isRouterReady, jobId, fetchCandidates, setStoreFilters]);

  // Update active filter count
  useEffect(() => {
    let count = 0;
    Object.entries(filters).forEach(([key, value]) => {
      if (value && key !== 'jobId') count++;
    });
    setActiveFilterCount(count);
  }, [filters]);

  // Handle page change
  const handlePageChange = (page: number) => {
    // Update URL with the new page number
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.set('page', page.toString());

    // Preserve existing query parameters
    if (jobId) {
      currentParams.set('jobId', jobId as string);
    }

    // Add search term if present
    if (storeFilters.searchTerm) {
      currentParams.set('searchTerm', storeFilters.searchTerm);
    }

    // Add status if present
    if (storeFilters.status) {
      currentParams.set('status', storeFilters.status);
    }

    // Update URL without full page reload
    router.push(`?${currentParams.toString()}`, undefined, { shallow: true });

    // Use store method to change page
    setPage(page);
  };

  // This function is now handled directly in the AdvancedFilterPanel component
  // We're keeping a simplified version for reference
  /*
  const handleFilterStatusChange = (status: string) => {
    setFilters(prev => ({
      ...prev,
      status: status === 'ALL' ? '' : status,
    }));
    setCurrentPage(1);
  };
  */

  // Use the candidates directly from the store
  const filteredCandidates = candidates;

  // Debug logging

  // Future functionality for adding candidates
  /*
  const handleAddCandidate = () => {
    // Navigate to candidate add form or open modal
    // Future functionality: window.location.href = '/candidates/new';
  };
  */

  // Add back the formatStatus function in the main component
  const formatStatus = (status: string) => {
    return status
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  // Helper function to validate email
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return email && emailRegex.test(email);
  };

  // Helper function to format location
  const formatLocation = (location: string) => {
    if (!location) return 'Not specified';

    // Handle comma-separated locations
    const parts = location.split(',').map(part => part.trim());

    // Remove duplicates and country names
    const uniqueParts = [];
    const seen = new Set();

    for (const part of parts) {
      const normalized = part.toLowerCase();
      if (
        !seen.has(normalized) &&
        !['united states', 'usa', 'us', 'united kingdom', 'uk', 'canada'].includes(normalized)
      ) {
        seen.add(normalized);
        uniqueParts.push(part);
      }
    }

    // If we have duplicate city names, just show once
    if (uniqueParts.length >= 2 && uniqueParts[0].toLowerCase() === uniqueParts[1].toLowerCase()) {
      return uniqueParts[0];
    }

    return uniqueParts.join(', ');
  };

  const columns = [
    {
      key: 'fullName',
      label: 'Candidate',
      icon: Users,
      render: (value: string, candidate: ICandidate) => {
        const name = value || candidate.name || 'Unknown';
        const email = candidate.email || '';
        const hasValidEmail = isValidEmail(email);

        return (
          <div className="flex flex-col">
            <div className="flex items-center gap-1">
              <span className="font-medium text-white">{name}</span>

              {/* Tier badges */}
              {candidate.tier === 'TOP' && (
                <span
                  className="inline-flex items-center justify-center w-5 h-5"
                  title="Top tier candidate"
                >
                  <span className="text-yellow-400">★</span>
                </span>
              )}
              {candidate.tier === 'SECOND' && (
                <span
                  className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-blue-500/20 border border-blue-500/30"
                  title="Second tier candidate"
                >
                  <span className="text-xs text-blue-300 font-bold">2</span>
                </span>
              )}

              {/* Matched badge - minimal and light purple */}
              {candidate.status === 'MATCHED' && (
                <span
                  className="inline-flex items-center gap-0.5 px-1 py-0 rounded-full bg-purple-400/20 border border-purple-400/30"
                  title="Matched with job"
                >
                  <svg className="w-2 h-2 text-purple-300" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-[9px] text-purple-300 font-medium tracking-wide">
                    MATCHED
                  </span>
                </span>
              )}
            </div>

            {hasValidEmail && (
              <span className="text-xs text-gray-400 truncate max-w-[200px]" title={email}>
                {email}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'displayTitle',
      label: 'Position',
      icon: Briefcase,
      render: (value: string, candidate: ICandidate) => {
        const title = value || candidate.jobTitle || candidate.__job__?.jobType || 'Not Provided';
        const company = candidate.currentCompany || '';
        return (
          <div className="flex flex-col">
            <span className="text-white truncate max-w-[250px]" title={title}>
              {title}
            </span>
            {company && (
              <span className="text-xs text-gray-400 truncate max-w-[200px]" title={company}>
                {company}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'location',
      label: 'Location',
      icon: MapPin,
      render: (value: string, candidate: ICandidate) => {
        const location = value || candidate.location || 'Not specified';
        const formattedLocation = formatLocation(location);

        // Check if there are multiple locations
        const locationParts = location.split(',').map(part => part.trim());
        const additionalCount = locationParts.length > 1 ? locationParts.length - 1 : 0;

        return (
          <span className="text-gray-300" title={location}>
            {formattedLocation}
            {additionalCount > 0 && (
              <span className="text-xs text-gray-500 ml-1">+{additionalCount}</span>
            )}
          </span>
        );
      },
    },
    {
      key: 'createdAt',
      label: 'Created',
      icon: Calendar,
      render: (value: string) => {
        if (!value) return <span className="text-gray-400">-</span>;
        const date = new Date(value);
        const days = Math.floor((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
        if (days === 0) return <span className="text-gray-300">Today</span>;
        if (days === 1) return <span className="text-gray-300">Yesterday</span>;
        if (days < 7) return <span className="text-gray-300">{days} days ago</span>;
        if (days < 30)
          return <span className="text-gray-300">{Math.floor(days / 7)} weeks ago</span>;
        return <span className="text-gray-300">{date.toLocaleDateString()}</span>;
      },
    },
  ];

  // Handle view details - fetch detailed candidate information
  const handleViewDetails = async (candidate: ICandidate) => {
    try {
      // Use store method to fetch detailed candidate information
      // The store method will automatically update selectedCandidate state
      const detailedCandidate = await fetchCandidateDetails(candidate.id, candidate.jobId);

      if (detailedCandidate) {
        setModalOpen(true);
      } else {
        console.error('No detailed candidate data received, using fallback');
        // Fallback to basic candidate data
        setSelectedCandidate(candidate);
        setModalOpen(true);
      }
    } catch (error) {
      console.error('Error fetching detailed candidate information:', error);
      // Fallback to basic candidate data
      setSelectedCandidate(candidate);
      setModalOpen(true);
    }
  };

  // Handle modal close
  const handleCloseModal = () => {
    setModalOpen(false);
  };

  // Update onRowClick to use the modal instead of redirecting
  const handleRowClick = async (candidate: ICandidate) => {
    await handleViewDetails(candidate);
  };

  if (!isRouterReady || loading) {
    return (
      <AppLayout>
        <ColorfulSmokeyOrbLoader text="Loading your Talents Hub" />
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          variant="fullwidth"
          title="Talent Hub"
          description="Search and manage your candidate pool"
          icon={Users}
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto px-2 sm:px-6 py-4 sm:py-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {/* Table Content with integrated search and filters */}
              <div className="w-full">
                <CandidateTable
                  data={filteredCandidates}
                  columns={columns}
                  itemsPerPage={pagination.itemsPerPage}
                  onRowClick={handleRowClick}
                  paginationData={{
                    currentPage: pagination.currentPage,
                    totalPages: pagination.totalPages,
                    totalItems: pagination.totalItems,
                    itemsPerPage: pagination.itemsPerPage,
                  }}
                  onPageChange={handlePageChange}
                  hideDetailsUrl={true}
                  onRefresh={() => fetchCandidates(pagination.currentPage, pagination.itemsPerPage)}
                  // Pass search and filter props
                  searchPlaceholder="Search candidates by name, email, position, or company..."
                  onSearch={searchTerm => {
                    setStoreFilters({ searchTerm });
                    fetchCandidates(1, pagination.itemsPerPage);
                  }}
                  showAdvancedFilters={true}
                  advancedFilterPanel={
                    <AdvancedFilterPanel
                      onFilterChange={newFilters => {
                        setStoreFilters(newFilters as any);
                      }}
                      jobData={jobs}
                      loading={loading}
                    />
                  }
                  activeFilterCount={activeFilterCount}
                  onClearFilters={() => {
                    setStoreFilters({
                      status: '',
                      jobId: selectedJobId || '',
                      searchTerm: '',
                      tier: '',
                      typeOfJob: '',
                      department: '',
                      experienceLevel: '',
                      skills: '',
                      location: '',
                    });
                  }}
                  searchTerm={storeFilters.searchTerm}
                  totalItems={pagination.totalItems}
                />
              </div>

              {/* Profile view toggle and components */}
              {selectedCandidate && isModalOpen && (
                <>
                  {/* Floating controls with higher z-index */}
                  <div className="fixed top-4 right-4 flex items-center gap-3 z-[60]">
                    <div className="bg-gray-800/90 backdrop-blur-md rounded-full p-1 flex items-center border border-indigo-500/30 shadow-lg shadow-indigo-500/20">
                      <button
                        type="button"
                        onClick={() => setViewType('detailed')}
                        className={`px-4 py-1.5 text-sm font-medium rounded-full flex items-center gap-1.5 transition-all duration-300 ${
                          viewType === 'detailed'
                            ? 'bg-purple-600 text-white shadow-md'
                            : 'text-gray-300 hover:bg-gray-700/70'
                        }`}
                      >
                        <FileText size={14} />
                        Detailed
                      </button>
                      <div className="text-gray-500 mx-1">|</div>
                      <button
                        type="button"
                        onClick={() => setViewType('status')}
                        className={`px-4 py-1.5 text-sm font-medium rounded-full flex items-center gap-1.5 transition-all duration-300 ${
                          viewType === 'status'
                            ? 'bg-purple-600 text-white shadow-md'
                            : 'text-gray-300 hover:bg-gray-700/70'
                        }`}
                      >
                        <RefreshCw size={14} />
                        Status Management
                      </button>
                    </div>
                    <button
                      type="button"
                      onClick={handleCloseModal}
                      className="bg-gray-800/90 backdrop-blur-md text-gray-300 hover:text-white hover:bg-red-600/80 p-2 rounded-full border border-gray-700/50 transition-all duration-300 shadow-lg"
                      aria-label="Close"
                    >
                      <X size={18} />
                    </button>
                  </div>

                  {/* Keep the overlay background only for the full profile view */}
                  {viewType === 'detailed' && (
                    <div className="fixed inset-0 bg-black/60 backdrop-blur-lg z-50"></div>
                  )}

                  <div className="fixed inset-0 flex items-center justify-center z-50">
                    <AnimatePresence mode="wait">
                      {viewType === 'detailed' ? (
                        <motion.div
                          key="detailed-view"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{
                            duration: 0.3,
                            ease: 'easeInOut',
                          }}
                          className="w-full h-full flex items-center justify-center"
                        >
                          <CandidateFullProfileStandard
                            candidate={selectedCandidate as any}
                            onClose={handleCloseModal}
                          />
                        </motion.div>
                      ) : (
                        <motion.div
                          key="status-view"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{
                            duration: 0.3,
                            ease: 'easeInOut',
                          }}
                          className="w-full h-full flex items-center justify-center"
                        >
                          <CandidateStatusDrawer
                            isOpen={viewType === 'status'}
                            onClose={() => setViewType('detailed')}
                            candidate={{
                              id: selectedCandidate.id,
                              firstName: selectedCandidate.fullName?.split(' ')[0] || '',
                              lastName:
                                selectedCandidate.fullName?.split(' ').slice(1).join(' ') ||
                                selectedCandidate.name,
                              status: selectedCandidate.status as CandidateStatus,
                              email: selectedCandidate.email,
                              experience: selectedCandidate.currentCompany
                                ? [
                                    {
                                      position: selectedCandidate.jobTitle || '',
                                      company: selectedCandidate.currentCompany,
                                    },
                                  ]
                                : [],
                            }}
                            jobs={jobs.map(job => ({
                              id: job.id,
                              title: job.title,
                              company: job.company || 'Unknown Company',
                              location: job.location || 'Remote',
                            }))}
                            onStatusChange={handleStatusChange}
                            refreshCandidate={refreshCandidate}
                          />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default CandidateTrackingPage;
