'use client';

import { motion } from 'framer-motion';
import { HeartHandshake } from 'lucide-react';
import { GetServerSideProps } from 'next';
import Image from 'next/image';

import PageHeader from '@/components/common/PageHeader';
import AppLayout from '@/components/steps/layout/AppLayout';
import styles from '@/styles/Marketplace.module.css';
import { withPageAuthRequired } from '@auth0/nextjs-auth0';

const partners = [
  // {
  //   name: 'Go Platform',
  //   logo: '/partners/logos/go-platform-logo.svg',
  //   description: 'Leading innovation in digital transformation and enterprise solutions.',
  //   url: 'https://www.go-platform.com/en/',
  // },
  // {
  //   name: 'AI Community',
  //   logo: '/partners/logos/brilliant-seed-up.png',
  //   description: 'Building the future of AI through community collaboration and innovation.',
  //   url: 'https://ai-community.brilliantseedup.com/',
  // },
  //   {
  //     name: 'Sortitt',
  //     logo: '/partners/logos/sortitt.svg',
  //     description: 'Intelligent sorting and organization solutions for modern businesses.',
  //     url: 'https://sortitt.com/'
  //   },
  {
    name: 'EQ Brains',
    logo: '/partners/logos/Logo-EQ-Brains.png',
    description: 'Emotional intelligence and cognitive development solutions.',
    url: 'https://eqbrains.com/',
  },
  // {
  //   name: 'AIQU Search',
  //   logo: '/partners/logos/aiqu.svg',
  //   description: 'Advanced AI-powered search and discovery platform.',
  //   url: 'https://aiqusearch.com',
  // },
];

function MarketplacePage() {
  return (
    <AppLayout>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          variant="fullwidth"
          title="Partner Marketplace"
          description="Connect with our trusted partners to enhance your recruitment process and access specialized services that complement our platform. These partnerships provide additional resources to help you find and develop the best talent."
          icon={HeartHandshake}
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
            {/* Partner Grid */}
            <div className={styles.partnerGrid}>
              {partners.map((partner, index) => (
                <motion.a
                  key={partner.name}
                  href={partner.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.partnerCard}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.1,
                    ease: 'easeOut',
                  }}
                >
                  <div className={styles.cardBody}>
                    <div className={styles.logoContainer}>
                      <Image
                        src={partner.logo}
                        alt={`${partner.name} logo`}
                        fill
                        style={{ objectFit: 'contain' }}
                      />
                    </div>
                    <h3 className={styles.cardTitle}>{partner.name}</h3>
                    <p className={styles.cardDescription}>{partner.description}</p>
                  </div>
                </motion.a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}

export const getServerSideProps: GetServerSideProps = withPageAuthRequired();

export default MarketplacePage;
