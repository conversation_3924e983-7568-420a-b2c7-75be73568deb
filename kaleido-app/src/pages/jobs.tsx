'use client';

import React, { Suspense, useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import { Briefcase } from 'lucide-react';
import { useRouter } from 'next/router';

import PageHeader from '@/components/common/PageHeader';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { useJobStats } from '@/hooks/useJobStats';

import PostedJobs from '../components/PostedJobs';
import JobsDashboard from '../components/PostedJobs/JobsDashboard';

const JobDescriptionCreationPage: React.FC = () => {
  const router = useRouter();
  const [initialView, setInitialView] = useState<string | null>(null);
  const [timeFilter, setTimeFilter] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const [isClient, setIsClient] = useState(false);
  const { jobStats, isLoading, refetch } = useJobStats({ timeFrame: timeFilter });

  // Simple client-side detection
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Check if there's a view parameter in the URL (only on client)
    if (router.query.view && isClient) {
      setInitialView(router.query.view as string);
    }
  }, [router.query, isClient]);

  const handleTimeFilterChange = (value: 'daily' | 'weekly' | 'monthly' | 'yearly') => {
    setTimeFilter(value);
    // Refetch the data with the new time filter
    refetch();
  };

  return (
    <AppLayout isLoading={false}>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          variant="fullwidth"
          title="Jobs"
          description="Manage & track your job postings and view candidate matches positions."
          icon={Briefcase}
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto px-2 sm:px-6 py-4 sm:py-8">
            {!isClient || isLoading ? (
              <ColorfulSmokeyOrbLoader text="Loading job statistics..." useModalBg={false} />
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                {/* Jobs Dashboard Stats */}
                <JobsDashboard stats={jobStats} timeFilter={timeFilter} />

                {/* Posted Jobs List */}
                <div className="mt-6">
                  <Suspense fallback={<ColorfulSmokeyOrbLoader text="Loading job listings..." />}>
                    <PostedJobs initialView={initialView} />
                  </Suspense>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default JobDescriptionCreationPage;
