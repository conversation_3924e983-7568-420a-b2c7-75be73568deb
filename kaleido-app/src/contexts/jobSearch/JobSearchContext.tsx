import { Alert, AlertDescription } from '@/components/ui/alert';
import { DetailedJobInfo, Job, JobsResponse } from '@/types/job';
import {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';

import apiClient from '@/lib/apiHelper';
import { useUser } from '@auth0/nextjs-auth0/client';

const ALERT_DURATION = 5000; // 5 seconds
const PENDING_JOB_APPLICATION_KEY = 'headstart_pending_job_application';
const REDIRECT_URL_KEY = 'headstart_redirect_after_auth';

interface CompanyData {
  id: string;
  clientId: string;
  createdAt: string;
  updatedAt: string;
  companyName: string;
  companyWebsite: string;
  department: string;
  industry: string;
  size: string;
  location: string;
  contactEmail: string;
  phoneNumber: string;
  logo: string;
  description: string;
  preferences: Record<string, any>;
  userId: string | null;
  layoutPreference: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  heroImage: string;
  featuredImages: string[];
  customCss: string;
  atsConfig: {
    apiKey: string;
    baseUrl: string;
    authType: string;
    provider: string;
    companyId: string | null;
    subdomain: string | null;
    providerId: string;
    jobEndpoint: string;
    applicantEndpoint: string;
  };
  isPublished: boolean;
}

interface AlertState {
  show: boolean;
  message: string;
  variant: 'default' | 'destructive';
}

interface JobSearchFilters {
  location: string;
  salary: string;
  currency: string;
  jobType: string;
  department?: string;
  experienceLevel?: string;
  industry?: string;
  companySize?: string;
}

const API_CACHE_KEY = 'api_cache_';
const COMPANY_CACHE_KEY = (slug: string) => `${API_CACHE_KEY}company_${slug}`;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

interface JobSearchContextType {
  // State
  jobs: JobsResponse | null;
  isLoading: boolean;
  searchTerm: string;
  filters: JobSearchFilters;
  showAllJobs: boolean;
  selectedJob: DetailedJobInfo | null;
  showTldr: boolean;
  alert: AlertState;
  isPublicView: boolean;
  currentCompany: CompanyData | null;

  // Modal Events
  onCloseModal: () => void;
  onOpenTldr: () => void;
  onCloseTldr: () => void;
  onViewJobDetails: (job: DetailedJobInfo) => void;

  // Actions
  setSearchTerm: (term: string) => void;
  setFilters: (filters: JobSearchFilters) => void;
  setShowAllJobs: (show: boolean) => void;
  handleApply: (job: Job) => Promise<void>;
  refreshJobs: (forcePublic?: boolean) => Promise<JobsResponse>;
  switchToPublicView: () => Promise<void>;
  switchToPrivateView: () => Promise<void>;
  fetchCompanyProfile: (slug: string) => Promise<void>;
}

const JobSearchContext = createContext<JobSearchContextType | undefined>(undefined);

// Helper functions for localStorage cache management
const getCompanyCache = (slug: string): CompanyData | null => {
  try {
    const cacheKey = COMPANY_CACHE_KEY(slug);
    const cached = localStorage.getItem(cacheKey);
    if (!cached) return null;

    const parsedCache: CacheEntry<CompanyData> = JSON.parse(cached);

    // Check if cache is expired
    if (Date.now() - parsedCache.timestamp > CACHE_DURATION) {
      localStorage.removeItem(cacheKey);
      return null;
    }

    // Validate cache data structure
    if (!parsedCache.data || !parsedCache.data.id) {
      localStorage.removeItem(cacheKey);
      return null;
    }

    return parsedCache.data;
  } catch (error) {
    console.error('Error reading company cache:', error);
    return null;
  }
};

const setCompanyCache = (slug: string, data: CompanyData) => {
  try {
    const cacheKey = COMPANY_CACHE_KEY(slug);
    const cacheEntry: CacheEntry<CompanyData> = {
      data,
      timestamp: Date.now(),
    };
    localStorage.setItem(cacheKey, JSON.stringify(cacheEntry));
  } catch (error) {
    console.error('Error setting company cache:', error);
  }
};

const clearCompanyCache = (slug?: string) => {
  try {
    if (slug) {
      localStorage.removeItem(COMPANY_CACHE_KEY(slug));
    } else {
      // Clear all company caches if no slug provided
      Object.keys(localStorage)
        .filter(key => key.startsWith(`${API_CACHE_KEY}company_`))
        .forEach(key => localStorage.removeItem(key));
    }
  } catch (error) {
    console.error('Error clearing company cache:', error);
  }
};

// Add this helper function at the top with other helpers
const getSlugFromUrl = (): string | null => {
  if (typeof window === 'undefined') return null;

  const path = window.location.pathname;
  const matches = path.match(/\/company-profile\/([^\/]+)/);
  return matches ? matches[1] : null;
};

export function JobSearchProvider({
  children,
  onCloseOverride,
  initialPublic = false,
}: {
  children: ReactNode;
  onCloseOverride?: () => void;
  initialPublic?: boolean;
}) {
  const { user, isLoading: isUserLoading } = useUser();
  const [jobs, setJobs] = useState<JobsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentCompany, setCurrentCompany] = useState<CompanyData | null>(null);
  const [filters, setFilters] = useState<JobSearchFilters>({
    location: '',
    salary: '',
    currency: '',
    jobType: '',
    department: '',
    experienceLevel: '',
    industry: '',
    companySize: '',
  });
  const [showAllJobs, setShowAllJobs] = useState(false);
  const [selectedJob, setSelectedJob] = useState<DetailedJobInfo | null>(null);
  const [showTldr, setShowTldr] = useState(false);
  const [alert, setAlert] = useState<AlertState>({
    show: false,
    message: '',
    variant: 'default',
  });
  const [isPublicView, setIsPublicView] = useState(initialPublic || !user);
  const [lastJobsFetch, setLastJobsFetch] = useState<number | null>(null);
  const companyCache = useRef<CompanyData | null>(null);
  const lastSlug = useRef<string | null>(null);

  const fetchCompanyProfile = useCallback(async (slug: string) => {
    try {
      // Clear previous company cache if slug has changed
      if (lastSlug.current && lastSlug.current !== slug) {
        clearCompanyCache(lastSlug.current);
      }

      // Update the last slug reference
      lastSlug.current = slug;

      // Check cache first
      const cachedData = getCompanyCache(slug);
      if (cachedData) {
        setCurrentCompany(cachedData);
        return cachedData;
      }

      setIsLoading(true);
      const backendApiUrl = process.env.NEXT_PUBLIC_API_URL_BASE || 'http://localhost:8080/api';
      const response = await fetch(`${backendApiUrl}/companies/profile/${slug}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'omit',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch company profile: ${response.status}`);
      }

      const data = await response.json();

      // Cache the new data with the slug
      setCompanyCache(slug, data);
      setCurrentCompany(data);
      return data;
    } catch (error) {
      console.error('Error fetching company:', error);
      setCurrentCompany(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getCompanyFromCache = async () => {
    // Try to get company from context or local storage
    let company = currentCompany;

    // If no company in context, try these steps in order:
    if (!company?.id) {
      // 1. Try last known slug from ref
      if (lastSlug.current) {
        const cachedCompany = getCompanyCache(lastSlug.current);
        if (cachedCompany) {
          company = cachedCompany;
          setCurrentCompany(cachedCompany);
        }
      }

      // 2. If still no company, try to get slug from URL
      if (!company?.id) {
        const urlSlug = getSlugFromUrl();
        if (urlSlug) {
          // Update lastSlug ref
          lastSlug.current = urlSlug;

          // Try to get from cache first
          const cachedCompany = getCompanyCache(urlSlug);
          if (cachedCompany) {
            company = cachedCompany;
            setCurrentCompany(cachedCompany);
          } else {
            // If not in cache, fetch it
            try {
              const fetchedCompany = await fetchCompanyProfile(urlSlug);
              if (fetchedCompany) {
                company = fetchedCompany;
              }
            } catch (error) {
              console.error('Error fetching company from URL slug:', error);
            }
          }
        }
      }
    }

    return company;
  };

  const refreshJobs = useCallback(
    async (forcePublic?: boolean): Promise<JobsResponse> => {
      setIsLoading(true);
      try {
        const slug = getSlugFromUrl();
        if (!slug) {
          setJobs(null);
          return {
            data: [],
            metadata: {
              total: 0,
              filtered: 0,
              preferences: {
                jobTypes: [],
                departments: [],
                locations: [],
              },
            },
          };
        }

        const company = await fetchCompanyProfile(slug);
        if (!company) {
          setJobs(null);
          return {
            data: [],
            metadata: {
              total: 0,
              filtered: 0,
              preferences: {
                jobTypes: [],
                departments: [],
                locations: [],
              },
            },
          };
        }

        const response = await apiClient.get<JobsResponse>(
          `/jobs/company/${company.id}${forcePublic || isPublicView ? '/public' : ''}`
        );

        setJobs(response);
        setLastJobsFetch(Date.now());
        return response;
      } catch (error) {
        console.error('Error fetching jobs:', error);
        setJobs(null);
        return {
          data: [],
          metadata: {
            total: 0,
            filtered: 0,
            preferences: {
              jobTypes: [],
              departments: [],
              locations: [],
            },
          },
        };
      } finally {
        setIsLoading(false);
      }
    },
    [isPublicView]
  );

  // Update jobs when company changes
  useEffect(() => {
    if (currentCompany?.id) {
      refreshJobs();
    }
  }, [currentCompany?.id, refreshJobs]);

  // Switch view functions
  const switchToPublicView = async () => {
    setIsPublicView(true);
    setLastJobsFetch(null);
    await refreshJobs(true); // Force public endpoint
  };

  const switchToPrivateView = async () => {
    if (!user?.sub) return;
    setIsPublicView(false);
    setLastJobsFetch(null);
    await refreshJobs(false); // Force private endpoint
  };

  // Initialize view and fetch jobs
  useEffect(() => {
    const initializeView = async () => {
      if (!user?.sub) {
        await switchToPublicView();
      } else {
        await switchToPrivateView();
      }
    };

    initializeView();
  }, [user?.sub, initialPublic]);

  // Handle user login/logout changes - switch to private view when user logs in
  useEffect(() => {
    if (user?.sub && isPublicView) {
      switchToPrivateView();
    } else if (!user?.sub && !isPublicView) {
      switchToPublicView();
    }
  }, [user?.sub, isPublicView]);

  // Handle filter changes
  useEffect(() => {
    if (lastJobsFetch !== null) {
      const timer = setTimeout(() => {
        refreshJobs();
      }, 300); // Add debounce

      return () => clearTimeout(timer);
    }
  }, [searchTerm, filters, showAllJobs]);

  const handleApply = useCallback(
    async (job: Job, onClose?: () => void) => {
      if (!user?.sub || job.alreadyApplied) return;

      try {
        setIsLoading(true);

        try {
          // Try to apply directly - the backend will validate and may throw an error
          const applicationResult = await apiClient.post(`/job-seekers/apply/${job.id}`, {
            userId: user.sub,
            coverLetter: '', // Optional cover letter
          });

          // If we get here, application was successful
          setJobs(prevJobs => {
            if (!prevJobs) return null;
            return {
              ...prevJobs,
              data: prevJobs.data.map(j => (j.id === job.id ? { ...j, alreadyApplied: true } : j)),
            };
          });

          setAlert({
            show: true,
            message: 'Successfully applied to job!',
            variant: 'default',
          });

          // Close modal if provided
          onClose?.();

          // Refresh jobs to get latest data
          await refreshJobs();

          return applicationResult;
        } catch (err: any) {
          // Check if this is a validation error
          if (err.response?.status === 400 && err.response?.data?.validationResult) {
            const validateResponse = err.response.data.validationResult;

            // Store application intent and redirect to setup
            try {
              localStorage.setItem(PENDING_JOB_APPLICATION_KEY, job.id);
              localStorage.setItem(REDIRECT_URL_KEY, window.location.href);
            } catch (error) {
              console.error('Error storing application intent:', error);
            }

            setAlert({
              show: true,
              message: 'Please complete your profile before applying.',
              variant: 'destructive',
            });

            // Redirect to job seeker profile setup with the validation data
            window.location.href = `/dashboard?role=job-seeker&showSetup=true&validationData=${encodeURIComponent(JSON.stringify(validateResponse))}`;
            return undefined; // Signal that a redirect happened
          }

          // Rethrow other errors
          throw err;
        }
      } catch (error) {
        console.error('Error applying to job:', error);
        setAlert({
          show: true,
          message: 'Failed to apply to job. Please try again.',
          variant: 'destructive',
        });
        return undefined;
      } finally {
        setIsLoading(false);
      }
    },
    [user?.sub, setJobs, setAlert, setIsLoading, refreshJobs]
  );

  // Modal Events
  const onCloseModal = () => {
    if (onCloseOverride) {
      onCloseOverride();
    } else {
      setSelectedJob(null);
      setShowTldr(false);
    }
  };

  const onOpenTldr = () => {
    setShowTldr(true);
  };

  const onCloseTldr = () => {
    setShowTldr(false);
  };

  const onViewJobDetails = (job: DetailedJobInfo) => {
    setSelectedJob(job);
  };

  // Initialize view and fetch jobs based on user authentication state
  useEffect(() => {
    const initializeView = async () => {
      if (!user?.sub) {
        await switchToPublicView();
      } else {
        await switchToPrivateView();
      }
    };
    initializeView();
  }, [user?.sub, initialPublic, switchToPublicView, switchToPrivateView]);

  // Handle user login/logout transitions - switch to private view when user logs in
  useEffect(() => {
    if (user?.sub && isPublicView) {
      switchToPrivateView();
    } else if (!user?.sub && !isPublicView) {
      switchToPublicView();
    }
  }, [user?.sub, isPublicView, switchToPrivateView, switchToPublicView]);

  // Clear cache when component unmounts or when company changes
  useEffect(() => {
    return () => {
      if (lastSlug.current) {
        clearCompanyCache(lastSlug.current);
      }
    };
  }, []);

  const value = {
    // State
    jobs,
    isLoading,
    searchTerm,
    filters,
    showAllJobs,
    selectedJob,
    showTldr,
    alert,
    isPublicView,
    currentCompany,

    // Modal Events
    onCloseModal,
    onOpenTldr,
    onCloseTldr,
    onViewJobDetails,

    // Actions
    setSearchTerm,
    setFilters,
    setShowAllJobs,
    handleApply,
    refreshJobs,
    switchToPublicView,
    switchToPrivateView,
    fetchCompanyProfile,
  };

  return (
    <JobSearchContext.Provider value={value}>
      {alert.show && (
        <div className="fixed bottom-4 right-4 z-50 animate-in slide-in-from-right-5 duration-300">
          <div className="min-w-[300px] max-w-[400px]">
            <Alert variant={alert.variant}>
              <AlertDescription>{alert.message}</AlertDescription>
            </Alert>
          </div>
        </div>
      )}
      {children}
    </JobSearchContext.Provider>
  );
}

export function useJobSearch() {
  const context = useContext(JobSearchContext);
  if (context === undefined) {
    throw new Error('useJobSearch must be used within a JobSearchProvider');
  }
  return context;
}
