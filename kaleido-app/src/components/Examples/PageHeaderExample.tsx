'use client';

import React, { useState } from 'react';

import { Briefcase, FileText, Info, Settings } from 'lucide-react';

import PageHeader from '@/components/common/PageHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { RadioCardGroup } from '@/components/ui/RadioCard';

const headerVariants = [
  {
    value: 'default',
    label: 'Default Header',
    description: 'Simple text-based header',
  },
  {
    value: 'image',
    label: 'Image Header',
    description: 'Header with background image',
  },
];

const iconOptions = [
  { value: 'briefcase', label: 'Briefcase', icon: <Briefcase className="h-4 w-4" /> },
  { value: 'file', label: 'File', icon: <FileText className="h-4 w-4" /> },
  { value: 'info', label: 'Info', icon: <Info className="h-4 w-4" /> },
  { value: 'settings', label: 'Settings', icon: <Settings className="h-4 w-4" /> },
];

const imageOptions = [
  { value: '', label: 'Random Image', description: 'Use a random image' },
  {
    value: '/images/landing/open-jobs/open-jobs-1.webp',
    label: 'Image 1',
    description: 'Office workspace',
  },
  {
    value: '/images/landing/open-jobs/open-jobs-3.webp',
    label: 'Image 3',
    description: 'Team meeting',
  },
  {
    value: '/images/landing/open-jobs/open-jobs-5.webp',
    label: 'Image 5',
    description: 'Modern office',
  },
];

const heightOptions = [
  { value: 'h-32', label: 'Small', description: '8rem / 128px' },
  { value: 'h-48', label: 'Medium', description: '12rem / 192px' },
  { value: 'h-64', label: 'Large', description: '16rem / 256px' },
];

const PageHeaderExample: React.FC = () => {
  const [variant, setVariant] = useState<'default' | 'image'>('image');
  const [selectedIcon, setSelectedIcon] = useState('briefcase');
  const [selectedImage, setSelectedImage] = useState('');
  const [selectedHeight, setSelectedHeight] = useState('h-48');
  const [title, setTitle] = useState('Page Title');
  const [description, setDescription] = useState(
    'This is a description of the page content. It provides context for users.'
  );

  const getIcon = () => {
    switch (selectedIcon) {
      case 'briefcase':
        return Briefcase;
      case 'file':
        return FileText;
      case 'info':
        return Info;
      case 'settings':
        return Settings;
      default:
        return Briefcase;
    }
  };

  return (
    <div className="space-y-8 p-4">
      {/* Preview */}
      <div className="mb-8">
        <PageHeader
          variant={variant}
          title={title}
          description={description}
          icon={getIcon()}
          imageSrc={selectedImage}
          imageHeight={selectedHeight}
        />
      </div>

      {/* Controls */}
      <Card className="border border-white/10 bg-white/5">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Left Column */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-white mb-2">Header Options</h3>

              <div className="space-y-2">
                <label className="text-xs text-white/70">Variant</label>
                <RadioCardGroup
                  options={headerVariants}
                  value={variant}
                  onChange={value => setVariant(value as 'default' | 'image')}
                  columns={2}
                />
              </div>

              <div className="space-y-2">
                <label className="text-xs text-white/70">Icon</label>
                <RadioCardGroup
                  options={iconOptions}
                  value={selectedIcon}
                  onChange={setSelectedIcon}
                  columns={2}
                />
              </div>

              {variant === 'image' && (
                <>
                  <div className="space-y-2">
                    <label className="text-xs text-white/70">Image</label>
                    <RadioCardGroup
                      options={imageOptions}
                      value={selectedImage}
                      onChange={setSelectedImage}
                      columns={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-xs text-white/70">Height</label>
                    <RadioCardGroup
                      options={heightOptions}
                      value={selectedHeight}
                      onChange={setSelectedHeight}
                      columns={3}
                    />
                  </div>
                </>
              )}
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-white mb-2">Content</h3>

              <div className="space-y-2">
                <label htmlFor="header-title" className="text-xs text-white/70">
                  Title
                </label>
                <input
                  id="header-title"
                  type="text"
                  value={title}
                  onChange={e => setTitle(e.target.value)}
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white text-sm"
                  placeholder="Enter page title"
                  aria-label="Page title"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="header-description" className="text-xs text-white/70">
                  Description
                </label>
                <textarea
                  id="header-description"
                  value={description}
                  onChange={e => setDescription(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white text-sm"
                  placeholder="Enter page description"
                  aria-label="Page description"
                />
              </div>

              <div className="pt-4">
                <Button
                  onClick={() => {
                    setTitle('Page Title');
                    setDescription(
                      'This is a description of the page content. It provides context for users.'
                    );
                    setSelectedIcon('briefcase');
                    setSelectedImage('');
                    setSelectedHeight('h-48');
                  }}
                  variant="outline"
                  className="w-full"
                >
                  Reset to Defaults
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PageHeaderExample;
