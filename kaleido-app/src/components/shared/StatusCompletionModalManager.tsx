'use client';

import React, { useEffect, useState } from 'react';

import StatusCompletionModal, { StatusCompletionModalProps } from './StatusCompletionModal';

/**
 * This component listens for custom events from background services
 * and displays the StatusCompletionModal when needed.
 *
 * It should be included in the app layout to ensure it's always available.
 */
const StatusCompletionModalManager: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalProps, setModalProps] = useState<
    Omit<StatusCompletionModalProps, 'isOpen' | 'onClose'>
  >({
    title: '',
    description: '',
    status: 'success',
  });

  useEffect(() => {
    // Generic handler for all job completion events
    const handleJobCompleted = async (event: CustomEvent) => {
      // Extract modal props from the event detail
      const {
        status,
        title,
        description,
        stats,
        errorDetails,
        targetPath,
        jobId,
        // Different managers might use different property names for details
        matchedCandidateDetails,
        candidateDetails,
        profileDetails,
        fileDetails,
        // Action information
        action,
        actionType,
        // Check if results are truncated
        hasMoreCandidates,
        totalMatchedShown,
        // Include result and creditData
        result,
        creditData,
      } = event.detail;

      // Check if we're on the match/rank screen
      const currentPath = window.location.pathname;
      const isMatchRankScreen =
        currentPath.includes('/jobs/') && currentPath.includes('/candidates');

      // Special handling for match/rank screen - don't show modal, just refresh
      if (isMatchRankScreen && action === 'matchRank') {
        // Import store dynamically to avoid circular dependencies
        const { useUnifiedJobStore } = await import('@/stores/unifiedJobStore');
        const store = useUnifiedJobStore.getState();

        // Check if we're viewing the same job that just completed
        const pathJobId = currentPath.split('/jobs/')[1]?.split('/')[0];
        if (pathJobId === jobId || store.selectedJobId === jobId) {
          // Force refresh without showing modal
          try {
            await store.fetchCandidates(jobId, store.currentPage || 1, store.filters || {});
            await store.fetchJobDetails(jobId, true);

            // Show subtle notification
            const { showToast } = await import('@/components/Toaster');
            const candidateCount = matchedCandidateDetails?.length || 0;
            showToast({
              message: `${candidateCount} new candidate${candidateCount !== 1 ? 's' : ''} matched successfully`,
              type: 'success',
            });
          } catch (error) {
            console.error('Error refreshing match/rank data:', error);
          }

          // Close the status manager
          if (event.detail.closeManager) {
            event.detail.closeManager();
          }

          return; // Don't show modal
        }
      }

      // Special handling for job edit screen with uploads - refresh data without showing modal
      if (currentPath.includes('/jobs/') && currentPath.includes('/edit') && action === 'upload') {
        // Import store dynamically to avoid circular dependencies
        const { useUnifiedJobStore } = await import('@/stores/unifiedJobStore');
        const store = useUnifiedJobStore.getState();

        // Check if we're viewing the same job that just completed upload
        const pathJobId = currentPath.split('/jobs/')[1]?.split('/')[0];
        if (pathJobId === jobId || store.selectedJobId === jobId) {
          // Force refresh without showing modal
          try {
            // First, update credit data immediately if available
            // Use creditData from event detail directly, or from result as fallback
            const creditInfo = creditData || result?.creditData;
            console.log(
              '[StatusCompletionModalManager] Upload completed with creditData:',
              creditInfo
            );

            // Fetch fresh data from server first
            await store.fetchJobCriteria(jobId, true);

            // Then update with the credit data from the upload response
            // This ensures we have the latest data from both the upload and the fetch
            if (creditInfo) {
              console.log('[StatusCompletionModalManager] Updating store with credit data:', {
                totalCandidates: creditInfo.totalCandidates,
                estimatedCreditCost: creditInfo.estimatedCreditCost,
                unevaluatedCandidatesCount: creditInfo.unevaluatedCandidatesCount,
              });

              // Use setState with a function to ensure we're working with the latest state
              useUnifiedJobStore.setState(state => ({
                matchRankCost: {
                  success: true,
                  creditCost: creditInfo.estimatedCreditCost || 0,
                  unevaluatedCandidatesCount: creditInfo.unevaluatedCandidatesCount || 0,
                  isValid: true,
                  message: `${creditInfo.totalCandidates} candidates ready for ranking`,
                  availableCredits: creditInfo.availableCredits || 0,
                },
                stats: {
                  ...state.stats,
                  totalCandidates: creditInfo.totalCandidates || 0,
                },
                // Update currentJob with new candidate count
                currentJob: state.currentJob
                  ? {
                      ...state.currentJob,
                      totalCandidates: creditInfo.totalCandidates || 0,
                      recentCandidates: state.currentJob.recentCandidates || [],
                    }
                  : state.currentJob,
              }));
            }

            // Dispatch a custom event to notify components that data has been updated
            window.dispatchEvent(
              new CustomEvent('uploadDataRefreshed', {
                detail: { jobId, creditData: creditInfo, timestamp: Date.now() },
              })
            );

            // Show subtle notification
            const { showToast } = await import('@/components/Toaster');
            // Get the success count from the result directly, not from stats
            const uploadCount = result?.successCount || result?.newCandidatesCount || 0;
            showToast({
              message: `${uploadCount} candidate${uploadCount !== 1 ? 's' : ''} uploaded successfully`,
              type: 'success',
            });
          } catch (error) {
            console.error('Error refreshing upload data:', error);
          }

          // Close the status manager
          if (event.detail.closeManager) {
            event.detail.closeManager();
          }

          return; // Don't show modal
        }
      }

      // Format details for the modal based on the event type
      let details: Array<{ id: string; name?: string; value?: string }> = [];

      // Handle MatchRank details
      if (matchedCandidateDetails?.length) {
        details = matchedCandidateDetails.map((candidate: any) => ({
          id: candidate.id,
          name: candidate.name,
          value: `${Math.round(candidate.matchScore > 1 ? candidate.matchScore : candidate.matchScore * 100)}% match`,
          status: 'Matched',
          type: 'candidate',
        }));
      }
      // Handle Upload details
      else if (fileDetails?.length) {
        details = fileDetails.map((file: any) => ({
          id: file.id || `file-${Math.random().toString(36).substr(2, 9)}`,
          name: file.filename || file.name,
          value: file.status || 'Processed',
          status: file.status,
          message: file.message,
          type: file.type,
        }));
      }
      // Handle Scout details
      else if (profileDetails?.length) {
        details = profileDetails.map((profile: any) => ({
          id: profile.id || `profile-${Math.random().toString(36).substr(2, 9)}`,
          name: profile.name,
          value: profile.status || 'Processed',
        }));
      }
      // Handle generic candidate details
      else if (candidateDetails?.length) {
        details = candidateDetails.map((candidate: any) => ({
          id: candidate.id || `candidate-${Math.random().toString(36).substr(2, 9)}`,
          name: candidate.name,
          value: candidate.status || 'Processed',
        }));
      }

      // Prepare custom content for truncated results
      let customContent = undefined;
      if (hasMoreCandidates && action === 'matchRank') {
        customContent = (
          <div className="text-center p-4 bg-yellow-500/10 rounded-lg border border-yellow-500/30">
            <p className="text-yellow-300 text-sm">
              Showing top {totalMatchedShown || 100} candidates. View all results in the job details
              page for complete list.
            </p>
          </div>
        );
      }

      // Set modal props and open the modal
      setModalProps({
        title,
        description,
        status,
        stats,
        details: details.slice(0, 15), // Limit to top 15 items to show more files and duplicates
        errorDetails,
        targetPath,
        jobId,
        closeManager: event.detail.closeManager,
        action,
        actionType,
        customContent,
      });

      setIsModalOpen(true);
    };

    // Add event listeners for all job types
    if (typeof window !== 'undefined') {
      // Match Rank jobs
      window.addEventListener('matchRankJobCompleted', handleJobCompleted as EventListener);
      // Upload jobs
      window.addEventListener('uploadJobCompleted', handleJobCompleted as EventListener);
      // Scout jobs
      window.addEventListener('scoutJobCompleted', handleJobCompleted as EventListener);
      // Profile setup completion
      window.addEventListener('profileSetupCompleted', handleJobCompleted as EventListener);
      // Generic job completion (for future job types)
      window.addEventListener('jobCompleted', handleJobCompleted as EventListener);
    }

    // Clean up event listeners
    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('matchRankJobCompleted', handleJobCompleted as EventListener);
        window.removeEventListener('uploadJobCompleted', handleJobCompleted as EventListener);
        window.removeEventListener('scoutJobCompleted', handleJobCompleted as EventListener);
        window.removeEventListener('profileSetupCompleted', handleJobCompleted as EventListener);
        window.removeEventListener('jobCompleted', handleJobCompleted as EventListener);
      }
    };
  }, []);

  // Handler to close the modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return <StatusCompletionModal isOpen={isModalOpen} onClose={handleCloseModal} {...modalProps} />;
};

export default StatusCompletionModalManager;
