import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import StatusCompletionModalManager from '../StatusCompletionModalManager';

// Mock the StatusCompletionModal component
jest.mock('../StatusCompletionModal', () => {
  return jest.fn(({ isOpen, title, description }) => {
    if (!isOpen) return null;
    return (
      <div data-testid="status-completion-modal">
        <h2>{title}</h2>
        <p>{description}</p>
      </div>
    );
  });
});

// Mock the unifiedJobStore
const mockFetchCandidates = jest.fn();
const mockFetchJobDetails = jest.fn();
const mockGetState = jest.fn();

jest.mock('@/stores/unifiedJobStore', () => ({
  useUnifiedJobStore: {
    getState: () => mockGetState(),
  },
}));

// Mock the Toaster
const mockShowToast = jest.fn();
jest.mock('@/components/Toaster', () => ({
  showToast: mockShowToast,
}));

describe('StatusCompletionModalManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set default mock return values
    mockGetState.mockReturnValue({
      selectedJobId: 'job123',
      currentPage: 1,
      filters: {},
      fetchCandidates: mockFetchCandidates,
      fetchJobDetails: mockFetchJobDetails,
    });
    // Mock window.location
    delete (window as any).location;
    (window as any).location = { pathname: '/jobs/job123/candidates' };
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Match/Rank Screen Special Handling', () => {
    it('should not show modal on match/rank screen for matchRank completion', async () => {
      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('matchRankJobCompleted', {
        detail: {
          status: 'success',
          title: 'Match Complete',
          description: 'Candidates matched',
          jobId: 'job123',
          action: 'matchRank',
          matchedCandidateDetails: [
            { id: '1', name: 'John Doe', matchScore: 85 },
            { id: '2', name: 'Jane Smith', matchScore: 90 },
          ],
          closeManager: jest.fn(),
        },
      });

      window.dispatchEvent(event);

      // Wait for async operations
      await waitFor(() => {
        expect(mockFetchCandidates).toHaveBeenCalledWith('job123', 1, {});
        expect(mockFetchJobDetails).toHaveBeenCalledWith('job123', true);
      });

      // Modal should not be shown
      expect(screen.queryByTestId('status-completion-modal')).not.toBeInTheDocument();

      // Toast should be shown
      await waitFor(() => {
        expect(mockShowToast).toHaveBeenCalledWith({
          message: '2 new candidates matched successfully',
          type: 'success',
        });
      });

      // Close manager should be called
      expect(event.detail.closeManager).toHaveBeenCalled();
    });

    it('should show modal for non-matchRank completions on match/rank screen', async () => {
      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('uploadJobCompleted', {
        detail: {
          status: 'success',
          title: 'Upload Complete',
          description: 'Files uploaded',
          jobId: 'job123',
          action: 'upload',
          fileDetails: [{ id: '1', filename: 'resume.pdf', status: 'Processed' }],
        },
      });

      window.dispatchEvent(event);

      // Modal should be shown
      await waitFor(() => {
        expect(screen.getByTestId('status-completion-modal')).toBeInTheDocument();
        expect(screen.getByText('Upload Complete')).toBeInTheDocument();
      });
    });

    it('should show modal when not on match/rank screen', async () => {
      // Change location to a different page
      (window as any).location.pathname = '/jobs/job123/edit';

      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('matchRankJobCompleted', {
        detail: {
          status: 'success',
          title: 'Match Complete',
          description: 'Candidates matched',
          jobId: 'job123',
          action: 'matchRank',
          matchedCandidateDetails: [{ id: '1', name: 'John Doe', matchScore: 85 }],
        },
      });

      window.dispatchEvent(event);

      // Modal should be shown since we're not on the match/rank screen
      await waitFor(() => {
        expect(screen.getByTestId('status-completion-modal')).toBeInTheDocument();
      });
    });

    it('should only refresh if viewing the same job', async () => {
      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('matchRankJobCompleted', {
        detail: {
          status: 'success',
          title: 'Match Complete',
          description: 'Candidates matched',
          jobId: 'different-job',
          action: 'matchRank',
          matchedCandidateDetails: [],
          closeManager: jest.fn(),
        },
      });

      window.dispatchEvent(event);

      // Should show modal instead of refreshing
      await waitFor(() => {
        expect(screen.getByTestId('status-completion-modal')).toBeInTheDocument();
      });

      // Should not refresh data
      expect(mockFetchCandidates).not.toHaveBeenCalled();
      expect(mockFetchJobDetails).not.toHaveBeenCalled();
    });

    it('should handle singular candidate count in toast message', async () => {
      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('matchRankJobCompleted', {
        detail: {
          status: 'success',
          title: 'Match Complete',
          description: 'Candidates matched',
          jobId: 'job123',
          action: 'matchRank',
          matchedCandidateDetails: [{ id: '1', name: 'John Doe', matchScore: 85 }],
          closeManager: jest.fn(),
        },
      });

      window.dispatchEvent(event);

      await waitFor(() => {
        expect(mockShowToast).toHaveBeenCalledWith({
          message: '1 new candidate matched successfully',
          type: 'success',
        });
      });
    });

    it('should handle fetch errors gracefully', async () => {
      mockFetchCandidates.mockRejectedValue(new Error('Network error'));
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('matchRankJobCompleted', {
        detail: {
          status: 'success',
          title: 'Match Complete',
          description: 'Candidates matched',
          jobId: 'job123',
          action: 'matchRank',
          matchedCandidateDetails: [],
          closeManager: jest.fn(),
        },
      });

      window.dispatchEvent(event);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Error refreshing match/rank data:',
          expect.any(Error)
        );
      });

      // closeManager should still be called even on error
      expect(event.detail.closeManager).toHaveBeenCalled();

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Modal Content Formatting', () => {
    it('should format matchedCandidateDetails correctly', async () => {
      (window as any).location.pathname = '/dashboard';

      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('matchRankJobCompleted', {
        detail: {
          status: 'success',
          title: 'Match Complete',
          description: 'Candidates matched',
          jobId: 'job123',
          action: 'matchRank',
          matchedCandidateDetails: [
            { id: '1', name: 'John Doe', matchScore: 0.85 },
            { id: '2', name: 'Jane Smith', matchScore: 90 },
          ],
        },
      });

      window.dispatchEvent(event);

      await waitFor(() => {
        expect(screen.getByTestId('status-completion-modal')).toBeInTheDocument();
      });
    });

    it('should handle truncated results with custom content', async () => {
      (window as any).location.pathname = '/dashboard';

      render(<StatusCompletionModalManager />);

      const event = new CustomEvent('matchRankJobCompleted', {
        detail: {
          status: 'success',
          title: 'Match Complete',
          description: 'Candidates matched',
          jobId: 'job123',
          action: 'matchRank',
          hasMoreCandidates: true,
          totalMatchedShown: 100,
          matchedCandidateDetails: [],
        },
      });

      window.dispatchEvent(event);

      await waitFor(() => {
        expect(screen.getByTestId('status-completion-modal')).toBeInTheDocument();
      });
    });
  });

  describe('Event Listeners', () => {
    it('should listen to multiple job completion event types', async () => {
      const eventTypes = [
        'uploadJobCompleted',
        'scoutJobCompleted',
        'profileSetupCompleted',
        'jobCompleted',
      ];

      for (const eventType of eventTypes) {
        // Fresh render for each event type
        const { unmount } = render(<StatusCompletionModalManager />);

        const event = new CustomEvent(eventType, {
          detail: {
            status: 'success',
            title: `${eventType} Complete`,
            description: 'Job completed',
            jobId: 'test-job',
            action: eventType.replace('JobCompleted', ''),
          },
        });

        (window as any).location.pathname = '/other-page';
        window.dispatchEvent(event);

        await waitFor(() => {
          expect(screen.getByText(`${eventType} Complete`)).toBeInTheDocument();
        });

        // Clean up for next iteration
        unmount();
      }
    });

    it('should clean up event listeners on unmount', () => {
      const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');

      const { unmount } = render(<StatusCompletionModalManager />);

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith(
        'matchRankJobCompleted',
        expect.any(Function)
      );
      expect(removeEventListenerSpy).toHaveBeenCalledWith(
        'uploadJobCompleted',
        expect.any(Function)
      );
      expect(removeEventListenerSpy).toHaveBeenCalledWith(
        'scoutJobCompleted',
        expect.any(Function)
      );
      expect(removeEventListenerSpy).toHaveBeenCalledWith(
        'profileSetupCompleted',
        expect.any(Function)
      );
      expect(removeEventListenerSpy).toHaveBeenCalledWith('jobCompleted', expect.any(Function));
    });
  });
});
