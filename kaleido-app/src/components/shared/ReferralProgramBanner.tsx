'use client';

import { referralApi } from '@/app/referral-partner/services/referralApi';
import { Button } from '@/components/ui/button';
import { ArrowRight, DollarSign, TrendingUp, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

interface ReferralProgramBannerProps {
  userType: 'jobseeker' | 'employer';
  userName?: string;
}

export const ReferralProgramBanner: React.FC<ReferralProgramBannerProps> = ({
  userType,
  userName,
}) => {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Check dismissed status first to avoid unnecessary API calls
    const dismissed = localStorage.getItem('referralBannerDismissed');
    if (dismissed === 'true') {
      setIsChecking(false);
      return;
    }

    checkReferralStatus();
  }, []);

  const checkReferralStatus = async () => {
    // Prevent multiple simultaneous checks
    if (!isChecking) return;

    try {
      const { isActive } = await referralApi.checkReferralStatus();
      // Only show banner if NOT already a referral partner
      setIsVisible(!isActive);
    } catch (error: any) {
      console.error('Error checking referral status:', error);
      // If 404 or network error, show banner
      // If other error (like rate limiting), hide banner to prevent loops
      if (error.status === 404 || error.code === 'NETWORK_ERROR') {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    } finally {
      setIsChecking(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('referralBannerDismissed', 'true');
  };

  const handleLearnMore = () => {
    // Navigate to referral onboarding with prefilled data
    const params = new URLSearchParams({
      source: userType,
      name: userName || (userType === 'jobseeker' ? 'Kaleido Talent' : ''),
      prefilled: 'true',
    });
    router.push(`/referral-partner/onboarding?${params.toString()}`);
  };

  if (isChecking || !isVisible) {
    return null;
  }

  return (
    <div className="relative mb-4 md:mb-6 overflow-hidden rounded-xl md:rounded-2xl border border-pink-500/20 bg-gradient-to-br from-purple-900/30 via-pink-900/20 to-indigo-900/30 backdrop-blur-xl shadow-2xl shadow-purple-500/10">
      {/* Organic glassmorphic background with colorful radial transparency */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-pink-500/10" />
      <div className="absolute top-0 left-1/4 w-20 h-20 md:w-32 md:h-32 bg-gradient-radial from-purple-400/20 via-transparent to-transparent opacity-40 blur-2xl" />
      <div className="absolute bottom-0 right-1/4 w-16 h-16 md:w-24 md:h-24 bg-gradient-radial from-pink-400/20 via-transparent to-transparent opacity-30 blur-xl" />
      <div className="absolute top-1/2 left-1/2 w-12 h-12 md:w-16 md:h-16 bg-gradient-radial from-indigo-400/15 via-transparent to-transparent opacity-25 blur-lg transform -translate-x-1/2 -translate-y-1/2" />

      {/* Animated gradient overlay with vibrant colors */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/8 via-pink-500/12 to-indigo-500/8 animate-pulse" />

      <div className="relative p-4 md:p-6">
        <button
          type="button"
          onClick={handleDismiss}
          className="absolute top-3 right-3 md:top-4 md:right-4 text-pink-200/60 hover:text-pink-100 transition-all duration-300 hover:scale-110 hover:rotate-90"
          aria-label="Dismiss banner"
        >
          <X className="h-4 w-4" />
        </button>

        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center gap-3 md:gap-4">
            {/* Modern icon cluster with vibrant colors */}
            <div className="relative">
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-xl md:rounded-2xl bg-gradient-to-br from-purple-500/30 to-pink-500/30 backdrop-blur-sm flex items-center justify-center border border-pink-400/20 shadow-lg shadow-purple-500/20">
                <DollarSign className="h-5 w-5 md:h-6 md:w-6 text-pink-100" />
              </div>
              <div className="absolute -top-0.5 -right-0.5 md:-top-1 md:-right-1 h-3 w-3 md:h-4 md:w-4 rounded-full bg-gradient-to-r from-emerald-400 to-cyan-400 flex items-center justify-center shadow-lg shadow-emerald-400/30">
                <TrendingUp className="h-2 w-2 md:h-2.5 md:w-2.5 text-white" />
              </div>
            </div>

            <div className="flex-1">
              <h3 className="text-lg md:text-xl font-bold bg-gradient-to-r from-pink-100 to-purple-100 bg-clip-text text-transparent mb-1">
                Earn Referral Rewards
              </h3>
              <p className="text-xs md:text-sm text-pink-200/70">
                {userType === 'jobseeker' ? 'Up to 10% commission' : 'Commission per hire'}
              </p>
            </div>
          </div>

          {/* Modern action buttons with enhanced colors */}
          <div className="flex items-center gap-2 md:gap-3 w-full md:w-auto">
            <Button
              onClick={handleLearnMore}
              size="sm"
              className="flex-1 md:flex-none bg-gradient-to-r from-purple-600 via-pink-600 to-purple-700 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white border-0 rounded-lg md:rounded-xl px-4 md:px-6 py-2 text-sm font-medium transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/30 shadow-lg shadow-purple-500/20"
            >
              <span className="hidden sm:inline">Start Now</span>
              <span className="sm:hidden">Start</span>
              <ArrowRight className="ml-1 md:ml-2 h-3 w-3 md:h-4 md:w-4" />
            </Button>
            <Button
              onClick={handleDismiss}
              size="sm"
              variant="ghost"
              className="flex-1 md:flex-none text-pink-200/70 hover:text-pink-100 hover:bg-pink-500/10 rounded-lg md:rounded-xl transition-all duration-300 border border-pink-400/20 hover:border-pink-300/30 text-sm"
            >
              Later
            </Button>
          </div>
        </div>

        {/* Minimal feature indicators with colorful accents */}
        <div className="flex flex-wrap items-center gap-3 md:gap-6 mt-3 md:mt-4 pt-3 md:pt-4 border-t border-pink-400/10">
          <div className="flex items-center gap-2 text-xs text-pink-200/60">
            <div className="h-1.5 w-1.5 rounded-full bg-gradient-to-r from-emerald-400 to-cyan-400 shadow-sm shadow-emerald-400/50" />
            <span>Real-time tracking</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-pink-200/60">
            <div className="h-1.5 w-1.5 rounded-full bg-gradient-to-r from-emerald-400 to-cyan-400 shadow-sm shadow-emerald-400/50" />
            <span>Monthly payouts</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-pink-200/60">
            <div className="h-1.5 w-1.5 rounded-full bg-gradient-to-r from-emerald-400 to-cyan-400 shadow-sm shadow-emerald-400/50" />
            <span>Easy setup</span>
          </div>
        </div>
      </div>
    </div>
  );
};
