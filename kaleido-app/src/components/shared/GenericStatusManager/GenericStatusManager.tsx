import React, { useCallback, useEffect, useRef, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { CheckCircle, ChevronDown, ChevronUp, Trash2, X } from 'lucide-react';
import { usePathname } from 'next/navigation';

import StatusCompletionModal from '@/components/shared/StatusCompletionModal';
import apiHelper from '@/lib/apiHelper';
import { useStatusManagerStore } from '@/stores/statusManagerStore';

import { CollapsedView } from './components/CollapsedView';
import { JobItem } from './components/JobItem';
import { useCompletionDetection } from './hooks/useCompletionDetection';
import { useJobPolling } from './hooks/useJobPolling';
import { useProgressAnimation } from './hooks/useProgressAnimation';
import { GenericStatusManagerProps, StatusJob, StatusManagerConfig } from './types';
import { filterJobsToDisplay, hasActiveProcessingJobs } from './utils/jobUtils';
import { createGenericStatusManager } from './utils/managerSingleton';
import {
  buildCompletionStats,
  extractDetailsFromResult,
  generateCompletionDescription,
} from './utils/workerTransformations';

// Re-export types for backward compatibility
export type { GenericStatusManagerProps, StatusJob, StatusManagerConfig };

export const GenericStatusManager: React.FC<GenericStatusManagerProps> = ({
  jobs,
  activeJobs,
  config,
  onUpdateJob,
  onRemoveJob,
  onClearCompleted,
  onComplete,
}) => {
  // Get store instance and actions
  const {
    instances,
    modalState,
    createInstance,
    removeInstance,
    ensureSingleInstance,
    updateJob: updateStoreJob,
    removeJob: removeStoreJob,
    showCompletionModal: showStoreCompletionModal,
    hideCompletionModal,
    setInstanceMinimized: setStoreMinimized,
    setInstanceCollapsed: setStoreCollapsed,
  } = useStatusManagerStore();
  const {
    title,
    icon,
    statusEndpoint,
    cancelEndpoint,
    pollInterval = 5000,
    maxRetries = 3,
    onJobComplete,
    onJobFailed,
    renderJobDetails,
    targetPath,
    getActualJobId,
    managerId = 'default',
    enableManagerCoordination = false,
    autoCloseOnCompletion = false,
    showCloseButton = true,
    completionDetailsConfig,
    onViewResults,
    viewResultsButtonText = 'View Results',
    showViewResultsButton = false,
    action,
    actionDisplay,
  } = config;

  // Get or create instance
  const instance = instances[managerId] || null;
  const isCollapsed = instance?.isCollapsed ?? false;
  const isMinimized = instance?.isMinimized ?? false;

  const [expandedJobId, setExpandedJobId] = useState<string | null>(null);
  const [completedJobStats, setCompletedJobStats] = useState<{
    result: any;
    hasErrors: boolean;
    stats: any;
    customContent?: any;
    errorDetails: any[];
    data: any;
    jobId: string;
    details?: any[];
  } | null>(null);
  const managerRef = useRef<ReturnType<typeof createGenericStatusManager> | null>(null);
  const [isManagerClosed, setIsManagerClosed] = useState(false);

  // Use ref to store updateStoreJob to prevent infinite loops
  const updateStoreJobRef = useRef(updateStoreJob);
  updateStoreJobRef.current = updateStoreJob;

  // Use modal state from store
  const showStatusCompletionModal = modalState.isOpen && modalState.jobType === managerId;
  const showCompletionModal = false; // This will be managed by the modal logic

  const pathname = usePathname();

  // Get jobs to display
  const jobsToDisplay = filterJobsToDisplay(jobs, activeJobs);

  // Initialize instance on mount
  useEffect(() => {
    // Reopen manager when new jobs are added
    if (activeJobs.length > 0 && isManagerClosed) {
      setIsManagerClosed(false);
    }

    if (activeJobs.length > 0 && !instance) {
      const jobsArray = activeJobs.map(jobId => ({
        id: jobId,
        type: managerId,
        status: jobs[jobId]?.status || 'active',
        progress: jobs[jobId]?.progress,
        message: jobs[jobId]?.message,
        error: jobs[jobId]?.error,
        result: jobs[jobId]?.result,
        metadata: jobs[jobId]?.metadata,
        createdAt: jobs[jobId]?.createdAt || new Date().toISOString(),
        updatedAt: jobs[jobId]?.updatedAt || new Date().toISOString(),
      }));

      createInstance(managerId, managerId, jobsArray);

      if (enableManagerCoordination) {
        ensureSingleInstance(managerId);
      }
    }
  }, [activeJobs.length, instance, managerId, enableManagerCoordination, jobs]);

  // Update store when jobs change
  useEffect(() => {
    if (instance) {
      Object.entries(jobs).forEach(([jobId, job]) => {
        if (activeJobs.includes(jobId)) {
          // Find the existing job in the store to compare
          const existingJob = instance.jobs.find(j => j.id === jobId);

          // Only update if there are actual changes to prevent infinite loops
          const hasChanges =
            !existingJob ||
            existingJob.status !== job.status ||
            existingJob.progress !== job.progress ||
            existingJob.message !== job.message ||
            existingJob.error !== job.error;

          if (hasChanges) {
            updateStoreJobRef.current(managerId, jobId, {
              status: job.status,
              progress: job.progress,
              message: job.message,
              error: job.error,
              result: job.result,
              metadata: job.metadata,
            });
          }
        }
      });
    }
  }, [jobs, activeJobs, instance, managerId]);

  // Helper functions for UI state
  const setIsCollapsed = useCallback(
    (collapsed: boolean) => {
      setStoreCollapsed(managerId, collapsed);
    },
    [managerId]
  );

  const setIsMinimized = useCallback(
    (minimized: boolean) => {
      setStoreMinimized(managerId, minimized);
    },
    [managerId]
  );

  const clearOtherManagers = useCallback(() => {
    if (enableManagerCoordination) {
      ensureSingleInstance(managerId);
    }
  }, [enableManagerCoordination, managerId]);

  // Handle completion modal
  const showCompletionModalImmediately = useCallback(
    (job: StatusJob) => {
      // Get the actual job ID using the config's getActualJobId function if available
      const actualJobId = getActualJobId
        ? getActualJobId(job)
        : job.result?.jobId || job.jobId || job.id;
      const targetUrl = targetPath ? `${targetPath}/${actualJobId}` : undefined;
      const currentUrl = pathname;

      // Check if we should dispatch custom event instead of showing internal modal
      if (config.useStatusCompletionModal) {
        // Extract stats and details
        const result = job.result || {};
        const details = extractDetailsFromResult(result, managerId);
        const stats = buildCompletionStats(managerId, result, details);

        // Prepare details object with correct property name based on action type
        const detailsProps: any = {};
        if (managerId === 'matchRank') {
          detailsProps.matchedCandidateDetails = details;
        } else if (managerId === 'scout') {
          detailsProps.profileDetails = details;
        } else {
          detailsProps.fileDetails = details; // Default for upload and other actions
        }

        // For upload events, immediately update the store with new candidate data
        if (managerId === 'upload' && result.creditData) {
          console.log(
            '[GenericStatusManager] Upload completed, updating store immediately with:',
            result.creditData
          );

          // Dynamically import store to avoid circular dependencies
          import('@/stores/unifiedJobStore').then(({ useUnifiedJobStore }) => {
            // Update store immediately with credit data using setState
            useUnifiedJobStore.setState((state: any) => ({
              matchRankCost: {
                success: true,
                creditCost: result.creditData.estimatedCreditCost || 0,
                unevaluatedCandidatesCount: result.creditData.unevaluatedCandidatesCount || 0,
                isValid: true,
                message: `${result.creditData.totalCandidates} candidates ready for ranking`,
                availableCredits: result.creditData.availableCredits || 0,
              },
              stats: {
                ...state.stats,
                totalCandidates: result.creditData.totalCandidates || 0,
              },
              // Force update currentJob if it matches
              currentJob:
                state.currentJob?.id === actualJobId
                  ? {
                      ...state.currentJob,
                      totalCandidates: result.creditData.totalCandidates || 0,
                    }
                  : state.currentJob,
            }));

            // Then fetch fresh data
            if (actualJobId) {
              const store = useUnifiedJobStore.getState();
              store.fetchJobCriteria(actualJobId, true);
            }
          });
        }

        // Dispatch event for StatusCompletionModalManager
        const eventName = `${managerId}JobCompleted`;
        const event = new CustomEvent(eventName, {
          detail: {
            jobId: actualJobId,
            action: managerId,
            actionType: 'completed',
            status: job.status === 'completed_with_errors' ? 'warning' : 'success',
            title: `${title} Complete!`,
            description:
              generateCompletionDescription(managerId, result, details) ||
              (job.status === 'completed_with_errors'
                ? 'Some items had issues during processing.'
                : 'All items processed successfully.'),
            stats,
            errorDetails: result.errorDetails || [],
            targetPath,
            ...detailsProps, // Spread the correct details property
            // Include truncation info if available
            hasMoreCandidates: result.hasMoreCandidates,
            totalMatchedShown: result.totalMatchedShown,
            // Include credit data if available (for upload events)
            creditData: result.creditData || null,
            result: result, // Pass the full result for access to creditData
            closeManager: () => {
              hideCompletionModal();
              setCompletedJobStats(null);
              setIsManagerClosed(true);
              Object.keys(jobs).forEach(jobId => {
                onRemoveJob(jobId);
                removeStoreJob(managerId, jobId);
              });
              removeInstance(managerId);
              if (onClearCompleted) {
                onClearCompleted();
              }
            },
          },
        });

        window.dispatchEvent(event);

        // Also update store for modal state tracking
        showStoreCompletionModal(actualJobId, managerId, job.result, targetUrl);
      } else {
        // Original logic for internal modal
        showStoreCompletionModal(actualJobId, managerId, job.result, targetUrl);

        // Set completed job stats for the modal
        setCompletedJobStats({
          hasErrors: job.status === 'completed_with_errors',
          stats: job.result?.stats || {},
          customContent: job.result?.customContent,
          errorDetails: job.result?.errorDetails || [],
          data: job.result,
          jobId: actualJobId,
          // Extract details based on the action type
          details: extractDetailsFromResult(job.result, managerId),
          result: job.result,
        });
      }

      // If we're on the same URL, no need to navigate
      if (!targetUrl || targetUrl === currentUrl) {
        // The store will handle refreshing the jobStore
      }
    },
    [
      targetPath,
      pathname,
      managerId,
      getActualJobId,
      config.useStatusCompletionModal,
      title,
      jobs,
      onRemoveJob,
      removeStoreJob,
      removeInstance,
      hideCompletionModal,
      onClearCompleted,
      showStoreCompletionModal,
    ]
  );

  // Use completion detection hook
  useCompletionDetection({
    jobs,
    onJobComplete,
    onComplete,
    showCompletionModal: showCompletionModalImmediately,
    onUpdateJob,
    enableManagerCoordination,
    managerId,
    action,
    clearOtherManagers,
  });

  // Use job polling hook
  useJobPolling({
    jobs,
    activeJobs,
    statusEndpoint,
    pollInterval,
    maxRetries,
    onUpdateJob,
    onJobComplete,
    onJobFailed,
    enableManagerCoordination,
    managerId,
    clearOtherManagers,
  });

  // Use progress animation hook for visual feedback
  useProgressAnimation({
    jobs,
    activeJobs,
    onUpdateJob,
    animationInterval: 3000, // 3 seconds
    incrementAmount: 5, // 5% each time
  });

  // Initialize manager coordination
  useEffect(() => {
    if (enableManagerCoordination) {
      managerRef.current = createGenericStatusManager(managerId);
      managerRef.current.registerManager(() => setIsManagerClosed(true));
      clearOtherManagers();

      return () => {
        if (managerRef.current) {
          managerRef.current.unregisterManager();
        }
      };
    }
  }, [enableManagerCoordination, managerId]);

  // Handle manager close functionality
  const handleCloseManager = useCallback(() => {
    Object.keys(jobs).forEach(jobId => {
      onRemoveJob(jobId);
      removeStoreJob(managerId, jobId);
    });
    setIsManagerClosed(true);
    removeInstance(managerId);
    if (onClearCompleted) {
      onClearCompleted();
    }
  }, [jobs, onRemoveJob, managerId, onClearCompleted]);

  // Handle delete all functionality
  const handleDeleteAll = useCallback(async () => {
    const activeJobsToCancel = jobsToDisplay.filter(job =>
      ['active', 'processing', 'waiting'].includes(job.status)
    );

    // Cancel all active jobs if there's a cancel endpoint
    if (cancelEndpoint) {
      for (const job of activeJobsToCancel) {
        try {
          await apiHelper.post(cancelEndpoint(job.id), {});
        } catch (error) {
          console.error(`Failed to cancel job ${job.id}:`, error);
        }
      }
    }

    // Clear all jobs from both local state and store
    Object.keys(jobs).forEach(jobId => {
      onRemoveJob(jobId);
      removeStoreJob(managerId, jobId);
    });

    // Clear the manager instance
    removeInstance(managerId);
    setIsManagerClosed(true);

    // Clear completed jobs if callback exists
    if (onClearCompleted) {
      onClearCompleted();
    }

    // Clear the store's completion cache
    const store = useStatusManagerStore.getState();
    store.clearCompletionCache();
  }, [
    jobsToDisplay,
    jobs,
    cancelEndpoint,
    onRemoveJob,
    managerId,
    removeInstance,
    onClearCompleted,
    removeStoreJob,
  ]);

  const cancelJob = async (jobId: string) => {
    if (!cancelEndpoint) return;

    try {
      await apiHelper.post(cancelEndpoint(jobId), {});
      onUpdateJob(jobId, { status: 'cancelled', message: 'Cancelled by user' });
    } catch (error) {
      console.error('Error cancelling job:', error);
      onUpdateJob(jobId, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Failed to cancel job',
        message: 'Failed to cancel job. Please try again.',
      });
    }
  };

  const isProcessing = hasActiveProcessingJobs(jobsToDisplay);

  // Auto-close functionality
  useEffect(() => {
    if (autoCloseOnCompletion && !isProcessing && jobsToDisplay.length > 0) {
      const timer = setTimeout(() => {
        setIsManagerClosed(true);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [autoCloseOnCompletion, isProcessing, jobsToDisplay.length]);

  const shouldRender =
    !isManagerClosed &&
    (jobsToDisplay.length > 0 ||
      activeJobs.length > 0 ||
      showCompletionModal ||
      showStatusCompletionModal);

  // Debug render decision

  // Only short-circuit render when there is absolutely nothing to show (no manager UI **and** no modal)
  if (!shouldRender && !showStatusCompletionModal) {
    return null;
  }

  return (
    <AnimatePresence>
      {/* StatusCompletionModal - only show if NOT using external modal */}
      {showStatusCompletionModal && completedJobStats && !config.useStatusCompletionModal && (
        <StatusCompletionModal
          key="status-completion-modal"
          isOpen={showStatusCompletionModal}
          onClose={() => {
            hideCompletionModal();
            setCompletedJobStats(null);
            // Don't close the manager here - let it reappear briefly before auto-closing
          }}
          title={
            completedJobStats.hasErrors ? `${title} Completed with Issues` : `${title} Complete!`
          }
          description={
            completedJobStats.hasErrors
              ? 'Some items had issues during processing.'
              : 'All items processed successfully.'
          }
          status={completedJobStats.hasErrors ? 'warning' : 'success'}
          stats={completedJobStats.stats}
          customContent={completedJobStats.customContent}
          details={completedJobStats.details || []}
          errorDetails={completedJobStats.errorDetails}
          targetPath={targetPath}
          jobId={
            completedJobStats?.data?.jobId ||
            completedJobStats?.result?.jobId ||
            completedJobStats.jobId
          }
          action={action}
          action_display={actionDisplay}
          actionType="completed"
          closeManager={() => {
            hideCompletionModal();
            setCompletedJobStats(null);
            // Close and clear the manager completely
            setIsManagerClosed(true);
            Object.keys(jobs).forEach(jobId => {
              onRemoveJob(jobId);
              removeStoreJob(managerId, jobId);
            });
            removeInstance(managerId);
            if (onClearCompleted) {
              onClearCompleted();
            }
          }}
        />
      )}

      {/* Completion Modal */}
      {showCompletionModal && completedJobStats && config.renderCompletionModal && (
        <div key="completion-modal">
          {config.renderCompletionModal(
            {
              id: completedJobStats.jobId,
              status: completedJobStats.hasErrors ? 'completed_with_errors' : 'completed',
              result: completedJobStats.data,
            } as StatusJob,
            () => {
              hideCompletionModal();
              setCompletedJobStats(null);
            }
          )}
        </div>
      )}

      {/* Collapsed State */}
      {isCollapsed && isProcessing && (
        <CollapsedView
          key="collapsed-state"
          isProcessing={isProcessing}
          title={title}
          onClick={() => setIsCollapsed(false)}
        />
      )}

      {/* Expanded State */}
      {!isCollapsed && (
        <motion.div
          key="expanded-state"
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: -100, opacity: 0 }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          className="fixed bottom-4 left-4 z-[100000]"
        >
          <div className="bg-white/10 backdrop-blur-xl rounded-lg shadow-xl border border-white/20 w-96">
            <div className="p-4 border-b border-white/20 flex items-center justify-between">
              <div className="flex items-center gap-2">
                {jobsToDisplay.some(
                  job => job.status === 'completed' || job.status === 'completed_with_errors'
                ) ? (
                  <CheckCircle className="text-green-500 h-6 w-6" />
                ) : (
                  icon
                )}
                <p className="text-lg font-semibold text-white text-md">{title}</p>
              </div>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  {isMinimized ? <ChevronDown size={18} /> : <ChevronUp size={18} />}
                </button>
                <button
                  type="button"
                  onClick={() => setIsCollapsed(true)}
                  className="text-gray-400 hover:text-white transition-colors"
                  title={`Collapse ${title.toLowerCase()}`}
                >
                  <ChevronDown size={18} />
                </button>
                <button
                  type="button"
                  onClick={handleDeleteAll}
                  className="text-gray-400 hover:text-red-400 transition-colors"
                  title={`Delete all ${title.toLowerCase()} jobs`}
                >
                  <Trash2 size={18} />
                </button>
                {showCloseButton &&
                  jobsToDisplay.every(job =>
                    ['completed', 'failed', 'cancelled', 'completed_with_errors'].includes(
                      job.status
                    )
                  ) && (
                    <button
                      type="button"
                      onClick={handleCloseManager}
                      className="text-gray-400 hover:text-white transition-colors"
                      title={`Close ${title.toLowerCase()}`}
                    >
                      <X size={18} />
                    </button>
                  )}
              </div>
            </div>

            <AnimatePresence>
              {!isMinimized && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
                    {jobsToDisplay.map(job => (
                      <JobItem
                        key={job.id}
                        job={job}
                        isExpanded={expandedJobId === job.id}
                        onToggleExpanded={() =>
                          setExpandedJobId(expandedJobId === job.id ? null : job.id)
                        }
                        onCancel={cancelJob}
                        onRemove={jobId => {
                          onRemoveJob(jobId);
                          removeStoreJob(managerId, jobId);
                        }}
                        cancelEndpoint={cancelEndpoint}
                        completionDetailsConfig={completionDetailsConfig}
                        renderJobDetails={renderJobDetails}
                        showViewResultsButton={showViewResultsButton}
                        onViewResults={onViewResults}
                        viewResultsButtonText={viewResultsButtonText}
                      />
                    ))}

                    {jobsToDisplay.length === 0 && (
                      <div className="text-center py-4 text-gray-400">
                        {activeJobs.length > 0 ? (
                          <div className="flex items-center justify-center gap-2">
                            <div className="relative w-4 h-4">
                              <div className="absolute inset-0 rounded-full border-2 border-pink-700 border-t-transparent animate-spin"></div>
                            </div>
                            <p>Initializing {title.toLowerCase()}...</p>
                          </div>
                        ) : (
                          <p>No {title.toLowerCase()} jobs</p>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Re-export createGenericStatusManager for backward compatibility
export { createGenericStatusManager };
