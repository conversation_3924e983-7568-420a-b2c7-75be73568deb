import React, { useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown, Eye, Filter, Trash2, X } from 'lucide-react';

import { showToast } from '@/components/Toaster';
import { Button } from '@/components/ui/button';
import { useCandidatesStore } from '@/stores/candidatesStore';

import GenericTable, { GenericTableProps } from './GenericTable';

// Extend the GenericTableProps to include additional props for search and filters
interface CandidateTableProps<T> extends Omit<GenericTableProps<T>, 'customActions'> {
  onRefresh?: () => void;
  searchPlaceholder?: string;
  onSearch?: (searchTerm: string) => void;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  showAdvancedFilters?: boolean;
  advancedFilterPanel?: React.ReactNode;
  activeFilterCount?: number;
  onClearFilters?: () => void;
  searchTerm?: string;
  totalItems?: number;
}

function CandidateTable<T extends { id: string }>(props: CandidateTableProps<T>) {
  const {
    onRefresh,
    searchPlaceholder = 'Search candidates...',
    onSearch,
    searchValue = '',
    onSearchChange,
    showAdvancedFilters = false,
    advancedFilterPanel,
    activeFilterCount = 0,
    onClearFilters,
    searchTerm,
    totalItems,
    ...restProps
  } = props;
  const deleteCandidate = useCandidatesStore(state => state.deleteCandidate);
  const [showFilterMenu, setShowFilterMenu] = useState(false);

  const handleDelete = async (e: React.MouseEvent, item: T) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this candidate?')) {
      try {
        const success = await deleteCandidate(item.id);

        if (success) {
          showToast({
            message: 'Candidate deleted successfully',
            isSuccess: true,
          });

          // Refresh the data if a refresh function is provided
          if (onRefresh) {
            onRefresh();
          }
        } else {
          throw new Error('Failed to delete candidate');
        }
      } catch (error) {
        console.error('Error deleting candidate:', error);
        showToast({
          message: 'Failed to delete candidate',
          isSuccess: false,
        });
      }
    }
  };

  const renderCustomActions = (item: T) => (
    <div className="flex items-center gap-2 justify-end text-[var(--foreground-rgb)] opacity-60">
      {props.onRowClick && (
        <Button
          variant="outline"
          size="sm"
          onClick={e => {
            if (props.hideDetailsUrl) {
              e.stopPropagation();
              props.onRowClick?.(item);
            } else {
              // Handle details click
              e.stopPropagation();
              const currentParams = new URLSearchParams(window.location.search);
              currentParams.set('candidateId', item.id);
              props.onRowClick?.(item);
            }
          }}
          className="hover:bg-[var(--button-secondary-bg)] border-[var(--card-border)] border hover:text-[var(--foreground-rgb)]"
        >
          <Eye className="w-4 h-4 mr-2" />
          Details
        </Button>
      )}
      <Button
        variant="outline"
        size="sm"
        onClick={e => handleDelete(e, item)}
        className="border-[var(--card-border)] border hover:bg-[var(--error-bg, rgba(239,68,68,0.2))] hover:text-[var(--error-color, #ef4444)]"
      >
        <Trash2 className="w-4 h-4" />
      </Button>
    </div>
  );

  const renderAdvancedFilterButton = () => {
    if (!showAdvancedFilters) return null;

    return (
      <>
        <Button
          variant="outline"
          className="flex items-center gap-2 border-gray-700 text-gray-300 rounded-lg whitespace-nowrap"
          onClick={() => setShowFilterMenu(!showFilterMenu)}
        >
          <Filter className="h-4 w-4" />
          <span className="hidden sm:inline">Filters</span>
          {activeFilterCount > 0 && (
            <span className="px-1.5 py-0.5 bg-purple-500/20 text-indigo-300 text-xs rounded-full">
              {activeFilterCount}
            </span>
          )}
          <ChevronDown className="h-4 w-4" />
        </Button>
      </>
    );
  };

  return (
    <div className="w-full">
      <div className="bg-gray-900/30 backdrop-blur-sm rounded-lg border border-gray-800 shadow-xl overflow-hidden">
        <GenericTable
          {...restProps}
          customActions={renderCustomActions}
          hideDelete={true}
          searchPlaceholder={searchPlaceholder}
          onSearch={onSearch}
          headerActions={renderAdvancedFilterButton()}
        />
      </div>

      {/* Advanced Filters Panel Modal */}
      {showAdvancedFilters && (
        <AnimatePresence>
          {showFilterMenu && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="mt-4"
            >
              <div className="bg-black/10 backdrop-blur-sm rounded-lg border border-white/10 shadow-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Filter className="h-5 w-5 text-indigo-400" />
                    <h3 className="text-white text-base font-medium">Advanced Filters</h3>
                  </div>
                  {activeFilterCount > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs border-gray-700"
                      onClick={onClearFilters}
                    >
                      <X className="mr-1 h-3.5 w-3.5" /> Clear Filters
                    </Button>
                  )}
                </div>
                {advancedFilterPanel}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  );
}

export default CandidateTable;
