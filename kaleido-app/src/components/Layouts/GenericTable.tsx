import '../../styles/theming.css';

import React, { useEffect, useRef, useState } from 'react';

import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Search, X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import TableDashboard, { ChartData, DashboardStat } from '@/components/Layouts/TableDashboard';
import { showToast } from '@/components/Toaster';
import { TableDropdownMenu } from '@/components/ui/DropdownMenu';
import { EmptyState } from '@/components/ui/EmptyState';
import { Skeleton } from '@/components/ui/skeleton';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { formatNumberWithK } from '@/utils/formatters';

const formatSalaryRange = (salaryRange: string | null | undefined) => {
  if (!salaryRange || typeof salaryRange !== 'string') return 'Not specified';

  // Extract currency code (3 uppercase letters)
  const currencyMatch = salaryRange.match(/^([A-Z]{3})/);
  const currency = currencyMatch ? currencyMatch[0] : '';

  // Extract numbers from the string, handling commas
  const numbers = salaryRange.match(/[\d,]+/g);
  if (!numbers || numbers.length === 0) return salaryRange;

  // Convert numbers, removing commas and format with k notation
  const formattedNumbers = numbers.map(num => formatNumberWithK(parseInt(num.replace(/,/g, ''))));

  // If we have a range (two numbers)
  if (formattedNumbers.length >= 2) {
    return `${currency} ${formattedNumbers[0]} - ${formattedNumbers[1]}`;
  }

  // If we only have one number
  return `${currency} ${formattedNumbers[0]}`;
};

const formatTableValue = (value: any) => {
  if (value === null || value === undefined) return '-';

  // Handle salary range formatting
  if (typeof value === 'string' && value.includes('USD')) {
    return formatSalaryRange(value);
  }

  // Handle arrays
  if (Array.isArray(value)) {
    return value.join(', ');
  }

  // Handle dates - both Date objects and date strings
  if (value instanceof Date) {
    // Check if it's a valid date
    if (isNaN(value.getTime())) {
      return '-';
    }
    return value.toLocaleDateString();
  }

  // Handle date strings
  if (typeof value === 'string' && (value.includes('-') || value.includes('/'))) {
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      return date.toLocaleDateString();
    }
  }

  return value.toString();
};

interface Column {
  key: string;
  label: string;
  icon?: React.ElementType;
  render?: (value: any, row: any) => React.ReactNode;
  clickable?: boolean;
  onClick?: (row: any) => void;
}

interface HasId {
  id: string;
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

// Additional props for EmptyState
interface EmptyStateConfig {
  type?: 'job' | 'candidate' | 'generic' | 'table';
  title?: string;
  description?: string;
  actionLabel?: string;
  actionRoute?: string;
  showButton?: boolean;
  onAction?: () => void;
}

export interface GenericTableProps<T> {
  data: T[];
  columns: Column[];
  itemsPerPage?: number;
  onRowClick?: (row: T) => void;
  expandableContent?: (item: T) => React.ReactNode;
  disablePagination?: boolean;
  customActions?: (item: T) => React.ReactNode;
  hideDelete?: boolean;
  hideDetailsUrl?: boolean;
  paginationData?: PaginationData;
  onPageChange?: (page: number) => void;
  currentPage?: number;
  totalItems?: number;
  showPagination?: boolean;
  // New dashboard props
  dashboardStats?: DashboardStat[];
  chartData?: ChartData[];
  distributionData?: ChartData[];
  showDashboard?: boolean;
  onTimeFilterChange?: (filter: 'daily' | 'weekly' | 'monthly' | 'yearly') => void;
  searchPlaceholder?: string;
  onSearch?: (searchTerm: string) => void;
  // Empty state configuration
  emptyStateConfig?: EmptyStateConfig;
  // Loading state
  isLoading?: boolean;
  // Header actions
  headerActions?: React.ReactNode;
}

function GenericTable<T>({
  data,
  columns,
  itemsPerPage = 10,
  onRowClick,
  expandableContent,
  disablePagination = false,
  customActions,
  hideDelete = false,
  hideDetailsUrl = false,
  paginationData,
  onPageChange,
  currentPage: serverCurrentPage,
  totalItems,
  showPagination = false,
  // New dashboard props
  dashboardStats = [],
  chartData = [],
  distributionData = [],
  showDashboard = false,
  onTimeFilterChange,
  searchPlaceholder = 'Search...',
  onSearch,
  // Empty state configuration
  emptyStateConfig,
  // Loading state
  isLoading = false,
  // Header actions
  headerActions,
}: GenericTableProps<T>) {
  // Use itemsPerPage from paginationData if available, otherwise use prop default
  const effectiveItemsPerPage = paginationData?.itemsPerPage || itemsPerPage;
  const [tableData, setTableData] = useState<T[]>(data);
  const [searchTerm, setSearchTerm] = useState('');
  const [timeFilter, setTimeFilter] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [isDarkTheme, setIsDarkTheme] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { deleteJob } = useJobsStore();
  const tableDataRef = useRef<T[]>(data);
  const actionButtonRefs = useRef<Record<string, HTMLButtonElement | null>>({});

  // Check if we're on mobile
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Use server-side pagination if provided, otherwise fallback to client-side
  const usingServerPagination = showPagination && serverCurrentPage !== undefined;

  // Only use client-side pagination if explicitly requested and no server pagination is provided
  const shouldUseClientPagination = !usingServerPagination && !paginationData && !disablePagination;

  const calculatedCurrentPage = usingServerPagination
    ? serverCurrentPage
    : paginationData?.currentPage || 1;

  const calculatedTotalPages = usingServerPagination
    ? totalItems
      ? Math.ceil(totalItems / effectiveItemsPerPage)
      : 1
    : paginationData?.totalPages ||
      (shouldUseClientPagination ? Math.ceil(tableData.length / effectiveItemsPerPage) : 1);

  // Only slice the data if using client-side pagination
  const currentItems = shouldUseClientPagination
    ? tableData.slice(
        (calculatedCurrentPage - 1) * effectiveItemsPerPage,
        calculatedCurrentPage * effectiveItemsPerPage
      )
    : tableData;

  useEffect(() => {
    setTableData(data);
    tableDataRef.current = data;
  }, [data]);

  // Update theme state on client side
  useEffect(() => {
    // Function to update the theme state
    const updateThemeState = () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      setIsDarkTheme(currentTheme !== 'light');
    };

    // Set initial theme state
    updateThemeState();

    // Listen for theme changes using MutationObserver
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.attributeName === 'data-theme') {
          updateThemeState();
        }
      });
    });

    // Observe theme changes
    observer.observe(document.documentElement, { attributes: true });

    // Clean up
    return () => {
      observer.disconnect();
    };
  }, []);

  const handleDetailsClick = (e: React.MouseEvent, item: T) => {
    e.stopPropagation();

    if (!hideDetailsUrl) {
      const params = new URLSearchParams(searchParams.toString());
      params.set('jobId', (item as any).id);

      if (window.location.pathname.includes('video-jd')) {
        params.set('showVideoJd', 'true');
      }

      router.push(`?${params.toString()}`);
    }

    onRowClick?.(item);
  };

  const handleDelete = async (e: React.MouseEvent, item: T) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        await deleteJob((item as any).id);

        setTableData(prevData =>
          prevData.filter(dataItem => (dataItem as any).id !== (item as any).id)
        );
        showToast({
          message: 'Item deleted successfully',
          isSuccess: true,
        });
      } catch (error) {
        console.error('Error deleting item:', error);
        showToast({
          message: 'Failed to delete item',
          isSuccess: false,
        });
      }
    }
  };

  const handleTimeFilterChange = (value: 'daily' | 'weekly' | 'monthly' | 'yearly') => {
    setTimeFilter(value);
    onTimeFilterChange?.(value);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchTerm);
    } else {
      // Implement client-side filtering if no onSearch callback is provided
      if (searchTerm.trim() === '') {
        setTableData(tableDataRef.current);
      } else {
        const filteredData = tableDataRef.current.filter(item => {
          // Search through all column values
          return columns.some(column => {
            const value = getValue(item, column.key);
            if (value === null || value === undefined) return false;
            return value.toString().toLowerCase().includes(searchTerm.toLowerCase());
          });
        });
        setTableData(filteredData);
      }
    }
  };

  const getValue = (obj: any, path: string) => {
    return path.split('.').reduce((acc, part) => acc && acc[part], obj);
  };

  // Function to get theme-aware CSS classes
  const getThemeClasses = () => {
    // These classes will be used throughout the component
    return {
      // Table container
      tableContainer:
        'overflow-hidden rounded-lg bg-white/5 backdrop-blur-md border border-[var(--card-border)] shadow-xl relative w-full',

      // Table header
      tableHeader: 'bg-white/5 backdrop-filter backdrop-blur-md',
      tableHeaderCell:
        'px-2 md:px-4 py-3 text-left text-xs font-medium uppercase tracking-wider opacity-60 text-[var(--foreground-color)]',

      // Table body
      tableRow: 'transition-colors',
      tableRowDivider: 'divide-y divide-[var(--card-border)]',
      tableCell: 'px-2 md:px-4 py-3 text-sm',
      tableCellContent: 'text-[var(--foreground-color)]',

      // Mobile card
      mobileCard:
        'w-full bg-[var(--card-bg)] backdrop-blur-md border border-[var(--card-border)] rounded-lg overflow-hidden',
      mobileCardLabel: 'text-xs opacity-60 text-[var(--foreground-color)]',
      mobileCardValue: 'text-sm text-[var(--foreground-color)]',
      mobileCardDivider: 'border-t border-[var(--card-border)]',

      // Actions button
      actionButton:
        'inline-flex items-center justify-center p-1.5 rounded-full bg-[var(--button-secondary-bg)] text-[var(--foreground-color)] hover:bg-[var(--button-secondary-hover)] hover:opacity-90 transition-colors',

      // Dropdown menu items (now used in portal)
      dropdownItem:
        'block w-full text-left px-4 py-2 text-sm text-[var(--foreground-color)] hover:bg-[var(--button-secondary-bg)] transition-colors',
      dropdownDivider: 'border-t border-[var(--card-border)] my-1',

      // Search input
      searchInput:
        'w-full bg-[var(--input-bg)] backdrop-blur-md border border-[var(--input-border)] rounded-lg py-2 pl-10 pr-4 text-[var(--input-text)] placeholder:text-[var(--input-placeholder)] focus:outline-none focus:ring-2 focus:ring-purple-700/50',
      searchIcon: 'absolute left-3 top-2.5 h-4 w-4 opacity-40',
    };
  };

  const themeClasses = getThemeClasses();

  const renderPagination = () => {
    if ((disablePagination && !showPagination) || calculatedTotalPages <= 1) return null;

    // Calculate page numbers to display
    const getPageNumbers = () => {
      const delta = 2; // Number of pages to show on each side of current page
      const pages: (number | string)[] = [];

      for (let i = 1; i <= calculatedTotalPages; i++) {
        if (
          i === 1 || // Always show first page
          i === calculatedTotalPages || // Always show last page
          (i >= calculatedCurrentPage - delta && i <= calculatedCurrentPage + delta) // Pages around current
        ) {
          pages.push(i);
        } else if (pages[pages.length - 1] !== '...') {
          pages.push('...');
        }
      }

      return pages;
    };

    const pageNumbers = getPageNumbers();

    return (
      <div className="px-2 sm:px-4 py-2 bg-white/5 backdrop-blur-sm border-t border-[var(--card-border)]">
        <div className="flex items-center justify-between">
          {/* Results info */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-[var(--foreground-color)] opacity-60">
              {(() => {
                const total = totalItems || paginationData?.totalItems || tableData.length;
                if (total === 0) return 'No results';

                // For server-side pagination, use actual items displayed
                // For client-side pagination, use the calculated range
                const actualItemsCount = currentItems.length;
                const start = (calculatedCurrentPage - 1) * effectiveItemsPerPage + 1;
                const end = usingServerPagination
                  ? Math.min(start + actualItemsCount - 1, total)
                  : Math.min(calculatedCurrentPage * effectiveItemsPerPage, total);

                return `Showing ${start} to ${end} of ${total} results`;
              })()}
            </span>
          </div>

          {/* Pagination controls */}
          <div className="flex items-center gap-0.5">
            {/* Previous button */}
            <button
              type="button"
              onClick={() => onPageChange?.(calculatedCurrentPage - 1)}
              disabled={calculatedCurrentPage <= 1}
              className="relative inline-flex items-center px-1.5 py-1 text-xs font-medium text-[var(--foreground-color)] bg-white/5 border border-[var(--card-border)] rounded hover:bg-white/10 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
              aria-label="Previous page"
            >
              <ChevronLeft className="h-3 w-3" />
            </button>

            {/* Page numbers */}
            <div className="hidden sm:flex items-center gap-0.5">
              {pageNumbers.map((page, index) =>
                page === '...' ? (
                  <span
                    key={`ellipsis-${index}`}
                    className="px-2 py-1 text-xs text-[var(--foreground-color)] opacity-50"
                  >
                    ...
                  </span>
                ) : (
                  <button
                    key={page}
                    type="button"
                    onClick={() => onPageChange?.(page as number)}
                    className={`relative inline-flex items-center px-2 py-1 text-xs font-medium rounded transition-colors ${
                      page === calculatedCurrentPage
                        ? 'bg-purple-600/80 text-white border border-purple-600/80'
                        : 'text-[var(--foreground-color)] bg-white/5 border border-[var(--card-border)] hover:bg-white/10'
                    }`}
                    aria-current={page === calculatedCurrentPage ? 'page' : undefined}
                  >
                    {page}
                  </button>
                )
              )}
            </div>

            {/* Mobile page indicator */}
            <div className="flex sm:hidden items-center px-2 py-1">
              <span className="text-xs text-[var(--foreground-color)]">
                {calculatedCurrentPage} / {calculatedTotalPages}
              </span>
            </div>

            {/* Next button */}
            <button
              type="button"
              onClick={() => onPageChange?.(calculatedCurrentPage + 1)}
              disabled={calculatedCurrentPage >= calculatedTotalPages}
              className="relative inline-flex items-center px-1.5 py-1 text-xs font-medium text-[var(--foreground-color)] bg-white/5 border border-[var(--card-border)] rounded hover:bg-white/10 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
              aria-label="Next page"
            >
              <ChevronRight className="h-3 w-3" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderTimeFilter = () => {
    if (!showDashboard) return null;

    return (
      <div className="flex justify-end mb-4">
        <div className="flex items-center gap-2 bg-[var(--button-secondary-bg)] border border-[var(--card-border)] p-1 rounded-md">
          <button
            type="button"
            onClick={() => handleTimeFilterChange('daily')}
            className={`px-3 py-1 rounded-sm text-sm ${
              timeFilter === 'daily'
                ? 'bg-[var(--button-secondary-bg)] text-[var(--foreground-rgb)]'
                : 'text-[var(--foreground-rgb)] opacity-70'
            }`}
          >
            Day
          </button>
          <button
            type="button"
            onClick={() => handleTimeFilterChange('weekly')}
            className={`px-3 py-1 rounded-sm text-sm ${
              timeFilter === 'weekly'
                ? 'bg-[var(--button-secondary-bg)] text-[var(--foreground-rgb)]'
                : 'text-[var(--foreground-rgb)] opacity-70'
            }`}
          >
            Week
          </button>
          <button
            type="button"
            onClick={() => handleTimeFilterChange('monthly')}
            className={`px-3 py-1 rounded-sm text-sm ${
              timeFilter === 'monthly'
                ? 'bg-[var(--button-secondary-bg)] text-[var(--foreground-rgb)]'
                : 'text-[var(--foreground-rgb)] opacity-70'
            }`}
          >
            Month
          </button>
          <button
            type="button"
            onClick={() => handleTimeFilterChange('yearly')}
            className={`px-3 py-1 rounded-sm text-sm ${
              timeFilter === 'yearly'
                ? 'bg-[var(--button-secondary-bg)] text-[var(--foreground-rgb)]'
                : 'text-[var(--foreground-rgb)] opacity-70'
            }`}
          >
            Year
          </button>
        </div>
      </div>
    );
  };

  const renderSearchBar = () => {
    if (!onSearch && !searchTerm) return null;

    return (
      <form onSubmit={handleSearch} className="flex-1">
        <div className="relative">
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className={themeClasses.searchInput}
          />
          <Search className={themeClasses.searchIcon} />
          {searchTerm && (
            <button
              type="button"
              onClick={() => {
                setSearchTerm('');
                onSearch?.('');
              }}
              className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </form>
    );
  };

  // Table skeleton component for loading state
  const TableSkeleton = () => {
    const isMobile = useMediaQuery('(max-width: 768px)');

    // Generate random widths for skeleton cells to create a more natural look
    const getRandomWidth = () => {
      const widths = ['w-1/4', 'w-1/3', 'w-2/3', 'w-3/4'];
      return widths[Math.floor(Math.random() * widths.length)];
    };

    if (isMobile) {
      // Mobile skeleton
      return (
        <div className="w-full">
          <div className="space-y-2">
            {[...Array(5)].map((_, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className={themeClasses.mobileCard}
              >
                <div className="p-3">
                  <div className="grid grid-cols-1 gap-1.5">
                    {[...Array(4)].map((_, colIndex) => (
                      <div key={colIndex} className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4 rounded-full" />
                        <div className="flex-1">
                          <Skeleton className="h-3 w-16 mb-1" />
                          <Skeleton className={`h-4 ${getRandomWidth()}`} />
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className={`mt-3 pt-3 ${themeClasses.mobileCardDivider} flex justify-end`}>
                    <Skeleton className="h-8 w-20 rounded-md" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      );
    }

    // Desktop skeleton
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={themeClasses.tableContainer}
      >
        <div className="overflow-x-auto max-h-[calc(100vh-150px)] w-full">
          <table className={`w-full table-auto ${themeClasses.tableRowDivider}`}>
            <thead>
              <tr className={themeClasses.tableHeader}>
                {[...Array(columns.length || 5)].map((_, index) => (
                  <th
                    key={index}
                    className={themeClasses.tableHeaderCell}
                    aria-label={`Column ${index + 1} header`} // Add aria-label for accessibility
                  >
                    <div className="flex items-center gap-1">
                      <Skeleton className="h-3 w-3 rounded-full" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                  </th>
                ))}
                <th
                  className={`${themeClasses.tableHeaderCell} text-center`}
                  aria-label="Actions column header" // Add aria-label for accessibility
                >
                  <div className="flex items-center gap-1 justify-center">
                    <Skeleton className="h-3 w-16" />
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className={themeClasses.tableRowDivider}>
              {[...Array(5)].map((_, rowIndex) => (
                <motion.tr
                  key={rowIndex}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: rowIndex * 0.05 }}
                  className={themeClasses.tableRow}
                >
                  {[...Array(columns.length || 5)].map((_, colIndex) => (
                    <td key={colIndex} className={themeClasses.tableCell}>
                      <Skeleton className={`h-4 ${getRandomWidth()}`} />
                    </td>
                  ))}
                  <td className={`${themeClasses.tableCell} text-center`}>
                    <div className="flex justify-center">
                      <Skeleton className="h-8 w-8 rounded-full" />
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>
    );
  };

  // emptyStateConfig is already destructured in the function parameters

  // Show loading skeleton
  if (isLoading) {
    return (
      <div className="w-full relative">
        {/* Dashboard and filters section */}
        {showDashboard && (
          <>
            {/* Time filter skeleton */}
            <div className="flex justify-end mb-4">
              <Skeleton className="h-8 w-64 rounded-md" />
            </div>

            {/* Dashboard Stats skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-24 w-full rounded-lg" />
              ))}
            </div>

            {/* Chart skeleton */}
            <Skeleton className="h-64 w-full rounded-lg mb-6" />
          </>
        )}

        {/* Search skeleton */}
        {(onSearch || searchTerm) && (
          <div className="flex flex-col sm:flex-row justify-between gap-4 mb-2">
            <Skeleton className="h-10 w-full rounded-lg" />
          </div>
        )}

        <TableSkeleton />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <EmptyState
        type={emptyStateConfig?.type || 'table'}
        title={emptyStateConfig?.title}
        description={emptyStateConfig?.description}
        actionLabel={emptyStateConfig?.actionLabel}
        actionRoute={emptyStateConfig?.actionRoute || '/job-description-creation'}
        showButton={emptyStateConfig?.showButton !== undefined ? emptyStateConfig.showButton : true}
        onAction={emptyStateConfig?.onAction}
      />
    );
  }

  // This function will be used by the mobile card view to render the mobile card
  const renderMobileCardView = () => {
    return (
      <div className="w-full">
        <div className="space-y-2">
          {currentItems.map(item => (
            <motion.div
              key={(item as any).id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={themeClasses.mobileCard}
            >
              <div className="p-3">
                <div className="grid grid-cols-1 gap-1.5">
                  {columns
                    .filter(col => col.key !== 'actions') // Filter out the actions column
                    .map((column, colIndex) => (
                      <div key={colIndex} className="flex items-center gap-2">
                        {column.icon && <column.icon className="w-4 h-4 opacity-70" />}
                        <div className="flex-1">
                          <div className={themeClasses.mobileCardLabel}>{column.label}</div>
                          <div
                            className={`${themeClasses.mobileCardValue} ${column.clickable ? 'cursor-pointer hover:underline' : ''}`}
                            onClick={
                              column.clickable
                                ? e => {
                                    e.stopPropagation();
                                    column.onClick?.(item);
                                  }
                                : undefined
                            }
                          >
                            {column.render ? (
                              <div className={themeClasses.tableCellContent}>
                                {column.render(getValue(item, column.key), item)}
                              </div>
                            ) : (
                              <span className={themeClasses.tableCellContent}>
                                {getValue(item, column.key) || '-'}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>

                {/* Actions row */}
                <div className={`mt-3 pt-3 ${themeClasses.mobileCardDivider} flex justify-end`}>
                  {customActions ? (
                    <div className={themeClasses.tableCellContent}>{customActions(item)}</div>
                  ) : (
                    <div className="relative inline-block text-left">
                      <button
                        type="button"
                        ref={el => {
                          actionButtonRefs.current[`mobile-${(item as any).id}`] = el;
                        }}
                        onClick={e => {
                          e.stopPropagation();
                          setOpenMenuId(
                            openMenuId === `mobile-${(item as any).id}`
                              ? null
                              : `mobile-${(item as any).id}`
                          );
                        }}
                        className={themeClasses.actionButton}
                        title="Actions"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 6h16M4 12h16M4 18h16"
                          />
                        </svg>
                      </button>
                      <TableDropdownMenu
                        isOpen={openMenuId === `mobile-${(item as any).id}`}
                        onClose={() => setOpenMenuId(null)}
                        triggerRef={{
                          current: actionButtonRefs.current[`mobile-${(item as any).id}`],
                        }}
                        onViewDetails={
                          onRowClick
                            ? () => {
                                if (hideDetailsUrl) {
                                  onRowClick?.(item);
                                } else {
                                  const e = new MouseEvent('click') as any;
                                  handleDetailsClick(e, item);
                                }
                              }
                            : undefined
                        }
                        onDelete={
                          !hideDelete
                            ? () => {
                                const e = new MouseEvent('click') as any;
                                handleDelete(e, item);
                              }
                            : undefined
                        }
                        hideViewDetails={!onRowClick}
                        hideDelete={hideDelete}
                      />
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full relative">
      {/* Dashboard and filters section */}
      {showDashboard && (
        <>
          {/* Time filter controls */}
          {renderTimeFilter()}

          {/* Dashboard Stats */}
          <TableDashboard
            stats={dashboardStats}
            chartData={chartData}
            distributionData={distributionData}
            timeFilter={timeFilter}
          />
        </>
      )}

      <div className="w-full mx-auto">
        {isMobile ? (
          // Mobile card view
          <div className="w-full">
            {(onSearch || searchTerm) && <div className="mb-4">{renderSearchBar()}</div>}
            {renderMobileCardView()}
            {renderPagination()}
          </div>
        ) : (
          // Desktop table view
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={themeClasses.tableContainer}
          >
            {/* Table header with integrated search */}
            {(onSearch || searchTerm || headerActions) && (
              <div className="p-4 bg-white/5 backdrop-blur-sm border-b border-[var(--card-border)]">
                <div className="flex items-center gap-3 justify-end">
                  {(onSearch || searchTerm) && (
                    <form onSubmit={handleSearch} className="flex-1 max-w-2xl">
                      <div className="relative">
                        <input
                          type="text"
                          placeholder={searchPlaceholder}
                          value={searchTerm}
                          onChange={e => setSearchTerm(e.target.value)}
                          className="w-full bg-gray-900/50 backdrop-blur-md border border-gray-700 rounded-lg py-2 pl-10 pr-10 text-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors duration-200"
                        />
                        <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                        {searchTerm && (
                          <button
                            type="button"
                            onClick={() => {
                              setSearchTerm('');
                              onSearch?.('');
                            }}
                            className="absolute right-3 top-2.5 text-gray-400 hover:text-white transition-colors"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </form>
                  )}
                  {headerActions && <div>{headerActions}</div>}
                </div>
              </div>
            )}
            <div className="overflow-x-auto max-h-[calc(100vh-200px)] w-full">
              <table className={`w-full table-auto ${themeClasses.tableRowDivider}`}>
                <thead>
                  <tr className={themeClasses.tableHeader}>
                    {columns.map(({ label, icon: Icon }) => (
                      <th key={label} className={themeClasses.tableHeaderCell}>
                        <div className="flex items-center gap-1">
                          {Icon && <Icon className="w-3 h-3" />}
                          {label}
                        </div>
                      </th>
                    ))}
                    {customActions && (
                      <th className={`${themeClasses.tableHeaderCell} text-center`}>
                        <div className="flex items-center gap-1 justify-center">Actions</div>
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className={themeClasses.tableRowDivider}>
                  {currentItems.map((item, index) => (
                    <motion.tr
                      key={(item as any).id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className={themeClasses.tableRow}
                    >
                      {columns.map((column, colIndex) => (
                        <td key={colIndex} className={themeClasses.tableCell}>
                          {column.clickable ? (
                            <div
                              onClick={e => {
                                e.stopPropagation();
                                column.onClick?.(item);
                              }}
                              className={`${themeClasses.tableCellContent} cursor-pointer hover:underline`}
                              style={{ color: 'var(--foreground-color)' }}
                            >
                              {column.render
                                ? column.render(getValue(item, column.key), item)
                                : getValue(item, column.key) || '-'}
                            </div>
                          ) : column.render ? (
                            <div
                              className={themeClasses.tableCellContent}
                              style={{ color: 'var(--foreground-color)' }}
                            >
                              {column.render(getValue(item, column.key), item)}
                            </div>
                          ) : (
                            <span
                              className={themeClasses.tableCellContent}
                              style={{ color: 'var(--foreground-color)' }}
                            >
                              {getValue(item, column.key) || '-'}
                            </span>
                          )}
                        </td>
                      ))}
                      {customActions && (
                        <td className={`${themeClasses.tableCell} text-center`}>
                          <div
                            className={themeClasses.tableCellContent}
                            style={{ color: 'var(--foreground-color)' }}
                          >
                            {customActions(item)}
                          </div>
                        </td>
                      )}
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
            {renderPagination()}
          </motion.div>
        )}
      </div>
    </div>
  );
}

export default GenericTable;
