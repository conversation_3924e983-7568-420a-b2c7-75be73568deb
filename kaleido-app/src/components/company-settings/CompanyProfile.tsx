import {
  Alert<PERSON>riangle,
  AtSign,
  Building2,
  Globe,
  Heart,
  Hexagon,
  ImageIcon,
  InfoIcon,
  Layout,
  Loader2,
  Mail,
  Phone,
  Plus,
  Save,
  Sparkles,
  Upload,
  Users,
  X,
} from 'lucide-react';

import StyledInput from '@/components/common/styledInputs/StyledInput';
import StyledSelect from '@/components/common/styledInputs/StyledSelect';
import Image from 'next/image';
import React from 'react';

interface CompanyProfileProps {
  formData: {
    companyName: string;
    companyWebsite: string;
    size: string;
    contactName: string;
    contactEmail: string;
    phoneNumber: string;
    industry: string;
    description: string;
    companyValues: string[];
    location: string;
    logo: string;
    isDemoMode?: boolean;
  };
  handleChange: (field: string, value: string | string[] | boolean) => void;
  handleSave: () => Promise<void>;
  handleCancel: () => void;
  isEditing: boolean;
  handleFetchDescription: () => Promise<void>;
  isFetchingDescription: boolean;
  handleFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  isUploading: boolean;
  uploadProgress: number;
  companySizeOptions: { value: string; label: string }[];
}

const CompanyProfile: React.FC<CompanyProfileProps> = ({
  formData,
  handleChange,
  handleSave,
  handleCancel,
  isEditing,
  handleFetchDescription,
  isFetchingDescription,
  handleFileUpload,
  isUploading,
  uploadProgress,
  companySizeOptions,
}) => {
  const [currentValue, setCurrentValue] = React.useState('');
  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto" style={{ maxHeight: 'calc(100vh - 180px)' }}>
        <div className="space-y-6 pb-20">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Main Info Section */}
            <div className="xl:col-span-2">
              <div className="overflow-hidden rounded-2xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all">
                <div className="p-6">
                  <h2 className="text-lg font-bold text-white mb-4 flex items-center gap-3">
                    <div className="p-2 bg-blue-500/20 rounded-full">
                      <Building2 className="w-6 h-6 text-blue-400" />
                    </div>
                    Company Information
                  </h2>

                  <div className="space-y-4">
                    {/* Company Information - 3 columns layout */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <Building2 className="w-4 h-4 text-white/70" />
                          Company Name
                        </label>
                        <StyledInput
                          value={formData.companyName}
                          onChange={e => handleChange('companyName', e.target.value)}
                          placeholder="Enter company name"
                          className="w-full"
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <Globe className="w-4 h-4 text-white/70" />
                          Company Website
                        </label>
                        <StyledInput
                          value={formData.companyWebsite}
                          onChange={e => handleChange('companyWebsite', e.target.value)}
                          placeholder="Enter company website"
                          className="w-full"
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <Building2 className="w-4 h-4 text-white/70" />
                          Industry
                        </label>
                        <StyledInput
                          value={formData.industry}
                          onChange={e => handleChange('industry', e.target.value)}
                          placeholder="Enter industry"
                          className="w-full"
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <Users className="w-4 h-4 text-white/70" />
                          Company Size
                        </label>
                        <StyledSelect
                          options={companySizeOptions}
                          value={formData.size}
                          onChange={value => handleChange('size', value)}
                          placeholder="Select company size"
                          className="w-full"
                          showListOnEmpty
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <Users className="w-4 h-4 text-white/70" />
                          Contact Name
                        </label>
                        <StyledInput
                          value={formData.contactName}
                          onChange={e => handleChange('contactName', e.target.value)}
                          placeholder="Enter contact person name"
                          className="w-full"
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <AtSign className="w-4 h-4 text-white/70" />
                          Contact Email
                        </label>
                        <StyledInput
                          value={formData.contactEmail}
                          onChange={e => handleChange('contactEmail', e.target.value)}
                          placeholder="Enter contact email"
                          className="w-full"
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <Phone className="w-4 h-4 text-white/70" />
                          Phone Number
                        </label>
                        <StyledInput
                          value={formData.phoneNumber}
                          onChange={e => handleChange('phoneNumber', e.target.value)}
                          placeholder="Enter phone number"
                          className="w-full"
                          inputType="phone"
                          variant="dark"
                        />
                      </div>

                      <div className="flex flex-col">
                        <label className="text-white/90 mb-2 text-sm flex items-center gap-2">
                          <Building2 className="w-4 h-4 text-white/70" />
                          Location
                        </label>
                        <StyledInput
                          value={formData.location}
                          onChange={e => handleChange('location', e.target.value)}
                          placeholder="Enter location"
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description Section */}
              <div className="overflow-hidden rounded-2xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all mt-6">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-bold text-white flex items-center gap-3">
                      <div className="p-2 bg-indigo-500/20 rounded-full">
                        <Layout className="w-6 h-6 text-indigo-400" />
                      </div>
                      Company Description
                    </h2>

                    {formData.companyWebsite && (
                      <button
                        type="button"
                        onClick={handleFetchDescription}
                        disabled={isFetchingDescription || !formData.companyWebsite}
                        className="flex items-center gap-2 text-sm text-white/80 hover:text-white bg-indigo-500/20 hover:bg-indigo-500/30 px-3 py-1.5 rounded-lg transition-colors"
                      >
                        {isFetchingDescription ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Sparkles className="w-4 h-4" />
                            Generate from Website
                          </>
                        )}
                      </button>
                    )}
                  </div>

                  <StyledInput
                    label="Tell candidates about your company mission, culture, and what makes it special"
                    value={formData.description}
                    onChange={e => handleChange('description', e.target.value)}
                    placeholder="Enter company description"
                    multiline
                    rows={6}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Company Values Section */}
              <div className="overflow-hidden rounded-2xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all mt-6">
                <div className="p-6">
                  <h2 className="text-lg font-bold text-white flex items-center gap-3 mb-4">
                    <div className="p-2 bg-pink-500/20 rounded-full">
                      <Heart className="w-6 h-6 text-pink-400" />
                    </div>
                    Company Values
                  </h2>

                  <div className="space-y-4">
                    <div className="flex gap-2">
                      <StyledInput
                        label="Add your company values"
                        value={currentValue}
                        onChange={e => setCurrentValue(e.target.value)}
                        onKeyDown={e => {
                          if (e.key === 'Enter' && currentValue.trim()) {
                            e.preventDefault();
                            const newValues = [
                              ...(formData.companyValues || []),
                              currentValue.trim(),
                            ];
                            handleChange('companyValues', newValues);
                            setCurrentValue('');
                          }
                        }}
                        placeholder="e.g., Innovation, Customer Focus, Work-Life Balance"
                        className="flex-1"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          if (currentValue.trim()) {
                            const newValues = [
                              ...(formData.companyValues || []),
                              currentValue.trim(),
                            ];
                            handleChange('companyValues', newValues);
                            setCurrentValue('');
                          }
                        }}
                        disabled={!currentValue.trim()}
                        className="px-4 py-2 bg-pink-500/20 hover:bg-pink-500/30 text-pink-300 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 self-end"
                      >
                        <Plus className="w-4 h-4" />
                        Add
                      </button>
                    </div>

                    <p className="text-sm text-white/60">
                      Type a value and press Enter or click Add. You can include commas, spaces, and
                      special characters.
                    </p>

                    {formData.companyValues && formData.companyValues.length > 0 && (
                      <div>
                        <p className="text-sm text-white/70 mb-2">Current values:</p>
                        <div className="flex flex-wrap gap-2">
                          {formData.companyValues.map((value, index) => (
                            <span
                              key={index}
                              className="group px-3 py-1 bg-pink-500/20 text-pink-300 rounded-full text-sm border border-pink-500/30 flex items-center gap-2"
                            >
                              {value}
                              <button
                                type="button"
                                onClick={() => {
                                  const newValues = formData.companyValues.filter(
                                    (_, i) => i !== index
                                  );
                                  handleChange('companyValues', newValues);
                                }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                                aria-label={`Remove ${value}`}
                                title="Remove value"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Email Settings and Logo Section - Side by side in a column */}
            <div className="xl:col-span-1 space-y-6">
              {/* Email Settings Section */}
              {false && (
                <div className="overflow-hidden rounded-2xl bg-black/5 backdrop-blur-lg border border-purple-500/20 transition-all hover:shadow-lg hover:shadow-purple-500/10">
                  <div className="p-6">
                    <h2 className="text-lg font-bold text-white mb-4 flex items-center gap-3">
                      <div className="p-2 bg-purple-500/30 rounded-full">
                        <AlertTriangle className="w-6 h-6 text-purple-300" />
                      </div>
                      Email Settings
                    </h2>

                    <div className="flex flex-col space-y-4">
                      <div className="relative p-5 bg-gradient-to-r from-purple-900/20 to-purple-700/10 rounded-lg border border-purple-500/20">
                        <div className="absolute -top-3 -right-3">
                          <div className="p-1.5 bg-black/10 rounded-full animate-pulse">
                            <InfoIcon className="w-5 h-5 text-purple-300" />
                          </div>
                        </div>

                        <div className="flex items-center justify-between gap-4">
                          <div className="min-w-0">
                            <p className="text-white font-medium flex items-center gap-2">
                              <Mail className="w-5 h-5 text-purple-300 flex-shrink-0" />
                              Demo Mode
                            </p>
                            <p className="text-white/70 text-sm mt-2 whitespace-normal">
                              When enabled, emails are only sent to you and whitelisted addresses
                              for testing purposes
                            </p>
                          </div>
                          <div className="flex-shrink-0">
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                className="sr-only peer"
                                checked={formData.isDemoMode}
                                onChange={e => handleChange('isDemoMode', e.target.checked)}
                                aria-label="Toggle demo mode"
                              />
                              <div className="w-12 h-6 bg-gray-600/50 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-gray-300 after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600 peer-checked:after:bg-white"></div>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Logo Section */}
              <div className="overflow-hidden rounded-2xl bg-primary/10 backdrop-blur-lg border border-white/5 transition-all">
                <div className="p-6">
                  <h2 className="text-lg font-bold text-white mb-4 flex items-center gap-3">
                    <div className="p-2 bg-emerald-500/20 rounded-full">
                      <ImageIcon className="w-6 h-6 text-emerald-400" />
                    </div>
                    Company Logo
                  </h2>

                  <div className="space-y-4">
                    <div className="flex flex-col items-center justify-center bg-white/10 border border-dashed border-white/20 rounded-xl p-6">
                      {formData.logo ? (
                        <Image
                          src={formData.logo}
                          alt={formData.companyName}
                          width={128}
                          height={128}
                          className="max-w-full h-32 object-contain rounded"
                        />
                      ) : (
                        <div className="text-center p-6">
                          <Hexagon className="h-12 w-12 text-white/20 mx-auto mb-2" />
                          <p className="text-white/40 text-sm">No logo uploaded</p>
                        </div>
                      )}
                    </div>

                    <label
                      htmlFor="company-logo-upload"
                      className={`flex items-center justify-center px-4 py-3 border border-white/20
                        rounded-lg cursor-pointer hover:bg-white/5 transition-colors text-sm w-full
                        ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <Upload className="mr-2 h-4 w-4 text-blue-400" />
                      <span className="text-white/80">Upload company logo</span>
                      <input
                        id="company-logo-upload"
                        type="file"
                        accept="image/jpeg,image/png,image/webp"
                        onChange={handleFileUpload}
                        disabled={isUploading}
                        className="sr-only"
                      />
                    </label>
                    <p className="text-xs text-white/60 text-center">JPG, PNG or WEBP (max. 5MB)</p>

                    {isUploading && (
                      <div className="mt-3">
                        <div className="flex justify-between text-xs text-white/60 mb-1">
                          <span>Uploading...</span>
                          <span>{uploadProgress}%</span>
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-1.5">
                          <div
                            className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Save button section */}
      {isEditing && (
        <div className="flex justify-end pt-4 border-t border-white/10 fixed bottom-0 left-0 right-0 bg-primary/90 backdrop-blur-lg p-4 z-10 w-full shadow-lg">
          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSave}
              className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save Company Profile
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyProfile;
