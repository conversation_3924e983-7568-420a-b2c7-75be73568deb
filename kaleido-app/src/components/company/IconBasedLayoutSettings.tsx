import { Building, Check, Cpu, Eye, Feather, Info, Layout, Paintbrush, Rocket } from 'lucide-react';
import React from 'react';

interface IconBasedLayoutSettingsProps {
  currentLayout?: string;
  onLayoutChange?: (layout: string) => void;
}

const LAYOUT_OPTIONS = [
  {
    id: 'modern',
    name: 'Modern Corporate',
    icon: Layout,
    color: 'bg-blue-500/10',
    iconColor: 'text-blue-500',
    description:
      'A sleek, professional design with a full-width hero section and interactive elements. Perfect for established companies wanting to showcase professionalism with a contemporary touch.',
  },
  {
    id: 'startup',
    name: 'Startup Vibe',
    icon: Rocket,
    color: 'bg-purple-500/10',
    iconColor: 'text-purple-500',
    description:
      'Bold, vibrant colors with dynamic animations. Ideal for innovative startups and tech companies wanting to showcase their cutting-edge culture and energy.',
  },
  {
    id: 'minimal',
    name: 'Minimalist',
    icon: Feather,
    color: 'bg-gray-500/10',
    iconColor: 'text-gray-500',
    description:
      'Clean, spacious design with elegant typography and plenty of whitespace. Best for companies that value simplicity, clarity, and sophisticated aesthetics.',
  },
  {
    id: 'tech',
    name: 'Tech Forward',
    icon: Cpu,
    color: 'bg-green-500/10',
    iconColor: 'text-green-500',
    description:
      'Dark mode with neon accents and interactive elements. Designed for technology companies, SaaS products, and developer-focused organizations.',
  },
  {
    id: 'creative',
    name: 'Creative Studio',
    icon: Paintbrush,
    color: 'bg-pink-500/10',
    iconColor: 'text-pink-500',
    description:
      'Artistic layout with asymmetrical elements and eye-catching animations. Perfect for creative agencies, design studios, and artistic companies.',
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    icon: Building,
    color: 'bg-indigo-500/10',
    iconColor: 'text-indigo-500',
    description:
      'Traditional corporate layout with comprehensive sections and data-driven approach. Ideal for large corporations, financial institutions, and B2B companies.',
  },
];

export default function IconBasedLayoutSettings({
  currentLayout = 'modern',
  onLayoutChange,
}: IconBasedLayoutSettingsProps) {
  const handleLayoutSelect = (layoutId: string) => {
    if (onLayoutChange) {
      onLayoutChange(layoutId);
    }
  };

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
      {LAYOUT_OPTIONS.map(option => {
        const IconComponent = option.icon;
        const isSelected = currentLayout === option.id;

        return (
          <button
            key={option.id}
            type="button"
            onClick={() => handleLayoutSelect(option.id)}
            className={`
              relative group flex flex-col items-center gap-3 p-4 rounded-xl transition-all duration-300 backdrop-blur-sm
              ${
                isSelected
                  ? 'border border-white/20 shadow-lg shadow-black/10 scale-105'
                  : 'border border-white/5 hover:border-white/10 hover:scale-102'
              }
            `}
          >
            {/* Selected indicator */}
            {isSelected && (
              <div className="absolute -top-2 -right-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full p-1 shadow-lg">
                <Check className="w-3 h-3 text-white" />
              </div>
            )}

            {/* Info icon with tooltip */}
            <div className="absolute top-2 right-2 group/info">
              <Info className="w-3.5 h-3.5 text-white/40 hover:text-white/60 transition-colors cursor-help" />
              <div className="absolute top-full right-0 mt-2 w-64 px-3 py-2 bg-black/80 backdrop-blur-md text-white text-xs rounded-lg opacity-0 invisible group-hover/info:opacity-100 group-hover/info:visible transition-all duration-300 shadow-xl z-30 border border-white/10">
                <p className="font-semibold mb-1">{option.name}</p>
                <p className="text-white/80 leading-relaxed">{option.description}</p>
                <div className="absolute -top-1 right-2 w-2 h-2 bg-black/80 rotate-45 border-l border-t border-white/10" />
              </div>
            </div>

            {/* Icon */}
            <div className={`p-2 rounded-md ${option.color} transition-colors`}>
              <IconComponent className={`w-5 h-5 ${option.iconColor}`} />
            </div>

            {/* Label */}
            <span
              className={`text-xs text-center font-semibold line-clamp-1 transition-colors duration-300 ${
                isSelected ? 'text-white' : 'text-white/80'
              }`}
            >
              {option.name}
            </span>
          </button>
        );
      })}
    </div>
  );
}
