import StepFooter from '@/components/steps/layout/StepFooter';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Eye, LucideIcon, X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useMemo } from 'react';
import '../../../../styles/theming.css';
import PreviewContent from '../../preview/PreviewContent';
import { StepProgress } from '@/components/JobSeeker/components/StepProgress';

interface StepLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  icon?: LucideIcon;
}

// Map step names to background images
const stepBackgroundImages: Record<string, string> = {
  'basic-job-information': '/images/insights/team_collaboration_revised.png',
  'skills-&-responsibilities': '/images/insights/skill_gap_analysis_revised_1.png',
  'candidate-qualifications': '/images/insights/career_development.png',
  'job-environment': '/images/insights/performance_analytics_turquoise.png',
  'hiring-manager-introduction': '/images/insights/professional_training_revised.png',
  'final-draft': '/images/insights/innovation_technology_revised.png',
  success: '/images/insights/team_collaboration.png',
};

const StepLayout: React.FC<StepLayoutProps> = ({ children, title, description, icon: Icon }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isPreviewOpen = searchParams?.get('preview') === 'on';
  // Default to fullscreen mode unless explicitly set to false
  const isFullScreen = searchParams?.get('fullscreen') !== 'false';

  // Get job store state for progress tracking
  const { activeStep, totalSteps } = useJobsStore();

  // Get current step name for background image
  const currentStepName = useMemo(() => {
    const currentStep = searchParams?.get('step');
    // The step parameter is already in the format we need (e.g., 'basic-job-information')
    return currentStep;
  }, [searchParams]);

  const backgroundImage = currentStepName ? stepBackgroundImages[currentStepName] : null;

  const togglePreview = () => {
    const params = new URLSearchParams(searchParams?.toString());
    if (isPreviewOpen) {
      params.delete('preview');
    } else {
      params.set('preview', 'on');
    }

    // Construct the new URL with updated search params
    const pathname = window.location.pathname;
    const newPath = `${pathname}?${params.toString()}`;
    router.push(newPath);
  };

  const handleBack = () => {
    router.back();
  };

  const handleClose = () => {
    // Redirect to jobs page
    router.push('/jobs');
  };

  // Full-screen layout
  if (isFullScreen) {
    return (
      <div className="h-screen w-screen flex flex-col relative bg-transparent">
        {/* Gradient Background - exactly matching BaseLayout */}
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-[var(--background-start)] via-[var(--background-middle)] to-[var(--background-end)]" />
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_-20%,var(--background-accent),transparent_70%)] mix-blend-soft-light" />
        </div>

        {/* Content wrapper */}
        <div className="relative z-10 h-full flex flex-col">
          {/* Full-screen header with background image */}
          <div className="relative h-[200px] w-full flex-shrink-0">
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: backgroundImage ? `url('${backgroundImage}')` : 'none',
                backgroundPosition: 'center 20%',
              }}
            />

            {/* Gradient overlays - similar to UnifiedJobView */}
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-900/50 to-purple-900/80" />
            <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-transparent to-transparent" />

            {/* Navigation buttons */}
            <div className="absolute top-4 left-4 right-4 flex justify-end items-center z-20">
              {/* Right side buttons */}
              <div className="flex items-center gap-2">
                {/* Preview button */}
                <button
                  type="button"
                  onClick={togglePreview}
                  aria-label={isPreviewOpen ? 'Close Preview' : 'Preview'}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-md backdrop-blur-md text-white transition-all duration-200 ${
                    isPreviewOpen
                      ? 'bg-gray-600/80 hover:bg-gray-700 border border-gray-500/50'
                      : 'bg-purple-600/80 hover:bg-purple-700'
                  }`}
                >
                  {isPreviewOpen ? (
                    <>
                      <X className="w-4 h-4" />
                      <span className="text-sm font-medium">Close Preview</span>
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4" />
                      <span className="text-sm font-medium">Preview</span>
                    </>
                  )}
                </button>

                {/* Close button */}
                <button
                  onClick={handleClose}
                  className="px-3 py-1.5 rounded-md bg-white/10 hover:bg-white/20 backdrop-blur-md border border-white/20 hover:border-white/30 transition-all duration-200"
                  aria-label="Close"
                >
                  <X className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>

            {/* Header Content */}
            <div className="relative h-full flex items-center px-8 lg:px-12">
              <div className="max-w-7xl mx-auto w-full">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center gap-6"
                >
                  {Icon && (
                    <div className="flex-shrink-0">
                      <div className="p-3 bg-white/10 backdrop-blur-md rounded-xl">
                        <Icon className="w-8 h-8 text-white" />
                      </div>
                    </div>
                  )}
                  <div>
                    <h1 className="text-3xl lg:text-4xl font-bold text-white mb-1">{title}</h1>
                    {description && (
                      <p className="text-base lg:text-lg text-gray-200/90">{description}</p>
                    )}
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Progress Indicator - Bottom Right of Header */}
            <div className="absolute bottom-4 right-8 z-20">
              <StepProgress
                currentStep={activeStep + 1}
                totalSteps={totalSteps - 1}
                overallCompletion={Math.min((activeStep / (totalSteps - 2)) * 100, 100)}
              />
            </div>
          </div>

          {/* Main Content Area with Preview Support */}
          <div className="flex-1 flex overflow-hidden bg-transparent">
            {/* Main Content */}
            <motion.div
              animate={{
                width: isPreviewOpen ? '60%' : '100%',
              }}
              transition={{
                duration: 0.3,
                type: 'spring',
                damping: 25,
                stiffness: 120,
              }}
              className="overflow-y-auto bg-transparent pb-[120px]"
            >
              <div className="max-w-7xl mx-auto p-4 lg:p-6">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.15 }}
                  className="relative backdrop-blur-md bg-black/10 rounded-2xl border border-white/10 shadow-[0_4px_20px_rgba(0,0,0,0.2)]"
                >
                  <div className="p-4 lg:p-6 relative z-10 text-white/90">
                    <div className="[&_*]:text-shadow-sm [&_*]:shadow-black/50">{children}</div>
                  </div>

                  {/* Very subtle gradient overlay for depth */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-b from-white/5 to-transparent pointer-events-none" />
                </motion.div>
              </div>
            </motion.div>

            {/* Preview Panel */}
            <AnimatePresence>
              {isPreviewOpen && (
                <motion.div
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 100 }}
                  transition={{ duration: 0.3 }}
                  className="w-2/5 border-l border-white/10 overflow-hidden relative"
                >
                  {/* Close button in preview panel */}
                  <button
                    onClick={togglePreview}
                    className="absolute top-4 right-4 z-30 p-2 rounded-lg bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-md border border-white/20 hover:border-white/30 transition-all duration-200 shadow-lg"
                    aria-label="Close preview panel"
                  >
                    <X className="w-5 h-5 text-white" />
                  </button>

                  <div className="h-full">
                    <PreviewContent />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* StepFooter */}
          <StepFooter />
        </div>
      </div>
    );
  }

  // Regular layout (non-fullscreen)
  return (
    <motion.div
      className="relative z-0 h-full flex flex-col overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{
        duration: 0.2,
        ease: 'easeInOut',
      }}
    >
      {/* Header with Background Image */}
      <div className="flex-none flex-shrink-0 relative overflow-hidden">
        {/* Background Image for Header Only */}
        {backgroundImage && (
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('${backgroundImage}')`,
            }}
          >
            {/* Gradient overlay for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-br from-background/95 via-background/90 to-background/85" />
            <div className="absolute inset-0 bg-gradient-to-t from-background via-background/70 to-background/60" />
          </div>
        )}

        {/* Header Content */}
        <div className="relative z-10 border-b border-gray-300/10 bg-transparent backdrop-blur-sm px-6 py-8">
          <div className="flex justify-between items-center">
            <div>
              <div className="flex items-center gap-3 mb-2">
                {Icon && (
                  <div className="p-2 bg-purple-700/50 backdrop-blur-sm rounded-full">
                    <Icon className="w-5 h-5 text-white/90" />
                  </div>
                )}
                <h1 className="text-3xl font-bold text-white">{title}</h1>
              </div>
              {description && <p className="text-gray-300 text-base ml-11">{description}</p>}
            </div>

            {/* Preview button */}
            <button
              type="button"
              onClick={togglePreview}
              aria-label={isPreviewOpen ? 'Close Preview' : 'Preview'}
              className="flex items-center gap-2 px-4 py-2 rounded-lg bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 hover:border-white/30 transition-all duration-200"
            >
              {isPreviewOpen ? (
                <>
                  <X className="w-4 h-4 text-white" />
                  <span className="text-sm text-white">Close</span>
                </>
              ) : (
                <>
                  <Eye className="w-4 h-4 text-white" />
                  <span className="text-sm text-white">Preview</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div
        className="relative z-0 flex-grow min-h-0 overflow-y-auto px-2 sm:px-3 md:px-4 py-2 sm:py-3 md:py-4 pb-[120px]
        [&_label]:text-white/90 [&_label]:text-sm
        [&_input]:bg-black/10
        [&_input]:backdrop-blur-xl
        [&_input]:border-white/10
        [&_input]:text-white
        [&_input]:text-sm
        [&_input:focus]:border-white/20
        [&_input:hover]:border-white/15
        [&_.MuiOutlinedInput-root]:text-white
        [&_.MuiOutlinedInput-root]:text-sm
        [&_.MuiInputLabel-root]:text-white/80
        [&_.MuiInputLabel-root]:text-sm
        [&_.MuiOutlinedInput-notchedOutline]:border-white/10
        [&_.MuiOutlinedInput-root:hover_.MuiOutlinedInput-notchedOutline]:border-white/20
        [&_.MuiOutlinedInput-root.Mui-focused_.MuiOutlinedInput-notchedOutline]:border-white/30
        [&_.MuiChip-root]:bg-white/10
        [&_.MuiChip-root]:backdrop-blur-lg
        [&_.MuiChip-label]:text-white/90
        [&_.MuiChip-label]:text-sm
        [&_.MuiMenuItem-root]:text-white/90
        [&_.MuiMenuItem-root]:text-sm
        [&_.MuiMenuItem-root:hover]:bg-white/5
        [&_.MuiCheckbox-root]:text-white/90
        [&_.ql-toolbar]:bg-black/20
        [&_.ql-toolbar]:backdrop-blur-xl
        [&_.ql-toolbar]:border-white/10
        [&_.ql-toolbar]:rounded-t-lg
        [&_.ql-container]:bg-black/10
        [&_.ql-container]:backdrop-blur-xl
        [&_.ql-container]:border-white/10
        [&_.ql-container]:rounded-b-lg
        [&_.ql-editor]:text-white/90
        [&_.ql-editor]:text-sm
        [&_.MuiAutocomplete-root_.MuiOutlinedInput-root]:bg-black/10
        [&_.MuiAutocomplete-root_.MuiOutlinedInput-root]:backdrop-blur-xl
        [&_.MuiAutocomplete-listbox]:bg-black/90
        [&_.MuiAutocomplete-option]:text-white/90
        [&_.MuiAutocomplete-option]:text-sm
        [&_.MuiAutocomplete-option:hover]:bg-white/10
        [&_h6]:text-md
        [&_h5]:text-md
        [&_h4]:text-md
        [&_h3]:text-md
        [&_h2]:text-md
        [&_h1]:text-md
        [&_p]:text-sm
        [&_.MuiTypography-h6]:text-md
        [&_.MuiTypography-body1]:text-sm
        [&_.MuiTypography-body2]:text-sm"
      >
        {children}
      </div>
    </motion.div>
  );
};

export default StepLayout;
