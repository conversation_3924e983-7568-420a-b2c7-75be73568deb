import React, { useEffect, useMemo } from 'react';

import { motion } from 'framer-motion';
import { Eye, X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import SmoothButton from '@/components/ui/SmoothButton';
import { steps, stepValidations } from '@/contexts/jobs/constants';
import { useJobs } from '@/contexts/jobs/JobsContext';

interface StepLayoutProps {
  title: string;
  description: string;
  children: React.ReactNode;
  className?: string;
  icon?: React.ElementType;
}

// Map step names to background images
const stepBackgroundImages: Record<string, string> = {
  'basic-job-information': '/images/insights/team_collaboration_revised.png',
  'skills-responsibilities': '/images/insights/skill_gap_analysis_revised_1.png',
  'candidate-qualifications': '/images/insights/career_development.png',
  'job-environment': '/images/insights/performance_analytics_turquoise.png',
  'hiring-manager-introduction': '/images/insights/professional_training_revised.png',
  'final-draft': '/images/insights/innovation_technology_revised.png',
  success: '/images/insights/team_collaboration.png',
};

const StepLayout: React.FC<StepLayoutProps> = ({
  title,
  description,
  children,
  className = '',
  icon,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isPreviewOpen = searchParams?.get('preview') === 'on';
  const { job } = useJobs();

  // Get current step name and required fields
  const currentStepName = useMemo(() => {
    const currentStep = searchParams?.get('step') || '0';
    const stepIndex = parseInt(currentStep, 10);
    return steps[stepIndex]?.name.toLowerCase().replace(/\s+/g, '-');
  }, [searchParams]);

  // Get required fields for current step
  const requiredFields = useMemo(() => {
    if (!currentStepName) return [];
    return stepValidations[currentStepName]?.requiredFields || [];
  }, [currentStepName]);

  // Save job data to localStorage before toggling preview
  const togglePreview = () => {
    // Save current job data to localStorage to prevent data loss
    if (job) {
      localStorage.setItem('job', JSON.stringify(job));
    }

    const params = new URLSearchParams(searchParams?.toString());
    if (isPreviewOpen) {
      params.delete('preview');
    } else {
      params.set('preview', 'on');
    }

    // Preserve all existing URL parameters
    const currentStep = searchParams?.get('step');
    if (currentStep) {
      params.set('step', currentStep);
    }

    // Construct the new URL with updated search params
    const pathname = window.location.pathname;
    const newPath = `${pathname}?${params.toString()}`;
    router.push(newPath);
  };

  // Ensure job data is preserved when component mounts
  useEffect(() => {
    // This ensures the job data is loaded from localStorage if needed
    const savedJob = localStorage.getItem('job');
    if (savedJob && !job) {
      // The job data will be loaded by the JobsContext automatically
    }
  }, [job]);

  // Create a new React element with required fields prop
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      // Use type assertion to tell TypeScript that requiredFields is a valid prop

      return React.cloneElement(child as React.ReactElement, { requiredFields });
    }
    return child;
  });

  // Get the background image for the current step
  const backgroundImage = currentStepName ? stepBackgroundImages[currentStepName] : null;

  return (
    <div className="h-full flex flex-col bg-background relative">
      {/* Background Image with Gradient Overlay */}
      {backgroundImage && (
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('${backgroundImage}')`,
          }}
        >
          {/* Gradient overlay for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-br from-background/95 via-background/90 to-background/85" />
          <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
        </div>
      )}

      {/* Content Container with built-in footer offset */}
      <div className="flex flex-col h-full relative z-10">
        {/* Header */}
        <div className="flex-none border-b border-gray-300/10 bg-background/80 backdrop-blur-sm h-[80px]">
          <div className="px-6 h-full flex items-center justify-between">
            {/* Left side - Title and Description */}
            <div>
              <h1 className="text-2xl font-bold text-white mb-1">{title}</h1>
              <p className="text-gray-400 text-sm">{description}</p>
            </div>

            {/* Right side - Preview Button */}
            <div className="flex items-center gap-4">
              <SmoothButton
                label={isPreviewOpen ? 'Close' : 'Preview'}
                icon={isPreviewOpen ? <X className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                onClick={togglePreview}
                className="text-sm lg:text-base"
              />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-y-auto" style={{ paddingBottom: '120px' }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="p-4 sm:p-6 lg:p-8 max-w-5xl mx-auto min-h-full"
          >
            <div className="bg-background/60 backdrop-blur-sm rounded-lg border border-gray-300/10 p-6 shadow-xl">
              {childrenWithProps}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default StepLayout;
