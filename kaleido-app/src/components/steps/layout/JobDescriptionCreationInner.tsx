'use client';

import React, { useEffect, useRef, useState } from 'react';

import axios from 'axios';
import { useSearchParams } from 'next/navigation';

import StepContent from '@/components/JobDescriptionCreation/StepContent';
import { useJobs } from '@/contexts/jobs/JobsContext';
import useIntercom from '@/hooks/useIntercom';
import { EventCategory } from '@/services/intercomEvents';

import AppLayout from './AppLayout';
import JobDescriptionLayout from './JobDescriptionLayout';

const JobDescriptionCreationInner: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const currentStep = searchParams?.get('step');
  const isSuccess = currentStep === 'success';
  // Default to fullscreen mode unless explicitly set to false
  const isFullScreen = searchParams?.get('fullscreen') !== 'false';

  // Get Intercom tracking
  const { trackEvent } = useIntercom();

  // Get jobs context
  const { activeStep, job } = useJobs();

  // Log job data for debugging
  useEffect(() => {
    if (isHydrated && job) {
    }
  }, [isHydrated, job, activeStep]);

  // We've removed the duplicate draft modal handling logic from here
  // All draft modal handling is now done in JobsContext.tsx

  useEffect(() => {
    setIsHydrated(true);
    setIsPageLoading(false);

    // Set up resize observer to detect content changes
    const resizeObserver = new ResizeObserver(() => {
      // This will trigger re-renders when content size changes
      // which helps the parent component measure correctly
    });

    if (contentRef.current) {
      resizeObserver.observe(contentRef.current);
    }

    // Clean up
    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Add beforeunload event handler to warn when navigating away
  useEffect(() => {
    if (!isHydrated) return;

    // Only add the event listener if we're past step 1 and not on the success step
    const shouldWarnOnExit = activeStep > 0 && !isSuccess;

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (shouldWarnOnExit) {
        // Standard way to show a confirmation dialog
        e.preventDefault();

        // Store the current state in localStorage
        if (job) {
          localStorage.setItem('job', JSON.stringify(job));
          localStorage.setItem('activeStep', activeStep.toString());
        }

        // Modern browsers require a non-empty string to be returned
        const message = 'Changes you made may not be saved.';
        e.preventDefault();
        // @ts-ignore - TypeScript doesn't like this but it's the standard way
        return message;
      }
    };

    if (shouldWarnOnExit) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isHydrated, activeStep, isSuccess, job]);

  // Track step changes
  useEffect(() => {
    if (currentStep && isHydrated) {
      // Track the current step
      trackEvent('job_creation_step_viewed', EventCategory.JOB_MANAGEMENT, {
        step: currentStep,
        isSuccess: isSuccess,
      });
    }
  }, [currentStep, isHydrated, trackEvent, isSuccess]);

  useEffect(() => {
    if (!isHydrated) return;

    const requestInterceptor = axios.interceptors.request.use(
      config => {
        setIsLoading(true);

        // Track API request
        if (config.url && config.method) {
          trackEvent('job_creation_api_request', EventCategory.ENGAGEMENT, {
            url: config.url,
            method: config.method,
            step: currentStep || 'unknown',
          });
        }

        return config;
      },
      error => {
        setIsLoading(false);

        // Track API request error
        trackEvent('job_creation_api_request_error', EventCategory.ERROR, {
          error: error.message,
          step: currentStep || 'unknown',
        });

        return Promise.reject(error);
      }
    );

    const responseInterceptor = axios.interceptors.response.use(
      response => {
        setIsLoading(false);

        // Track API response success
        if (response.config.url) {
          trackEvent('job_creation_api_response_success', EventCategory.ENGAGEMENT, {
            url: response.config.url,
            status: response.status,
            step: currentStep || 'unknown',
          });
        }

        return response;
      },
      error => {
        setIsLoading(false);

        // Track API response error
        trackEvent('job_creation_api_response_error', EventCategory.ERROR, {
          url: error.config?.url || 'unknown',
          status: error.response?.status || 'unknown',
          error: error.message,
          step: currentStep || 'unknown',
        });

        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [isHydrated]);

  if (!isHydrated || isLoading || isPageLoading) {
    // Return a skeleton loader that matches the layout to prevent flashing
    return (
      <div className="w-full h-screen bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900">
        <div className="animate-pulse">
          {/* Header skeleton */}
          <div className="h-[200px] bg-gradient-to-b from-purple-900/30 to-purple-900/50">
            <div className="h-full flex items-center px-8 lg:px-12">
              <div className="max-w-7xl mx-auto w-full">
                <div className="flex items-center gap-6">
                  <div className="w-14 h-14 bg-white/10 rounded-xl"></div>
                  <div>
                    <div className="h-8 w-64 bg-white/10 rounded mb-2"></div>
                    <div className="h-5 w-96 bg-white/10 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Content skeleton */}
          <div className="p-6">
            <div className="max-w-7xl mx-auto">
              <div className="bg-black/10 backdrop-blur-md rounded-2xl p-6">
                <div className="space-y-4">
                  <div className="h-6 bg-white/10 rounded w-1/3"></div>
                  <div className="h-4 bg-white/10 rounded w-full"></div>
                  <div className="h-4 bg-white/10 rounded w-5/6"></div>
                  <div className="h-4 bg-white/10 rounded w-4/6"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If fullscreen mode, bypass AppLayout entirely
  if (isFullScreen) {
    return (
      <div
        ref={contentRef}
        className="w-full h-screen overflow-hidden transition-opacity duration-200"
      >
        <JobDescriptionLayout hideCard={isSuccess} fullScreen={true}>
          <StepContent />
        </JobDescriptionLayout>
      </div>
    );
  }

  return (
    <AppLayout>
      <div
        ref={contentRef}
        className="w-full h-full overflow-hidden transition-opacity duration-200"
      >
        <JobDescriptionLayout hideCard={isSuccess}>
          <StepContent />
        </JobDescriptionLayout>
      </div>
    </AppLayout>
  );
};

export default JobDescriptionCreationInner;
