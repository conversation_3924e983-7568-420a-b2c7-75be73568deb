'use client';

import React, { useContext, useEffect, useState } from 'react';

import { IncompleteProfileAlert } from '@/components/JobSeeker/components/IncompleteProfileAlert';
import { JobSeekerSetupSlider } from '@/components/JobSeeker/JobSeekerSetupSlider';
import { IJobSeekerProfile } from '@/components/JobSeeker/types';
import { OPEN_JOBSEEKER_SETUP } from '@/components/layout/ProfileCompletionIndicator';
import { useAuth0Token } from '@/hooks/useAuth0Token';
import useProactiveProfileValidation from '@/hooks/useProactiveProfileValidation';
import apiHelper, { logoutUser } from '@/lib/apiHelper';
import { useOnboardingStore } from '@/stores/onboardingStore';
import { UserRole } from '@/types/roles';
import PubSub from 'pubsub-js';
import { LoaderContext } from './AppLayout';
import BaseLayout from './BaseLayout';

interface JobSeekerLayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

// Extended interface for job seeker profile with LinkedIn data
interface ExtendedJobSeekerProfile extends Partial<IJobSeekerProfile> {
  linkedInProfile?: any;
}

const JobSeekerLayout: React.FC<JobSeekerLayoutProps> = ({ children, isLoading = false }) => {
  const { user, isLoading: userLoading } = useAuth0Token();
  const [initialStep, setInitialStep] = useState<string | undefined>(undefined);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const { setApiLoading } = useContext(LoaderContext);

  // Use Zustand store for onboarding management
  const {
    profile,
    isLoading: isCheckingProfile,
    shouldShowSetupSlider,
    hasCheckedOnboarding,
    fetchProfile,
    setShouldShowSetupSlider,
    markOnboardingComplete,
  } = useOnboardingStore();

  // Use proactive profile validation hook
  const { shouldShowAlert, dismissAlert, validation } = useProactiveProfileValidation(
    [UserRole.JOB_SEEKER],
    {
      checkOnMount: !shouldShowSetupSlider && hasCheckedOnboarding && !userLoading,
      minCompletionThreshold: 60, // Show alert if completion is below 60%
      dismissedStorageKey: 'jobSeekerProfileAlert',
    }
  );

  // Helper function to get initial data for setup slider
  const getInitialData = () => {
    const linkedInData = extractLinkedInData(user);
    const baseData = {
      firstName:
        profile?.firstName ||
        linkedInData.firstName ||
        user?.given_name ||
        user?.name?.split(' ')[0] ||
        '',
      lastName:
        profile?.lastName ||
        linkedInData.lastName ||
        user?.family_name ||
        user?.name?.split(' ').slice(1).join(' ') ||
        '',
      email: profile?.email || linkedInData.email || user?.email || '',
      ...linkedInData,
    };

    // Only include profile data that's compatible with the setup slider
    if (profile) {
      return {
        ...baseData,
        id: profile.id,
        userId: profile.userId,
        clientId: profile.clientId,
        fullName: profile.fullName,
        phone: profile.phone,
        location: profile.location,
        summary: profile.summary,
        skills: profile.skills,
        experience: profile.experience,
        education: profile.education,
        linkedinUrl: profile.linkedinUrl,
        githubUrl: profile.githubUrl,
        portfolioUrl: profile.portfolioUrl,
        myValues: profile.myValues,
        preferences: profile.preferences,
      };
    }

    return baseData;
  };

  // Listen for events to open the JobSeekerSetupSlider
  useEffect(() => {
    // Subscribe to the OPEN_JOBSEEKER_SETUP event
    const token = PubSub.subscribe(OPEN_JOBSEEKER_SETUP, (_, data) => {
      // If there's an initialStep specified, use it
      if (data && data.initialStep) {
        setInitialStep(data.initialStep);
      }
      // Show the setup slider
      setShouldShowSetupSlider(true);
    });

    // Cleanup subscription on unmount
    return () => {
      PubSub.unsubscribe(token);
    };
  }, [setShouldShowSetupSlider]);

  // Fetch profile and check onboarding status on mount
  useEffect(() => {
    if (user && !userLoading && !hasCheckedOnboarding) {
      fetchProfile();
    }
  }, [user, userLoading, hasCheckedOnboarding, fetchProfile]);

  // Add function to handle unauthorized error
  const handleUnauthorized = async () => {
    // Use centralized logout (without federated to avoid logging out of Google)
    await logoutUser();
  };

  // Update the effect with the new config
  useEffect(() => {
    if (!user) return;

    // Check for existing validation status in localStorage
    const skipValidation = localStorage.getItem(`skip_validation_${user.sub}`);
    if (skipValidation === 'true') {
      // User has already chosen to skip validation, continue loading
    } else {
      // Check if this is a new user by looking for profile existence
      const checkUserStatus = async () => {
        try {
          // Try to get the user's profile to determine if they're new
          const profileResponse = await apiHelper.get('/job-seekers');

          // If user has a profile and has completed onboarding, they're an existing user
          if (profileResponse && profileResponse.hasCompletedOnboarding) {
            // This is an existing user - check if validation is needed
            const lastLogin = localStorage.getItem(`last_login_${user.sub}`);
            const currentTime = Date.now();
            const validationInterval = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

            if (!lastLogin || currentTime - parseInt(lastLogin) > validationInterval) {
              // This is a fresh session for an existing user, show the validation modal
              setShowLogoutModal(true);
            } else {
              // Not a fresh session, just proceed without validation
              // Still mark validation as skipped to avoid future checks
              localStorage.setItem(`skip_validation_${user.sub}`, 'true');
            }

            // Update the last login time
            localStorage.setItem(`last_login_${user.sub}`, currentTime.toString());
          } else {
            // This is a new user or user hasn't completed onboarding
            // Skip validation modal and let them go through normal onboarding
            setShowLogoutModal(false);
            // Mark as skipped so they don't see the modal later
            localStorage.setItem(`skip_validation_${user.sub}`, 'true');
            localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
          }
        } catch (error) {
          // If there's an error fetching profile (e.g., 404 for new user), treat as new user
          setShowLogoutModal(false);
          // Mark as skipped so they don't see the modal later
          localStorage.setItem(`skip_validation_${user.sub}`, 'true');
          localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
        }
      };

      checkUserStatus();
    }
  }, [user]);

  // Extract LinkedIn profile data if available
  const extractLinkedInData = (user: any): ExtendedJobSeekerProfile => {
    if (!user || !user.linkedInProfile?.profile) return {};

    const linkedInProfile = user.linkedInProfile.profile;

    // Map LinkedIn profile data to job seeker profile fields
    const profileData: ExtendedJobSeekerProfile = {
      // Basic info
      firstName:
        linkedInProfile.firstName || linkedInProfile.given_name || user.name?.split(' ')[0] || '',
      lastName:
        linkedInProfile.lastName ||
        linkedInProfile.family_name ||
        user.name?.split(' ').slice(1).join(' ') ||
        '',
      email: linkedInProfile.email || user.email || '',

      // Professional info
      linkedinUrl:
        linkedInProfile.publicProfileUrl ||
        `https://www.linkedin.com/in/${linkedInProfile.vanityName || ''}`,

      // Summary/headline if available
      summary: linkedInProfile.headline || linkedInProfile.summary || '',

      // Location
      location: linkedInProfile.locationName || linkedInProfile.location?.name || '',

      // Profile image - store in a property that will be used by the backend
      resumeUrl: linkedInProfile.pictureUrl || linkedInProfile.picture || user.picture || '',

      // Skills - if available
      skills: Array.isArray(linkedInProfile?.skills)
        ? linkedInProfile.skills.map((skill: any) => skill.name || skill)
        : [],

      // Experience - if available
      experience: Array.isArray(linkedInProfile?.positions?.values)
        ? linkedInProfile.positions.values.map((position: any) => ({
            companyName: position.company?.name || '',
            title: position.title || '',
            location: position.location?.name || '',
            startDate:
              position.startDate && position.startDate.year && position.startDate.month
                ? new Date(position.startDate.year, position.startDate.month - 1).toISOString()
                : null,
            endDate:
              position.endDate && position.endDate.year && position.endDate.month
                ? new Date(position.endDate.year, position.endDate.month - 1).toISOString()
                : null,
            current: position.isCurrent || false,
            description: position.summary || '',
          }))
        : [],

      // Education - if available
      education: Array.isArray(linkedInProfile?.educations?.values)
        ? linkedInProfile.educations.values.map((education: any) => ({
            institution: education.schoolName || '',
            degree: education.degree || '',
            field: education.fieldOfStudy || '',
            startDate: education.startDate
              ? new Date(education.startDate.year, education.startDate.month - 1).toISOString()
              : null,
            endDate: education.endDate
              ? new Date(education.endDate.year, education.endDate.month - 1).toISOString()
              : null,
            description: education.notes || '',
          }))
        : [],

      // Store the raw LinkedIn profile for reference
      linkedInProfile: linkedInProfile,
    };

    return profileData;
  };

  // Function to run validation when user chooses to continue
  const runProfileValidation = async () => {
    if (!user) return;
    setShowLogoutModal(false);

    // Set up an abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      setApiLoading(true);

      // Use our cached API hook with abort signal
      const validation = await apiHelper.post(`/job-seekers/validate-profile`, user, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      // Clear the timeout since request completed
      clearTimeout(timeoutId);

      // Show setup slider if profile is invalid (has mandatory missing fields)
      setShouldShowSetupSlider(!validation.isValid);

      // Update the profile in the store if we have validation data
      if (validation.record) {
        // The store will automatically determine if setup slider should show
        // based on hasCompletedOnboarding status
      }

      // Save to localStorage that validation was successful
      localStorage.setItem(`skip_validation_${user.sub}`, 'true');
    } catch (error: any) {
      // Clear the timeout
      clearTimeout(timeoutId);

      console.error('Error checking job seeker profile:', error);

      // Handle AbortError (timeout)
      if (error.name === 'AbortError') {
        console.warn('Profile validation request timed out');
      }

      // Handle unauthorized (403) errors by logging out
      if (error?.response?.status === 403) {
        await handleUnauthorized();
        return;
      }

      // For all errors, show setup slider as fallback
      setShouldShowSetupSlider(true);
    } finally {
      setApiLoading(false);
    }
  };

  // Function to handle modal continue button - skip validation
  const handleContinueAnyway = () => {
    // Store the skip validation preference in localStorage
    if (user?.sub) {
      localStorage.setItem(`skip_validation_${user.sub}`, 'true');
      // Also update last login time
      localStorage.setItem(`last_login_${user.sub}`, Date.now().toString());
    }

    setShowLogoutModal(false);
  };

  const handleJobSeekerSetup = async () => {
    try {
      setApiLoading(true);
      // Mark onboarding as complete in the store
      markOnboardingComplete();
      // Refresh the profile data
      await fetchProfile();
    } catch (error) {
      console.error('Error saving job seeker profile:', error);
    } finally {
      setApiLoading(false);
    }
  };

  return (
    <>
      <BaseLayout
        userRole={UserRole.JOB_SEEKER}
        isLoading={isLoading || (isCheckingProfile && !shouldShowSetupSlider)}
      >
        {children}
      </BaseLayout>

      {/* Proactive Profile Validation Alert */}
      {shouldShowAlert && validation && (
        <IncompleteProfileAlert validation={validation} onClose={dismissAlert} />
      )}

      {/* Logout Modal */}
      {/* {showLogoutModal && (
        <LogoutModal
          onContinue={runProfileValidation}
          onLogout={handleUnauthorized}
          onClose={handleContinueAnyway}
          message="We need to validate your profile data to ensure you have access to all features. Would you like to run validation now, skip it, or sign out and sign back in?"
        />
      )} */}

      {/* Job Seeker Setup Slider */}
      {shouldShowSetupSlider && (
        <JobSeekerSetupSlider
          onComplete={handleJobSeekerSetup}
          onClose={() => setShouldShowSetupSlider(false)}
          initialData={getInitialData() as any}
          initialStep={initialStep}
        />
      )}
    </>
  );
};

export default JobSeekerLayout;
