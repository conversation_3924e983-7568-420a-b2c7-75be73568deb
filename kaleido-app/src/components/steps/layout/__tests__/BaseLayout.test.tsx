import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import BaseLayout from '../BaseLayout';
import { UserRole } from '@/types/roles';
import { useAuth0Token } from '@/hooks/useAuth0Token';
import { useUserRoles } from '@/hooks/useUserRoles';
import { useJobs } from '@/contexts/jobs/JobsContext';
import { getNavItemsByRoles } from '@/lib/navigation';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
  useSearchParams: jest.fn(),
}));
jest.mock('@/hooks/useAuth0Token');
jest.mock('@/hooks/useUserRoles');
jest.mock('@/contexts/jobs/JobsContext');
jest.mock('@/lib/navigation', () => ({
  ...jest.requireActual('@/lib/navigation'),
  getNavItemsByRoles: jest.fn(),
}));

// Mock components
jest.mock('@/components/layout/Header', () => ({
  Header: ({ userRole }: any) => <div data-testid="header">Header - {userRole}</div>,
}));

jest.mock('@/components/layout/Sidebar', () => ({
  Sidebar: ({ navItems }: any) => (
    <div data-testid="sidebar">
      {navItems.map((item: any) => (
        <div key={item.href} data-testid={`nav-${item.label}`}>
          {item.label}
        </div>
      ))}
    </div>
  ),
}));

jest.mock('@/components/layout/MobileMenu', () => ({
  MobileMenu: ({ navItems }: any) => (
    <div data-testid="mobile-menu">
      {navItems.map((item: any) => (
        <div key={item.href} data-testid={`mobile-nav-${item.label}`}>
          {item.label}
        </div>
      ))}
    </div>
  ),
}));

jest.mock('@/components/navigation/ReferralPartnerGuard', () => ({
  ReferralPartnerGuard: ({ children }: any) => <>{children}</>,
}));

const mockUseRouter = useRouter as jest.Mock;
const mockUsePathname = usePathname as jest.Mock;
const mockUseSearchParams = useSearchParams as jest.Mock;
const mockUseAuth0Token = useAuth0Token as jest.Mock;
const mockUseUserRoles = useUserRoles as jest.Mock;
const mockUseJobs = useJobs as jest.Mock;
const mockGetNavItemsByRoles = getNavItemsByRoles as jest.Mock;

describe('BaseLayout - Dual Role Navigation', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    });

    // Setup default mocks
    mockUseRouter.mockReturnValue({
      push: jest.fn(),
      replace: jest.fn(),
    });
    mockUsePathname.mockReturnValue('/dashboard');
    mockUseSearchParams.mockReturnValue(new URLSearchParams());
    mockUseAuth0Token.mockReturnValue({
      user: { sub: 'user123', email: '<EMAIL>' },
      isLoading: false,
    });
    mockUseJobs.mockReturnValue({
      job: null,
      setFullJobDescription: jest.fn(),
    });

    // Clear localStorage
    localStorage.clear();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  describe('Single Role Navigation', () => {
    it('should show only job seeker navigation items for job seeker role', async () => {
      const jobSeekerNavItems = [
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.JOB_SEEKER] },
        { label: 'My Applications', href: '/applications', roles: [UserRole.JOB_SEEKER] },
        { label: 'Profile', href: '/profile', roles: [UserRole.JOB_SEEKER] },
      ];

      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [],
        isLoading: false,
      });

      mockGetNavItemsByRoles.mockReturnValue(jobSeekerNavItems);

      render(
        <BaseLayout userRole={UserRole.JOB_SEEKER}>
          <div>Test Content</div>
        </BaseLayout>,
        { wrapper }
      );

      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument();
      });

      // Verify correct navigation items are shown
      expect(screen.getByTestId('nav-Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('nav-My Applications')).toBeInTheDocument();
      expect(screen.getByTestId('nav-Profile')).toBeInTheDocument();

      // Verify getNavItemsByRoles was called with correct parameters
      expect(mockGetNavItemsByRoles).toHaveBeenCalledWith(UserRole.JOB_SEEKER, []);
    });

    it('should show only employer navigation items for employer role', async () => {
      const employerNavItems = [
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.EMPLOYER] },
        { label: 'Jobs', href: '/jobs', roles: [UserRole.EMPLOYER] },
        { label: 'Talent Hub', href: '/talent-hub', roles: [UserRole.EMPLOYER] },
        { label: 'Company Settings', href: '/company-settings', roles: [UserRole.EMPLOYER] },
      ];

      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.EMPLOYER,
        additionalRoles: [],
        isLoading: false,
      });

      mockGetNavItemsByRoles.mockReturnValue(employerNavItems);

      render(
        <BaseLayout userRole={UserRole.EMPLOYER}>
          <div>Test Content</div>
        </BaseLayout>,
        { wrapper }
      );

      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument();
      });

      expect(screen.getByTestId('nav-Jobs')).toBeInTheDocument();
      expect(screen.getByTestId('nav-Talent Hub')).toBeInTheDocument();
      expect(screen.queryByTestId('nav-My Applications')).not.toBeInTheDocument();
    });
  });

  describe('Dual Role Navigation', () => {
    it('should show combined navigation for job seeker with referral partner role', async () => {
      const combinedNavItems = [
        // Job seeker items
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.JOB_SEEKER] },
        { label: 'My Applications', href: '/applications', roles: [UserRole.JOB_SEEKER] },
        { label: 'Profile', href: '/profile', roles: [UserRole.JOB_SEEKER] },
        // Referral partner items
        {
          label: 'Partner Dashboard',
          href: '/referral-partner',
          roles: [UserRole.REFERRAL_PARTNER],
        },
        {
          label: 'Open Job Board',
          href: '/referral-partner/jobs',
          roles: [UserRole.REFERRAL_PARTNER],
        },
        {
          label: 'Earnings',
          href: '/referral-partner/earnings',
          roles: [UserRole.REFERRAL_PARTNER],
        },
      ];

      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        isLoading: false,
      });

      mockGetNavItemsByRoles.mockReturnValue(combinedNavItems);

      render(
        <BaseLayout userRole={UserRole.JOB_SEEKER}>
          <div>Test Content</div>
        </BaseLayout>,
        { wrapper }
      );

      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument();
      });

      // Verify job seeker items
      expect(screen.getByTestId('nav-My Applications')).toBeInTheDocument();

      // Verify referral partner items
      expect(screen.getByTestId('nav-Partner Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('nav-Open Job Board')).toBeInTheDocument();
      expect(screen.getByTestId('nav-Earnings')).toBeInTheDocument();

      // Verify getNavItemsByRoles was called with both roles
      expect(mockGetNavItemsByRoles).toHaveBeenCalledWith(UserRole.JOB_SEEKER, [
        UserRole.REFERRAL_PARTNER,
      ]);
    });

    it('should show combined navigation for employer with referral partner role', async () => {
      const combinedNavItems = [
        // Employer items
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.EMPLOYER] },
        { label: 'Jobs', href: '/jobs', roles: [UserRole.EMPLOYER] },
        { label: 'Talent Hub', href: '/talent-hub', roles: [UserRole.EMPLOYER] },
        { label: 'Company Settings', href: '/company-settings', roles: [UserRole.EMPLOYER] },
        // Referral partner items
        {
          label: 'Partner Dashboard',
          href: '/referral-partner',
          roles: [UserRole.REFERRAL_PARTNER],
        },
        {
          label: 'Open Job Board',
          href: '/referral-partner/jobs',
          roles: [UserRole.REFERRAL_PARTNER],
        },
        {
          label: 'Earnings',
          href: '/referral-partner/earnings',
          roles: [UserRole.REFERRAL_PARTNER],
        },
      ];

      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.EMPLOYER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        isLoading: false,
      });

      mockGetNavItemsByRoles.mockReturnValue(combinedNavItems);

      render(
        <BaseLayout userRole={UserRole.EMPLOYER}>
          <div>Test Content</div>
        </BaseLayout>,
        { wrapper }
      );

      await waitFor(() => {
        expect(screen.getByTestId('sidebar')).toBeInTheDocument();
      });

      // Verify employer items
      expect(screen.getByTestId('nav-Jobs')).toBeInTheDocument();
      expect(screen.getByTestId('nav-Talent Hub')).toBeInTheDocument();

      // Verify referral partner items
      expect(screen.getByTestId('nav-Partner Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('nav-Open Job Board')).toBeInTheDocument();
      expect(screen.getByTestId('nav-Earnings')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should handle loading state while fetching additional roles', async () => {
      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: undefined,
        isLoading: true,
      });

      mockGetNavItemsByRoles.mockReturnValue([
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.JOB_SEEKER] },
      ]);

      render(
        <BaseLayout userRole={UserRole.JOB_SEEKER}>
          <div>Test Content</div>
        </BaseLayout>,
        { wrapper }
      );

      // Should still render with empty additional roles while loading
      expect(mockGetNavItemsByRoles).toHaveBeenCalledWith(UserRole.JOB_SEEKER, []);
    });
  });

  describe('Mobile Navigation', () => {
    it('should show combined navigation in mobile menu', async () => {
      const combinedNavItems = [
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.JOB_SEEKER] },
        { label: 'My Applications', href: '/applications', roles: [UserRole.JOB_SEEKER] },
        {
          label: 'Partner Dashboard',
          href: '/referral-partner',
          roles: [UserRole.REFERRAL_PARTNER],
        },
        {
          label: 'Earnings',
          href: '/referral-partner/earnings',
          roles: [UserRole.REFERRAL_PARTNER],
        },
      ];

      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        isLoading: false,
      });

      mockGetNavItemsByRoles.mockReturnValue(combinedNavItems);

      // Mock window width for mobile
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });

      render(
        <BaseLayout userRole={UserRole.JOB_SEEKER}>
          <div>Test Content</div>
        </BaseLayout>,
        { wrapper }
      );

      await waitFor(() => {
        expect(screen.getByTestId('mobile-menu')).toBeInTheDocument();
      });

      // Verify mobile navigation items
      expect(screen.getByTestId('mobile-nav-My Applications')).toBeInTheDocument();
      expect(screen.getByTestId('mobile-nav-Partner Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('mobile-nav-Earnings')).toBeInTheDocument();
    });
  });

  describe('Dynamic Role Updates', () => {
    it('should update navigation when additional roles change', async () => {
      // Initial state - no additional roles
      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [],
        isLoading: false,
      });

      mockGetNavItemsByRoles.mockReturnValue([
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.JOB_SEEKER] },
        { label: 'My Applications', href: '/applications', roles: [UserRole.JOB_SEEKER] },
      ]);

      const { rerender } = render(
        <BaseLayout userRole={UserRole.JOB_SEEKER}>
          <div>Test Content</div>
        </BaseLayout>,
        { wrapper }
      );

      await waitFor(() => {
        expect(screen.getByTestId('nav-Dashboard')).toBeInTheDocument();
        expect(screen.getByTestId('nav-My Applications')).toBeInTheDocument();
        expect(screen.queryByTestId('nav-Partner Dashboard')).not.toBeInTheDocument();
      });

      // Update to include referral partner role
      mockUseUserRoles.mockReturnValue({
        primaryRole: UserRole.JOB_SEEKER,
        additionalRoles: [UserRole.REFERRAL_PARTNER],
        isLoading: false,
      });

      mockGetNavItemsByRoles.mockReturnValue([
        { label: 'Dashboard', href: '/dashboard', roles: [UserRole.JOB_SEEKER] },
        { label: 'My Applications', href: '/applications', roles: [UserRole.JOB_SEEKER] },
        {
          label: 'Partner Dashboard',
          href: '/referral-partner',
          roles: [UserRole.REFERRAL_PARTNER],
        },
        {
          label: 'Earnings',
          href: '/referral-partner/earnings',
          roles: [UserRole.REFERRAL_PARTNER],
        },
      ]);

      rerender(
        <BaseLayout userRole={UserRole.JOB_SEEKER}>
          <div>Test Content</div>
        </BaseLayout>
      );

      await waitFor(() => {
        expect(screen.getByTestId('nav-Partner Dashboard')).toBeInTheDocument();
        expect(screen.getByTestId('nav-Earnings')).toBeInTheDocument();
      });
    });
  });
});
