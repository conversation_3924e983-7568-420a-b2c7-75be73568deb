'use client';

import '../../../styles/theming.css';

import { NavItem, getGradientByPath, getNavItemsByRoles } from '@/lib/navigation';
import { AnimatePresence, motion } from 'framer-motion';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { Suspense, useEffect, useMemo, useState } from 'react';

import { Header } from '@/components/layout/Header';
import { MobileMenu } from '@/components/layout/MobileMenu';
import { Sidebar } from '@/components/layout/Sidebar';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { ReferralPartnerGuard } from '@/components/navigation/ReferralPartnerGuard';
import { createMatchRankConfig } from '@/components/shared/GenericStatusManager/configs/matchRankConfig';
import { GenericStatusManager } from '@/components/shared/GenericStatusManager/GenericStatusManager';
import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';

import { SIDEBAR } from '@/constants/layout';
import { useJobs } from '@/contexts/jobs/JobsContext';
import { useAuth0Token } from '@/hooks/useAuth0Token';
import { useUserRoles } from '@/hooks/useUserRoles';
import { UserRole } from '@/types/roles';
import { handleMatchRankNavigationComplete } from '@/utils/candidateNavigation';
import PreviewContent from '../preview/PreviewContent';

interface BaseLayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
  userRole: UserRole;
}

// Gradient background component
const GradientBackground = () => {
  // Use CSS variables directly without the theme hook
  return (
    <motion.div
      className={`fixed inset-0 z-0 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-[var(--background-start)] via-[var(--background-middle)] to-[var(--background-end)] after:absolute after:inset-0 after:bg-[radial-gradient(circle_at_70%_-20%,var(--background-accent),transparent_70%)] after:mix-blend-soft-light`}
      initial={false}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    />
  );
};

const BaseLayout: React.FC<BaseLayoutProps> = ({ children, userRole }) => {
  const { user, isLoading: userLoading } = useAuth0Token();
  const { additionalRoles, isLoading: rolesLoading } = useUserRoles();
  const { job, setFullJobDescription } = useJobs();
  const [isChecking, setIsChecking] = useState(false);
  const searchParams = useSearchParams() ?? new URLSearchParams();
  const pathname = usePathname() ?? '/';
  const router = useRouter();
  const [showApprovalModal, setShowApprovalModal] = useState(false);

  const [expanded, setExpanded] = useState(() => {
    // Initialize from localStorage on mount
    if (typeof window !== 'undefined') {
      const savedExpanded = localStorage.getItem('sidebarExpanded');
      return savedExpanded !== null ? savedExpanded === 'true' : false;
    }
    return false;
  });

  const [currentGradient, setCurrentGradient] = useState<string>(getGradientByPath(pathname));
  const currentStep = searchParams?.get('step');
  const isPublishedStep = pathname === '/job-description-creation' && currentStep === 'published';
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 0
  );
  const previewWidth = windowWidth < 768 ? '100%' : windowWidth < 1024 ? '45%' : '40%';
  const widgetWidth = windowWidth < 768 ? '100%' : windowWidth < 1024 ? '40%' : '30%';

  // Status manager stores
  const {
    jobs: matchRankJobs,
    activeJobs: activeMatchRankJobs,
    updateJobStatus: updateMatchRankJob,
    removeJob: removeMatchRankJob,
  } = useMatchRankJobsStore();

  // Convert MatchRankJob to StatusJob format
  const convertedMatchRankJobs = Object.fromEntries(
    Object.entries(matchRankJobs).map(([key, job]) => [
      key,
      {
        id: job.jobId,
        jobId: job.jobId,
        status: job.status,
        progress: job.progress,
        result: job.result,
        error: job.error,
        message: job.message,
        createdAt: job.createdAt,
        notifiedCompletion: job.notifiedCompletion,
        errorCount: job.errorCount,
        metadata: job.metadata || {},
      },
    ])
  );

  // Get preview state from URL
  const [isWidgetOpenState, setIsWidgetOpenState] = useState(false);

  const isFinalDraft = pathname === '/job-description-creation' && currentStep === 'final-draft';
  const isPreviewOpen = searchParams?.get('preview') === 'on' && !isFinalDraft;

  // Control widget visibility - explicitly set to false on published step to prevent side panel
  useEffect(() => {
    if (isPublishedStep) {
      // Force widget to be closed on the published step to allow full-width success screen
      setIsWidgetOpenState(false);
    } else {
      setIsWidgetOpenState(false);
    }
  }, [isPublishedStep]);

  // Add effect to save job changes to localStorage
  useEffect(() => {
    if (job && Object.keys(job).length > 0) {
      job.clientId = user?.sub;
      localStorage.setItem('job', JSON.stringify(job));
    }
  }, [job, user?.sub]);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Modify the localStorage effects
  useEffect(() => {
    // Load from localStorage on initial mount only
    const loadFromStorage = () => {
      const storedData = localStorage.getItem('job');
      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData);
          // Only set if we have data and it's different from current job
          if (
            Object.keys(parsedData).length > 0 &&
            JSON.stringify(parsedData) !== JSON.stringify(job)
          ) {
            setFullJobDescription(parsedData);
          }
        } catch (error) {
          console.error('Error parsing stored job description:', error);
        }
      }
    };

    loadFromStorage();
  }, []); // Keep empty dependency array for initial load

  // Check company approval status
  useEffect(() => {
    if (!user?.sub || userRole !== UserRole.EMPLOYER) {
      // Only check approval for employer users
      return;
    }

    try {
      const cachedCompanyData = localStorage.getItem('api_cache_/companies/client');
      if (cachedCompanyData) {
        const parsedCache = JSON.parse(cachedCompanyData);
        // The actual company data is nested inside 'data' property
        const companyData = parsedCache.data;

        // Check if company is approved
        if (companyData && (companyData.isApproved === false || companyData.isApproved === null)) {
          setShowApprovalModal(true);
        } else {
          setShowApprovalModal(false);
        }
      }
    } catch (error) {
      console.error('Error checking company approval status:', error);
    }
  }, [user?.sub, userRole]);

  // Modify the step change handler to only save, not load
  useEffect(() => {
    if (!currentStep) return;

    // Save current state to localStorage when step changes
    if (Object.keys(job).length > 0) {
      job.clientId = user?.sub;
      localStorage.setItem('job', JSON.stringify(job));
    }
  }, [currentStep, job]); // Remove setFullJobDescription from dependencies

  // Get user role from localStorage - memoized to prevent unnecessary recalculations
  const userRoleMemo = useMemo(() => {
    return userRole; // Now we use the provided userRole
  }, [userRole]);

  // Filter navigation items based on user role - memoized to prevent unnecessary recalculations
  const filteredNavItems = useMemo(() => {
    // Use getNavItemsByRoles now that we have additionalRoles from useUserRoles hook
    const items = getNavItemsByRoles(userRoleMemo, additionalRoles || []);

    // Check for company data in localStorage
    let companyData = null;
    if (typeof window !== 'undefined') {
      try {
        const cachedCompanyData = localStorage.getItem('api_cache_/companies/client');
        if (cachedCompanyData) {
          const parsedCache = JSON.parse(cachedCompanyData);
          // The actual company data is nested inside 'data' property
          companyData = parsedCache.data;
        }
      } catch (error) {
        console.error('Error parsing company data from localStorage:', error);
      }
    }

    // Filter out ATS Integration if no API key
    const filteredItems = items.filter(item => {
      // Use type assertion to allow string comparison
      const href = item.href as string;
      if (href === '/ats-integration') {
        // Show ATS Integration if we have any atsConfig regardless of apiKey value
        const shouldShow = Boolean(companyData && companyData.atsConfig);
        //
        return shouldShow;
      }
      return true;
    });

    return filteredItems as NavItem[];
  }, [userRoleMemo, additionalRoles]);

  // Separate referral partner items for conditional rendering
  const { referralPartnerItems, otherNavItems } = useMemo(() => {
    const referralItems = filteredNavItems.filter((item: NavItem) =>
      (item.roles as unknown as UserRole[]).includes(UserRole.REFERRAL_PARTNER)
    );
    const otherItems = filteredNavItems.filter(
      (item: NavItem) => !(item.roles as unknown as UserRole[]).includes(UserRole.REFERRAL_PARTNER)
    );
    return { referralPartnerItems: referralItems, otherNavItems: otherItems };
  }, [filteredNavItems]);

  // Final navigation items to pass to sidebar - includes referral partner items if user has that role
  const finalNavItems = useMemo(() => {
    // If user is referral partner, show all items (guard will handle activation check)
    if (
      userRoleMemo === UserRole.REFERRAL_PARTNER ||
      additionalRoles?.includes(UserRole.REFERRAL_PARTNER)
    ) {
      return filteredNavItems;
    }

    // Otherwise, show only non-referral partner items
    return otherNavItems;
  }, [filteredNavItems, otherNavItems, userRoleMemo, additionalRoles]);

  // Optimize layout transitions
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent layout shift during hydration
  if (!mounted) {
    return null;
  }

  // If still loading user info or checking role, show loading state
  if (userLoading || isChecking || rolesLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ColorfulSmokeyOrbLoader />
      </div>
    );
  }

  // Handle logout for approval pending modal

  return (
    <div
      className="relative min-h-screen bg-transparent"
      data-testid={`${userRole.toLowerCase()}-layout`}
    >
      {/* Render ApprovalPendingModal if company is not approved */}
      {/* {showApprovalModal && userRole === UserRole.EMPLOYER && (
        <ApprovalPendingModal
          isOpen={showApprovalModal}
          onLogout={handleLogout}
          userType="employer"
        />
      )} */}

      <AnimatePresence mode="wait">
        <GradientBackground />
      </AnimatePresence>

      {/* Add GenericStatusManager component */}
      {(userRole === UserRole.EMPLOYER ||
        userRole === UserRole.ADMIN ||
        userRole === UserRole.SUPER_ADMIN) && (
        <GenericStatusManager
          jobs={convertedMatchRankJobs}
          activeJobs={activeMatchRankJobs}
          config={createMatchRankConfig()}
          onUpdateJob={(jobId: string, updates: any) => {
            updateMatchRankJob(jobId, updates.status, updates);
          }}
          onRemoveJob={removeMatchRankJob}
          onComplete={async jobId => {
            // Navigate based on whether candidates exist
            await handleMatchRankNavigationComplete(router, jobId, true);
          }}
        />
      )}

      {/* Wrap main content in Suspense boundary */}
      <Suspense fallback={<ColorfulSmokeyOrbLoader text="Loading..." useModalBg={false} />}>
        <motion.div
          className="relative bg-transparent"
          initial={false}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className="min-h-screen flex bg-transparent">
            {/* Hide sidebar and header on my-jobs page */}
            {pathname !== '/my-jobs' && (
              <>
                {/* Hide sidebar on mobile/medium devices */}
                <div className="hidden lg:block">
                  {userRoleMemo === UserRole.REFERRAL_PARTNER ||
                  additionalRoles?.includes(UserRole.REFERRAL_PARTNER) ? (
                    <ReferralPartnerGuard>
                      <Sidebar
                        expanded={expanded}
                        setExpanded={setExpanded}
                        pathname={pathname}
                        navItems={finalNavItems}
                      />
                    </ReferralPartnerGuard>
                  ) : (
                    <Sidebar
                      expanded={expanded}
                      setExpanded={setExpanded}
                      pathname={pathname}
                      navItems={finalNavItems}
                    />
                  )}
                </div>

                {userRoleMemo === UserRole.REFERRAL_PARTNER ||
                additionalRoles?.includes(UserRole.REFERRAL_PARTNER) ? (
                  <ReferralPartnerGuard>
                    <MobileMenu
                      isMobileMenuOpen={isMobileMenuOpen}
                      setIsMobileMenuOpen={setIsMobileMenuOpen}
                      pathname={pathname}
                      navItems={finalNavItems}
                      expanded={expanded}
                      setExpanded={setExpanded}
                    />
                  </ReferralPartnerGuard>
                ) : (
                  <MobileMenu
                    isMobileMenuOpen={isMobileMenuOpen}
                    setIsMobileMenuOpen={setIsMobileMenuOpen}
                    pathname={pathname}
                    navItems={finalNavItems}
                    expanded={expanded}
                    setExpanded={setExpanded}
                  />
                )}
              </>
            )}

            {/* Main Content Area - Adjusted for sidebar width */}
            <div className="flex-1 h-screen bg-transparent">
              <motion.div
                initial={false} // Prevent initial animation
                animate={{
                  marginLeft:
                    pathname === '/my-jobs' || windowWidth < 1024
                      ? '0px'
                      : expanded
                        ? SIDEBAR.EXPANDED_WIDTH
                        : SIDEBAR.COLLAPSED_WIDTH,
                }}
                transition={{
                  duration: 0.3,
                  type: 'spring',
                  damping: 25,
                  stiffness: 120,
                }}
                className="relative flex flex-col h-full bg-transparent"
                style={{
                  width:
                    pathname === '/my-jobs' || windowWidth < 1024
                      ? '100%'
                      : `calc(100% - ${expanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH})`,
                }}
              >
                <div className="relative flex flex-col h-full bg-transparent backdrop-filter backdrop-blur-none">
                  {pathname !== '/my-jobs' && (
                    <Header
                      pathname={pathname}
                      navItems={finalNavItems}
                      expanded={expanded}
                      setExpanded={setExpanded}
                      // isWidgetOpen={isWidgetOpenState}
                      // toggleWidget={toggleWidget}
                    />
                  )}

                  {/* Main content wrapper */}
                  <div
                    className={`flex-1 flex flex-row relative ${pathname === '/my-jobs' ? 'mt-0' : userRole === UserRole.REFERRAL_PARTNER ? 'mt-0' : 'mt-2'} overflow-hidden bg-transparent`}
                  >
                    <motion.main
                      animate={{
                        width: isPreviewOpen
                          ? `calc(100% - ${previewWidth})`
                          : isWidgetOpenState && isPublishedStep
                            ? `calc(100% - ${widgetWidth})`
                            : '100%',
                      }}
                      transition={{
                        duration: 0.3,
                        type: 'spring',
                        damping: 25,
                        stiffness: 120,
                      }}
                      className={`relative flex-1 flex flex-col h-full ${isPublishedStep ? '' : 'overflow-auto'} pr-0 bg-transparent`}
                      style={{
                        maxWidth: isPreviewOpen
                          ? `calc(100% - ${previewWidth})`
                          : isWidgetOpenState && isPublishedStep
                            ? `calc(100% - ${widgetWidth})`
                            : '100%',
                      }}
                    >
                      {/* Wrap referral partner page content with guard */}
                      {pathname.startsWith('/referral-partner') ? (
                        <ReferralPartnerGuard>{children}</ReferralPartnerGuard>
                      ) : (
                        children
                      )}
                    </motion.main>

                    {/* Preview Panel */}
                    {isPreviewOpen && (
                      <motion.div
                        initial={{ opacity: 0, x: 100 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 100 }}
                        transition={{ duration: 0.3 }}
                        className="fixed right-0 top-0 h-screen z-20 overflow-auto ml-0 border-l border-white/10"
                        style={{ width: `calc(${previewWidth} - 4px)` }}
                      >
                        <div className="h-full bg-black/20 backdrop-blur-md p-4 overflow-y-auto">
                          <PreviewContent />
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </Suspense>
    </div>
  );
};

export default BaseLayout;
