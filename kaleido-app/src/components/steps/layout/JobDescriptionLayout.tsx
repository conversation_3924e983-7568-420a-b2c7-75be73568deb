import { useSearchParams } from 'next/navigation';
import React, { Suspense, useRef } from 'react';

import StepFooter from './StepFooter';

interface JobDescriptionLayoutProps {
  children: React.ReactNode;
  hideCard?: boolean; // New prop to explicitly hide the card
  fullScreen?: boolean; // New prop for full screen mode
}

const JobDescriptionLayout: React.FC<JobDescriptionLayoutProps> = ({
  children,
  hideCard = false, // Default to showing the card
  fullScreen = false,
}) => {
  const searchParams = useSearchParams();
  const currentStep = searchParams?.get('step');
  const isPublished = currentStep === 'published';
  const isSuccess = currentStep === 'success'; // Add explicit check for success step
  const showFooter = currentStep !== undefined && !isPublished && !isSuccess; // Don't show footer on success either

  // Determine if we should hide the card container
  const shouldHideCard = hideCard || isSuccess;

  // Create refs for content and container
  const contentRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // For full screen mode, render children directly without any wrapper
  if (fullScreen) {
    return <Suspense>{children}</Suspense>;
  }

  return (
    <div className="flex flex-col h-full bg-transparent">
      <div className="relative flex flex-col flex-grow bg-transparent">
        <main className="flex-grow relative bg-transparent" ref={containerRef}>
          <div
            className={`h-full ${isPublished ? 'p-0' : 'p-2 sm:p-3 md:p-4'} bg-transparent ${isPublished || shouldHideCard ? 'flex items-center justify-center' : ''} w-full`}
            ref={contentRef}
          >
            {shouldHideCard || isPublished ? (
              // Render children directly without the card container
              <Suspense>{children}</Suspense>
            ) : (
              // Render children with the card container
              <div
                className={`relative backdrop-blur-md bg-black/10 rounded-2xl p-3 sm:p-4 md:p-5
                shadow-[0_4px_20px_rgba(0,0,0,0.2)] h-auto w-full`}
              >
                {/* Content wrapper with enhanced text legibility */}
                <div className="relative z-10 text-white/90">
                  <div className="space-y-4 [&_*]:text-shadow-sm [&_*]:shadow-black/50 min-h-[300px]">
                    <Suspense>{children}</Suspense>
                  </div>
                </div>

                {/* Very subtle gradient overlay for depth */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-b from-white/5 to-transparent pointer-events-none" />
              </div>
            )}
          </div>
        </main>
      </div>
      {showFooter && (
        <div className="relative z-20">
          <StepFooter />
        </div>
      )}
    </div>
  );
};

export default JobDescriptionLayout;
