'use client';

import React, { useContext, useEffect, useState } from 'react';

import { useAuth0Token } from '@/hooks/useAuth0Token';
import { apiClient as apiHelper, logoutUser } from '@/lib/apiHelper';
import { CompanySetupFormData } from '@/stores/companySetupStore';
import { useCompanyStore } from '@/stores/companyStore';
import { UserRole } from '@/types/roles';

// Type alias for compatibility
type CompanySetupData = CompanySetupFormData & {
  id?: string;
  onboardingRequired?: boolean;
};

import { LoaderContext } from './AppLayout';
import BaseLayout from './BaseLayout';

interface AdminLayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children, isLoading = false }) => {
  const { user, isLoading: userLoading } = useAuth0Token();
  const [showCompanySetup, setShowCompanySetup] = useState(false);
  const [initialData, setInitialData] = useState<Partial<CompanySetupData>>({});
  const [isChecking, setIsChecking] = useState(true);
  const { setApiLoading } = useContext(LoaderContext);
  const [hasError, setHasError] = useState(false);

  // Add function to handle unauthorized error
  const handleUnauthorized = async () => {
    // Use centralized logout (without federated to avoid logging out of Google)
    await logoutUser();
  };

  // Check if company exists or needs onboarding
  useEffect(() => {
    const checkCompany = async () => {
      if (!user) return;
      if (user.role === UserRole.JOB_SEEKER) {
        setShowCompanySetup(false);
        return;
      }

      try {
        setApiLoading(true);
        // Force refresh by clearing the cache first
        if (typeof window !== 'undefined') {
          localStorage.removeItem('api_cache_/companies/client');
        }

        // Get fresh company data using the store
        const { fetchCompany } = useCompanyStore.getState();
        const result = await fetchCompany(true, user?.sub); // Force refresh

        // Check if onboarding is required
        if (result && 'onboardingRequired' in result && result.onboardingRequired) {
          // Populate the form with existing company data
          setInitialData(result as Partial<CompanySetupData>);
          setShowCompanySetup(true);
        } else {
          setShowCompanySetup(!result);
        }
      } catch (error: any) {
        console.error('Error checking company:', error);
        if (error?.response?.status === 403) {
          await handleUnauthorized();
          return;
        }
        setShowCompanySetup(true);
      } finally {
        setIsChecking(false);
        setApiLoading(false);
      }
    };

    if (user && !userLoading) {
      checkCompany();
    } else if (!userLoading) {
      setIsChecking(false);
    }
  }, [user, userLoading, setApiLoading]);

  const handleCompanySetup = async (companyData: CompanySetupData) => {
    try {
      setApiLoading(true);
      // Use the correct company endpoint
      await apiHelper.patch('/companies', companyData);
      // Clear the company cache to ensure fresh data is loaded
      if (typeof window !== 'undefined') {
        localStorage.removeItem('api_cache_/companies/client');
      }
      setShowCompanySetup(false);
    } catch (error) {
      console.error('Error saving company data:', error);
      setHasError(true);
      throw error;
    }
  };

  // Log that we're using the AdminLayout
  useEffect(() => {}, [user?.sub]);

  return (
    <>
      <BaseLayout userRole={UserRole.ADMIN} isLoading={isLoading || isChecking}>
        <div className="admin-background-glow min-h-full">{children}</div>
      </BaseLayout>
    </>
  );
};

export default AdminLayout;
