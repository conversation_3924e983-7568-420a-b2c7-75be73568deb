import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import userEvent from '@testing-library/user-event';
import { ReferralProgramBanner } from '@/components/shared/ReferralProgramBanner';
import { ReferralProgramCard } from '@/components/job-seeker/referral/ReferralProgramCard';
import { referralApi } from '@/app/referral-partner/services/referralApi';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));
jest.mock('@/app/referral-partner/services/referralApi', () => ({
  referralApi: {
    checkReferralStatus: jest.fn(),
    activateForJobSeeker: jest.fn(),
    activateForCompany: jest.fn(),
  },
}));

const mockUseRouter = useRouter as jest.Mock;
const mockCheckReferralStatus = referralApi.checkReferralStatus as jest.Mock;
const mockActivateForJobSeeker = referralApi.activateForJobSeeker as jest.Mock;

describe('Referral Program Activation Flow', () => {
  let queryClient: QueryClient;
  const mockPush = jest.fn();

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    });

    mockUseRouter.mockReturnValue({
      push: mockPush,
    });

    jest.clearAllMocks();
    localStorage.clear();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  describe('ReferralProgramBanner', () => {
    it('should not display banner for users who are already referral partners', async () => {
      mockCheckReferralStatus.mockResolvedValue({ isActive: true });

      const { container } = render(<ReferralProgramBanner userType="jobseeker" />, { wrapper });

      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });
    });

    it('should display banner for users who are not referral partners', async () => {
      mockCheckReferralStatus.mockResolvedValue({ isActive: false });

      render(<ReferralProgramBanner userType="jobseeker" userName="John" />, { wrapper });

      await waitFor(() => {
        expect(screen.getByText(/Start Now/i)).toBeInTheDocument();
      });
    });

    it('should navigate to onboarding when Start Earning is clicked', async () => {
      mockCheckReferralStatus.mockResolvedValue({ isActive: false });

      render(<ReferralProgramBanner userType="jobseeker" userName="John Doe" />, { wrapper });

      await waitFor(() => {
        expect(screen.getByText(/Start Now/i)).toBeInTheDocument();
      });

      const startNowButton = screen.getByText(/Start Now/i);
      fireEvent.click(startNowButton);

      expect(mockPush).toHaveBeenCalledWith(
        expect.stringContaining('/referral-partner/onboarding?')
      );
    });

    it('should dismiss banner when X is clicked', async () => {
      mockCheckReferralStatus.mockResolvedValue({ isActive: false });

      const { container } = render(<ReferralProgramBanner userType="jobseeker" />, { wrapper });

      await waitFor(() => {
        expect(screen.getByLabelText('Dismiss banner')).toBeInTheDocument();
      });

      const dismissButton = screen.getByLabelText('Dismiss banner');
      fireEvent.click(dismissButton);

      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });

      expect(localStorage.getItem('referralBannerDismissed')).toBe('true');
    });
  });

  describe('ReferralProgramCard', () => {
    it('should show loading state initially', () => {
      mockCheckReferralStatus.mockReturnValue(new Promise(() => {})); // Never resolves

      const { container } = render(<ReferralProgramCard />, { wrapper });

      // Check for the animated skeleton loader instead of Loading... text
      expect(container.querySelector('.animate-pulse')).toBeInTheDocument();
    });

    it('should show activation button for non-partners', async () => {
      mockCheckReferralStatus.mockResolvedValue({ isActive: false });

      render(<ReferralProgramCard />, { wrapper });

      await waitFor(() => {
        expect(screen.getByText(/Start Earning Now/i)).toBeInTheDocument();
      });
    });

    it('should show referral info for active partners', async () => {
      mockCheckReferralStatus.mockResolvedValue({
        isActive: true,
        partner: {
          referralCode: 'REF123',
        },
      });

      render(<ReferralProgramCard />, { wrapper });

      await waitFor(() => {
        expect(screen.getByText(/REF123/i)).toBeInTheDocument();
      });
    });

    it('should handle activation', async () => {
      const user = userEvent.setup();
      mockCheckReferralStatus.mockResolvedValue({ isActive: false });
      mockActivateForJobSeeker.mockResolvedValue({
        referralCode: 'REF456',
      });

      render(<ReferralProgramCard />, { wrapper });

      await waitFor(() => {
        expect(screen.getByText(/Start Earning Now/i)).toBeInTheDocument();
      });

      const activateButton = screen.getByText(/Start Earning Now/i);
      await user.click(activateButton);

      await waitFor(() => {
        expect(mockActivateForJobSeeker).toHaveBeenCalled();
        expect(screen.getByText(/REF456/i)).toBeInTheDocument();
      });
    });
  });
});
