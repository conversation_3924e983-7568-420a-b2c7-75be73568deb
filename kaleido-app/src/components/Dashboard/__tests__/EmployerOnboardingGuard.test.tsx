import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import { EmployerOnboardingGuard } from '../EmployerOnboardingGuard';
import { UserRole } from '@/shared/types';

// Mock next/router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

describe('EmployerOnboardingGuard', () => {
  const mockReplace = jest.fn();

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      replace: mockReplace,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render children for non-employer users', () => {
    const userData = {
      userRole: UserRole.JOB_SEEKER,
      company: null,
    };

    render(
      <EmployerOnboardingGuard userData={userData}>
        <div data-testid="child-content">Dashboard Content</div>
      </EmployerOnboardingGuard>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(mockReplace).not.toHaveBeenCalled();
  });

  it('should render children for employer with onboarding complete', () => {
    const userData = {
      userRole: UserRole.EMPLOYER,
      company: {
        id: 'company-1',
        onboardingRequired: false,
      },
    };

    render(
      <EmployerOnboardingGuard userData={userData}>
        <div data-testid="child-content">Dashboard Content</div>
      </EmployerOnboardingGuard>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(mockReplace).not.toHaveBeenCalled();
  });

  it('should redirect and not render children for employer needing onboarding', async () => {
    const userData = {
      userRole: UserRole.EMPLOYER,
      company: {
        id: 'company-1',
        onboardingRequired: true,
      },
    };

    render(
      <EmployerOnboardingGuard userData={userData}>
        <div data-testid="child-content">Dashboard Content</div>
      </EmployerOnboardingGuard>
    );

    // Should redirect
    await waitFor(() => {
      expect(mockReplace).toHaveBeenCalledWith('/company-onboarding');
    });

    // Should not render children
    expect(screen.queryByTestId('child-content')).not.toBeInTheDocument();
  });

  it('should render children for employer without company data', () => {
    const userData = {
      userRole: UserRole.EMPLOYER,
      company: null,
    };

    render(
      <EmployerOnboardingGuard userData={userData}>
        <div data-testid="child-content">Dashboard Content</div>
      </EmployerOnboardingGuard>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(mockReplace).not.toHaveBeenCalled();
  });

  it('should handle admin users correctly', () => {
    const userData = {
      userRole: UserRole.ADMIN,
      company: {
        id: 'company-1',
        onboardingRequired: true,
      },
    };

    render(
      <EmployerOnboardingGuard userData={userData}>
        <div data-testid="child-content">Admin Dashboard</div>
      </EmployerOnboardingGuard>
    );

    // Admins should not be redirected even if company needs onboarding
    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(mockReplace).not.toHaveBeenCalled();
  });

  it('should handle undefined onboardingRequired gracefully', () => {
    const userData = {
      userRole: UserRole.EMPLOYER,
      company: {
        id: 'company-1',
        // onboardingRequired is undefined
      },
    };

    render(
      <EmployerOnboardingGuard userData={userData}>
        <div data-testid="child-content">Dashboard Content</div>
      </EmployerOnboardingGuard>
    );

    // Should render children when onboardingRequired is undefined
    expect(screen.getByTestId('child-content')).toBeInTheDocument();
    expect(mockReplace).not.toHaveBeenCalled();
  });
});
