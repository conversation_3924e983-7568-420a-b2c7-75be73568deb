import React from 'react';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/router';
import { JobSeekerOnboardingGuard } from '../JobSeekerOnboardingGuard';
import { UserRole } from '@/shared/types';

// Mock next/router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

describe('JobSeekerOnboardingGuard', () => {
  const mockReplace = jest.fn();

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      replace: mockReplace,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render children for non-job-seeker users', () => {
    const userData = {
      userRole: UserRole.EMPLOYER,
      dashboardStats: {},
    };

    const onboardingData = {
      shouldShowSetupSlider: false,
      hasCheckedOnboarding: true,
    };

    render(
      <JobSeekerOnboardingGuard userData={userData} onboardingData={onboardingData}>
        <div data-testid="child-content">Dashboard Content</div>
      </JobSeekerOnboardingGuard>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
  });

  it('should render children for job seeker with onboarding complete', () => {
    const userData = {
      userRole: UserRole.JOB_SEEKER,
      dashboardStats: {
        onboardingStatus: {
          hasCompletedOnboarding: true,
          canApplyForJobs: true,
        },
      },
    };

    const onboardingData = {
      shouldShowSetupSlider: false,
      hasCheckedOnboarding: true,
    };

    render(
      <JobSeekerOnboardingGuard userData={userData} onboardingData={onboardingData}>
        <div data-testid="child-content">Dashboard Content</div>
      </JobSeekerOnboardingGuard>
    );

    expect(screen.getByTestId('child-content')).toBeInTheDocument();
  });

  it('should not render children while checking onboarding for job seeker', () => {
    const userData = {
      userRole: UserRole.JOB_SEEKER,
      dashboardStats: {
        onboardingStatus: {
          hasCompletedOnboarding: false,
          canApplyForJobs: false,
        },
      },
    };

    const onboardingData = {
      shouldShowSetupSlider: false,
      hasCheckedOnboarding: false, // Still checking
    };

    render(
      <JobSeekerOnboardingGuard userData={userData} onboardingData={onboardingData}>
        <div data-testid="child-content">Dashboard Content</div>
      </JobSeekerOnboardingGuard>
    );

    // Should not render children while checking
    expect(screen.queryByTestId('child-content')).not.toBeInTheDocument();
  });

  it('should not render children when setup slider should be shown', () => {
    const userData = {
      userRole: UserRole.JOB_SEEKER,
      dashboardStats: {
        onboardingStatus: {
          hasCompletedOnboarding: false,
          canApplyForJobs: false,
        },
      },
    };

    const onboardingData = {
      shouldShowSetupSlider: true, // Setup slider should be shown
      hasCheckedOnboarding: true,
    };

    render(
      <JobSeekerOnboardingGuard userData={userData} onboardingData={onboardingData}>
        <div data-testid="child-content">Dashboard Content</div>
      </JobSeekerOnboardingGuard>
    );

    // Should not render children when setup slider is needed
    expect(screen.queryByTestId('child-content')).not.toBeInTheDocument();
  });

  it('should handle job seeker without onboarding status gracefully', () => {
    const userData = {
      userRole: UserRole.JOB_SEEKER,
      dashboardStats: {}, // No onboarding status
    };

    const onboardingData = {
      shouldShowSetupSlider: false,
      hasCheckedOnboarding: true,
    };

    render(
      <JobSeekerOnboardingGuard userData={userData} onboardingData={onboardingData}>
        <div data-testid="child-content">Dashboard Content</div>
      </JobSeekerOnboardingGuard>
    );

    // Should render children when no explicit onboarding needed
    expect(screen.getByTestId('child-content')).toBeInTheDocument();
  });

  it('should handle admin and graduate users correctly', () => {
    // Test Admin
    const adminUserData = {
      userRole: UserRole.ADMIN,
      dashboardStats: {},
    };

    const { rerender } = render(
      <JobSeekerOnboardingGuard
        userData={adminUserData}
        onboardingData={{ shouldShowSetupSlider: false, hasCheckedOnboarding: true }}
      >
        <div data-testid="admin-content">Admin Dashboard</div>
      </JobSeekerOnboardingGuard>
    );

    expect(screen.getByTestId('admin-content')).toBeInTheDocument();

    // Test Graduate
    const graduateUserData = {
      userRole: UserRole.GRADUATE,
      dashboardStats: {},
    };

    rerender(
      <JobSeekerOnboardingGuard
        userData={graduateUserData}
        onboardingData={{ shouldShowSetupSlider: false, hasCheckedOnboarding: true }}
      >
        <div data-testid="graduate-content">Graduate Dashboard</div>
      </JobSeekerOnboardingGuard>
    );

    expect(screen.getByTestId('graduate-content')).toBeInTheDocument();
  });
});
