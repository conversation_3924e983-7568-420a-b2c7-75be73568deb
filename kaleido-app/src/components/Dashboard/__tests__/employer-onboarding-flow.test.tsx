import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import Dashboard from '../Dashboard';
import * as useEnhancedUserDataModule from '@/hooks/useEnhancedUserData';
import * as useAuthSyncModule from '@/hooks/useAuthSync';
import * as useAuthStoreModule from '@/stores/authStore';
import { UserRole } from '@/shared/types';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));
jest.mock('@/hooks/useAuthSync');
jest.mock('@/stores/authStore');
jest.mock('@/hooks/useEnhancedUserData');
jest.mock('@/stores/onboardingStore', () => ({
  useOnboardingStore: jest.fn(() => ({
    profile: null,
    company: null,
    isLoading: false,
    error: null,
    shouldShowSetupSlider: false,
    hasCheckedOnboarding: false,
    userRole: null,
    fetchProfile: jest.fn(),
    fetchCompany: jest.fn(),
    setProfile: jest.fn(),
    setCompany: jest.fn(),
    setShouldShowSetupSlider: jest.fn(),
    updateOnboardingProgress: jest.fn(),
    markOnboardingComplete: jest.fn(),
    markCompanyOnboardingComplete: jest.fn(),
    setUserRole: jest.fn(),
    reset: jest.fn(),
    getOverallProgress: jest.fn(() => 0),
    getNeedsOnboarding: jest.fn(() => false),
    getCompanyNeedsOnboarding: jest.fn(() => false),
    getMissingRequiredFields: jest.fn(() => []),
    shouldRedirectToOnboarding: jest.fn(() => null),
  })),
}));
jest.mock('@/stores/companyStore');

// Mock Auth0 UserProvider
jest.mock('@auth0/nextjs-auth0/client', () => ({
  UserProvider: ({ children }: { children: React.ReactNode }) => children,
  useUser: jest.fn(() => ({
    user: { sub: 'test-user-id' },
    isLoading: false,
  })),
}));

// Mock components
jest.mock('../Employer/EmployerDashboard', () => ({
  __esModule: true,
  default: ({ stats, loading }: any) => (
    <div data-testid="employer-dashboard">
      Employer Dashboard - Loading: {loading ? 'true' : 'false'}
    </div>
  ),
}));

jest.mock('../../Layouts/ColourfulLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="loader">Loading...</div>,
}));

describe('Employer Onboarding Flow', () => {
  let queryClient: QueryClient;
  const mockPush = jest.fn();
  const mockReplace = jest.fn();

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    });

    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
      replace: mockReplace,
      query: {},
      pathname: '/dashboard',
    });

    // Default auth state
    (useAuthSyncModule.useAuthSync as jest.Mock).mockReturnValue(undefined);
    (useAuthStoreModule.useAuthStore as any).mockReturnValue({
      isAuthenticated: true,
      isLoading: false,
      session: { user: { sub: 'test-user-id' } },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>);
  };

  describe('Race Condition Tests', () => {
    it('should show loading state while checking onboarding status', async () => {
      // Mock enhanced user data as loading initially
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      renderWithProviders(<Dashboard />);

      // Should show loader while data is loading
      expect(screen.getByTestId('loader')).toBeInTheDocument();
      expect(screen.queryByTestId('employer-dashboard')).not.toBeInTheDocument();
    });

    it('should not render employer dashboard until onboarding check is complete', async () => {
      // Start with loading state
      const mockUseEnhancedUserData = useEnhancedUserDataModule.default as jest.Mock;
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      const { rerender } = renderWithProviders(<Dashboard />);

      // Verify loading state
      expect(screen.getByTestId('loader')).toBeInTheDocument();

      // Simulate data loaded with onboarding required
      act(() => {
        mockUseEnhancedUserData.mockReturnValue({
          userData: {
            userRole: UserRole.EMPLOYER,
            dashboardStats: {},
            company: {
              id: 'company-1',
              onboardingRequired: true,
            },
          },
          isLoading: false,
          error: null,
        });
      });

      rerender(
        <QueryClientProvider client={queryClient}>
          <Dashboard />
        </QueryClientProvider>
      );

      // Should not show employer dashboard when onboarding is required
      await waitFor(() => {
        expect(screen.queryByTestId('employer-dashboard')).not.toBeInTheDocument();
      });
    });

    it('should redirect to company-onboarding when onboarding is required', async () => {
      // Mock employer with onboarding required
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {},
          company: {
            id: 'company-1',
            onboardingRequired: true,
            clientId: 'test-user-id',
          },
        },
        isLoading: false,
        error: null,
      });

      renderWithProviders(<Dashboard />);

      // Should redirect to company onboarding
      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith('/company-onboarding');
      });
    });

    it('should show employer dashboard when onboarding is complete', async () => {
      // Mock employer with onboarding complete
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {
            jobs: { total: 5 },
            candidates: { total: 10 },
          },
          company: {
            id: 'company-1',
            onboardingRequired: false,
            clientId: 'test-user-id',
          },
        },
        isLoading: false,
        error: null,
      });

      renderWithProviders(<Dashboard />);

      // Should show employer dashboard
      await waitFor(() => {
        expect(screen.getByTestId('employer-dashboard')).toBeInTheDocument();
        expect(screen.getByText(/Loading: false/)).toBeInTheDocument();
      });

      // Should not redirect
      expect(mockReplace).not.toHaveBeenCalled();
    });

    it('should handle employer without company data gracefully', async () => {
      // Mock employer without company data
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: {
          userRole: UserRole.EMPLOYER,
          dashboardStats: {},
          company: null,
        },
        isLoading: false,
        error: null,
      });

      renderWithProviders(<Dashboard />);

      // Should show employer dashboard (no redirect)
      await waitFor(() => {
        expect(screen.getByTestId('employer-dashboard')).toBeInTheDocument();
      });

      // Should not redirect (assumes new employer needs to be created)
      expect(mockReplace).not.toHaveBeenCalled();
    });

    it('should handle simultaneous loading states correctly', async () => {
      const mockUseEnhancedUserData = useEnhancedUserDataModule.default as jest.Mock;
      const mockUseAuthStore = useAuthStoreModule.useAuthStore as any;

      // Start with auth loading
      mockUseAuthStore.mockReturnValue({
        isAuthenticated: false,
        isLoading: true,
        session: null,
      });

      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      const { rerender } = renderWithProviders(<Dashboard />);

      // Should show loader
      expect(screen.getByTestId('loader')).toBeInTheDocument();

      // Auth completes first
      act(() => {
        mockUseAuthStore.mockReturnValue({
          isAuthenticated: true,
          isLoading: false,
          session: { user: { sub: 'test-user-id' } },
        });
      });

      rerender(
        <QueryClientProvider client={queryClient}>
          <Dashboard />
        </QueryClientProvider>
      );

      // Should still show loader while user data loads
      expect(screen.getByTestId('loader')).toBeInTheDocument();

      // User data completes
      act(() => {
        mockUseEnhancedUserData.mockReturnValue({
          userData: {
            userRole: UserRole.EMPLOYER,
            dashboardStats: {},
            company: {
              id: 'company-1',
              onboardingRequired: false,
            },
          },
          isLoading: false,
          error: null,
        });
      });

      rerender(
        <QueryClientProvider client={queryClient}>
          <Dashboard />
        </QueryClientProvider>
      );

      // Should show dashboard
      await waitFor(() => {
        expect(screen.getByTestId('employer-dashboard')).toBeInTheDocument();
      });
    });

    it('should not flicker between loading and dashboard states', async () => {
      const mockUseEnhancedUserData = useEnhancedUserDataModule.default as jest.Mock;

      // Track render states
      const renderStates: string[] = [];

      // Mock to track what's rendered
      jest.spyOn(console, 'log').mockImplementation();

      // Initial loading state
      mockUseEnhancedUserData.mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      const TestWrapper = () => {
        const { userData, isLoading } = useEnhancedUserDataModule.default();

        if (isLoading) {
          renderStates.push('loading');
          return <div data-testid="loader">Loading...</div>;
        }

        if (userData?.userRole === UserRole.EMPLOYER) {
          renderStates.push('dashboard');
          return <div data-testid="employer-dashboard">Dashboard</div>;
        }

        return null;
      };

      const { rerender } = renderWithProviders(<TestWrapper />);

      // Update to loaded state
      act(() => {
        mockUseEnhancedUserData.mockReturnValue({
          userData: {
            userRole: UserRole.EMPLOYER,
            dashboardStats: {},
            company: { onboardingRequired: false },
          },
          isLoading: false,
          error: null,
        });
      });

      rerender(
        <QueryClientProvider client={queryClient}>
          <TestWrapper />
        </QueryClientProvider>
      );

      // Verify no flickering - should go from loading to dashboard without intermediate states
      expect(renderStates).toEqual(['loading', 'dashboard']);
    });
  });

  describe('Dashboard Page Redirect Logic', () => {
    it('should handle redirect timing in dashboard page correctly', async () => {
      // Mock the dashboard page if it doesn't exist
      const DashboardPage = Dashboard;

      // Mock initial state
      (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
        userData: null,
        isLoading: true,
        error: null,
      });

      const { rerender } = renderWithProviders(<DashboardPage />);

      // Update with employer needing onboarding
      act(() => {
        (useEnhancedUserDataModule.default as jest.Mock).mockReturnValue({
          userData: {
            userRole: UserRole.EMPLOYER,
            dashboardStats: {},
            company: {
              id: 'company-1',
              onboardingRequired: true,
            },
          },
          isLoading: false,
          error: null,
        });
      });

      rerender(
        <QueryClientProvider client={queryClient}>
          <DashboardPage />
        </QueryClientProvider>
      );

      // Should redirect before rendering employer dashboard
      await waitFor(() => {
        expect(mockReplace).toHaveBeenCalledWith('/company-onboarding');
      });

      // Should not render employer dashboard
      expect(screen.queryByTestId('employer-dashboard')).not.toBeInTheDocument();
    });
  });
});
