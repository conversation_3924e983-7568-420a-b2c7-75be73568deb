'use client';

import React, { useEffect, useState } from 'react';

import {
  BarChart3,
  Bell,
  Briefcase,
  FileText,
  Lightbulb,
  PlusCircle,
  Search,
  Star,
  Users,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useDashboardStats } from '@/hooks/useDashboardStats';
import apiHelper from '@/lib/apiHelper';

import { AnimatedBackground } from '../AnimatedBackground';
import { CongratulatoryModal, HiredJobInfo } from './CongratulatoryModal';
import { ReferralProgramBanner } from '@/components/shared/ReferralProgramBanner';

interface JobSeekerDashboardProps {
  stats: {
    applications: any;
    matchedJobs: any;
    notifications: any;
  };
  loading: boolean;
}

const JobSeekerDashboard: React.FC<JobSeekerDashboardProps> = ({ stats, loading }) => {
  const [hiredJobInfo, setHiredJobInfo] = useState<HiredJobInfo | null>(null);
  const [showHiredModal, setShowHiredModal] = useState(false);
  const { dashboardStats } = useDashboardStats();
  const router = useRouter();

  // Check for hired status on load
  useEffect(() => {
    const checkHiredStatus = async () => {
      try {
        const response = await apiHelper.get('/dashboard/hired-status');
        if (response?.isHired) {
          setHiredJobInfo(response.jobInfo);
          setShowHiredModal(true);
        }
      } catch (error) {
        console.error('Error checking hired status:', error);
      }
    };

    checkHiredStatus();
  }, []);

  // Navigation handlers
  const handleViewJobs = () => {
    router.push('/jobs/marketplace');
  };

  const handleViewProfile = () => {
    router.push('/profile');
  };

  const handleViewApplications = () => {
    router.push('/applications');
  };

  if (loading) {
    return (
      <div className="relative flex h-full w-full items-center p-3">
        <div className="w-full max-w-7xl">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div
                key={i}
                className="col-span-1 animate-pulse bg-white/5 rounded-xl min-h-[180px]"
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <AnimatedBackground />
      <div
        data-testid="jobseeker-dashboard"
        className="relative flex h-full w-full items-start p-3 sm:p-4 overflow-y-auto"
      >
        <div className="w-full max-w-7xl mx-auto">
          {/* Welcome Section */}
          <div className="mb-6">
            <h1 className="text-md font-medium text-white/90 mb-1">Welcome to your dashboard</h1>
            <p className="text-sm text-white/70">
              Here's an overview of your job applications and matched opportunities
            </p>
          </div>

          {/* Referral Program Banner */}
          <ReferralProgramBanner userType="jobseeker" />

          {/* Quick Actions */}
          <div className="mb-8">
            <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
              <PlusCircle className="w-4 h-4 text-pink-400 mr-2" />
              Quick Actions
              <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {/* Browse Jobs Card */}
              <Card
                className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
                onClick={handleViewJobs}
              >
                <div className="relative h-32 overflow-hidden">
                  <Image
                    src="/images/landing/open-jobs/open-jobs-2.webp"
                    alt="Browse Jobs"
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
                </div>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center text-white">
                    <Search className="w-4 h-4 text-pink-400 mr-2" />
                    Browse Jobs
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-4">
                  <CardDescription className="text-xs text-white/70">
                    Explore job opportunities that match your skills and experience
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Update Profile Card */}
              <Card
                className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
                onClick={handleViewProfile}
              >
                <div className="relative h-32 overflow-hidden">
                  <Image
                    src="/images/landing/open-jobs/open-jobs-4.webp"
                    alt="Update Profile"
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
                </div>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center text-white">
                    <Users className="w-4 h-4 text-pink-400 mr-2" />
                    Update Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-4">
                  <CardDescription className="text-xs text-white/70">
                    Keep your profile up to date to improve your job matching results
                  </CardDescription>
                </CardContent>
              </Card>

              {/* View Applications Card */}
              <Card
                className="group overflow-hidden border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
                onClick={handleViewApplications}
              >
                <div className="relative h-32 overflow-hidden">
                  <Image
                    src="/images/landing/open-jobs/open-jobs-6.webp"
                    alt="View Applications"
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-purple-900/80 to-transparent"></div>
                </div>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center text-white">
                    <FileText className="w-4 h-4 text-pink-400 mr-2" />
                    View Applications
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-4">
                  <CardDescription className="text-xs text-white/70">
                    Track the status of your job applications and interview progress
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="mb-8">
            <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
              <BarChart3 className="w-4 h-4 text-pink-400 mr-2" />
              Key Metrics
              <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
            </h2>
            <div className="overflow-hidden rounded-xl border border-white/5 backdrop-blur-sm bg-purple-900/5">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 divide-y sm:divide-y-0 sm:divide-x divide-white/10">
                {/* Total Applications */}
                <div className="relative p-5 hover:bg-purple-500/5 transition-all duration-200 group">
                  <div className="flex flex-col justify-between h-full">
                    <div className="mb-2">
                      <p className="text-xs font-medium text-white/70">Total Applications</p>
                    </div>
                    <div className="flex items-end justify-between">
                      <p className="text-2xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-white to-purple-200">
                        {stats.applications?.total || 0}
                      </p>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <FileText className="w-16 h-16 text-pink-400" />
                      </div>
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-500/40 to-pink-500/40 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                </div>

                {/* Interviewing */}
                <div className="relative p-5 hover:bg-blue-500/5 transition-all duration-200 group">
                  <div className="flex flex-col justify-between h-full">
                    <div className="mb-2">
                      <p className="text-xs font-medium text-white/70">Interviewing</p>
                    </div>
                    <div className="flex items-end justify-between">
                      <p className="text-2xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-white to-blue-200">
                        {stats.applications?.statusBreakdown?.interviewing || 0}
                      </p>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <Briefcase className="w-16 h-16 text-blue-400" />
                      </div>
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-blue-500/40 to-purple-500/40 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                </div>

                {/* Matched Jobs */}
                <div className="relative p-5 hover:bg-pink-500/5 transition-all duration-200 group">
                  <div className="flex flex-col justify-between h-full">
                    <div className="mb-2">
                      <p className="text-xs font-medium text-white/70">Matched Jobs</p>
                    </div>
                    <div className="flex items-end justify-between">
                      <p className="text-2xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-white to-pink-200">
                        {stats.matchedJobs?.total || 0}
                      </p>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <Star className="w-16 h-16 text-pink-400" />
                      </div>
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-pink-500/40 to-purple-500/40 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                </div>

                {/* Unread Notifications */}
                <div className="relative p-5 hover:bg-indigo-500/5 transition-all duration-200 group">
                  <div className="flex flex-col justify-between h-full">
                    <div className="mb-2">
                      <p className="text-xs font-medium text-white/70">Unread Notifications</p>
                    </div>
                    <div className="flex items-end justify-between">
                      <p className="text-2xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-white to-indigo-200">
                        {stats.notifications?.unread || 0}
                      </p>
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 opacity-10 group-hover:opacity-20 transition-opacity duration-200">
                        <Bell className="w-16 h-16 text-indigo-400" />
                      </div>
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-indigo-500/40 to-purple-500/40 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Applications and Profile Completion in two columns */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Recent Applications */}
            <div>
              <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
                <FileText className="w-4 h-4 text-pink-400 mr-2" />
                Recent Applications
                <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
              </h2>
              <Card className="border-white/5 shadow-lg hover:shadow-xl hover:shadow-purple-900/10 backdrop-blur-xl transition-all duration-200">
                <CardContent className="p-4">
                  {stats.applications?.recentApplications &&
                  stats.applications.recentApplications.length > 0 ? (
                    <div className="overflow-hidden">
                      <table className="w-full">
                        <tbody>
                          {stats.applications.recentApplications
                            .slice(0, 3)
                            .map((app: any, index: number) => (
                              <tr
                                key={index}
                                className="border-b border-white/5 hover:bg-purple-500/5 cursor-pointer transition-all duration-200"
                                onClick={() => router.push(`/applications?id=${app.id}`)}
                              >
                                <td className="py-2.5">
                                  <div className="flex items-center gap-2">
                                    <Briefcase className="w-3.5 h-3.5 text-pink-400 shrink-0" />
                                    <span className="text-white/80 text-xs font-medium truncate">
                                      {app.jobTitle}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-3 mt-1.5">
                                    <div className="flex items-center gap-1">
                                      <Users className="w-3 h-3 text-pink-400/60 shrink-0" />
                                      <span className="text-white/60 text-xs">
                                        {app.companyName}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <FileText className="w-3 h-3 text-pink-400/60 shrink-0" />
                                      <span className="text-white/60 text-xs">
                                        {new Date(app.appliedAt).toLocaleDateString('en-US', {
                                          month: 'short',
                                          day: 'numeric',
                                        })}
                                      </span>
                                    </div>
                                  </div>
                                </td>
                                <td className="py-2.5 text-right align-top">
                                  {app.status === 'NEW' && (
                                    <PlusCircle className="w-4 h-4 text-purple-300/70 ml-auto" />
                                  )}
                                  {app.status === 'INTERVIEWING' && (
                                    <Star className="w-4 h-4 text-blue-300/70 ml-auto" />
                                  )}
                                  {app.status === 'HIRED' && (
                                    <Briefcase className="w-4 h-4 text-green-300/70 ml-auto" />
                                  )}
                                  {!['NEW', 'INTERVIEWING', 'HIRED'].includes(app.status) && (
                                    <FileText className="w-4 h-4 text-gray-300/70 ml-auto" />
                                  )}
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-white/60 text-xs">No recent applications found</p>
                      <Button
                        onClick={handleViewJobs}
                        variant="link"
                        className="text-pink-400 text-xs mt-2"
                      >
                        Browse available jobs
                      </Button>
                    </div>
                  )}
                  {stats.applications?.recentApplications &&
                    stats.applications.recentApplications.length > 0 && (
                      <div className="mt-2 text-center">
                        <Button
                          onClick={() => router.push('/applications')}
                          variant="link"
                          className="text-pink-400 text-xs py-1"
                        >
                          View all applications
                        </Button>
                      </div>
                    )}
                </CardContent>
              </Card>
            </div>

            {/* Profile Completion */}
            <div>
              <h2 className="text-sm font-medium text-white/80 mb-4 flex items-center">
                <Lightbulb className="w-4 h-4 text-pink-400 mr-2" />
                Profile Completion
                <div className="h-px bg-gradient-to-r from-pink-500/20 to-transparent flex-grow ml-3"></div>
              </h2>
              <Card className="border-white/5 shadow-lg hover:shadow-xl hover:shadow-purple-900/10 backdrop-blur-xl transition-all duration-200">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center text-white">
                    <Users className="w-4 h-4 text-pink-400 mr-2" />
                    Profile Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-4">
                  {dashboardStats?.onboardingStatus ? (
                    <div className="space-y-4">
                      {/* Overall Progress */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs">
                          <span className="text-white/70">Overall Completion</span>
                          <span className="text-white/90">
                            {dashboardStats.onboardingStatus.overall.percentage}%
                          </span>
                        </div>
                        <Progress
                          value={dashboardStats.onboardingStatus.overall.percentage}
                          className="h-2 bg-white/5"
                          indicatorClassName="bg-purple-400/30"
                        />
                      </div>

                      {/* Section Progress */}
                      <div className="space-y-3">
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-white/70">Basic Information</span>
                            <span className="text-white/90">
                              {dashboardStats.onboardingStatus.sections.basicInfo.completed
                                ? 'Complete'
                                : 'Incomplete'}
                            </span>
                          </div>
                          <Progress
                            value={dashboardStats.onboardingStatus.sections.basicInfo.percentage}
                            className="h-1.5 bg-white/5"
                            indicatorClassName="bg-pink-400/30"
                          />
                        </div>

                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-white/70">Professional Info</span>
                            <span className="text-white/90">
                              {dashboardStats.onboardingStatus.sections.professionalInfo.completed
                                ? 'Complete'
                                : 'Incomplete'}
                            </span>
                          </div>
                          <Progress
                            value={
                              dashboardStats.onboardingStatus.sections.professionalInfo.percentage
                            }
                            className="h-1.5 bg-white/5"
                            indicatorClassName="bg-pink-400/30"
                          />
                        </div>

                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-white/70">Job Preferences</span>
                            <span className="text-white/90">
                              {dashboardStats.onboardingStatus.sections.preferences.completed
                                ? 'Complete'
                                : 'Incomplete'}
                            </span>
                          </div>
                          <Progress
                            value={dashboardStats.onboardingStatus.sections.preferences.percentage}
                            className="h-1.5 bg-white/5"
                            indicatorClassName="bg-pink-400/30"
                          />
                        </div>

                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-white/70">Additional Info</span>
                            <span className="text-white/90">
                              {dashboardStats.onboardingStatus.sections.additionalInfo.completed
                                ? 'Complete'
                                : 'Incomplete'}
                            </span>
                          </div>
                          <Progress
                            value={
                              dashboardStats.onboardingStatus.sections.additionalInfo.percentage
                            }
                            className="h-1.5 bg-white/5"
                            indicatorClassName="bg-pink-400/30"
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Fallback to old system if onboarding status not available */}
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-white/70">Resume Upload</span>
                          <span className="text-white/90">
                            {stats.applications?.total ? 'Complete' : 'Incomplete'}
                          </span>
                        </div>
                        <Progress
                          value={stats.applications?.total ? 100 : 0}
                          className="h-1.5 bg-white/5"
                          indicatorClassName="bg-pink-400/30"
                        />
                      </div>

                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-white/70">Skills Assessment</span>
                          <span className="text-white/90">
                            {stats.matchedJobs?.total ? 'Complete' : 'Incomplete'}
                          </span>
                        </div>
                        <Progress
                          value={stats.matchedJobs?.total ? 100 : 0}
                          className="h-1.5 bg-white/5"
                          indicatorClassName="bg-pink-400/30"
                        />
                      </div>

                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-white/70">Video Introduction</span>
                          <span className="text-white/90">
                            {stats.applications?.statusBreakdown?.culturalFitAnswered
                              ? 'Complete'
                              : 'Incomplete'}
                          </span>
                        </div>
                        <Progress
                          value={stats.applications?.statusBreakdown?.culturalFitAnswered ? 100 : 0}
                          className="h-1.5 bg-white/5"
                          indicatorClassName="bg-pink-400/30"
                        />
                      </div>
                    </div>
                  )}

                  <CardFooter className="px-0 pt-2 pb-0">
                    <Button
                      variant="link"
                      className="text-pink-400 text-xs p-0"
                      onClick={handleViewProfile}
                    >
                      {dashboardStats?.onboardingStatus?.canApplyForJobs
                        ? 'Edit Profile'
                        : 'Complete Profile'}
                    </Button>
                  </CardFooter>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Congratulatory Modal for Hired Status */}
      <CongratulatoryModal
        isOpen={showHiredModal}
        onClose={() => setShowHiredModal(false)}
        jobInfo={hiredJobInfo}
      />
    </>
  );
};

export default JobSeekerDashboard;
