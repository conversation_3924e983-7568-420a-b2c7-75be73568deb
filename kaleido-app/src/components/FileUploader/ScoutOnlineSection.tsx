import React from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { Linkedin, ListChecks, Search } from 'lucide-react';

import { useJobStore } from '@/stores/unifiedJobStore';
import { useScoutedCandidatesStore } from '@/stores/scoutedCandidatesStore';

import ScoutOnlineTable from './ScoutOnlineTable';

interface ScoutOnlineSectionProps {
  jobId: string;
  job: any;
  selectedJob: any;
  showScoutOnline: boolean;
  setShowScoutOnline: (show: boolean) => void;
  isScoutOnlineLoading: boolean;
  setIsScoutOnlineLoading: (loading: boolean) => void;
  setUploadedCandidateCount: React.Dispatch<React.SetStateAction<number>>;
  onCandidateCountChange: (count: number) => void;
}

const ScoutOnlineSection: React.FC<ScoutOnlineSectionProps> = ({
  jobId,
  job,
  selectedJob,
  showScoutOnline,
  setShowScoutOnline,
  isScoutOnlineLoading,
  setIsScoutOnlineLoading,
  setUploadedCandidateCount,
  onCandidateCountChange,
}) => {
  // Get jobEditStore actions
  const { onWorkerComplete, fetchJobCriteria } = useJobStore();
  return (
    <>
      <div
        id="scout-online-section"
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 py-4 px-4 border-b border-white/10 bg-gradient-to-r from-pink-900/10 to-pink-800/5"
      >
        <div className="flex items-center gap-3">
          <div className="bg-pink-500/20 p-2 rounded-full">
            <Linkedin className="h-5 w-5 text-pink-400" />
          </div>
          <div>
            <p className="text-sm lg:text-md font-medium">Scout Online</p>
            <p className="text-xs text-white/60">Find candidates from online sources</p>
          </div>
        </div>
        <button
          type="button"
          onClick={() => setShowScoutOnline(!showScoutOnline)}
          className="px-3 py-1.5 rounded-full bg-pink-500/10 hover:bg-pink-500/30 text-sm font-medium transition-all duration-300 flex items-center gap-2 border border-pink-500/20 self-start sm:self-auto"
        >
          {showScoutOnline ? (
            <>
              <ListChecks className="h-4 w-4" />
              <span className="hidden sm:inline">Hide Scout</span>
              <span className="sm:hidden">Hide</span>
            </>
          ) : (
            <>
              <Search className="h-4 w-4" />
              <span className="hidden sm:inline">Show Scout</span>
              <span className="sm:hidden">Show</span>
            </>
          )}
        </button>
      </div>

      <AnimatePresence>
        {showScoutOnline && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-visible max-w-full"
            style={{ maxWidth: '100%', overflowX: 'hidden', display: 'block' }}
          >
            <div className="max-w-full overflow-x-hidden">
              <ScoutOnlineTable
                jobId={jobId}
                jobType={job?.jobType || ''}
                onScoutStarted={() => {
                  // We don't need to refresh the job data or update the candidate count here
                  // because scouted candidates are not automatically added to the job
                  // The ScoutOnlineTable component will handle fetching when it mounts
                }}
                onCandidateAdded={async (addedCount: number) => {
                  // Update the uploaded candidate count immediately for instant UI update
                  setUploadedCandidateCount(prev => {
                    const newCount = prev + addedCount;
                    onCandidateCountChange(newCount);
                    return newCount;
                  });

                  // Also update jobEditStore with the new candidate count
                  await onWorkerComplete(jobId, 'scout');

                  // Refresh job criteria to sync with backend
                  await fetchJobCriteria(jobId, true);
                }}
                isLoading={isScoutOnlineLoading}
                setIsLoading={setIsScoutOnlineLoading}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ScoutOnlineSection;
