import { AnimatePresence, motion } from 'framer-motion';
import React, { useCallback, useEffect, useState } from 'react';
import { ExtendedUploadedFile, UploadedFile } from './types';

import { RankNowButton } from '@/components/MatchRank/components/RankNowButton';
import apiHelper from '@/lib/apiHelper';
import { useJobStore, useUnifiedJobStore } from '@/stores/unifiedJobStore';
import { UserPlus } from 'lucide-react';
import { NewUploadsFileList } from './NewUploadsFileList';
import { PreviousUploadsFileList } from './PreviousUploadsFileList';

interface TotalCandidatesSectionProps {
  jobId: string;
  files: UploadedFile[];
  isUploading: boolean;
  activeJobs: string[];
  handleRankClick: () => Promise<void>;
  handleDelete: (fileId: string) => void;
  useJobEditStore?: boolean;
}

const TotalCandidatesSection: React.FC<TotalCandidatesSectionProps> = ({
  jobId,
  files,
  isUploading,
  activeJobs,
  handleRankClick,
  handleDelete,
  useJobEditStore = false,
}) => {
  const [showCandidates, setShowCandidates] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [candidateData, setCandidateData] = useState<{
    totalCandidates: number;
    candidates: any[];
    matchRankCost: any;
  }>({
    totalCandidates: 0,
    candidates: [],
    matchRankCost: {
      success: true,
      creditCost: 0,
      unevaluatedCandidatesCount: 0,
      isValid: true,
      message: 'No cost calculated',
      availableCredits: 0,
    },
  });

  // Get data from Zustand store only for job criteria
  const { currentJob, fetchJobCriteria } = useJobStore();

  const criteriaData = currentJob;
  const candidates = candidateData.candidates;
  const matchRankCostData = candidateData.matchRankCost;
  const totalCandidates = candidateData.totalCandidates;

  // Direct API fetch function - no caching, always fresh
  const fetchFreshData = useCallback(async () => {
    if (!jobId) return;

    try {
      setIsRefreshing(true);
      console.log('[TotalCandidatesSection] Fetching fresh data from API for job:', jobId);

      // Always fetch fresh data from API
      const response = await apiHelper.get(`/jobs/${jobId}/criteria?t=${Date.now()}`); // Add timestamp to prevent caching

      if (response) {
        const totalCount = response.totalCandidates || response.stats?.totalCandidates || 0;
        const recentCandidates = response.recentCandidates || [];
        const matchRankCostData = response.matchRankCost || {
          success: true,
          creditCost: 0,
          unevaluatedCandidatesCount: 0,
          isValid: true,
          message: 'No cost calculated',
          availableCredits: 0,
        };

        console.log('[TotalCandidatesSection] Fresh data received:', {
          totalCandidates: totalCount,
          recentCandidates: recentCandidates.length,
          matchRankCost: matchRankCostData,
        });

        // Update local state
        setCandidateData({
          totalCandidates: totalCount,
          candidates: recentCandidates,
          matchRankCost: matchRankCostData,
        });

        // Also update the store so CandidateSourcesHeader gets the update
        useUnifiedJobStore.setState((state: any) => ({
          stats: {
            ...state.stats,
            totalCandidates: totalCount,
          },
          currentJob: state.currentJob
            ? {
                ...state.currentJob,
                totalCandidates: totalCount,
                recentCandidates: recentCandidates,
              }
            : null,
          matchRankCost: matchRankCostData,
        }));
      }
    } catch (error) {
      console.error('[TotalCandidatesSection] Error fetching fresh data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [jobId]);

  // Fetch job data on mount and when jobId changes
  useEffect(() => {
    if (jobId) {
      fetchFreshData();
    }
  }, [jobId, fetchFreshData]);

  // Listen for upload events and refresh data
  useEffect(() => {
    const handleUploadEvent = (event: CustomEvent) => {
      console.log('[TotalCandidatesSection] Upload event received:', event.type, event.detail);

      // Check if this event is for our job
      const eventJobId = event.detail?.jobId || event.detail?.result?.jobId;
      if (eventJobId === jobId) {
        // Wait a bit for server to process, then fetch fresh data
        setTimeout(() => {
          fetchFreshData();
        }, 2000); // 2 second delay to ensure server has processed
      }
    };

    const handleCandidateAdded = (event: CustomEvent) => {
      console.log('[TotalCandidatesSection] Candidate added event received:', event.detail);

      // Check if this event is for our job
      if (event.detail?.jobId === jobId) {
        // Wait a bit for server to process, then fetch fresh data
        setTimeout(() => {
          fetchFreshData();
        }, 2500); // 2.5 second delay to ensure server has processed
      }
    };

    // Listen for all events
    window.addEventListener('uploadDataRefreshed', handleUploadEvent as EventListener);
    window.addEventListener('uploadJobCompleted', handleUploadEvent as EventListener);
    window.addEventListener('candidateAdded', handleCandidateAdded as EventListener);

    return () => {
      window.removeEventListener('uploadDataRefreshed', handleUploadEvent as EventListener);
      window.removeEventListener('uploadJobCompleted', handleUploadEvent as EventListener);
      window.removeEventListener('candidateAdded', handleCandidateAdded as EventListener);
    };
  }, [jobId, fetchFreshData]);

  // Function to check if thresholds are valid (both must be greater than 0)
  const isThresholdValid = useCallback(() => {
    // If we don't have criteria data yet, assume thresholds are valid to not block the button
    if (!criteriaData) {
      return true;
    }

    const topThreshold = criteriaData.topCandidateThreshold || 0;
    const secondTierThreshold = criteriaData.secondTierCandidateThreshold || 0;

    // Both thresholds must be greater than 0
    return topThreshold > 0 && secondTierThreshold > 0;
  }, [criteriaData]);

  // Create previouslyUploadedFiles from candidates data
  const previouslyUploadedFiles: ExtendedUploadedFile[] = candidates.map(candidate => ({
    id: candidate.id,
    name: candidate.originalFilename || `${candidate.fullName}.pdf`,
    size: 0,
    type: 'application/pdf',
    status: 'success' as const,
    isPreviouslyUploaded: true,
    progress: 100,
    candidateName: candidate.fullName,
    candidateId: candidate.id,
    isNew: false,
    isVirtualFile: false,
  }));

  return (
    <motion.div
      initial={{ opacity: 1 }}
      animate={{
        opacity: isRefreshing ? 0.7 : 1,
      }}
      transition={{ duration: 0.3 }}
      className="mt-8 bg-gradient-to-r from-purple-900/20 to-purple-800/10  border border-purple-700/20 backdrop-blur-sm relative"
    >
      {/* Loading overlay */}
      <AnimatePresence>
        {isRefreshing && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/20 backdrop-blur-sm z-10 flex items-center justify-center rounded-lg"
          >
            <div className="flex items-center gap-2 bg-purple-900/80 px-4 py-2 rounded-full">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              <span className="text-white text-sm">Refreshing candidates...</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main header with Total Candidates and RankNow button */}
      <div className="p-4 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 border-b border-white/10">
        <div className="flex items-center gap-3">
          <div className="bg-pink-700/20 p-3 rounded-full">
            <UserPlus className="h-6 w-6 text-pink-700" />
          </div>
          <div className="flex-1">
            <p className="text-sm lg:text-md font-medium text-white">Total Candidates</p>
            <div className="flex items-center gap-2">
              <span className="text-xl lg:text-2xl font-bold text-white">
                {totalCandidates || 0}
              </span>
              <span className="text-xs text-white/60">from all sources</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="relative group">
            <RankNowButton
              jobId={jobId}
              candidateCount={totalCandidates}
              className="mx-auto"
              isThresholdValid={isThresholdValid}
              matchRankCost={matchRankCostData}
              onRankClick={async () => {
                await handleRankClick();
                // Refresh data after ranking operation
                await fetchJobCriteria(jobId, true);
              }}
              useJobEditStore={useJobEditStore}
              disabled={
                isUploading || activeJobs.length > 0 || files.some(f => f.status === 'uploading')
              }
            />

            {/* Tooltip for disabled state */}
            {(isUploading ||
              activeJobs.length > 0 ||
              files.some(f => f.status === 'uploading') ||
              !isThresholdValid() ||
              totalCandidates === 0) && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black/80 backdrop-blur-sm text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 w-64 text-center pointer-events-none border border-white/10">
                {isUploading || activeJobs.length > 0 || files.some(f => f.status === 'uploading')
                  ? 'Please wait for all file uploads to complete before ranking candidates'
                  : !isThresholdValid()
                    ? 'Both Top and 2nd-tier thresholds must be greater than 0 to rank candidates'
                    : totalCandidates === 0
                      ? 'You need to add candidates before ranking'
                      : 'Unable to rank candidates at this time'}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Show/Hide Candidates button row */}
      {totalCandidates > 0 && (
        <div className="px-4 py-3 border-b border-white/5">
          <button
            type="button"
            onClick={() => setShowCandidates(!showCandidates)}
            className="w-full px-4 py-2 text-gray-100/70 rounded-lg bg-pink-700/10 hover:bg-pink-700/20 text-sm font-medium transition-all duration-300 flex items-center justify-center gap-2 border border-purple-700/10"
          >
            <span>{showCandidates ? 'Hide Candidates' : 'Show Candidates'}</span>
            <svg
              className={`w-4 h-4 transition-transform duration-200 ${showCandidates ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>
        </div>
      )}

      {/* Candidate Lists */}
      <AnimatePresence>
        {showCandidates && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-visible"
            style={{ display: 'block' }}
          >
            <div className="p-4">
              {/* Display previously uploaded files */}
              <PreviousUploadsFileList
                files={previouslyUploadedFiles}
                job={{ ...criteriaData, candidates }}
                uploadedCandidateCount={totalCandidates}
                onDelete={handleDelete}
              />

              {/* Display newly uploaded files */}
              <NewUploadsFileList files={files} onDelete={handleDelete} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default TotalCandidatesSection;
