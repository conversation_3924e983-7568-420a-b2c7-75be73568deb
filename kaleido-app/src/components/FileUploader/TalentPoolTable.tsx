import React, { useCallback, useEffect, useState } from 'react';

import { motion } from 'framer-motion';
import {
  Award,
  Briefcase,
  Check,
  ChevronLeft,
  ChevronRight,
  Database,
  GraduationCap,
  Info,
  Mail,
  MapPin,
  Plus,
  Search,
  User,
} from 'lucide-react';

import { showToast } from '@/components/Toaster';
import { Button } from '@/components/ui/button';
import CustomTooltip from '@/components/ui/custom-tooltip';
import apiHelper from '@/lib/apiHelper';

import MatchAnalysisTooltip from './MatchAnalysisTooltip';

export interface TalentPoolCandidate {
  id: string;
  fullName: string;
  firstName: string;
  lastName: string;
  jobTitle?: string;
  currentCompany?: string;
  location?: string;
  yearsOfExperience?: number | null;
  skills?: string[];
  evaluation?: {
    matchScore?: number;
  };
  email?: string;
  experience?: Array<{
    title: string;
    company?: string;
    endDate?: string;
    startDate?: string;
    description?: string;
    duration?: number;
    location?: string;
    achievements?: string[];
  }>;
  evaluations?: Array<{
    id: string;
    candidateId: string;
    jobId: string;
    status: string;
    matchScore: number;
    createdAt: string;
    updatedAt: string;
    evaluation?: {
      matchScore: number;
      lastEvaluatedAt: string;
      criterionMatchedOn: string[];
      detailedScoreAnalysis: {
        areasOfStrength: string[];
        detailedReasoning: Record<string, any>;
        areasForImprovement: string[];
        overallMatchPercentage: number;
        specificCriteriaMatched: Record<string, number>;
        missingCriticalRequirements: string[];
      };
      yourReasoningForScoring: string;
    };
  }>;
}

interface TalentPoolTableProps {
  jobId: string;
  jobType: string;
  searchTerm?: string;
  onCandidateAdded: (addedCount: number) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const TalentPoolTable: React.FC<TalentPoolTableProps> = ({
  jobId,
  jobType,
  searchTerm = '',
  onCandidateAdded,
  isLoading,
  setIsLoading,
}) => {
  const [candidates, setCandidates] = useState<TalentPoolCandidate[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCandidates, setTotalCandidates] = useState(0);
  const [selectedCandidates, setSelectedCandidates] = useState<Set<string>>(new Set());
  const [addingCandidates, setAddingCandidates] = useState(false);
  const [isClientFiltered, setIsClientFiltered] = useState(false);

  const itemsPerPage = 5;

  // Store all candidates for client-side filtering
  const [allCandidates, setAllCandidates] = useState<TalentPoolCandidate[]>([]);

  // Fetch candidates from the talent pool based on job type
  const fetchCandidates = async (page: number = 1) => {
    setIsLoading(true);
    try {
      // Always use the matching endpoint
      const response = await apiHelper.get('/candidates/matching', {
        params: {
          page,
          limit: itemsPerPage,
          // Only use jobType if it's not empty
          ...(jobType ? { jobType } : {}),
          jobId, // Include jobId to find matching candidates
        },
      });

      // Handle the response structure from the backend
      if (response) {
        let items: TalentPoolCandidate[] = [];
        let total = 0;

        // Extract items from response based on structure
        if (response.items) {
          items = response.items || [];
          total = response.total || items.length;
        } else if (Array.isArray(response)) {
          items = response;
          total = response.length;
        } else {
          items = response.data || [];
          total = items.length;
        }

        // Store all candidates for filtering
        setAllCandidates(items);

        // If we're searching, don't update the candidates directly
        // Let the search effect handle it
        if (searchTerm && searchTerm.length >= 3) {
          return;
        }

        // Update state with filtered items
        setCandidates(items);
        setTotalCandidates(total);

        // Calculate total pages
        if (response.totalPages) {
          setTotalPages(response.totalPages);
        } else if (total) {
          setTotalPages(Math.ceil(total / itemsPerPage));
        } else {
          // Default to 1 page if we can't determine the total
          setTotalPages(1);
        }
      } else {
        // Handle empty response
        setCandidates([]);
        setTotalCandidates(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      setCandidates([]); // Set empty array on error
      setTotalCandidates(0);
      setTotalPages(1);

      // Show a simpler error message to avoid overwhelming the user
      showToast({
        title: 'Error',
        message: 'Failed to load candidates from talent pool. Please try again.',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Add selected candidates to the job
  const addSelectedCandidates = async () => {
    if (selectedCandidates.size === 0) return;

    setAddingCandidates(true);
    try {
      const candidateIds = Array.from(selectedCandidates);
      await apiHelper.post(`/jobs/${jobId}/add-candidates`, {
        candidateIds,
      });

      showToast({
        title: 'Success',
        message: `Added ${candidateIds.length} candidate(s) to the job`,
        type: 'success',
      });

      // Clear selection and refresh
      setSelectedCandidates(new Set());
      onCandidateAdded(candidateIds.length);

      // Dispatch event to update TotalCandidatesSection
      window.dispatchEvent(
        new CustomEvent('candidateAdded', {
          detail: {
            jobId,
            count: candidateIds.length,
            source: 'talentPool',
          },
        })
      );
    } catch (error) {
      console.error('Error adding candidates:', error);
      showToast({
        title: 'Error',
        message: 'Failed to add candidates to the job',
        type: 'error',
      });
    } finally {
      setAddingCandidates(false);
    }
  };

  // Helper function to get the latest evaluation from the evaluations array
  const getLatestEvaluation = (candidate: TalentPoolCandidate) => {
    if (!candidate.evaluations || candidate.evaluations.length === 0) {
      return null;
    }

    // Sort evaluations by updatedAt in descending order (newest first)
    return [...candidate.evaluations].sort(
      (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    )[0];
  };

  // Helper function to get the latest match score
  const getLatestMatchScore = (candidate: TalentPoolCandidate): number | null => {
    // First check if there are evaluations
    const latestEvaluation = getLatestEvaluation(candidate);
    if (latestEvaluation?.evaluation?.matchScore) {
      return latestEvaluation.evaluation.matchScore;
    }

    // Fall back to the evaluation property if no evaluations array
    if (candidate.evaluation?.matchScore) {
      return candidate.evaluation.matchScore;
    }

    // If no match score is found, use a default value
    return 0;
  };

  // Helper function to get the latest experience from the array if none is in the candidate object
  const getLatestExperience = (candidate: TalentPoolCandidate): string => {
    if (candidate.jobTitle) {
      return candidate.jobTitle;
    }

    // Check if the candidate has experience array and it's not empty
    if (
      candidate.experience &&
      Array.isArray(candidate.experience) &&
      candidate.experience.length > 0
    ) {
      // Sort experience by endDate in descending order (newest first)
      const sortedExperience = [...candidate.experience].sort((a, b) => {
        const dateA = a.endDate ? new Date(a.endDate).getTime() : 0;
        const dateB = b.endDate ? new Date(b.endDate).getTime() : 0;
        return dateB - dateA;
      });

      // Return the title and company of the latest experience
      const latest = sortedExperience[0];
      if (latest.title) {
        return latest.company ? `${latest.title} at ${latest.company}` : latest.title;
      }
    }

    return 'N/A';
  };

  // Toggle candidate selection
  const toggleCandidateSelection = (candidateId: string) => {
    const newSelection = new Set(selectedCandidates);
    if (newSelection.has(candidateId)) {
      newSelection.delete(candidateId);
    } else {
      newSelection.add(candidateId);
    }
    setSelectedCandidates(newSelection);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);

    // If we're filtering by search term, handle pagination client-side
    if ((searchTerm && searchTerm.length >= 3) || isClientFiltered) {
      // Apply pagination to filtered results
      const paginatedItems = getPaginatedCandidates();
      setCandidates(paginatedItems);
    } else {
      // Otherwise fetch from API
      fetchCandidates(page);
    }
  };

  // Initial fetch when job or job type changes
  useEffect(() => {
    setCurrentPage(1);
    fetchCandidates(1);
  }, [jobId, jobType]);

  // Get filtered candidates based on search term
  const getFilteredCandidates = useCallback(() => {
    if (!searchTerm || searchTerm.length < 3) {
      return allCandidates;
    }

    const searchTermLower = searchTerm.toLowerCase();
    return allCandidates.filter(candidate => {
      const fullName =
        candidate.fullName || `${candidate.firstName || ''} ${candidate.lastName || ''}`.trim();
      return fullName.toLowerCase().includes(searchTermLower);
    });
  }, [allCandidates, searchTerm]);

  // Get paginated candidates for current view
  const getPaginatedCandidates = useCallback(() => {
    const filteredCandidates = getFilteredCandidates();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCandidates.slice(startIndex, endIndex);
  }, [getFilteredCandidates, currentPage, itemsPerPage]);

  // Handle search term changes with client-side filtering
  useEffect(() => {
    // Skip if we haven't loaded candidates yet
    if (allCandidates.length === 0) return;

    // Reset to first page when search term changes
    setCurrentPage(1);

    const filteredItems = getFilteredCandidates();
    const paginatedItems = filteredItems.slice(0, itemsPerPage); // First page

    if (searchTerm && searchTerm.length >= 3) {
      // Apply client-side filtering
      setIsClientFiltered(true);
    } else {
      // No search term or too short, show all candidates
      setIsClientFiltered(false);
    }

    // Update state with filtered and paginated items
    setCandidates(paginatedItems);
    setTotalCandidates(filteredItems.length);
    setTotalPages(Math.ceil(filteredItems.length / itemsPerPage));
  }, [searchTerm, allCandidates, itemsPerPage, getFilteredCandidates]);

  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center py-4">
        <div className="animate-spin rounded-full h-6 w-6 border-2 border-gray-300 border-t-purple-600 mb-2"></div>
        <p className="text-white/70 text-xs">Searching talent pool...</p>
      </div>
    );
  }

  return (
    <div className="w-full bg-purple-950/20 p-4 mb-4 overflow-hidden">
      {candidates.length === 0 ? (
        <div className="text-center py-4 text-white/70">
          <p className="text-xs">No matching candidates found.</p>
          {searchTerm && searchTerm.length >= 3 ? (
            <>
              <p className="text-xs mt-1">No candidates found matching "{searchTerm}".</p>
              <p className="text-xs mt-1 text-yellow-400">
                <Info className="w-3 h-3 inline mr-1" />
                Results were filtered client-side by name.
              </p>
            </>
          ) : jobType && jobType.trim() !== '' ? (
            <p className="text-xs mt-1">We searched for candidates related to "{jobType}".</p>
          ) : (
            <p className="text-xs mt-1">We searched your entire talent pool.</p>
          )}
          {!searchTerm && <p className="text-xs mt-1">Try uploading new resumes.</p>}
          {searchTerm && searchTerm.length >= 3 && (
            <p className="text-xs mt-1">Try a different search term or clear the search.</p>
          )}
        </div>
      ) : (
        <div className="flex flex-col h-full">
          {/* Total count display */}
          <div className="flex items-center justify-end mb-2 gap-2">
            {searchTerm && searchTerm.length >= 3 && (
              <div className="flex items-center bg-purple-500/20 text-purple-400 text-xs px-2 py-1 rounded">
                <Search className="w-3 h-3 mr-1" />
                <span>Searching: "{searchTerm}"</span>
              </div>
            )}
            <div className="flex items-center bg-purple-600/20 text-purple-400 text-xs px-2 py-1 rounded">
              <Database className="w-3 h-3 mr-1" />
              <span>
                {totalCandidates} matching candidate{totalCandidates !== 1 ? 's' : ''} found
              </span>
            </div>
          </div>

          <div className="relative overflow-hidden rounded-lg">
            <div className="overflow-visible scrollbar-thin scrollbar-thumb-purple-600/20 scrollbar-track-transparent">
              <table className="w-full border-collapse">
                <thead className="sticky top-0 z-10 bg-black/20">
                  <tr className="text-white/80">
                    <th className="px-3 py-2 text-left text-xs font-medium w-[40px]">
                      <span className="flex items-center">Select</span>
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium w-[25%]">
                      <span className="flex items-center">
                        <User className="w-3 h-3 mr-1" />
                        Name
                      </span>
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium w-[25%]">
                      <span className="flex items-center">
                        <Briefcase className="w-3 h-3 mr-1" />
                        Experience
                      </span>
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium w-[15%]">
                      <span className="flex items-center">
                        <Award className="w-3 h-3 mr-1" />
                        Match Score
                      </span>
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium w-[40px]">
                      <span className="flex items-center">
                        <Info className="w-3 h-3 mr-1" />
                        Info
                      </span>
                    </th>
                    <th className="px-3 py-2 text-left text-xs font-medium w-[80px]">Action</th>
                  </tr>
                </thead>
                <tbody className="relative">
                  {candidates.map(candidate => (
                    <tr
                      key={candidate.id}
                      className="border-b border-white/10 hover:bg-white/5 transition-colors"
                    >
                      <td className="px-3 py-2">
                        <div
                          className={`w-4 h-4 rounded border flex items-center justify-center cursor-pointer ${
                            selectedCandidates.has(candidate.id)
                              ? 'bg-purple-600 border-purple-600'
                              : 'border-white/30'
                          }`}
                          onClick={() => toggleCandidateSelection(candidate.id)}
                        >
                          {selectedCandidates.has(candidate.id) && (
                            <Check className="w-2 h-2 text-white" />
                          )}
                        </div>
                      </td>
                      <td className="px-3 py-2 text-white text-xs">
                        <div className="flex items-center">
                          <User className="w-3 h-3 text-purple-400 mr-1 flex-shrink-0" />
                          <span className="truncate max-w-[150px]">
                            {candidate.fullName ||
                              (candidate.firstName && candidate.lastName
                                ? `${candidate.firstName} ${candidate.lastName}`
                                : 'Unknown')}
                          </span>
                        </div>
                      </td>
                      <td className="px-3 py-2 text-white/80 text-xs">
                        <div className="flex items-center">
                          <Briefcase className="w-3 h-3 text-purple-400 mr-1 flex-shrink-0" />
                          <div className="flex flex-col">
                            <span>{getLatestExperience(candidate)}</span>
                            {candidate.yearsOfExperience && (
                              <span className="text-white/50 text-xs">
                                {candidate.yearsOfExperience} years
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-3 py-2">
                        {getLatestMatchScore(candidate) ? (
                          <div className="flex items-center">
                            <Award className="w-3 h-3 text-purple-400 mr-1 flex-shrink-0" />
                            <div
                              className={`px-2 py-0.5 rounded text-xs font-medium ${
                                getLatestMatchScore(candidate) >= 70
                                  ? 'bg-green-500/20 text-green-400'
                                  : getLatestMatchScore(candidate) >= 50
                                    ? 'bg-yellow-500/20 text-yellow-400'
                                    : 'bg-red-500/20 text-red-400'
                              }`}
                            >
                              {Math.round(getLatestMatchScore(candidate))}%
                            </div>
                          </div>
                        ) : (
                          <span className="text-white/50 text-xs flex items-center">
                            <Award className="w-3 h-3 text-purple-400 mr-1 flex-shrink-0" />
                            N/A
                          </span>
                        )}
                      </td>
                      <td className="px-3 py-2">
                        <CustomTooltip
                          content={
                            candidate.evaluations && candidate.evaluations.length > 0 ? (
                              <MatchAnalysisTooltip
                                candidate={candidate}
                                evaluation={getLatestEvaluation(candidate)?.evaluation}
                              />
                            ) : (
                              <div className="p-3 w-full">
                                <div className="flex items-center justify-between border-b border-white/10 pb-3 mb-3">
                                  <div className="flex items-center gap-2">
                                    <User className="w-5 h-5 text-purple-400" />
                                    <h3 className="font-medium text-base">Candidate Information</h3>
                                  </div>
                                  {candidate.yearsOfExperience && (
                                    <div className="flex items-center gap-2 text-white/80">
                                      <Briefcase className="w-4 h-4 text-purple-400" />
                                      <span className="text-sm">
                                        {candidate.yearsOfExperience} years experience
                                      </span>
                                    </div>
                                  )}
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                                  {/* Left column */}
                                  <div className="space-y-4">
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2">
                                        <Info className="w-4 h-4 text-purple-400" />
                                        <h4 className="font-medium text-sm">Profile Details</h4>
                                      </div>
                                      <div className="space-y-2 text-xs text-white/80">
                                        <p>No detailed evaluation available for this candidate.</p>

                                        {candidate.jobTitle && (
                                          <div className="flex items-start gap-2 mt-2">
                                            <Briefcase className="w-3 h-3 text-purple-400 mt-0.5 flex-shrink-0" />
                                            <div>
                                              <span className="font-medium">Current Role:</span>
                                              <p>{candidate.jobTitle}</p>
                                            </div>
                                          </div>
                                        )}

                                        {candidate.currentCompany && (
                                          <div className="flex items-start gap-2 mt-2">
                                            <Award className="w-3 h-3 text-purple-400 mt-0.5 flex-shrink-0" />
                                            <div>
                                              <span className="font-medium">Company:</span>
                                              <p>{candidate.currentCompany}</p>
                                            </div>
                                          </div>
                                        )}

                                        {candidate.location && (
                                          <div className="flex items-start gap-2 mt-2">
                                            <MapPin className="w-3 h-3 text-purple-400 mt-0.5 flex-shrink-0" />
                                            <div>
                                              <span className="font-medium">Location:</span>
                                              <p>{candidate.location}</p>
                                            </div>
                                          </div>
                                        )}

                                        {candidate.email && (
                                          <div className="flex items-start gap-2 mt-2">
                                            <Mail className="w-3 h-3 text-purple-400 mt-0.5 flex-shrink-0" />
                                            <div>
                                              <span className="font-medium">Contact:</span>
                                              <p>{candidate.email}</p>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>

                                  {/* Right column */}
                                  <div className="space-y-4">
                                    {candidate.skills && candidate.skills.length > 0 && (
                                      <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                          <GraduationCap className="w-4 h-4 text-purple-400" />
                                          <h4 className="font-medium text-sm">
                                            Skills & Expertise
                                          </h4>
                                        </div>
                                        <div className="flex flex-wrap gap-1.5">
                                          {candidate.skills.slice(0, 15).map((skill, idx) => (
                                            <span
                                              key={idx}
                                              className="px-2 py-0.5 bg-purple-500/20 text-purple-400 rounded text-xs"
                                            >
                                              {skill}
                                            </span>
                                          ))}
                                          {candidate.skills.length > 15 && (
                                            <span className="px-2 py-0.5 bg-gray-500/20 text-gray-400 rounded text-xs">
                                              +{candidate.skills.length - 15} more
                                            </span>
                                          )}
                                        </div>
                                      </div>
                                    )}

                                    {candidate.experience && candidate.experience.length > 0 && (
                                      <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                          <Briefcase className="w-4 h-4 text-purple-400" />
                                          <h4 className="font-medium text-sm">Recent Experience</h4>
                                        </div>
                                        <div className="space-y-2">
                                          {candidate.experience.slice(0, 2).map((exp, idx) => (
                                            <div
                                              key={idx}
                                              className="text-xs text-white/80 border-l-2 border-purple-500/30 pl-2"
                                            >
                                              <p className="font-medium">{exp.title}</p>
                                              {exp.company && <p>{exp.company}</p>}
                                              {exp.duration && (
                                                <p className="text-white/60">
                                                  {exp.duration}{' '}
                                                  {exp.duration === 1 ? 'month' : 'months'}
                                                </p>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )
                          }
                          position="bottom"
                          width="max-w-[90vw] md:max-w-2xl min-w-[300px] md:min-w-[500px]"
                          className="backdrop-blur-xl"
                        >
                          <button
                            type="button"
                            className="p-1 rounded-full hover:bg-purple-500/20 transition-colors"
                            aria-label="View candidate details"
                            title="View candidate details"
                          >
                            <Info className="w-4 h-4 text-purple-400" />
                            <span className="sr-only">View candidate details</span>
                          </button>
                        </CustomTooltip>
                      </td>
                      <td className="px-3 py-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-white/10 hover:bg-purple-500/20 hover:text-purple-400 text-xs h-7 px-2"
                          onClick={() => {
                            toggleCandidateSelection(candidate.id);
                            if (!selectedCandidates.has(candidate.id)) {
                              const newSelection = new Set(selectedCandidates);
                              newSelection.add(candidate.id);
                              setSelectedCandidates(newSelection);
                            }
                          }}
                        >
                          <Plus className="w-3 h-3 mr-1" />
                          Add
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-3 sticky bottom-0 bg-purple-950/20 py-2">
              <div className="flex items-center gap-1">
                <button
                  type="button"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="p-1 rounded-md hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed text-white"
                  aria-label="Previous page"
                  title="Previous page"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>

                <span className="px-2 py-1 text-white/90 text-xs">
                  {currentPage} / {totalPages}
                </span>

                <button
                  type="button"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="p-1 rounded-md hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed text-white"
                  aria-label="Next page"
                  title="Next page"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}

          {/* Add Selected Button */}
          {selectedCandidates.size > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-3 flex justify-end sticky bottom-0 right-0 z-20 py-2"
            >
              <Button
                onClick={addSelectedCandidates}
                disabled={addingCandidates}
                className="bg-purple-600 hover:bg-purple-700 text-white text-xs h-8 px-3"
              >
                {addingCandidates ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent mr-1"></div>
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="w-3 h-3 mr-1" />
                    Add {selectedCandidates.size} Candidate{selectedCandidates.size > 1 ? 's' : ''}
                  </>
                )}
              </Button>
            </motion.div>
          )}
        </div>
      )}
    </div>
  );
};

export default TalentPoolTable;
