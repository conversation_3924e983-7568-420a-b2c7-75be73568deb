import React, { useCallback, useEffect, useRef, useState } from 'react';

import { motion } from 'framer-motion';
import { usePathname, useSearchParams } from 'next/navigation';
import { useDropzone } from 'react-dropzone';

// Removed theme helpers - always use dark theme
import SubscriptionLimitModal from '@/components/Subscription/SubscriptionLimitModal';
import { showToast } from '@/components/Toaster';

import { useSubscription } from '@/hooks/useSubscription';
import apiHelper from '@/lib/apiHelper';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';
import { useScoutedCandidatesStore } from '@/stores/scoutedCandidatesStore';
import { useUploadJobsStore } from '@/stores/unifiedJobStore';
import { CreditActionType } from '@/types/subscription';
import { getNotificationWorker } from '@/workers/notification-worker';

import CandidateSourcesHeader from './FileUploader/CandidateSourcesHeader';
import ResumeUploadSection from './FileUploader/ResumeUploadSection';
import ScoutOnlineSection from './FileUploader/ScoutOnlineSection';
import TalentHubSection from './FileUploader/TalentHubSection';
import TotalCandidatesSection from './FileUploader/TotalCandidatesSection';
import { ExtendedUploadedFile, FileUploaderProps, UploadedFile } from './FileUploader/types';

const FileUploader: React.FC<FileUploaderProps & { isProcessing?: boolean }> = ({
  jobId,
  selectedJob,
  onUploadComplete,
  onCandidateCountChange = () => {},
  isProcessing = false,
  job,
  isAtsJob = false,
  preventAutoRefresh = false,
  useJobEditStore: useJobEditStoreFlag = false,
}): JSX.Element => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get subscription refresh function for updating credit count
  const { refreshSubscriptionData } = useSubscription();

  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [previouslyUploadedFiles, setPreviouslyUploadedFiles] = useState<ExtendedUploadedFile[]>(
    []
  );
  const [completedFiles, setCompletedFiles] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);

  const [uploadedCandidateCount, setUploadedCandidateCount] = useState(0);

  // State for tracking files to upload
  const [pendingFiles, setPendingFiles] = useState<File[]>([]);

  // Subscription limit modal state
  const [showSubscriptionLimitModal, setShowSubscriptionLimitModal] = useState(false);
  const [subscriptionLimitData, setSubscriptionLimitData] = useState<{
    actionType: CreditActionType;
    message?: string;
    requiredCredits?: number;
    availableCredits?: number;
  } | null>(null);

  // Internal state for component status
  const [isUploading, setIsUploading] = useState(false);
  const [isMatching, setIsMatching] = useState(false);
  const [isMatchReady, setIsMatchReady] = useState(false);

  const [showTalentPool, setShowTalentPool] = useState(false);
  const [isTalentPoolLoading, setIsTalentPoolLoading] = useState(false);
  const [showScoutOnline, setShowScoutOnline] = useState(false);
  const [isScoutOnlineLoading, setIsScoutOnlineLoading] = useState(false);
  const [showResumeUpload, setShowResumeUpload] = useState(false);

  // Use the upload jobs store
  const { addJob, jobs, activeJobs } = useUploadJobsStore();

  // Get state from matchrankDetailsStore
  const { setHasFileUploads } = useMatchRankDetailsStore();

  // Track current job ID for this uploader instance
  const currentJobIdRef = useRef<string | null>(null);
  const jobStore = useJobStore();

  useEffect(() => {
    localStorage.setItem('uploadReturnPath', pathname);

    const updatedFiles = [];

    // Show all candidates, not just those with resume files
    if (selectedJob?.candidates?.length > 0) {
      const candidateFiles = selectedJob.candidates.map((candidate: any) => ({
        id: candidate.id,
        name: candidate.originalFilename || `${candidate.fullName}.pdf`,
        status: 'success' as const,
        progress: 100,
        candidateName: candidate.fullName,
        candidateEmail: candidate.email,
        candidateId: candidate.id,
        // Flag to indicate if this is a candidate without an uploaded resume
        isVirtualFile: !candidate.originalFilename,
      }));

      updatedFiles.push(...candidateFiles);
    }

    // Then check for uploaded resumes without candidates
    if (selectedJob?.uploadedResumes?.length > 0) {
      const existingNames = updatedFiles.map(f => f.name);

      const resumeFiles = selectedJob.uploadedResumes
        .filter((filename: string) => !existingNames.includes(filename))
        .map((filename: string) => ({
          id: Math.random().toString(36).substring(7),
          name: filename,
          status: 'success' as const,
          progress: 100,
        }));

      updatedFiles.push(...resumeFiles);
    }

    // Set the previously uploaded files
    setPreviouslyUploadedFiles(updatedFiles);

    // Update candidate count - ensure we count all candidates including duplicates
    const candidateCount = selectedJob?.candidates?.length || 0;
    setUploadedCandidateCount(candidateCount);
    onCandidateCountChange(candidateCount);
  }, [pathname, selectedJob, jobId]);

  // Listen for upload job status changes
  useEffect(() => {
    const handleJobStatusChange = (event: CustomEvent) => {
      const { jobId, status, result } = event.detail;

      // Only handle events for our current job
      if (jobId === currentJobIdRef.current) {
        if (status === 'completed') {
          // Update UI for completed job
          handleJobCompletion(result);
        } else if (status === 'failed') {
          // Handle failed uploads
          setFiles(prev =>
            prev.map(f => ({
              ...f,
              status: 'error',
              progress: 0,
              error: result?.error || 'Upload failed',
            }))
          );
          setIsUploading(false);
        }
      }
    };

    // Add event listener for the custom event
    window.addEventListener('uploadJobStatusChanged', handleJobStatusChange as EventListener);

    // Clean up
    return () => {
      window.removeEventListener('uploadJobStatusChanged', handleJobStatusChange as EventListener);
    };
  }, []);

  // Function to handle job completion
  const handleJobCompletion = async (result: any) => {
    // Update files status to success
    setFiles(prev =>
      prev.map(f => ({
        ...f,
        status: 'success',
        progress: 100,
      }))
    );

    // Update candidate count based on the result
    if (result?.result?.newCandidatesCount !== undefined) {
      // Only count NEW candidates, not duplicates that already existed
      const newCandidatesCount = result.result.newCandidatesCount || 0;

      // Use a callback form to ensure we're using the latest state
      setUploadedCandidateCount(prev => {
        const newCount = prev + newCandidatesCount;
        onCandidateCountChange(newCount);
        return newCount;
      });
    } else if (result?.result?.candidates?.length > 0) {
      // Fallback: Count only non-duplicate candidates
      const newCandidatesCount = result.result.candidates.filter(c => !c.isDuplicate).length;

      // Use a callback form to ensure we're using the latest state
      setUploadedCandidateCount(prev => {
        const newCount = prev + newCandidatesCount;
        onCandidateCountChange(newCount);
        return newCount;
      });
    } else if (result?.result?.successCount) {
      // Legacy response format - only count successful uploads, not duplicates
      const successCount = result.result.successCount || 0;

      // Use a callback form to ensure we're using the latest state
      setUploadedCandidateCount(prev => {
        const newCount = prev + successCount;
        onCandidateCountChange(newCount);
        return newCount;
      });
    }

    // Set completion state
    setCompletedFiles(files.length);
    setIsMatchReady(true);
    setIsUploading(false);

    // Resume upload is now free - no credit consumption needed

    // Call the onUploadComplete callback if provided
    if (onUploadComplete) {
      onUploadComplete(result);
    }

    // Always refresh job store to ensure real-time updates
    try {
      // Force refresh job criteria to get latest data including candidates, totalCandidates, and matchRankCost
      await jobStore.fetchJobCriteria(jobId, true);

      // If this is the currently selected job, also trigger a worker complete event
      if (jobStore.selectedJobId === jobId || jobStore.currentJob?.id === jobId) {
        await jobStore.onWorkerComplete(jobId, 'upload');
      }
    } catch (error) {
      console.error('Error refreshing job store after upload completion:', error);
    }

    // Clear the block flag to allow API calls again
    if (typeof window !== 'undefined') {
      window['blockJobsApiCalls'] = false;
    }
  };

  // Resume uploads are now free - no credit validation needed
  const proceedWithUpload = async (filesToUpload: File[]) => {
    // Set pending files for the upload process
    setPendingFiles(filesToUpload);

    // Directly call handleCreditConfirmation with files since uploads are free
    await handleCreditConfirmation(filesToUpload);

    return true;
  };

  // Function to handle upload directly (no credit confirmation needed since uploads are free)
  const handleCreditConfirmation = async (filesToUpload?: File[]) => {
    const files = filesToUpload || pendingFiles;
    try {
      // Proceed with the upload
      await uploadBatch(files);

      // Refresh subscription data to update credit count
      await refreshSubscriptionData();
    } catch (error) {
      console.error('Error during upload:', error);
      showToast({
        message: 'Upload failed. Please try again.',
        isSuccess: false,
      });
    } finally {
      // Clear pending files
      setPendingFiles([]);
    }
  };

  const uploadBatch = async (acceptedFiles: File[]) => {
    // Set the global API block flag immediately
    if (typeof window !== 'undefined') {
      window['blockJobsApiCalls'] = true;
    }

    const formData = new FormData();

    // Add all files to the formData
    acceptedFiles.forEach(file => {
      formData.append('files', file);
    });

    formData.append('jobId', jobId);

    try {
      setIsUploading(true);
      setFiles(prev => prev.map(f => ({ ...f, status: 'uploading', progress: 10 })));

      // Use apiHelper instead of fetch
      const data = await apiHelper.post('/candidates/upload', formData);

      // Handle queued job response
      if (data.isQueued && data.jobId) {
        // Store the job ID for tracking
        currentJobIdRef.current = data.jobId;

        // Add job to the upload jobs store
        addJob({
          jobId: data.jobId,
          status: 'queued',
          totalFiles: data.totalFiles,
          jobType: 'file-upload',
          relatedId: jobId,
        });

        // Update files to show they're being processed
        setFiles(prev =>
          prev.map(f => ({
            ...f,
            status: 'uploading',
            progress: 10,
          }))
        );

        // Set total files for progress tracking
        setTotalFiles(data.totalFiles);

        // Keep uploading state true as we're still processing
        setIsUploading(true);

        // This prevents unnecessary API calls that would refresh the page
        return;
      }

      // Handle duplicates consistently whether from single or batch upload
      if (data.duplicateFiles && data.duplicateFiles.length > 0) {
        setFiles(prev =>
          prev.map(f => {
            const duplicateFile = data.duplicateFiles.find(dup => dup.filename === f.name);
            if (duplicateFile) {
              return {
                ...f,
                status: 'duplicate',
                progress: 100,
                duplicateMessage: duplicateFile.message || 'Resume already exists for this job',
                isDuplicate: true,
              };
            } else {
              return { ...f, status: 'pending', progress: 0 };
            }
          })
        );

        // Count duplicate files as successful for completion metrics
        const successfulFiles = files.filter(
          f => f.status === 'success' || f.status === 'duplicate'
        ).length;
        setCompletedFiles(successfulFiles);

        // If all files are duplicates, mark the process as ready for matching
        if (files.every(f => f.status === 'duplicate')) {
          setIsMatchReady(true);
        }

        setIsUploading(false);

        return;
      }

      // Handle immediate completion for single file upload
      if (data.status === 'completed' && data.candidates?.length > 0) {
        // Single file upload with immediate response
        setFiles(prev =>
          prev.map(f => ({
            ...f,
            status: 'success',
            progress: 100,
          }))
        );

        // Update candidate count and completion metrics
        // Only count non-duplicate candidates
        const newCandidateCount = data.candidates.filter(c => !c.isDuplicate).length;

        // Use callback form to ensure we're using the latest state
        setUploadedCandidateCount(prev => {
          const newCount = prev + newCandidateCount;
          onCandidateCountChange(newCount);
          return newCount;
        });

        setCompletedFiles(files.length);

        setIsMatchReady(true);
        setIsUploading(false);

        // Resume upload is now free - no credit consumption needed

        return;
      }

      // If it's a queued job, add it to the upload jobs store
      if (data.jobId && data.isQueued) {
        // Save the current job ID for this uploader instance
        currentJobIdRef.current = data.jobId;

        // Add job to the store instead of the old manager
        addJob({
          jobId: data.jobId,
          status: 'queued',
          totalFiles: data.totalFiles,
          jobType: 'resume_upload',
          relatedId: jobId,
        });

        setFiles(prev =>
          prev.map(f => ({
            ...f,
            status: 'uploading',
            progress: 0,
          }))
        );

        // Show a toast or notification that the user can navigate away
        if (typeof window !== 'undefined') {
          const notificationWorker = getNotificationWorker();
          if (notificationWorker) {
            notificationWorker.showNotification(
              `Processing ${data.totalFiles} Files`,
              "You can navigate away - we'll notify you when processing is complete.",
              10000
            );
          }
        }
      } else {
        // Handle single file upload response
        setFiles(prev =>
          prev.map(f => {
            // Check for individual file errors in the response
            let fileError = null;
            let fileDuplicate = null;

            // Check in failedUploads array for errors
            if (data.failedUploads?.find) {
              fileError = data.failedUploads?.find(failed => failed.filename === f.name);
            }

            // Check in duplicateFiles array for duplicates
            if (data.duplicateFiles?.find) {
              fileDuplicate = data.duplicateFiles?.find(dup => dup.filename === f.name);
            }

            // Also check directly in the response for single file errors
            if (!fileError && !fileDuplicate && data.file === f.name && data.status === 'error') {
              fileError = { filename: f.name, error: data.error || 'Upload failed' };
            }

            if (fileDuplicate) {
              return {
                ...f,
                status: 'duplicate',
                progress: 100,
                duplicateMessage: fileDuplicate.message || 'Resume already exists for this job',
              };
            } else if (fileError) {
              console.error(`File error for ${f.name}:`, fileError);
              return {
                ...f,
                status: 'error',
                progress: 0,
                error: fileError.error || 'Unknown error occurred',
              };
            } else {
              return { ...f, status: 'success', progress: 100 };
            }
          })
        );
      }

      // Update candidate count - only count NEW candidates
      const candidateCount = data.candidates?.filter(c => !c.isDuplicate).length || 0;

      // Use callback form to ensure we're using the latest state
      setUploadedCandidateCount(prev => {
        // Only add new candidates, not duplicates
        const newCount = prev + candidateCount;
        onCandidateCountChange(newCount);
        return newCount;
      });

      // Count successful and duplicate files
      const successfulFiles = files.filter(
        f => f.status === 'success' || f.status === 'duplicate'
      ).length;
      setCompletedFiles(successfulFiles);

      // After marking duplicate files as successful in counts
      const successfulDuplicates = files.filter(f => f.status === 'duplicate').length;
      setCompletedFiles(prev => prev + successfulDuplicates);

      // After all updates are complete in the uploadBatch function
      // Add this code to set match ready state if there are previously uploaded files
      if (
        previouslyUploadedFiles.length > 0 ||
        files.some(f => f.status === 'success' || f.status === 'duplicate')
      ) {
        setIsMatchReady(true);
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Batch upload error:', error);
      setFiles(prev => prev.map(f => ({ ...f, status: 'error', progress: 0 })));
      setIsUploading(false);
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      // Set the global API block flag immediately
      if (typeof window !== 'undefined') {
        window['blockJobsApiCalls'] = true;
      }

      // Set the file uploads flag in the store to track changes
      setHasFileUploads(true);

      // Fetch the job to get the list of uploaded resumes
      try {
        // const jobData = await getJobs();
        const uploadedResumes = selectedJob?.uploadedResumes || [];

        // Check for duplicates
        const duplicateFiles: File[] = [];
        const newFiles: File[] = [];

        acceptedFiles.forEach(file => {
          if (
            uploadedResumes.includes(file.name) ||
            previouslyUploadedFiles.some(prevFile => prevFile.name === file.name)
          ) {
            duplicateFiles.push(file);
          } else {
            newFiles.push(file);
          }
        });

        // If there are duplicates, mark them as such
        if (duplicateFiles.length > 0) {
          const allFiles = [...duplicateFiles, ...newFiles];

          const fileObjects = allFiles.map(file => ({
            id: Math.random().toString(36).substring(7),
            name: file.name,
            size: file.size,
            type: file.type,
            status:
              uploadedResumes.includes(file.name) ||
              previouslyUploadedFiles.some(prevFile => prevFile.name === file.name)
                ? ('duplicate' as const)
                : ('pending' as const),
            progress: 0,
            duplicateMessage:
              uploadedResumes.includes(file.name) ||
              previouslyUploadedFiles.some(prevFile => prevFile.name === file.name)
                ? 'Resume already exists for this job'
                : undefined,
            isDuplicate:
              uploadedResumes.includes(file.name) ||
              previouslyUploadedFiles.some(prevFile => prevFile.name === file.name),
          }));

          setFiles((prev: any) => [...prev, ...fileObjects]);
          setCompletedFiles(0);
          setTotalFiles(allFiles.length);

          // If all files are duplicates, update completion counters but don't proceed with upload
          if (newFiles.length === 0) {
            // Mark duplicate files as successful in counts
            const successfulDuplicates = files.filter(f => f.status === 'duplicate').length;
            setCompletedFiles(prev => prev + successfulDuplicates);
            setIsMatchReady(true);

            return;
          }

          // Continue with only non-duplicate files
          acceptedFiles = newFiles;
        } else {
          // No duplicates, proceed normally
          const newFileObjects = acceptedFiles.map(file => ({
            id: Math.random().toString(36).substring(7),
            name: file.name,
            status: 'pending' as const,
            progress: 0,
          }));

          setFiles((prev: any) => [...prev, ...newFileObjects]);
          setCompletedFiles(0);
          setTotalFiles(acceptedFiles.length);
        }

        // Resume uploads are now free - proceed directly with upload
        const canProceed = await proceedWithUpload(acceptedFiles);
        if (!canProceed) {
          // Reset the API block flag if we can't proceed
          if (typeof window !== 'undefined') {
            window['blockJobsApiCalls'] = false;
          }
          return;
        }
      } catch (error) {
        console.error('Error checking for duplicates:', error);

        // Fallback to normal upload if we can't check for duplicates
        const newFiles = acceptedFiles.map(file => ({
          id: Math.random().toString(36).substring(7),
          name: file.name,
          status: 'pending' as const,
          progress: 0,
        }));

        setFiles((prev: any) => [...prev, ...newFiles]);
        setCompletedFiles(0);
        setTotalFiles(acceptedFiles.length);

        // Resume uploads are now free - proceed directly with upload
        const canProceed = await proceedWithUpload(acceptedFiles);
        if (!canProceed) {
          // Reset the API block flag if we can't proceed
          if (typeof window !== 'undefined') {
            window['blockJobsApiCalls'] = false;
          }
          return;
        }
      }
    },
    [
      jobId,
      onUploadComplete,
      onCandidateCountChange,
      previouslyUploadedFiles,
      refreshSubscriptionData,
    ]
  );

  // Use dropzone for file uploads
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/zip': ['.zip'],
      'message/rfc822': ['.eml'],
      'application/octet-stream': ['.mbox'],
    },
    multiple: true,
  });

  const handleDelete = async (fileId: string) => {
    try {
      // Check if the file is in the regular files list
      const fileToDelete = files.find(f => f.id === fileId);

      // Check if the file is in the previously uploaded files list
      const previouslyUploadedFileToDelete = previouslyUploadedFiles.find(f => f.id === fileId);

      if (!fileToDelete && !previouslyUploadedFileToDelete) return;

      // If the file is already uploaded successfully, call the delete endpoint
      if ((fileToDelete && fileToDelete.status === 'success') || previouslyUploadedFileToDelete) {
        const targetFile = fileToDelete || previouslyUploadedFileToDelete;

        // Use candidate ID if available (for all candidate types), otherwise fall back to filename
        if (targetFile && 'candidateId' in targetFile && targetFile.candidateId) {
          await apiHelper.delete(`/candidates/job/${jobId}/candidate/${targetFile.candidateId}`);
        } else {
          // Fallback to filename-based deletion for backward compatibility
          const filename = targetFile?.name;
          if (filename) {
            await apiHelper.delete(`/candidates/resume/${jobId}/${encodeURIComponent(filename)}`);
          }
        }
      }

      // Remove file from local state
      if (fileToDelete) {
        setFiles(prev => prev.filter(f => f.id !== fileId));
        setTotalFiles(prev => prev - 1);
        if (fileToDelete.status === 'success') {
          setCompletedFiles(prev => prev - 1);
        }
      }

      // Remove from previously uploaded files if applicable
      if (previouslyUploadedFileToDelete) {
        setPreviouslyUploadedFiles(prev => prev.filter(f => f.id !== fileId));
      }

      // Clear the block flag to allow API calls again
      if (typeof window !== 'undefined') {
        window['blockJobsApiCalls'] = false;
      }

      // Refresh subscription data to update credit calculations
      try {
        await refreshSubscriptionData();
      } catch (error) {
        console.error('Error refreshing subscription data after candidate deletion:', error);
      }
    } catch (error) {
      console.error('Delete error:', error);
      // You might want to show an error toast here
    }
  };

  const handleRankClick = useCallback(async () => {
    // Start matching process and reset match ready state
    setIsMatching(true);
    setIsMatchReady(false);

    try {
      // Use the asynchronous match-rank process instead of the synchronous process-resumes endpoint
      const response = await apiHelper.post(`/jobs/start-match-rank/${jobId}`, {
        topTierThreshold: 0.7,
        secondTierThreshold: 0.5,
      });

      // Check if the response contains a queueJobId
      if (response && response.queueJobId) {
        // Add the job to the matchRankJobsStore with the queue job ID
        const { addJob } = useMatchRankJobsStore.getState();
        addJob({
          jobId: response.queueJobId, // Use the queue job ID returned by the API
          status: 'queued',
          message: 'Match and rank process started...',
        });
      }

      // Set upload complete state but don't show modal since the process is now asynchronous
      setIsMatching(false);

      // Show toast notification that the process has started
      showToast({
        message: 'Match and rank process started. You can track progress in the status manager.',
        isSuccess: true,
      });

      // Set view mode to list in matchrankDetailsStore
      const matchRankStore = useMatchRankDetailsStore.getState();
      matchRankStore.setViewMode('list');

      // Reset the file uploads flag after starting the ranking process
      matchRankStore.resetFormState();

      // Always refresh job criteria to get updated match rank cost and candidate data
      try {
        await jobStore.fetchJobCriteria(jobId, true);
      } catch (error) {
        console.error('Error refreshing job criteria after ranking:', error);
      }
    } catch (error) {
      console.error('Error starting match and rank process:', error);
      setIsMatching(false);

      // Check if this is a credit-related error
      // The apiHelper should have already shown the credit modal for 403 errors
      let errorMessage = 'Failed to start match and rank process';

      if (error && typeof error === 'object') {
        // Check if it's an API error with a message
        if ('message' in error && typeof error.message === 'string') {
          // If it's a credit-related error, the modal should already be shown
          if (
            error.message.toLowerCase().includes('credit') ||
            error.message.toLowerCase().includes('insufficient')
          ) {
            errorMessage = error.message;
          } else {
            errorMessage = error.message;
          }
        }

        // Check if it's a 402 error (Payment Required - credits)
        if ('status' in error && error.status === 402) {
          // Don't show toast for credit errors as the modal is more appropriate
          return;
        }
      }

      // Show error toast only for non-credit errors
      showToast({
        message: errorMessage,
        isSuccess: false,
      });
    }
  }, [jobId]);

  // Replace polling useEffect with store-based monitoring
  useEffect(() => {
    // Make uploadJobsStore available globally for JobsStore to check active uploads
    if (typeof window !== 'undefined') {
      window['uploadJobsStore'] = useUploadJobsStore.getState();

      // Add a flag to explicitly block API calls during upload
      if (isUploading) {
        window['blockJobsApiCalls'] = true;
      } else {
        window['blockJobsApiCalls'] = false;
      }

      // Add API interceptor to log blocked calls
      if (typeof window['apiCallsBlocked'] === 'undefined') {
        window['apiCallsBlocked'] = true; // Only set up once

        // Add original fetch to track API calls
        const originalFetch = window.fetch;
        window.fetch = function (...args) {
          // Log blocked API calls to jobs endpoints
          const url = args[0];
          if (
            window['blockJobsApiCalls'] === true &&
            typeof url === 'string' &&
            (url.includes('/jobs/') || url.includes('/by-status'))
          ) {
            console.warn('🚫 Blocking API call during upload:', url);
          }
          return originalFetch.apply(this, args);
        };
      }
    }

    // Check if we have an active job for this job ID
    const activeJobsForThisJob = Object.values(jobs).filter(
      job => job.relatedId === jobId && activeJobs.includes(job.jobId)
    );

    if (activeJobsForThisJob.length > 0) {
      // We have at least one active job, ensure we're in the uploading state
      setIsUploading(true);

      // Set the global block flag
      if (typeof window !== 'undefined') {
        window['blockJobsApiCalls'] = true;
      }

      // Keep track of the current job ID
      currentJobIdRef.current = activeJobsForThisJob[0].jobId;
    } else if (isUploading && currentJobIdRef.current) {
      // Check if our current job has completed but we haven't updated the UI
      const currentJob = jobs[currentJobIdRef.current];
      if (currentJob && (currentJob.status === 'completed' || currentJob.status === 'failed')) {
        // The job completed but we missed the event, manually update the UI
        handleJobCompletion(currentJob.result);

        // Remove the block
        if (typeof window !== 'undefined') {
          window['blockJobsApiCalls'] = false;
        }
      }
    }
  }, [jobs, activeJobs, jobId, isUploading]);

  // Initialize state when previouslyUploadedFiles changes
  useEffect(() => {
    // Initialize the file uploads state based on whether there are already uploaded files
    if (previouslyUploadedFiles.length > 0) {
      setHasFileUploads(true);
    }
  }, [previouslyUploadedFiles.length, setHasFileUploads]);

  // Listen for match-rank completion event
  useEffect(() => {
    const handleMatchRankCompleted = async (event: CustomEvent) => {
      const { relatedJobId } = event.detail;

      // Only handle the event if it's for our current job
      if (relatedJobId === jobId) {
        try {
          // Force refresh job criteria to get latest data
          await jobStore.fetchJobCriteria(jobId, true);
        } catch (error) {
          console.error('Error refreshing job criteria after match-rank completion:', error);
        }
      }
    };

    // Add event listener for match-rank completion
    window.addEventListener('matchRankJobCompleted', handleMatchRankCompleted as EventListener);

    return () => {
      window.removeEventListener(
        'matchRankJobCompleted',
        handleMatchRankCompleted as EventListener
      );
    };
  }, [jobId, jobStore]);

  // Listen for the viewScoutedCandidates event
  useEffect(() => {
    const handleViewScoutedCandidates = (event: CustomEvent) => {
      const { jobId: eventJobId } = event.detail;

      // Only handle the event if it's for our current job
      if (eventJobId === jobId) {
        // Show the Scout Online section
        setShowScoutOnline(true);

        // Scroll to the Scout Online section
        setTimeout(() => {
          const scoutOnlineSection = document.getElementById('scout-online-section');
          if (scoutOnlineSection) {
            scoutOnlineSection.scrollIntoView({ behavior: 'smooth' });
          }
        }, 300); // Give time for the section to expand
      }
    };

    // Add event listener
    window.addEventListener('viewScoutedCandidates', handleViewScoutedCandidates as EventListener);

    // Check if we have a jobId in localStorage
    if (typeof window !== 'undefined') {
      const storedJobId = localStorage.getItem('viewScoutedCandidatesJobId');
      if (storedJobId === jobId) {
        // Clear the localStorage item
        localStorage.removeItem('viewScoutedCandidatesJobId');

        // Show the Scout Online section
        setShowScoutOnline(true);

        // Scroll to the Scout Online section after a short delay
        setTimeout(() => {
          const scoutOnlineSection = document.getElementById('scout-online-section');
          if (scoutOnlineSection) {
            scoutOnlineSection.scrollIntoView({ behavior: 'smooth' });
          }
        }, 300);
      }
    }

    return () => {
      // Clean up event listener and any relevant state or references when component unmounts
      window.removeEventListener(
        'viewScoutedCandidates',
        handleViewScoutedCandidates as EventListener
      );
      currentJobIdRef.current = null;
    };
  }, [jobId]);

  // Handle URL parameter changes to collapse Scout Online section when appropriate
  useEffect(() => {
    const mode = searchParams?.get('mode');
    const urlJobId = searchParams?.get('jobId');

    // If we're in edit mode and it's for our current job, collapse the Scout Online section
    // This happens when navigating from scout completion modal with "View Result" button
    if (mode === 'edit' && urlJobId === jobId) {
      // Check if we have scouted candidates for this job
      const { candidatesByJobId } = useScoutedCandidatesStore.getState();
      const scoutedCandidates = candidatesByJobId[jobId] || [];

      // If we have scouted candidates, collapse the scout section to show the results
      if (scoutedCandidates.length > 0) {
        setShowScoutOnline(false);
      }
    }
  }, [searchParams, jobId]);

  return (
    <>
      <motion.div className="min-h-full flex flex-col max-w-full z-10 transition-all duration-150">
        {/* Main container with proper spacing for header */}
        <div className="flex flex-col text-foreground overflow-x-hidden px-2 lg:px-4 py-2">
          {/* Candidates Summary Header - ensure it has adequate space */}
          <div className="w-full">
            <CandidateSourcesHeader jobId={jobId} uploadedCandidateCount={uploadedCandidateCount} />
          </div>

          {/* Accordion sections container */}
          <div className="flex-1 space-y-4">
            {/* Talent Hub Section */}
            <TalentHubSection
              jobId={jobId}
              job={job}
              selectedJob={selectedJob}
              showTalentPool={showTalentPool}
              setShowTalentPool={setShowTalentPool}
              isTalentPoolLoading={isTalentPoolLoading}
              setIsTalentPoolLoading={setIsTalentPoolLoading}
              setUploadedCandidateCount={setUploadedCandidateCount}
              onCandidateCountChange={onCandidateCountChange}
            />

            {/* Scout Online Section - Only render if feature flag is enabled */}
            <ScoutOnlineSection
              jobId={jobId}
              job={job}
              selectedJob={selectedJob}
              showScoutOnline={showScoutOnline}
              setShowScoutOnline={setShowScoutOnline}
              isScoutOnlineLoading={isScoutOnlineLoading}
              setIsScoutOnlineLoading={setIsScoutOnlineLoading}
              setUploadedCandidateCount={setUploadedCandidateCount}
              onCandidateCountChange={onCandidateCountChange}
            />

            {/* Upload Resumes Section */}
            <ResumeUploadSection
              showResumeUpload={showResumeUpload}
              setShowResumeUpload={setShowResumeUpload}
              isUploading={isUploading}
              getRootProps={getRootProps}
              getInputProps={getInputProps}
            />

            {/* Total Candidates Section - key based on activeJobs to force re-render */}
            <TotalCandidatesSection
              key={`total-candidates-${jobId}-${activeJobs.length}`}
              jobId={jobId}
              files={files}
              isUploading={isUploading}
              activeJobs={activeJobs}
              handleRankClick={handleRankClick}
              handleDelete={handleDelete}
              useJobEditStore={useJobEditStoreFlag}
            />
          </div>
        </div>
      </motion.div>

      {/* Subscription Limit Modal */}
      {showSubscriptionLimitModal && subscriptionLimitData && (
        <SubscriptionLimitModal
          isOpen={showSubscriptionLimitModal}
          onClose={() => {
            setShowSubscriptionLimitModal(false);
            setSubscriptionLimitData(null);
            // Reset the API block flag if user closes modal
            if (typeof window !== 'undefined') {
              window['blockJobsApiCalls'] = false;
            }
          }}
          actionType={subscriptionLimitData.actionType}
          message={subscriptionLimitData.message}
          requiredCredits={subscriptionLimitData.requiredCredits}
          availableCredits={subscriptionLimitData.availableCredits}
        />
      )}
    </>
  );
};

export default FileUploader;
