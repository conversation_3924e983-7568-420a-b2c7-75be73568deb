'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Building2, Database, GraduationCap, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

export default function EntitiesContent() {
  const router = useRouter();

  const entityTypes = [
    {
      title: 'Employers',
      description: 'View and manage employer registrations',
      icon: Building2,
      href: '/admin/entities/employers',
      color: 'blue',
    },
    {
      title: 'Job Seekers',
      description: 'View and manage job seeker registrations',
      icon: User,
      href: '/admin/entities/job-seekers',
      color: 'purple',
    },
    {
      title: 'Graduates',
      description: 'View and manage graduate registrations',
      icon: GraduationCap,
      href: '/admin/entities/graduates',
      color: 'pink',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-2 bg-purple-500/10 rounded-full">
            <Database className="w-5 h-5 text-purple-400" />
          </div>
          <h1 className="text-2xl font-bold text-white/90">Entities Management</h1>
        </div>
        <p className="text-white/70">
          View and manage all entity registrations. Approve or decline new registrations.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {entityTypes.map(entity => (
          <motion.div
            key={entity.title}
            whileHover={{ scale: 1.02 }}
            className="relative w-full overflow-hidden border border-white/5 shadow-lg rounded-xl h-full flex flex-col hover:shadow-xl backdrop-blur-xl bg-gradient-to-br from-white/5 via-white/5 to-white/5 hover:from-white/10 hover:via-white/10 hover:to-white/10 transition-all duration-300 ease-in-out cursor-pointer"
            onClick={() => router.push(entity.href)}
          >
            <div className="p-4 border-b border-white/5 flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-purple-500/10 rounded-full">
                  <entity.icon className="w-5 h-5 text-purple-400" />
                </div>
                <h3 className="text-md font-medium text-white/80">{entity.title}</h3>
              </div>
            </div>

            <div className="p-4 flex-grow">
              <p className="text-white/70 mb-4">{entity.description}</p>
              <Button
                variant="outline"
                className="w-full border-white/10 hover:bg-white/5"
                onClick={e => {
                  e.stopPropagation();
                  router.push(entity.href);
                }}
              >
                View {entity.title}
              </Button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
