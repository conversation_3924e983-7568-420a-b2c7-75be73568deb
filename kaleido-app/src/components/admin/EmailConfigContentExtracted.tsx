'use client';

import { useEffect, useState } from 'react';

import { AlertCircle, CheckCircle, Edit3, Eye, Mail, Send, Settings, Users, X } from 'lucide-react';

import { apiClient } from '@/lib/apiHelper';

import EmailPreviewDrawer from '../email/EmailPreviewDrawer';

interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  sampleData: any;
  category: 'candidate' | 'company' | 'admin' | 'system';
}

interface EmailHistory {
  id: string;
  templateId: string;
  templateName: string;
  recipient: string;
  subject: string;
  sentAt: Date;
  status: 'sent' | 'failed' | 'pending';
  error?: string;
}

interface BulkRecipient {
  email: string;
  fullName?: string;
}

interface WaitlistUser {
  email: string;
  fullName: string;
  isVip: boolean;
  createdAt: string;
}

interface WaitlistResponse {
  success: boolean;
  recipients: WaitlistUser[];
  total: number;
  vipCount: number;
  regularCount: number;
}

// This is extracted from email-configuration.tsx without the AppLayout wrapper
const EmailConfigContentExtracted: React.FC = () => {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [customContent, setCustomContent] = useState<any>({});
  const [recipientEmail, setRecipientEmail] = useState('');
  const [recipients, setRecipients] = useState<BulkRecipient[]>([]);
  const [bulkMode, setBulkMode] = useState(false);
  const [csvInput, setCsvInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailHistory, setEmailHistory] = useState<EmailHistory[]>([]);
  const [activeTab, setActiveTab] = useState<'templates' | 'send' | 'history'>('templates');
  const [previewMode, setPreviewMode] = useState(false);
  const [sendResult, setSendResult] = useState<{
    success: boolean;
    message: string;
    details?: any;
  } | null>(null);
  const [waitlistUsers, setWaitlistUsers] = useState<WaitlistUser[]>([]);
  const [showWaitlistSelector, setShowWaitlistSelector] = useState(false);

  // Load email templates on component mount
  useEffect(() => {
    loadEmailTemplates();
    loadEmailHistory();
  }, []);

  const loadEmailTemplates = async () => {
    try {
      const data: any = await apiClient.get('/email-admin/templates');

      // Categorize templates
      const categorizedTemplates = data.templates.map((template: any) => ({
        ...template,
        category: getCategoryFromId(template.id),
      }));

      setTemplates(categorizedTemplates);
    } catch (error) {
      console.error('Failed to load email templates:', error);

      // Fallback to mock templates for development/testing
      const mockTemplates: EmailTemplate[] = [
        {
          id: 'job_application_submitted_candidate',
          name: 'Job Application Submitted (Candidate Confirmation)',
          description: 'Confirmation email sent to candidates after applying for a job',
          category: 'candidate' as const,
          sampleData: {
            candidateName: 'John Doe',
            jobTitle: 'Senior Software Engineer',
            companyName: 'Tech Corp',
            applicationDate: new Date().toLocaleDateString(),
          },
        },
        {
          id: 'job_application_received_company',
          name: 'Job Application Received (Company Notification)',
          description: 'Notification sent to companies when receiving a new application',
          category: 'company' as const,
          sampleData: {
            candidateName: 'John Doe',
            jobTitle: 'Senior Software Engineer',
            companyName: 'Tech Corp',
            applicationDate: new Date().toLocaleDateString(),
            candidateEmail: '<EMAIL>',
            candidatePhone: '+****************',
            resumeUrl: 'https://example.com/resume.pdf',
          },
        },
        // Add more mock templates as needed
      ];

      setTemplates(mockTemplates);
    }
  };

  const loadEmailHistory = async () => {
    try {
      const data: any = await apiClient.get('/email-admin/history');
      setEmailHistory(data.history || []);
    } catch (error) {
      console.error('Failed to load email history:', error);
      // Set mock history for development
      setEmailHistory([]);
    }
  };

  const loadWaitlistUsers = async () => {
    try {
      const response: WaitlistResponse = await apiClient.get('/email-admin/waitlist-recipients');

      if (response.success) {
        setWaitlistUsers(response.recipients);
      }
    } catch (error) {
      console.error('Failed to load waitlist users:', error);
    }
  };

  const getCategoryFromId = (templateId: string): EmailTemplate['category'] => {
    if (templateId.includes('candidate')) return 'candidate';
    if (templateId.includes('company')) return 'company';
    if (templateId.includes('admin')) return 'admin';
    return 'system';
  };

  const getCategoryIcon = (category: EmailTemplate['category']) => {
    switch (category) {
      case 'candidate':
        return Users;
      case 'company':
        return Mail;
      case 'admin':
        return Settings;
      case 'system':
        return Settings;
      default:
        return Mail;
    }
  };

  const getCategoryColor = (category: EmailTemplate['category']) => {
    switch (category) {
      case 'candidate':
        return 'from-blue-500 to-cyan-500';
      case 'company':
        return 'from-purple-500 to-pink-500';
      case 'admin':
        return 'from-orange-500 to-red-500';
      case 'system':
        return 'from-green-500 to-emerald-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const handleTemplateSelect = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setCustomContent(template.sampleData || {});
    setActiveTab('send');
  };

  const handleSendEmail = async () => {
    if (!selectedTemplate) return;

    setIsLoading(true);
    setSendResult(null);

    try {
      const emailRecipients = bulkMode ? recipients.map(r => r.email) : [recipientEmail];

      const payload = {
        templateId: selectedTemplate.id,
        recipients: bulkMode
          ? recipients
          : [
              {
                email: recipientEmail,
                fullName: customContent.candidateName || customContent.companyName || 'User',
              },
            ],
        customContent,
        isBulk: bulkMode,
      };

      const response: any = await apiClient.post('/email-admin/send', payload);

      setSendResult({
        success: response.success,
        message:
          response.message ||
          (bulkMode ? 'Bulk emails sent successfully!' : 'Email sent successfully!'),
        details: response,
      });

      // Reload history after sending
      loadEmailHistory();

      // Clear form if successful
      if (response.success) {
        if (bulkMode) {
          setRecipients([]);
          setCsvInput('');
        } else {
          setRecipientEmail('');
        }
      }
    } catch (error: any) {
      setSendResult({
        success: false,
        message: error.message || 'Failed to send email',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addRecipient = (email: string, fullName?: string) => {
    if (!email || !email.trim()) return;

    // Check if email is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      alert('Please enter a valid email address');
      return;
    }

    // Check if already added
    if (recipients.some(r => r.email === email)) {
      alert('This email is already in the list');
      return;
    }

    setRecipients([...recipients, { email, fullName }]);
    setRecipientEmail(''); // Clear input after adding
  };

  const removeRecipient = (email: string) => {
    setRecipients(recipients.filter(r => r.email !== email));
  };

  const clearAllRecipients = () => {
    setRecipients([]);
    setCsvInput('');
  };

  const handleCsvImport = () => {
    if (!csvInput.trim()) return;

    const lines = csvInput.split(/[\n,]+/).map(line => line.trim());
    const newRecipients: BulkRecipient[] = [];

    lines.forEach(line => {
      if (!line) return;

      // Check if line contains both email and name (comma-separated)
      const parts = line.split(',').map(p => p.trim());

      if (parts.length >= 2) {
        // Format: email,name
        const [email, ...nameParts] = parts;
        const fullName = nameParts.join(' ').trim();

        if (email && email.includes('@')) {
          newRecipients.push({ email, fullName });
        }
      } else if (parts[0].includes('@')) {
        // Just email
        newRecipients.push({ email: parts[0] });
      }
    });

    // Add unique recipients only
    const existingEmails = new Set(recipients.map(r => r.email));
    const uniqueNewRecipients = newRecipients.filter(r => !existingEmails.has(r.email));

    setRecipients([...recipients, ...uniqueNewRecipients]);
    setCsvInput(''); // Clear CSV input after import
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Email Configuration</h1>
              <p className="text-purple-200">Manage and send email templates</p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex gap-2">
            {[
              { id: 'templates', label: 'Templates', icon: Mail },
              { id: 'send', label: 'Send Email', icon: Send },
              { id: 'history', label: 'History', icon: Eye },
            ].map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                    activeTab === tab.id
                      ? 'bg-white/20 text-white border border-white/30'
                      : 'text-purple-200 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Templates Tab */}
        {activeTab === 'templates' && (
          <>
            <div className="lg:col-span-3">
              <div className="rounded-2xl border border-white/20 p-6">
                <h2 className="text-xl font-semibold text-white mb-4">Available Email Templates</h2>

                {/* Template Categories */}
                {['candidate', 'company', 'admin', 'system'].map(category => {
                  const categoryTemplates = templates.filter(t => t.category === category);
                  if (categoryTemplates.length === 0) return null;

                  const Icon = getCategoryIcon(category as EmailTemplate['category']);
                  const colorClass = getCategoryColor(category as EmailTemplate['category']);

                  return (
                    <div key={category} className="mb-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className={`p-2 bg-gradient-to-r ${colorClass} rounded-lg`}>
                          <Icon className="w-5 h-5 text-white" />
                        </div>
                        <h3 className="text-lg font-medium text-white capitalize">
                          {category} Templates
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {categoryTemplates.map(template => (
                          <div
                            key={template.id}
                            onClick={() => handleTemplateSelect(template)}
                            className="bg-white/5 backdrop-blur-lg rounded-xl border border-white/10 p-4 cursor-pointer transition-all hover:bg-white/10 hover:border-white/20 hover:scale-105"
                          >
                            <h4 className="text-white font-medium mb-2">{template.name}</h4>
                            <p className="text-purple-200 text-sm mb-3">{template.description}</p>
                            <div className="flex items-center justify-between">
                              <span
                                className={`px-2 py-1 rounded-full text-xs bg-gradient-to-r ${colorClass} text-white`}
                              >
                                {category}
                              </span>
                              <button
                                type="button"
                                title="Edit template"
                                className="text-purple-300 hover:text-white transition-colors"
                              >
                                <Edit3 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}

        {/* Send Email Tab */}
        {activeTab === 'send' && (
          <>
            {/* Email Form */}
            <div className="lg:col-span-2">
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
                <h2 className="text-xl font-semibold text-white mb-4">Send Email</h2>

                {/* Selected Template */}
                {selectedTemplate && (
                  <div className="mb-6 p-4 bg-white/5 rounded-xl border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-white font-medium">{selectedTemplate.name}</h3>
                      <button
                        type="button"
                        onClick={() => setSelectedTemplate(null)}
                        title="Remove selected template"
                        className="text-purple-300 hover:text-white transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    <p className="text-purple-200 text-sm">{selectedTemplate.description}</p>
                  </div>
                )}

                {/* Recipient Email Section */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-white font-medium">Recipients</label>
                    <div className="flex items-center gap-2">
                      <span className="text-purple-200 text-sm">Bulk Mode</span>
                      <button
                        type="button"
                        onClick={() => setBulkMode(!bulkMode)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          bulkMode ? 'bg-purple-500' : 'bg-white/20'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            bulkMode ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>

                  {!bulkMode ? (
                    /* Single Email Mode */
                    <>
                      <input
                        type="email"
                        value={recipientEmail}
                        onChange={e => setRecipientEmail(e.target.value)}
                        placeholder="Enter recipient email address"
                        className="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20"
                      />

                      {/* Quick Email Options */}
                      <div className="flex gap-2 mt-2">
                        {['<EMAIL>', '<EMAIL>'].map(email => (
                          <button
                            key={email}
                            type="button"
                            onClick={() => setRecipientEmail(email)}
                            className="px-3 py-1 text-xs bg-white/5 border border-white/10 rounded-lg text-purple-200 hover:bg-white/10 hover:text-white transition-all"
                          >
                            {email}
                          </button>
                        ))}
                      </div>
                    </>
                  ) : (
                    /* Bulk Email Mode */
                    <>
                      {/* Add Single Recipient */}
                      <div className="flex gap-2 mb-3">
                        <input
                          type="email"
                          value={recipientEmail}
                          onChange={e => setRecipientEmail(e.target.value)}
                          onKeyDown={e => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addRecipient(recipientEmail);
                            }
                          }}
                          placeholder="Enter email and press Enter to add"
                          className="flex-1 px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20"
                        />
                        <button
                          type="button"
                          onClick={() => addRecipient(recipientEmail)}
                          disabled={!recipientEmail.trim()}
                          className="px-4 py-3 bg-purple-500 text-white rounded-xl hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                        >
                          Add
                        </button>
                      </div>

                      {/* Waitlist Users for System Templates */}
                      {selectedTemplate?.category === 'system' && (
                        <div className="mb-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-white font-medium">Waitlist Users</h4>
                            <button
                              type="button"
                              onClick={() => {
                                if (waitlistUsers.length === 0) {
                                  loadWaitlistUsers();
                                }
                                setShowWaitlistSelector(!showWaitlistSelector);
                              }}
                              className="px-3 py-1 text-xs bg-white/5 border border-white/10 rounded-lg text-purple-200 hover:bg-white/10 hover:text-white transition-all"
                            >
                              {showWaitlistSelector ? 'Hide' : 'Show'} Waitlist
                            </button>
                          </div>

                          {showWaitlistSelector && (
                            <div className="bg-white/5 rounded-xl border border-white/10 p-4 max-h-48 overflow-y-auto">
                              {waitlistUsers.length === 0 ? (
                                <p className="text-purple-300 text-sm text-center py-4">
                                  Loading waitlist users...
                                </p>
                              ) : (
                                <div className="space-y-2">
                                  {waitlistUsers.map((user, index) => (
                                    <div
                                      key={index}
                                      className="flex items-center justify-between bg-white/5 rounded-lg px-3 py-2"
                                    >
                                      <div className="flex items-center gap-3">
                                        <div className="flex flex-col">
                                          <span className="text-purple-200 text-sm">
                                            {user.email}
                                          </span>
                                          <span className="text-purple-300 text-xs">
                                            {user.fullName}
                                          </span>
                                        </div>
                                        {user.isVip && (
                                          <span className="px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs rounded-full">
                                            VIP
                                          </span>
                                        )}
                                      </div>
                                      <button
                                        type="button"
                                        onClick={() => addRecipient(user.email, user.fullName)}
                                        disabled={recipients.some(r => r.email === user.email)}
                                        className="px-2 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                                      >
                                        {recipients.some(r => r.email === user.email)
                                          ? 'Added'
                                          : 'Add'}
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}

                      {/* CSV Import */}
                      <div className="mb-3">
                        <label className="block text-purple-200 text-sm mb-2">
                          Bulk Import (Email or Email,Name format)
                        </label>
                        <textarea
                          value={csvInput}
                          onChange={e => setCsvInput(e.target.value)}
                          placeholder="<EMAIL>,John Doe&#10;<EMAIL>,Jane Smith&#10;or just emails separated by commas/newlines"
                          rows={3}
                          className="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 text-sm"
                        />
                        <div className="flex gap-2 mt-2">
                          <button
                            type="button"
                            onClick={handleCsvImport}
                            disabled={!csvInput.trim()}
                            className="px-3 py-1 text-xs bg-green-500/20 border border-green-500/30 text-green-300 rounded-lg hover:bg-green-500/30 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                          >
                            Import Emails
                          </button>
                          <button
                            type="button"
                            onClick={clearAllRecipients}
                            disabled={recipients.length === 0}
                            className="px-3 py-1 text-xs bg-red-500/20 border border-red-500/30 text-red-300 rounded-lg hover:bg-red-500/30 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                          >
                            Clear All
                          </button>
                        </div>
                      </div>

                      {/* Recipients List */}
                      {recipients.length > 0 && (
                        <div className="bg-white/5 rounded-xl border border-white/10 p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-white font-medium">
                              Recipients ({recipients.length})
                            </h4>
                          </div>
                          <div className="max-h-32 overflow-y-auto space-y-2">
                            {recipients.map((recipient, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between bg-white/5 rounded-lg px-3 py-2"
                              >
                                <div className="flex flex-col">
                                  <span className="text-purple-200 text-sm">{recipient.email}</span>
                                  {recipient.fullName && (
                                    <span className="text-purple-300 text-xs">
                                      {recipient.fullName}
                                    </span>
                                  )}
                                </div>
                                <button
                                  type="button"
                                  onClick={() => removeRecipient(recipient.email)}
                                  className="text-red-300 hover:text-red-200 transition-colors"
                                  title="Remove recipient"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* Custom Content Editor */}
                {selectedTemplate && (
                  <div className="mb-6">
                    <label className="block text-white font-medium mb-2">Custom Content</label>
                    <textarea
                      value={JSON.stringify(customContent, null, 2)}
                      onChange={e => {
                        try {
                          setCustomContent(JSON.parse(e.target.value));
                        } catch (error) {
                          // Invalid JSON, keep the text as is for editing
                        }
                      }}
                      rows={8}
                      className="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 font-mono text-sm"
                      placeholder="Edit email content data..."
                    />
                    <p className="text-purple-300 text-xs mt-1">
                      Edit the JSON data to customize email content
                    </p>
                  </div>
                )}

                {/* Send Button */}
                <button
                  type="button"
                  onClick={handleSendEmail}
                  disabled={
                    isLoading ||
                    !selectedTemplate ||
                    (bulkMode ? recipients.length === 0 : !recipientEmail)
                  }
                  className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      {bulkMode ? 'Sending to all recipients...' : 'Sending...'}
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4" />
                      {bulkMode ? `Send to ${recipients.length} Recipients` : 'Send Email'}
                    </>
                  )}
                </button>

                {/* Send Result */}
                {sendResult && (
                  <div
                    className={`mt-4 p-4 rounded-xl border ${
                      sendResult.success
                        ? 'bg-green-500/10 border-green-500/20 text-green-300'
                        : 'bg-red-500/10 border-red-500/20 text-red-300'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {sendResult.success ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <AlertCircle className="w-5 h-5" />
                      )}
                      <span className="font-medium">
                        {sendResult.success ? 'Success!' : 'Error!'}
                      </span>
                    </div>
                    <p className="mt-1 text-sm">{sendResult.message}</p>

                    {/* Bulk Email Results */}
                    {sendResult.details?.summary && (
                      <div className="mt-3 p-3 bg-white/5 rounded-lg">
                        <h4 className="text-white font-medium mb-2">Bulk Send Summary</h4>
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <span className="text-purple-200">Total:</span>
                            <span className="text-white ml-1">
                              {sendResult.details.summary.total}
                            </span>
                          </div>
                          <div>
                            <span className="text-green-300">Successful:</span>
                            <span className="text-white ml-1">
                              {sendResult.details.summary.successful}
                            </span>
                          </div>
                          <div>
                            <span className="text-red-300">Failed:</span>
                            <span className="text-white ml-1">
                              {sendResult.details.summary.failed}
                            </span>
                          </div>
                          <div>
                            <span className="text-purple-200">Success Rate:</span>
                            <span className="text-white ml-1">
                              {sendResult.details.summary.successRate}%
                            </span>
                          </div>
                        </div>

                        {sendResult.details.results?.failed?.length > 0 && (
                          <div className="mt-3">
                            <h5 className="text-red-300 font-medium text-xs mb-1">
                              Failed Recipients:
                            </h5>
                            <div className="max-h-20 overflow-y-auto space-y-1">
                              {sendResult.details.results.failed.map(
                                (failure: any, index: number) => (
                                  <div key={index} className="text-xs text-red-200">
                                    {failure.email}: {failure.error}
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Preview Panel */}
            <div className="lg:col-span-1">
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Email Preview</h3>

                {selectedTemplate ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                      <h4 className="text-white font-medium mb-2">Template Info</h4>
                      <p className="text-purple-200 text-sm mb-2">{selectedTemplate.name}</p>
                      <p className="text-purple-300 text-xs">{selectedTemplate.description}</p>
                    </div>

                    <div className="p-4 bg-white/5 rounded-xl border border-white/10">
                      <h4 className="text-white font-medium mb-2">Sample Data</h4>
                      <pre className="text-purple-200 text-xs overflow-auto max-h-40">
                        {JSON.stringify(selectedTemplate.sampleData, null, 2)}
                      </pre>
                    </div>

                    <button
                      type="button"
                      onClick={() => setPreviewMode(true)}
                      className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all"
                    >
                      <Eye className="w-4 h-4" />
                      Open Email Preview
                    </button>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Mail className="w-12 h-12 text-purple-300 mx-auto mb-3" />
                    <p className="text-purple-200">Select a template to see preview</p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* History Tab */}
        {activeTab === 'history' && (
          <div className="lg:col-span-3">
            <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Email History</h2>

              {emailHistory.length > 0 ? (
                <div className="space-y-4">
                  {emailHistory.map(email => (
                    <div
                      key={email.id}
                      className="bg-white/5 backdrop-blur-lg rounded-xl border border-white/10 p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-white font-medium">{email.templateName}</h3>
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                email.status === 'sent'
                                  ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                                  : email.status === 'failed'
                                    ? 'bg-red-500/20 text-red-300 border border-red-500/30'
                                    : 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                              }`}
                            >
                              {email.status}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <span className="text-purple-300">Recipient:</span>
                              <p className="text-white">{email.recipient}</p>
                            </div>
                            <div>
                              <span className="text-purple-300">Subject:</span>
                              <p className="text-white">{email.subject}</p>
                            </div>
                            <div>
                              <span className="text-purple-300">Sent:</span>
                              <p className="text-white">
                                {new Date(email.sentAt).toLocaleString()}
                              </p>
                            </div>
                          </div>

                          {email.error && (
                            <div className="mt-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                              <span className="text-red-300 text-sm">Error: {email.error}</span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          <button
                            type="button"
                            title="View email details"
                            className="p-2 text-purple-300 hover:text-white hover:bg-white/10 rounded-lg transition-all"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            type="button"
                            title="Resend email"
                            className="p-2 text-purple-300 hover:text-white hover:bg-white/10 rounded-lg transition-all"
                          >
                            <Send className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Mail className="w-16 h-16 text-purple-300 mx-auto mb-4" />
                  <h3 className="text-white font-medium mb-2">No Email History</h3>
                  <p className="text-purple-200">
                    Emails sent through this dashboard will appear here
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Email Preview Drawer */}
      <EmailPreviewDrawer
        isOpen={previewMode}
        onClose={() => setPreviewMode(false)}
        template={selectedTemplate}
        customContent={customContent}
        recipientEmail={recipientEmail}
      />
    </div>
  );
};

export default EmailConfigContentExtracted;
