'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, XCircle } from 'lucide-react';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { apiClient as apiHelper } from '@/lib/apiHelper';
import { showToast } from '@/components/Toaster';

interface Company {
  id: string;
  clientId: string;
  companyName: string;
  contactEmail: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  industry?: string;
  size?: string;
}

interface CompaniesResponse {
  pending: Company[];
  approved: Company[];
}

export default function RegistrationContent() {
  const [companies, setCompanies] = useState<{
    pending: Company[];
    approved: Company[];
  }>({
    pending: [],
    approved: [],
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      setIsLoading(true);
      const response = (await apiHelper.get('/companies/grouped-by-status')) as CompaniesResponse;
      setCompanies(response);
    } catch (error) {
      console.error('Error fetching companies:', error);
      showToast({
        message: 'Failed to fetch company registrations',
        isSuccess: false,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (companyId: string) => {
    try {
      await apiHelper.patch(`/companies/${companyId}/approve`);
      showToast({
        message: 'Company approved successfully',
        isSuccess: true,
      });
      fetchCompanies();
    } catch (error) {
      console.error('Error approving company:', error);
      showToast({
        message: 'Failed to approve company',
        isSuccess: false,
      });
    }
  };

  const handleReject = async (companyId: string) => {
    try {
      await apiHelper.patch(`/companies/${companyId}/reject`);
      showToast({
        message: 'Company rejected',
        isSuccess: true,
      });
      fetchCompanies();
    } catch (error) {
      console.error('Error rejecting company:', error);
      showToast({
        message: 'Failed to reject company',
        isSuccess: false,
      });
    }
  };

  if (isLoading) {
    return <ColorfulSmokeyOrbLoader text="Loading registrations..." useModalBg={false} />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Pending Registrations */}
      <div>
        <h2 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
          <Clock className="w-5 h-5 text-yellow-400" />
          Pending Registrations ({companies.pending.length})
        </h2>
        <div className="grid gap-4">
          {companies.pending.length === 0 ? (
            <div className="bg-white/5 border border-white/10 rounded-lg p-6 text-center">
              <p className="text-white/60">No pending registrations</p>
            </div>
          ) : (
            companies.pending.map(company => (
              <div
                key={company.id}
                className="bg-white/5 border border-white/10 rounded-lg p-4 hover:bg-white/10 transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-medium text-white">{company.companyName}</h3>
                    <p className="text-sm text-white/60">{company.contactEmail}</p>
                    <div className="flex gap-4 mt-2 text-xs text-white/40">
                      {company.industry && <span>Industry: {company.industry}</span>}
                      {company.size && <span>Size: {company.size}</span>}
                    </div>
                    <p className="text-xs text-white/40 mt-1">
                      Applied: {new Date(company.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleApprove(company.id)}
                      className="px-3 py-1 bg-green-500/20 text-green-400 rounded hover:bg-green-500/30 transition-colors"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => handleReject(company.id)}
                      className="px-3 py-1 bg-red-500/20 text-red-400 rounded hover:bg-red-500/30 transition-colors"
                    >
                      Reject
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Approved Companies */}
      <div>
        <h2 className="text-xl font-semibold text-white/90 mb-4 flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-green-400" />
          Approved Companies ({companies.approved.length})
        </h2>
        <div className="grid gap-4">
          {companies.approved.length === 0 ? (
            <div className="bg-white/5 border border-white/10 rounded-lg p-6 text-center">
              <p className="text-white/60">No approved companies yet</p>
            </div>
          ) : (
            companies.approved.map(company => (
              <div key={company.id} className="bg-white/5 border border-white/10 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-medium text-white">{company.companyName}</h3>
                    <p className="text-sm text-white/60">{company.contactEmail}</p>
                    <div className="flex gap-4 mt-2 text-xs text-white/40">
                      {company.industry && <span>Industry: {company.industry}</span>}
                      {company.size && <span>Size: {company.size}</span>}
                    </div>
                    <p className="text-xs text-white/40 mt-1">
                      Approved: {new Date(company.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                  <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">
                    Active
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </motion.div>
  );
}
