'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Building2, GraduationCap, User } from 'lucide-react';

import EmployersTable from '@/components/admin/EmployersTable';
import GraduatesTable from '@/components/admin/GraduatesTable';
import JobSeekersTable from '@/components/admin/JobSeekersTable';
import RegistrationDashboard from '@/components/admin/RegistrationDashboard';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import apiHelper from '@/lib/apiHelper';

// Default stats data from API response
const defaultStats = {
  employers: {
    total: 0,
    approved: 0,
    pending: 0,
    declined: 0,
    newRegistrations: {
      daily: 0,
      weekly: 0,
      monthly: 0,
      yearly: 0,
    },
    registrationTrends: [],
    approvalRate: null,
  },
  jobSeekers: {
    total: 0,
    approved: 0,
    pending: 0,
    declined: 0,
    newRegistrations: {
      daily: 0,
      weekly: 0,
      monthly: 0,
      yearly: 0,
    },
    registrationTrends: [],
    approvalRate: null,
  },
  graduates: {
    total: 0,
    approved: 0,
    pending: 0,
    declined: 0,
    newRegistrations: {
      daily: 0,
      weekly: 0,
      monthly: 0,
      yearly: 0,
    },
    registrationTrends: [],
    approvalRate: null,
  },
};

export default function RegistrationContentWrapper() {
  const [registrationStats, setRegistrationStats] = useState(defaultStats);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('employers');
  const [timeFilter, setTimeFilter] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');

  useEffect(() => {
    const fetchRegistrationStats = async () => {
      setIsLoading(true);
      try {
        const response = await apiHelper.get('/dashboard/registration-stats');
        setRegistrationStats(response);
      } catch (error) {
        console.error('Error fetching registration stats:', error);
        // Keep using the default stats if the API call fails
      } finally {
        setIsLoading(false);
      }
    };

    fetchRegistrationStats();
  }, []);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const handleTimeFilterChange = (value: 'daily' | 'weekly' | 'monthly' | 'yearly') => {
    setTimeFilter(value);
  };

  // Custom tab component that doesn't use router
  interface CustomTabButtonProps {
    id: string;
    label: string;
    icon: React.ReactNode;
    isActive: boolean;
    badgeCount?: number;
  }

  const CustomTabButton: React.FC<CustomTabButtonProps> = ({
    id,
    label,
    icon,
    isActive,
    badgeCount = 0,
  }) => (
    <button
      onClick={() => handleTabChange(id)}
      className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all ${
        isActive
          ? 'bg-purple-700/20 text-white'
          : 'bg-transparent text-white/60 hover:text-white hover:bg-white/5'
      }`}
    >
      <div className="w-4 h-4 flex-shrink-0 mr-4">{icon}</div>
      <span className="text-sm font-medium">{label}</span>
      {badgeCount > 0 && (
        <span className="bg-purple-700/30 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
          {badgeCount > 99 ? '99+' : badgeCount}
        </span>
      )}
    </button>
  );

  if (isLoading) {
    return <ColorfulSmokeyOrbLoader text="Loading registration data..." useModalBg={false} />;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="space-y-6">
        {/* Time filter controls */}
        <div className="flex justify-end mb-4">
          <div className="flex items-center gap-2 bg-white/5 border border-white/10 p-1 rounded-md">
            <button
              onClick={() => handleTimeFilterChange('daily')}
              className={`px-3 py-1 rounded-sm text-sm ${
                timeFilter === 'daily' ? 'bg-purple-700/20 text-white' : 'text-white/70'
              }`}
            >
              Day
            </button>
            <button
              onClick={() => handleTimeFilterChange('weekly')}
              className={`px-3 py-1 rounded-sm text-sm ${
                timeFilter === 'weekly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
              }`}
            >
              Week
            </button>
            <button
              onClick={() => handleTimeFilterChange('monthly')}
              className={`px-3 py-1 rounded-sm text-sm ${
                timeFilter === 'monthly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
              }`}
            >
              Month
            </button>
            <button
              onClick={() => handleTimeFilterChange('yearly')}
              className={`px-3 py-1 rounded-sm text-sm ${
                timeFilter === 'yearly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
              }`}
            >
              Year
            </button>
          </div>
        </div>

        {/* Registration Dashboard Stats - Always visible */}
        {registrationStats ? (
          <>
            <RegistrationDashboard stats={registrationStats} timeFilter={timeFilter} />
          </>
        ) : (
          <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <p className="text-yellow-400">No registration stats available</p>
          </div>
        )}

        {/* Entity selection tabs */}
        <div className="mt-8 mb-4">
          <h2 className="text-xl font-semibold text-white/90 mb-4">Registration Details</h2>
          <div className="flex bg-black/10 backdrop-blur-md rounded-full p-1.5 border border-white/10 w-fit">
            <CustomTabButton
              id="employers"
              label="Employers"
              icon={<Building2 />}
              isActive={activeTab === 'employers'}
              badgeCount={registrationStats?.employers.total || 0}
            />
            <CustomTabButton
              id="jobSeekers"
              label="Job Seekers"
              icon={<User />}
              isActive={activeTab === 'jobSeekers'}
              badgeCount={registrationStats?.jobSeekers.total || 0}
            />
            <CustomTabButton
              id="graduates"
              label="Graduates"
              icon={<GraduationCap />}
              isActive={activeTab === 'graduates'}
              badgeCount={registrationStats?.graduates.total || 0}
            />
          </div>
        </div>

        {/* Entity tables */}
        <div>
          {activeTab === 'employers' && <EmployersTable />}

          {activeTab === 'jobSeekers' && <JobSeekersTable />}

          {activeTab === 'graduates' && <GraduatesTable />}
        </div>
      </div>
    </motion.div>
  );
}
