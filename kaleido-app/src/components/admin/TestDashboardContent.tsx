// This component wraps the test-dashboard page content
// We need to dynamically import it to avoid SSR issues

import dynamic from 'next/dynamic';

// Dynamically import the test dashboard page
const TestDashboardPage = dynamic(() => import('@/app/test-dashboard/page'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-white/60">Loading test dashboard...</div>
    </div>
  ),
});

export default function TestDashboardContent() {
  // The TestDashboardPage is a standalone component that doesn't use AppLayout
  return <TestDashboardPage />;
}
