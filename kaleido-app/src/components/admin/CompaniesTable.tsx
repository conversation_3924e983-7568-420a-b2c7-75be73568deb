import '@/styles/admin.css';

import React, { useEffect, useState } from 'react';

import { formatDistanceToNow } from 'date-fns';
import { motion } from 'framer-motion';
import { Building2, Search } from 'lucide-react';
import Image from 'next/image';

import apiHelper from '@/lib/apiHelper';
import { Info } from '@phosphor-icons/react';

import CompanyDetailsModal from './CompanyDetailsModal';

interface CompanyStats {
  jobCount: number;
  videoJDCount: number;
  candidateCount: number;
  lastActivity: string;
}

interface Company {
  id: string;
  clientId: string;
  companyName: string;
  industry: string;
  size: string;
  location: string;
  logo: string;
  createdAt: string;
  updatedAt: string;
  isApproved: boolean;
  stats?: CompanyStats;
}

interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface CompaniesTableProps {}

const CompaniesTable: React.FC<CompaniesTableProps> = () => {
  const [companiesWithStats, setCompaniesWithStats] = useState<Company[]>([]);
  const [meta, setMeta] = useState<PaginationMeta>({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const fetchCompanies = async (page = 1, search = '') => {
    setIsLoading(true);
    try {
      // Get companies data with stats already included from the optimized endpoint
      const response = await apiHelper.get(
        `/companies/usage?page=${page}&limit=${meta.limit}${search ? `&search=${search}` : ''}`
      );

      // Store the pagination metadata
      setMeta(response.meta);

      // The backend now returns companies with stats already included
      // No need for additional API calls per company
      setCompaniesWithStats(response.data);
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchCompanies(1, searchTerm);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= meta.totalPages) {
      fetchCompanies(newPage, searchTerm);
    }
  };

  const handleOpenDetails = (companyId: string) => {
    setSelectedCompanyId(companyId);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetails = () => {
    setIsDetailsModalOpen(false);
  };

  return (
    <div className="space-y-4">
      {/* Search and filters */}
      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <input
              type="text"
              placeholder="Search companies..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="w-full bg-white/5 border border-white/10 rounded-lg py-2 pl-10 pr-4 text-white placeholder:text-white/40 focus:outline-none focus:ring-2 focus:ring-purple-700/50"
            />
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-white/40" />
          </div>
        </form>
      </div>

      {/* Companies table */}
      <div className="bg-white/5 rounded-lg border border-white/10 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-white/10">
            <thead>
              <tr className="bg-white/5">
                <th className="px-4 py-3 text-left text-xs font-medium text-white/60 uppercase tracking-wider">
                  Company
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-white/60 uppercase tracking-wider">
                  Industry
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-white/60 uppercase tracking-wider">
                  Jobs
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-white/60 uppercase tracking-wider">
                  Video JDs
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-white/60 uppercase tracking-wider">
                  Candidates
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-white/60 uppercase tracking-wider">
                  Last Activity
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-white/60 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {isLoading ? (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-white/60">
                    Loading companies...
                  </td>
                </tr>
              ) : companiesWithStats.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-white/60">
                    No companies found
                  </td>
                </tr>
              ) : (
                companiesWithStats.map((company, index) => (
                  <motion.tr
                    key={company.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="hover:bg-white/5"
                  >
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden bg-white/10 flex items-center justify-center">
                          {company.logo ? (
                            <Image
                              src={company.logo}
                              alt={company.companyName}
                              width={32}
                              height={32}
                              className="h-8 w-8 object-cover"
                            />
                          ) : (
                            <Building2 className="h-4 w-4 text-white/60" />
                          )}
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-white">
                            {company.companyName}
                          </div>
                          <div className="text-xs text-white/60">
                            {company.location || 'No location'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm text-white">
                        {company.industry || 'Not specified'}
                      </div>
                      <div className="text-xs text-white/60">{company.size || 'Unknown size'}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-center">
                      <div className="inline-flex items-center px-2 py-1 rounded-full bg-blue-500/20 text-blue-300">
                        <span className="text-xs font-medium">{company.stats?.jobCount || 0}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-center">
                      <div className="inline-flex items-center px-2 py-1 rounded-full bg-purple-500/20 text-purple-300">
                        <span className="text-xs font-medium">
                          {company.stats?.videoJDCount || 0}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-center">
                      <div className="inline-flex items-center px-2 py-1 rounded-full bg-green-500/20 text-green-300">
                        <span className="text-xs font-medium">
                          {company.stats?.candidateCount || 0}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm text-white/80">
                        {company.stats?.lastActivity
                          ? formatDistanceToNow(new Date(company.stats.lastActivity), {
                              addSuffix: true,
                            })
                          : 'Never'}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-center">
                      <button
                        onClick={() => handleOpenDetails(company.id)}
                        className="inline-flex items-center justify-center p-1.5 rounded-full bg-purple-700/20 text-purple-400 hover:bg-purple-700/30 hover:text-purple-300 transition-colors"
                        title="View details"
                      >
                        <Info className="h-4 w-4" />
                      </button>
                    </td>
                  </motion.tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {meta.totalPages > 1 && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-white/10 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(meta.page - 1)}
                disabled={meta.page === 1}
                className="relative inline-flex items-center px-4 py-2 border border-white/10 text-sm font-medium rounded-md text-white bg-white/5 hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(meta.page + 1)}
                disabled={meta.page === meta.totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-white/10 text-sm font-medium rounded-md text-white bg-white/5 hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-white/60">
                  Showing <span className="font-medium">{(meta.page - 1) * meta.limit + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(meta.page * meta.limit, meta.total)}
                  </span>{' '}
                  of <span className="font-medium">{meta.total}</span> results
                </p>
              </div>
              <div>
                <nav
                  className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                  aria-label="Pagination"
                >
                  <button
                    onClick={() => handlePageChange(meta.page - 1)}
                    disabled={meta.page === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-white/10 bg-white/5 text-sm font-medium text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Previous</span>
                    <svg
                      className="h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                  {Array.from({ length: Math.min(5, meta.totalPages) }, (_, i) => {
                    const pageNum = meta.page <= 3 ? i + 1 : meta.page - 2 + i;
                    if (pageNum <= meta.totalPages) {
                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            meta.page === pageNum
                              ? 'z-10 bg-purple-700/30 border-purple-700 text-white'
                              : 'border-white/10 bg-white/5 text-white hover:bg-white/10'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                    return null;
                  })}
                  <button
                    onClick={() => handlePageChange(meta.page + 1)}
                    disabled={meta.page === meta.totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-white/10 bg-white/5 text-sm font-medium text-white hover:bg-white/10 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Next</span>
                    <svg
                      className="h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Company Details Modal */}
      {selectedCompanyId && (
        <CompanyDetailsModal
          companyId={selectedCompanyId}
          isOpen={isDetailsModalOpen}
          onClose={handleCloseDetails}
        />
      )}
    </div>
  );
};

export default CompaniesTable;
