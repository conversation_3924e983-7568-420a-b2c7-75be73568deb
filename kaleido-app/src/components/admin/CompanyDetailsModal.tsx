import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

import { format } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import {
  BarChart3,
  Briefcase,
  Building2,
  Calendar,
  ChevronLeft,
  ChevronRight,
  CreditCard,
  ExternalLink,
  FileText,
  UserPlus,
  Video,
  X,
} from 'lucide-react';
import Image from 'next/image';

import apiHelper from '@/lib/apiHelper';

interface CompanyDetailsProps {
  companyId: string;
  isOpen: boolean;
  onClose: () => void;
}

interface CompanyDetail {
  id: string;
  companyName: string;
  industry: string;
  size: string;
  location: string;
  logo: string;
  companyWebsite: string;
  contactEmail: string;
  createdAt: string;
  updatedAt: string;
  isApproved: boolean;
  stats: {
    jobCount: number;
    videoJDCount: number;
    candidateCount: number;
    lastActivity: string;
    recentJobs: {
      id: string;
      jobType: string;
      createdAt: string;
      status: string;
      candidateCount: number;
    }[];
    recentVideoJDs: {
      id: string;
      jobType: string;
      createdAt: string;
      status: string;
    }[];
    activityTimeline: {
      date: string;
      action: string;
      details: string;
    }[];
    usageMetrics: {
      jobsCreatedThisMonth: number;
      jobsCreatedLastMonth: number;
      candidatesAddedThisMonth: number;
      candidatesAddedLastMonth: number;
      videoJDsCreatedThisMonth: number;
      videoJDsCreatedLastMonth: number;
    };
  };
  subscription?: {
    plan: string;
    startDate: string;
    endDate: string;
    limits: {
      resumeUploadLimit: number;
      resumeUploadUsed: number;
      scoutLimit: number;
      scoutUsed: number;
      matchRankLimit: number;
      matchRankUsed: number;
      videoJdLimit: number;
      videoJdUsed: number;
      videoJdMaxDuration: number;
      cultureFitQuestionsLimit: number;
      cultureFitQuestionsUsed: number;
      jobDescriptionLimit: number;
      jobDescriptionUsed: number;
      teamMembersLimit: number;
      teamMembersUsed: number;
      atsIntegration: string;
      databaseRetentionMonths: number;
      lastResetDate: string;
    };
  };
}

const CompanyDetailsModal: React.FC<CompanyDetailsProps> = ({ companyId, isOpen, onClose }) => {
  const [companyDetails, setCompanyDetails] = useState<CompanyDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Create portal container when component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      let container = document.getElementById('company-details-portal');
      if (!container) {
        container = document.createElement('div');
        container.id = 'company-details-portal';
        document.body.appendChild(container);
      }
      setPortalContainer(container);

      // Clean up on unmount
      return () => {
        if (container && container.childNodes.length === 0 && document.body.contains(container)) {
          document.body.removeChild(container);
        }
      };
    }
  }, []);

  useEffect(() => {
    if (isOpen && companyId) {
      fetchCompanyDetails();
    }
  }, [isOpen, companyId]);

  const fetchCompanyDetails = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Fetch detailed company data only when modal opens
      // This is called on-demand to avoid heavy queries during initial page load
      const response = await apiHelper.get(`/companies/${companyId}/details`);
      setCompanyDetails(response);
    } catch (error: any) {
      console.error('Error fetching company details:', error);
      setError(error.message || 'Failed to load company details. Please try again.');
      setCompanyDetails(null);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen || !portalContainer) return null;

  // Use createPortal to render the modal directly to the document body
  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-[9999] flex justify-end">
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Side drawer */}
          <motion.div
            initial={{ opacity: 1, x: '100%' }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 1, x: '100%' }}
            transition={{ type: 'spring', damping: 30, stiffness: 300 }}
            className="fixed top-0 right-0 h-full w-full md:w-[70vw] lg:w-[70vw] xl:w-[70vw] bg-gray-900/60 backdrop-blur-xl border-l border-white/10 shadow-2xl overflow-hidden flex flex-col z-[9999]"
          >
            {/* Header with close button */}
            <div className="flex items-center justify-between p-6 border-b border-white/10 sticky top-0 bg-gray-900 z-10">
              <h3 className="text-xl font-semibold text-white">
                {isLoading ? 'Loading...' : companyDetails?.companyName || 'Company Details'}
              </h3>
              <button
                onClick={onClose}
                className="p-2 text-white/60 hover:text-white focus:outline-none hover:bg-white/5 rounded-full transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content - scrollable area */}
            <div className="flex-1 overflow-y-auto p-6">
              {isLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="w-8 h-8 border-t-2 border-b-2 border-purple-500 rounded-full animate-spin"></div>
                </div>
              ) : error ? (
                <div className="p-4 text-red-400 bg-red-900/20 rounded-md">{error}</div>
              ) : companyDetails ? (
                <div className="space-y-6">
                  {/* Company info */}
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="flex-1 space-y-4">
                      <div className="flex items-center gap-4">
                        <div className="flex-shrink-0 w-16 h-16 rounded-full overflow-hidden bg-white/10 flex items-center justify-center">
                          {companyDetails.logo ? (
                            <Image
                              src={companyDetails.logo}
                              alt={companyDetails.companyName}
                              width={64}
                              height={64}
                              className="w-16 h-16 object-cover"
                            />
                          ) : (
                            <Building2 className="w-8 h-8 text-white/60" />
                          )}
                        </div>
                        <div>
                          <h4 className="text-lg font-medium text-white">
                            {companyDetails.companyName}
                          </h4>
                          <p className="text-sm text-white/60">
                            {companyDetails.industry} • {companyDetails.size}
                          </p>
                          <p className="text-sm text-white/60">{companyDetails.location}</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="p-4 bg-white/5 rounded-lg">
                          <p className="text-xs text-white/40 uppercase">Contact</p>
                          <p className="text-sm text-white mt-1">{companyDetails.contactEmail}</p>
                          {companyDetails.companyWebsite && (
                            <a
                              href={companyDetails.companyWebsite}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 text-sm text-purple-400 hover:text-purple-300 mt-1"
                            >
                              <ExternalLink className="w-3 h-3" />
                              Website
                            </a>
                          )}
                        </div>
                        <div className="p-4 bg-white/5 rounded-lg">
                          <p className="text-xs text-white/40 uppercase">Account</p>
                          <p className="text-sm text-white mt-1">
                            Created: {format(new Date(companyDetails.createdAt), 'MMM d, yyyy')}
                          </p>
                          <p className="text-sm text-white/60 mt-1">
                            Status:{' '}
                            {companyDetails.isApproved ? (
                              <span className="text-green-400">Approved</span>
                            ) : (
                              <span className="text-yellow-400">Pending</span>
                            )}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1">
                      <div className="p-4 bg-white/5 rounded-lg h-full">
                        <div className="flex items-center gap-2 mb-4">
                          <BarChart3 className="w-4 h-4 text-purple-400" />
                          <h4 className="text-sm font-medium text-white">Usage Metrics</h4>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-xs text-white/40">Jobs Created</p>
                            <div className="flex items-end gap-2 mt-1">
                              <p className="text-lg font-semibold text-white">
                                {companyDetails.stats.usageMetrics.jobsCreatedThisMonth}
                              </p>
                              <p className="text-xs text-white/60 mb-1">this month</p>
                            </div>
                            <p className="text-xs text-white/40 mt-1">
                              vs {companyDetails.stats.usageMetrics.jobsCreatedLastMonth} last month
                            </p>
                          </div>

                          <div>
                            <p className="text-xs text-white/40">Video JDs</p>
                            <div className="flex items-end gap-2 mt-1">
                              <p className="text-lg font-semibold text-white">
                                {companyDetails.stats.usageMetrics.videoJDsCreatedThisMonth}
                              </p>
                              <p className="text-xs text-white/60 mb-1">this month</p>
                            </div>
                            <p className="text-xs text-white/40 mt-1">
                              vs {companyDetails.stats.usageMetrics.videoJDsCreatedLastMonth} last
                              month
                            </p>
                          </div>

                          <div>
                            <p className="text-xs text-white/40">Candidates Added</p>
                            <div className="flex items-end gap-2 mt-1">
                              <p className="text-lg font-semibold text-white">
                                {companyDetails.stats.usageMetrics.candidatesAddedThisMonth}
                              </p>
                              <p className="text-xs text-white/60 mb-1">this month</p>
                            </div>
                            <p className="text-xs text-white/40 mt-1">
                              vs {companyDetails.stats.usageMetrics.candidatesAddedLastMonth} last
                              month
                            </p>
                          </div>

                          <div>
                            <p className="text-xs text-white/40">Total Activity</p>
                            <div className="flex items-end gap-2 mt-1">
                              <p className="text-lg font-semibold text-white">
                                {companyDetails.stats.jobCount + companyDetails.stats.videoJDCount}
                              </p>
                              <p className="text-xs text-white/60 mb-1">items</p>
                            </div>
                            <p className="text-xs text-white/40 mt-1">
                              {companyDetails.stats.candidateCount} candidates total
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tabs for different sections */}
                  <div className="mt-8">
                    <div className="border-b border-white/10 mb-6">
                      <div className="flex -mb-px">
                        <button className="py-2 px-4 border-b-2 border-purple-500 text-white">
                          Recent Activity
                        </button>
                      </div>
                    </div>

                    {/* Recent Jobs */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-4">
                        <Briefcase className="w-4 h-4 text-blue-400" />
                        <h4 className="text-sm font-medium text-white">Recent Jobs</h4>
                      </div>

                      <div className="bg-white/5 rounded-lg overflow-hidden">
                        <table className="min-w-full divide-y divide-white/10">
                          <thead>
                            <tr className="bg-white/5">
                              <th className="px-4 py-2 text-left text-xs font-medium text-white/60 uppercase">
                                Job Title
                              </th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-white/60 uppercase">
                                Created
                              </th>
                              <th className="px-4 py-2 text-center text-xs font-medium text-white/60 uppercase">
                                Status
                              </th>
                              <th className="px-4 py-2 text-center text-xs font-medium text-white/60 uppercase">
                                Candidates
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-white/10">
                            {companyDetails.stats.recentJobs.map(job => (
                              <tr key={job.id} className="hover:bg-white/5">
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-white">
                                  {job.jobType}
                                </td>
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-white/60">
                                  {format(new Date(job.createdAt), 'MMM d, yyyy')}
                                </td>
                                <td className="px-4 py-2 whitespace-nowrap text-center">
                                  <span
                                    className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                      job.status === 'ACTIVE'
                                        ? 'bg-green-500/20 text-green-400'
                                        : 'bg-gray-500/20 text-gray-400'
                                    }`}
                                  >
                                    {job.status}
                                  </span>
                                </td>
                                <td className="px-4 py-2 whitespace-nowrap text-center text-sm text-white/60">
                                  {job.candidateCount}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {/* Subscription Details */}
                    {companyDetails.subscription && (
                      <div className="mb-6">
                        <div className="flex items-center gap-2 mb-4">
                          <CreditCard className="w-4 h-4 text-pink-400" />
                          <h4 className="text-sm font-medium text-white">Subscription Details</h4>
                        </div>

                        <div className="bg-white/5 rounded-lg p-4">
                          <div className="flex flex-col md:flex-row gap-6">
                            <div className="flex-1">
                              <p className="text-xs text-white/40 uppercase">Plan</p>
                              <p className="text-lg font-semibold text-white mt-1 capitalize">
                                {companyDetails.subscription.plan.toLowerCase()}
                              </p>
                              <div className="mt-3">
                                <p className="text-xs text-white/40 uppercase">
                                  Subscription Period
                                </p>
                                <p className="text-sm text-white mt-1">
                                  {format(
                                    new Date(companyDetails.subscription.startDate),
                                    'MMM d, yyyy'
                                  )}{' '}
                                  -{' '}
                                  {format(
                                    new Date(companyDetails.subscription.endDate),
                                    'MMM d, yyyy'
                                  )}
                                </p>
                              </div>
                            </div>

                            <div className="flex-1">
                              <p className="text-xs text-white/40 uppercase mb-2">Usage Limits</p>
                              <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-white/60">Resume Uploads</span>
                                  <span className="text-sm text-white">
                                    {companyDetails.subscription.limits.resumeUploadUsed} /{' '}
                                    {companyDetails.subscription.limits.resumeUploadLimit}
                                  </span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-white/60">Video JDs</span>
                                  <span className="text-sm text-white">
                                    {companyDetails.subscription.limits.videoJdUsed} /{' '}
                                    {companyDetails.subscription.limits.videoJdLimit}
                                  </span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-white/60">Job Descriptions</span>
                                  <span className="text-sm text-white">
                                    {companyDetails.subscription.limits.jobDescriptionUsed} /{' '}
                                    {companyDetails.subscription.limits.jobDescriptionLimit}
                                  </span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-white/60">Team Members</span>
                                  <span className="text-sm text-white">
                                    {companyDetails.subscription.limits.teamMembersUsed} /{' '}
                                    {companyDetails.subscription.limits.teamMembersLimit}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Recent Video JDs */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-4">
                        <Video className="w-4 h-4 text-purple-400" />
                        <h4 className="text-sm font-medium text-white">Recent Video JDs</h4>
                      </div>

                      <div className="bg-white/5 rounded-lg overflow-hidden">
                        <table className="min-w-full divide-y divide-white/10">
                          <thead>
                            <tr className="bg-white/5">
                              <th className="px-4 py-2 text-left text-xs font-medium text-white/60 uppercase">
                                Job Title
                              </th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-white/60 uppercase">
                                Created
                              </th>
                              <th className="px-4 py-2 text-center text-xs font-medium text-white/60 uppercase">
                                Status
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-white/10">
                            {companyDetails.stats.recentVideoJDs.map(videoJD => (
                              <tr key={videoJD.id} className="hover:bg-white/5">
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-white">
                                  {videoJD.jobType}
                                </td>
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-white/60">
                                  {format(new Date(videoJD.createdAt), 'MMM d, yyyy')}
                                </td>
                                <td className="px-4 py-2 whitespace-nowrap text-center">
                                  <span
                                    className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                      videoJD.status === 'COMPLETED'
                                        ? 'bg-green-500/20 text-green-400'
                                        : 'bg-yellow-500/20 text-yellow-400'
                                    }`}
                                  >
                                    {videoJD.status}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    {/* Activity Timeline */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-blue-400" />
                          <h4 className="text-sm font-medium text-white">Activity Timeline</h4>
                        </div>
                        <div className="text-xs text-white/60">
                          Showing{' '}
                          {Math.min(
                            (currentPage - 1) * itemsPerPage + 1,
                            companyDetails.stats.activityTimeline.length
                          )}{' '}
                          -{' '}
                          {Math.min(
                            currentPage * itemsPerPage,
                            companyDetails.stats.activityTimeline.length
                          )}{' '}
                          of {companyDetails.stats.activityTimeline.length}
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-lg overflow-hidden mb-4">
                        <table className="min-w-full divide-y divide-white/10">
                          <thead>
                            <tr className="bg-white/5">
                              <th className="px-4 py-2 text-left text-xs font-medium text-white/60 uppercase w-12">
                                Icon
                              </th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-white/60 uppercase">
                                Action
                              </th>
                              <th className="px-4 py-2 text-left text-xs font-medium text-white/60 uppercase">
                                Details
                              </th>
                              <th className="px-4 py-2 text-right text-xs font-medium text-white/60 uppercase">
                                Date
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-white/10">
                            {companyDetails.stats.activityTimeline
                              .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                              .map((activity, index) => (
                                <tr key={index} className="hover:bg-white/5">
                                  <td className="px-4 py-3 whitespace-nowrap">
                                    <div className="flex items-center justify-center">
                                      {activity.action.toLowerCase().includes('candidate') ? (
                                        <UserPlus className="w-5 h-5 text-blue-400" />
                                      ) : activity.action.toLowerCase().includes('job') ? (
                                        <Briefcase className="w-5 h-5 text-green-400" />
                                      ) : activity.action.toLowerCase().includes('video') ? (
                                        <Video className="w-5 h-5 text-purple-400" />
                                      ) : (
                                        <FileText className="w-5 h-5 text-yellow-400" />
                                      )}
                                    </div>
                                  </td>
                                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-white">
                                    {activity.action}
                                  </td>
                                  <td className="px-4 py-3 text-sm text-white/60">
                                    {activity.details}
                                  </td>
                                  <td className="px-4 py-3 whitespace-nowrap text-right text-xs text-white/40">
                                    {format(new Date(activity.date), 'MMM d, yyyy')}
                                  </td>
                                </tr>
                              ))}
                          </tbody>
                        </table>
                      </div>

                      {/* Pagination controls */}
                      {companyDetails.stats.activityTimeline.length > itemsPerPage && (
                        <div className="flex justify-center items-center gap-2">
                          <button
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                            className={`p-1 rounded-full ${
                              currentPage === 1
                                ? 'text-white/20 cursor-not-allowed'
                                : 'text-white/60 hover:text-white hover:bg-white/10'
                            }`}
                          >
                            <ChevronLeft className="w-4 h-4" />
                          </button>
                          <div className="text-sm text-white/60">
                            Page {currentPage} of{' '}
                            {Math.ceil(companyDetails.stats.activityTimeline.length / itemsPerPage)}
                          </div>
                          <button
                            onClick={() =>
                              setCurrentPage(prev =>
                                Math.min(
                                  prev + 1,
                                  Math.ceil(
                                    companyDetails.stats.activityTimeline.length / itemsPerPage
                                  )
                                )
                              )
                            }
                            disabled={
                              currentPage >=
                              Math.ceil(companyDetails.stats.activityTimeline.length / itemsPerPage)
                            }
                            className={`p-1 rounded-full ${
                              currentPage >=
                              Math.ceil(companyDetails.stats.activityTimeline.length / itemsPerPage)
                                ? 'text-white/20 cursor-not-allowed'
                                : 'text-white/60 hover:text-white hover:bg-white/10'
                            }`}
                          >
                            <ChevronRight className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>,
    portalContainer
  );
};

export default CompanyDetailsModal;
