'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { X } from 'lucide-react';
import React from 'react';
import { PostedJobDetails } from '@/components/PostedJobs/PostedJobDetails';

interface Job {
  id: string;
  jobType: string;
  department: string;
  companyName: string;
  location?: string[];
  salaryRange?: string;
  experienceLevel?: string;
  typeOfJob?: string;
  isPublished: boolean;
  slug?: string;
  createdAt: string;
  referralSettings?: {
    acceptsReferrals: boolean;
    bountyConfiguration?: {
      type: 'PERCENTAGE' | 'FIXED';
      value: number;
    };
  };
  metrics?: {
    views: number;
    applications: number;
  };
}

interface JobDetailsSliderProps {
  isVisible: boolean;
  job: Job | null;
  onClose: () => void;
  className?: string;
}

export const JobDetailsSlider: React.FC<JobDetailsSliderProps> = ({
  isVisible,
  job,
  onClose,
  className = '',
}) => {
  return (
    <AnimatePresence>
      {isVisible && job && (
        <motion.div
          initial={{ y: '100%', opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: '100%', opacity: 0 }}
          transition={{
            type: 'spring',
            damping: 25,
            stiffness: 300,
            opacity: { duration: 0.3 },
          }}
          className={`fixed inset-0 z-50 ${className}`}
        >
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Slider Content */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
            }}
            className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-slate-900/95 via-purple-950/95 to-slate-900/95 backdrop-blur-xl border-t border-purple-400/30 shadow-2xl shadow-purple-500/40 max-h-[85vh] overflow-hidden"
          >
            {/* Top accent line */}
            <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-purple-400/60 to-transparent" />
            <div className="absolute -top-1 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-pink-400/40 to-transparent" />

            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <div>
                <h2 className="text-xl font-semibold text-white">Job Details</h2>
                <p className="text-sm text-white/60 mt-1">
                  {job.jobType} at {job.companyName}
                </p>
              </div>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={onClose}
                className="p-2.5 bg-white/10 hover:bg-white/15 border border-white/20 hover:border-white/30 rounded-lg transition-all duration-200"
              >
                <X className="w-5 h-5 text-white/90" />
              </motion.button>
            </div>

            {/* Content */}
            <div className="overflow-y-auto max-h-[calc(85vh-120px)]">
              <PostedJobDetails job={job} onClose={onClose} showEditOptions={false} />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default JobDetailsSlider;
