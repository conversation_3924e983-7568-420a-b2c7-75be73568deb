'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import {
  AlertCircle,
  ArrowRight,
  Brain,
  CheckCircle,
  Rocket,
  Sparkles,
  Target,
  TrendingUp,
  X,
} from 'lucide-react';
import Image from 'next/image';

interface RecruitmentMaturityModalProps {
  isOpen: boolean;
  onClose: () => void;
  score: number;
  formData: {
    firstName: string;
    lastName: string;
    position: string;
    company: string;
  };
}

interface MaturityLevel {
  range: [number, number];
  title: string;
  level: string;
  description: string;
  variant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  strengths: string[];
  challenges: string[];
  recommendations: string[];
  nextStep: string;
  percentile?: string;
}

const maturityLevels: MaturityLevel[] = [
  {
    range: [34, 40],
    title: 'Transformative',
    level: 'Transformative',
    description: "You're in the top 10% of companies when it comes to recruitment efficiency.",
    variant: 'success',
    percentile: 'top 10%',
    strengths: [
      'Exceptional candidate quality and application volume management',
      'Streamlined processes that save time and money',
      'Strong Video Intro Assessment and low turnover rates',
      'Integrated technology stack that enhances efficiency',
      'Future-focused approach to skills and talent planning',
    ],
    challenges: [
      'Even rockstars have room for innovation',
      'Potential blind spots in emerging recruitment trends',
      'Opportunity to leverage AI for even better matching',
      'Video-first approach could enhance candidate engagement',
    ],
    recommendations: [
      "Enhance with AI: Leverage Kaleido's AI-powered matching to identify hidden gems",
      'Video JD: Add video job descriptions for higher engagement',
      'Video Introduction: Get to know your talents faster and cut through the noise',
    ],
    nextStep:
      "You're already succeeding - imagine what you could achieve with cutting-edge AI enhancement. Join our Founder's Circle to influence the future of recruitment.",
  },
  {
    range: [26, 33],
    title: 'Optimized',
    level: 'Optimized Recruitment Process',
    description: "You're performing well but missing opportunities for optimization.",
    variant: 'primary',
    strengths: [
      'Good foundation with reasonable time-to-fill',
      'Decent candidate quality and manageable processes',
      'Some technology integration in place',
      'Awareness of cultural fit importance',
    ],
    challenges: [
      'Qualified candidates lost to faster, more engaging processes',
      'Time wasted on manual screening could be automated',
      'Job descriptions may not attract ideal candidates',
      'Missing data-driven insights for optimization',
    ],
    recommendations: [
      'Smart Automation: Implement AI-powered screening to focus on qualified candidates',
      'Enhanced Job Descriptions: Use our tool to create compelling, targeted descriptions',
      'Video Integration: Add video elements to stand out from competitors',
      'Analytics Dashboard: Track and optimize your recruitment ROI',
    ],
    nextStep:
      "You're doing good work - let's make it great. Join our Early Access program to see how AI can amplify your existing strengths.",
  },
  {
    range: [18, 25],
    title: 'Developing',
    level: 'Developing Recruitment Process',
    description: "You're facing common challenges that are costing you time and talent.",
    variant: 'warning',
    strengths: [
      'Basic processes in place',
      'Some awareness of recruitment metrics',
      'Effort toward candidate experience',
      'Recognition that improvement is needed',
    ],
    challenges: [
      'Long time-to-fill means top candidates go elsewhere',
      'Unqualified applications waste significant time',
      'Poor Video Intro Assessment leads to early turnover',
      'Fragmented tools create inefficiencies',
    ],
    recommendations: [
      'Process Overhaul: Implement integrated recruitment platform',
      'AI-Powered Matching: Reduce screening time',
      'Cultural Fit Tools: Use our assessment features to improve retention',
      'Quick Wins: Start with job description optimization for immediate impact',
    ],
    nextStep:
      'The gap between you and top performers is closeable. Take our free assessment to see exactly where to focus first.',
  },
  {
    range: [10, 17],
    title: 'Foundational',
    level: 'Foundational Recruitment Process',
    description:
      'Your recruitment process has significant issues that are seriously impacting your business.',
    variant: 'danger',
    strengths: [
      "You're aware enough to take this assessment",
      'Basic hiring processes exist',
      'Some candidates are still being hired',
      'Recognition that change is needed',
    ],
    challenges: [
      'Extremely long hiring cycles lose 90% of top candidates',
      'Poor job descriptions attract wrong candidates',
      'Manual processes waste 60%+ of your time',
      'High turnover suggests fundamental matching problems',
      'Lack of Video Intro Assessment creates retention issues',
    ],
    recommendations: [
      'Urgent Process Redesign: Complete recruitment transformation needed',
      'AI Implementation: Automate screening and matching immediately',
      'Professional Development: Invest in modern recruitment training',
      'Technology Upgrade: Move from manual to integrated AI platform',
    ],
    nextStep:
      'Your recruitment challenges are costing you significant money and talent. Book a free strategy call to create your transformation roadmap.',
  },
];

const getMaturityLevel = (score: number): MaturityLevel => {
  return (
    maturityLevels.find(level => score >= level.range[0] && score <= level.range[1]) ||
    maturityLevels[maturityLevels.length - 1]
  );
};

export default function RecruitmentMaturityModal({
  isOpen,
  onClose,
  score,
  formData,
}: RecruitmentMaturityModalProps) {
  const maturityLevel = getMaturityLevel(score);

  const modalImage = '/images/landing/open-jobs/open-jobs-7.webp';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden p-0">
        <div className="flex h-full">
          {/* Left Side - Image Panel */}
          <div className="hidden md:flex md:w-2/5 relative overflow-hidden">
            <Image
              src={modalImage}
              alt="Recruitment Maturity Assessment"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            <div className="absolute bottom-6 left-6 right-6">
              <div className="bg-white/10 backdrop-blur-md rounded-lg p-4 border border-white/20">
                <h3 className="text-white font-semibold text-lg mb-2">
                  Your Maturity: {maturityLevel.title}
                </h3>
                <p className="text-white/80 text-sm">
                  Score: {score}/40 -{' '}
                  {maturityLevel.percentile && `Top ${maturityLevel.percentile}`}
                </p>
              </div>
            </div>
          </div>

          {/* Right Side - Content Panel */}
          <div className="flex-1 md:w-3/5 relative">
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
            >
              <X className="w-5 h-5 text-gray-600" />
            </button>

            <div className="space-y-8 h-full overflow-y-auto p-8">
              {/* Header */}
              <div className="text-center space-y-3">
                <h2 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Your Maturity is {maturityLevel.title}
                </h2>
                <p className="text-2xl font-semibold text-gray-800">(Score {score}-40)</p>
              </div>

              {/* Level Description */}
              <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6">
                <div className="flex items-start gap-3">
                  <TrendingUp className="w-6 h-6 text-purple-600 mt-1" />
                  <div>
                    <p className="font-semibold text-lg text-gray-800">
                      Your Level: <span className="text-purple-600">{maturityLevel.level}</span>
                    </p>
                    <p className="text-gray-600 mt-1 italic">{maturityLevel.description}</p>
                  </div>
                </div>
              </div>

              {/* Strengths Section */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  What You're Doing Well:
                </h3>
                <ul className="space-y-2">
                  {maturityLevel.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Sparkles className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{strength}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Challenges Section */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  <AlertCircle className="w-6 h-6 text-orange-600" />
                  Where You Might Be Losing Talent:
                </h3>
                <ul className="space-y-2">
                  {maturityLevel.challenges.map((challenge, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Target className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{challenge}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Final Summary Section */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                  <Brain className="w-6 h-6 text-purple-600" />
                  Strategic Final Summary:
                </h3>
                <ul className="space-y-3">
                  {maturityLevel.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Rocket className="w-5 h-5 text-purple-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">
                        <span className="font-semibold">{recommendation.split(':')[0]}:</span>
                        {recommendation.split(':')[1]}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Next Step */}
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 text-white">
                <h3 className="text-xl font-bold mb-3 flex items-center gap-2">
                  <ArrowRight className="w-6 h-6" />
                  Suggested Next Step:
                </h3>
                <p className="text-lg leading-relaxed">{maturityLevel.nextStep}</p>
              </div>

              {/* Kaleido Talent Section */}
              <div className="border-t pt-8 space-y-6">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    Unlocking Future-Ready Recruitment with Kaleido Talent
                  </h3>
                  <div className="flex justify-center mb-6">
                    <Image
                      src="/images/logos/kaleido-logo-full.webp"
                      alt="Kaleido Talent"
                      width={200}
                      height={80}
                      className="object-contain"
                    />
                  </div>
                </div>

                <p className="text-gray-700 text-center leading-relaxed">
                  Beyond merely addressing current recruitment challenges, Kaleido Talent offers
                  long-term strategic advantages, positioning itself as more than just a recruitment
                  tool.
                </p>

                <div className="space-y-4">
                  {[
                    {
                      title: 'AI Meets EI for Holistic Hiring',
                      color: 'text-purple-600',
                    },
                    {
                      title: 'Data-Driven Insights for Strategic Decisions',
                      color: 'text-pink-600',
                    },
                    {
                      title: 'Enhanced Candidate Experience as a Competitive Edge',
                      color: 'text-purple-600',
                    },
                    {
                      title: 'Future-Proofing Talent Strategy',
                      color: 'text-pink-600',
                    },
                    {
                      title: 'Credibility and Trust',
                      color: 'text-purple-600',
                    },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <CheckCircle className={`w-6 h-6 ${item.color}`} />
                      <span className="text-lg font-semibold text-gray-800">{item.title}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6">
                <Button
                  onClick={() => window.open('/contact', '_blank')}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 text-lg font-medium transition-all transform hover:scale-105"
                >
                  Get Started with Kaleido
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button
                  onClick={onClose}
                  variant="outline"
                  className="border-2 border-purple-600 text-purple-600 hover:bg-purple-50 px-8 py-3 text-lg font-medium"
                >
                  Close Assessment
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
