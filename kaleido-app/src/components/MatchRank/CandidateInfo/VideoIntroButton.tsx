import React, { useState } from 'react';

import { Mail, Video } from 'lucide-react';

import CulturalFitSetupModal from '@/components/CultureFit/CulturalFitSetupModal';
import { showToast } from '@/components/Toaster';
import { apiClient } from '@/lib/apiHelper';

interface VideoIntroButtonProps {
  candidateId: string;
  candidateName: string;
  candidateEmail?: string;
  candidateSource?: string;
  jobId: string;
  jobTitle: string;
  videoIntroEmailSent?: boolean;
  emailCorrespondence?: Array<{
    id: string;
    type: 'SENT' | 'RECEIVED';
    subject: string;
    content: string;
    from: string;
    to: string;
    timestamp: Date;
    emailType: 'interview' | 'offer' | 'status' | 'general';
    metadata?: {
      jobId?: string;
      jobTitle?: string;
      companyName?: string;
      [key: string]: any;
    };
  }>;
}

export const VideoIntroButton: React.FC<VideoIntroButtonProps> = ({
  candidateId,
  candidateName,
  candidateEmail,
  candidateSource,
  jobId,
  jobTitle,
  videoIntroEmailSent = false,
  emailCorrespondence = [],
}) => {
  // Determine if candidate is from upload or scout source (not job-seeker)
  const isUploadOrScout =
    candidateSource &&
    (candidateSource === 'RESUME_UPLOAD' ||
      candidateSource === 'LINKEDIN_SCOUT' ||
      candidateSource === 'scout' ||
      candidateSource === 'upload');
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [showCulturalFitSetupModal, setShowCulturalFitSetupModal] = useState(false);
  const [culturalFitSetupMessage, setCulturalFitSetupMessage] = useState('');
  const [localEmailSent, setLocalEmailSent] = useState(false);

  // Use the computed field from backend, with fallback to emailCorrespondence check, and local state
  const hasVideoIntroEmailSent =
    videoIntroEmailSent ||
    localEmailSent ||
    emailCorrespondence.some(
      email =>
        email.type === 'SENT' &&
        (email.subject.includes('Shortlisted') ||
          email.metadata?.emailType === 'video_intro_shortlist') &&
        email.metadata?.jobId === jobId
    );

  const handleSendVideoIntroEmail = async () => {
    if (!candidateEmail || !candidateId || !jobId || isSendingEmail) {
      showToast({
        message: 'Missing required information to send email',
        type: 'error',
      });
      return;
    }

    setIsSendingEmail(true);
    try {
      const response = await apiClient.post('/email/invite-video-intro', {
        candidateEmail,
        candidateName,
        jobTitle,
        jobId,
        candidateId,
      });

      if (response) {
        showToast({
          message: 'Video introduction email sent successfully!',
          type: 'success',
        });
        // Update local state to disable the button immediately
        setLocalEmailSent(true);
      }
    } catch (error: any) {
      console.error('Failed to send video intro email:', error);

      // Check if it's the cultural fit questions missing error
      // Handle both ApiError (from apiHelper) and direct axios errors
      const errorMessage = error.message || error.response?.data?.message || 'Failed to send email';
      const errorStatus = error.status || error.response?.status;

      if (
        errorStatus === 400 &&
        !isUploadOrScout &&
        (errorMessage.includes('cultural fit questions') ||
          errorMessage.includes('cultural fit') ||
          errorMessage.includes('video intro') ||
          errorMessage.includes('questionnaire'))
      ) {
        setCulturalFitSetupMessage(
          errorMessage ||
            'Video Introduction questionnaires can be set up to enhance candidate evaluation.'
        );
        setShowCulturalFitSetupModal(true);
      } else {
        showToast({
          message: errorMessage,
          type: 'error',
        });
      }
    } finally {
      setIsSendingEmail(false);
    }
  };

  if (!candidateEmail) {
    return null;
  }

  return (
    <>
      <div className="pt-2">
        <div className="w-full flex">
          <button
            type="button"
            onClick={handleSendVideoIntroEmail}
            disabled={isSendingEmail || hasVideoIntroEmailSent}
            className={`
            flex items-center justify-center gap-2 px-6 py-2.5 rounded-xl text-sm font-medium transition-all duration-300
            backdrop-blur-sm shadow-lg relative overflow-hidden border min-w-[200px]
            ${
              hasVideoIntroEmailSent
                ? 'bg-gradient-to-br from-white/25 via-emerald-500/40 to-green-500/50 border-white/40 text-white cursor-not-allowed shadow-emerald-500/30'
                : isSendingEmail
                  ? 'bg-gradient-to-br from-white/15 via-gray-400/30 to-gray-500/40 border-white/25 text-white cursor-not-allowed shadow-gray-500/20'
                  : 'bg-gradient-to-br from-white/20 via-purple-500/30 to-fuchsia-500/40 border-white/30 text-white hover:shadow-xl hover:shadow-purple-500/40 hover:scale-[1.02] active:scale-[0.98] hover:from-white/25 hover:via-purple-500/40 hover:to-fuchsia-500/50'
            }
          `}
          >
            {hasVideoIntroEmailSent ? (
              <>
                <Mail className="w-4 h-4" />
                <span className="font-medium">
                  {isUploadOrScout ? 'Resend Interest Email' : 'Resend Video Intro Email'}
                </span>
              </>
            ) : isSendingEmail ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span className="font-medium">Sending...</span>
              </>
            ) : (
              <>
                {isUploadOrScout ? <Mail className="w-4 h-4" /> : <Video className="w-4 h-4" />}
                <span className="font-medium">
                  {isUploadOrScout ? 'Send Interest Email' : 'Send Video Intro Email'}
                </span>
              </>
            )}
          </button>
        </div>
        {hasVideoIntroEmailSent && (
          <p className="text-xs text-purple-300 mt-2 text-center opacity-80">
            {isUploadOrScout
              ? 'Interest email sent - awaiting response'
              : 'Video introduction email sent - awaiting response'}
          </p>
        )}
      </div>

      {/* Cultural Fit Setup Modal */}
      <CulturalFitSetupModal
        isOpen={showCulturalFitSetupModal && !hasVideoIntroEmailSent}
        onClose={() => setShowCulturalFitSetupModal(false)}
        jobId={jobId}
        message={culturalFitSetupMessage}
        onSkip={() => {
          setShowCulturalFitSetupModal(false);
          // Optionally retry sending the email with skipCulturalFit flag
          // This would require updating the backend endpoint to accept this flag
        }}
      />
    </>
  );
};
