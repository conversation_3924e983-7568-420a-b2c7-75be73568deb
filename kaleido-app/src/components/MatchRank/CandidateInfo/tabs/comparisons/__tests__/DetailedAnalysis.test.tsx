import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DetailedAnalysis } from '../DetailedAnalysis';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock recharts components
jest.mock('recharts', () => ({
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
}));

describe('DetailedAnalysis', () => {
  const mockCandidateAnalysis = {
    '<PERSON>': {
      scores: {
        skills: 75,
        experience: 70,
        leadership: 55,
        culturalFit: 70,
        availability: 85,
      },
      strengths: ['Strong background in marketing', 'Experience in content production'],
      weaknesses: ['Less focus on brand marketing', 'Limited leadership experience'],
      overallRank: 2,
      keyDifferentiator: 'Content production expertise',
      uniqueAdvantages: ['Proficient in graphic design'],
      riskFactors: ['May lack depth in brand strategy'],
    },
    'Christian Malone': {
      scores: {
        skills: 70,
        experience: 80,
        leadership: 60,
        culturalFit: 75,
        availability: 90,
      },
      strengths: ['Strong attention to detail', 'Experience in brand marketing'],
      weaknesses: ['Limited direct experience in art direction'],
      overallRank: 1,
      keyDifferentiator: 'Brand marketing background',
      uniqueAdvantages: ['Extensive experience in sustainability'],
      riskFactors: ['Potential mismatch in creative design skills'],
    },
  };

  const mockCandidateAnalysisWithIds = {
    'b322ae9f-d1b2-4c6b-861e-e8236adc2eff': {
      name: 'David Wallace',
      scores: { skills: 75, experience: 70, leadership: 55, culturalFit: 70, availability: 85 },
      strengths: ['Marketing background'],
      weaknesses: ['Limited leadership'],
      overallRank: 2,
    },
    'bb002a51-ee2f-48cb-b775-fbda3605e839': {
      name: 'Christian Malone',
      scores: { skills: 70, experience: 80, leadership: 60, culturalFit: 75, availability: 90 },
      strengths: ['Brand marketing'],
      weaknesses: ['Limited art direction'],
      overallRank: 1,
    },
  };

  const mockCandidateAnalysisWithUUIDs = {
    'candidate-uuid-1': {
      scores: { skills: 80, experience: 75, leadership: 60, culturalFit: 70, availability: 85 },
      strengths: ['Technical skills'],
      weaknesses: ['Communication'],
      overallRank: 1,
    },
    candidate2: {
      scores: { skills: 70, experience: 80, leadership: 70, culturalFit: 75, availability: 80 },
      strengths: ['Leadership'],
      weaknesses: ['Technical depth'],
      overallRank: 2,
    },
  };

  const mockMetadata = {
    jobTitle: 'Art Director',
    companyName: 'Test Company',
    candidateNames: [
      { id: 'b322ae9f-d1b2-4c6b-861e-e8236adc2eff', name: 'David Wallace' },
      { id: 'bb002a51-ee2f-48cb-b775-fbda3605e839', name: 'Christian Malone' },
    ],
  };

  const mockHeadToHeadComparisons = [
    {
      candidate1: 'Christian Malone',
      candidate2: 'David Wallace',
      keyDifference: 'Christian has stronger brand marketing background',
      recommendation: 'Choose Christian for strategic work',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render candidate analysis when data is provided', () => {
      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
          headToHeadComparisons={mockHeadToHeadComparisons}
        />
      );

      expect(screen.getAllByText('David Wallace').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Christian Malone').length).toBeGreaterThan(0);
    });

    it('should show no data message when candidateAnalysis is null', () => {
      render(<DetailedAnalysis candidateAnalysis={null} />);

      expect(screen.getByText('No analysis data available')).toBeInTheDocument();
    });

    it('should show empty data message when candidateAnalysis is empty object', () => {
      render(<DetailedAnalysis candidateAnalysis={{}} />);

      expect(screen.getByText('Analysis Data Present But Empty')).toBeInTheDocument();
      expect(
        screen.getByText('The candidate analysis object exists but contains no candidate data.')
      ).toBeInTheDocument();
    });
  });

  describe('Name Resolution', () => {
    it('should use direct names when available', () => {
      render(
        <DetailedAnalysis candidateAnalysis={mockCandidateAnalysis} metadata={mockMetadata} />
      );

      expect(screen.getByText('David Wallace')).toBeInTheDocument();
      expect(screen.getByText('Christian Malone')).toBeInTheDocument();
    });

    it('should use name from analysis data when key is UUID', () => {
      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysisWithIds}
          metadata={mockMetadata}
        />
      );

      expect(screen.getByText('David Wallace')).toBeInTheDocument();
      expect(screen.getByText('Christian Malone')).toBeInTheDocument();
    });

    it('should format UUID keys into readable names', () => {
      const uuidAnalysis = {
        'a1b2c3d4-e5f6-7890-abcd-ef1234567890': {
          scores: { skills: 80 },
          strengths: ['Good skills'],
          overallRank: 1,
        },
      };

      render(<DetailedAnalysis candidateAnalysis={uuidAnalysis} />);

      expect(screen.getByText('Candidate a1b2c3d4')).toBeInTheDocument();
    });

    it('should format candidateX patterns correctly', () => {
      const candidatePatternAnalysis = {
        candidate1: {
          scores: { skills: 80 },
          strengths: ['Good skills'],
          overallRank: 1,
        },
        candidate2: {
          scores: { skills: 75 },
          strengths: ['Decent skills'],
          overallRank: 2,
        },
      };

      render(<DetailedAnalysis candidateAnalysis={candidatePatternAnalysis} />);

      expect(screen.getByText('Candidate1')).toBeInTheDocument();
      expect(screen.getByText('Candidate2')).toBeInTheDocument();
    });

    it('should properly capitalize irregular names', () => {
      const irregularNamesAnalysis = {
        'john DOE': {
          scores: { skills: 80 },
          strengths: ['Good skills'],
          overallRank: 1,
        },
      };

      render(<DetailedAnalysis candidateAnalysis={irregularNamesAnalysis} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('should resolve names from metadata when available', () => {
      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysisWithIds}
          metadata={mockMetadata}
        />
      );

      // Should use names from metadata, not the UUIDs
      expect(screen.getAllByText('David Wallace').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Christian Malone').length).toBeGreaterThan(0);
      expect(screen.queryByText('b322ae9f-d1b2-4c6b-861e-e8236adc2eff')).not.toBeInTheDocument();
    });
  });

  describe('Score Calculation and Display', () => {
    it('should calculate and display overall scores correctly', () => {
      render(
        <DetailedAnalysis candidateAnalysis={mockCandidateAnalysis} metadata={mockMetadata} />
      );

      // David's overall score: (75+70+55+70+85)/5 = 71%
      expect(screen.getAllByText('71%').length).toBeGreaterThan(0);
      // Christian's overall score: (70+80+60+75+90)/5 = 75%
      expect(screen.getAllByText('75%').length).toBeGreaterThan(0);
    });

    it('should handle missing scores gracefully', () => {
      const analysisWithMissingScores = {
        'Test Candidate': {
          scores: { skills: 80 }, // Missing other scores
          strengths: ['Good skills'],
          overallRank: 1,
        },
      };

      render(<DetailedAnalysis candidateAnalysis={analysisWithMissingScores} />);

      // Should still render without crashing
      expect(screen.getByText('Test Candidate')).toBeInTheDocument();
    });

    it('should display top metrics correctly', () => {
      render(
        <DetailedAnalysis candidateAnalysis={mockCandidateAnalysis} metadata={mockMetadata} />
      );

      // Should show the highest scoring metrics for each candidate
      expect(screen.getAllByText('Skills').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Experience').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Availability').length).toBeGreaterThan(0);
    });

    it('should sort candidates by rank', () => {
      render(
        <DetailedAnalysis candidateAnalysis={mockCandidateAnalysis} metadata={mockMetadata} />
      );

      const candidates = screen.getAllByText(/\d+%$/); // Get all percentage scores
      // Christian (rank 1) should appear before David (rank 2)
      const textContent = document.body.textContent || '';
      const christianIndex = textContent.indexOf('Christian Malone');
      const davidIndex = textContent.indexOf('David Wallace');
      expect(christianIndex).toBeLessThan(davidIndex);
    });
  });

  describe('Head-to-Head Comparisons', () => {
    it('should display head-to-head comparisons when provided', () => {
      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
          headToHeadComparisons={mockHeadToHeadComparisons}
        />
      );

      expect(screen.getByText('Key Differences')).toBeInTheDocument();
      expect(
        screen.getByText('Christian has stronger brand marketing background')
      ).toBeInTheDocument();
      expect(screen.getByText('Choose Christian for strategic work')).toBeInTheDocument();
      expect(screen.getByText('vs')).toBeInTheDocument();
    });

    it('should not display head-to-head section when no comparisons provided', () => {
      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
          headToHeadComparisons={[]}
        />
      );

      expect(screen.queryByText('Key Differences')).not.toBeInTheDocument();
    });

    it('should match candidate colors correctly in comparisons', () => {
      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
          headToHeadComparisons={mockHeadToHeadComparisons}
        />
      );

      // Should render the vs section
      expect(screen.getByText('vs')).toBeInTheDocument();
      // Should have colored indicators for each candidate
      const comparisonElement = screen.getByText('vs').closest('div');
      expect(comparisonElement).toBeInTheDocument();
    });
  });

  describe('Debug Logging', () => {
    it('should log debug information on mount', () => {
      const consoleSpy = jest.spyOn(console, 'log');

      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
          headToHeadComparisons={mockHeadToHeadComparisons}
        />
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        '🔍 DetailedAnalysis Debug Data:',
        expect.objectContaining({
          candidateAnalysis: mockCandidateAnalysis,
          candidateAnalysisKeys: ['David Wallace', 'Christian Malone'],
          metadata: mockMetadata,
        })
      );
    });

    it('should show debug details for empty data', () => {
      render(<DetailedAnalysis candidateAnalysis={{}} />);

      const detailsElement = screen.getByText('Show raw data');
      expect(detailsElement).toBeInTheDocument();

      // Click to expand details
      fireEvent.click(detailsElement);
      expect(screen.getByText('{}')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed candidate data gracefully', () => {
      const malformedData = {
        'Valid Candidate': {
          scores: { skills: 80 },
          strengths: ['Good skills'],
          overallRank: 1,
        },
        'Invalid Candidate': null, // Invalid data
      };

      render(<DetailedAnalysis candidateAnalysis={malformedData} />);

      // Should render valid candidate and handle invalid one
      expect(screen.getByText('Valid Candidate')).toBeInTheDocument();
      // Should not crash on invalid data
      expect(screen.getByText('Invalid Candidate')).toBeInTheDocument();
    });

    it('should handle missing metadata gracefully', () => {
      render(
        <DetailedAnalysis
          candidateAnalysis={mockCandidateAnalysisWithIds}
          metadata={null}
          headToHeadComparisons={mockHeadToHeadComparisons}
        />
      );

      // Should still render candidates with fallback names
      expect(screen.getAllByText('David Wallace').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Christian Malone').length).toBeGreaterThan(0);
    });
  });

  describe('Chart Integration', () => {
    it('should render pie charts for each candidate', () => {
      render(
        <DetailedAnalysis candidateAnalysis={mockCandidateAnalysis} metadata={mockMetadata} />
      );

      // Should have pie charts for visualization
      const pieCharts = screen.getAllByTestId('pie-chart');
      expect(pieCharts.length).toBeGreaterThan(0);
    });
  });
});
