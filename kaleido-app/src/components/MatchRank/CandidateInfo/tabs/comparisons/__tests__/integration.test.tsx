import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ComparisonDetailModal } from '../ComparisonDetailModal';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock Chart.js and recharts
jest.mock('react-chartjs-2', () => ({
  Bar: ({ data }: any) => <div data-testid="bar-chart">{data?.labels?.join(', ')}</div>,
  Radar: ({ data }: any) => <div data-testid="radar-chart">{data?.labels?.join(', ')}</div>,
}));

jest.mock('recharts', () => ({
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
}));

jest.mock('chart.js', () => ({ Chart: { register: jest.fn() } }));
jest.mock('chartjs-plugin-datalabels', () => ({}));

describe('Comparison Components Integration Tests', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Integration', () => {
    it('should render modal and handle navigation between tabs', () => {
      const simpleComparison = {
        id: 'integration-test',
        title: 'Integration Test Comparison',
        comparisonResults: {
          executiveSummary: 'Test executive summary',
          comparisonResults: {
            candidateAnalysis: {
              'Test Candidate': {
                scores: { skills: 80 },
                strengths: ['Strong skills'],
              },
            },
            recommendations: {
              hiringStrategy: 'Choose the best candidate',
            },
          },
        },
        metadata: {
          candidateNames: [{ id: '1', name: 'Test Candidate' }],
        },
      };

      render(
        <ComparisonDetailModal isOpen={true} onClose={mockOnClose} comparison={simpleComparison} />
      );

      // Should render modal
      expect(screen.getByText('Integration Test Comparison')).toBeInTheDocument();

      // Should have all tabs available
      expect(screen.getAllByText('Executive Summary').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Detailed Analysis').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Candidate Profiles').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Visual Insights').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Final Summary').length).toBeGreaterThan(0);

      // Should be able to navigate between tabs
      fireEvent.click(screen.getAllByText('Detailed Analysis')[0]);
      expect(screen.getAllByText('Detailed Analysis').length).toBeGreaterThan(0);

      fireEvent.click(screen.getAllByText('Final Summary')[0]);
      expect(screen.getAllByText('Final Summary').length).toBeGreaterThan(0);
    });

    it('should handle close functionality', () => {
      const comparison = {
        id: 'close-test',
        title: 'Close Test',
        comparisonResults: {},
        metadata: {},
      };

      render(<ComparisonDetailModal isOpen={true} onClose={mockOnClose} comparison={comparison} />);

      // Should have close button
      const closeButton = screen.getAllByRole('button')[0]; // First button is close
      fireEvent.click(closeButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should not render when closed', () => {
      const comparison = {
        id: 'closed-test',
        title: 'Should Not Show',
        comparisonResults: {},
        metadata: {},
      };

      render(
        <ComparisonDetailModal isOpen={false} onClose={mockOnClose} comparison={comparison} />
      );

      // Should not render modal content when closed
      expect(screen.queryByText('Should Not Show')).not.toBeInTheDocument();
    });
  });

  describe('Data Display Integration', () => {
    it('should display executive summary content', () => {
      const comparisonWithSummary = {
        id: 'summary-test',
        title: 'Summary Test',
        comparisonResults: {
          executiveSummary: 'This is a test executive summary for display',
        },
        metadata: {},
      };

      render(
        <ComparisonDetailModal
          isOpen={true}
          onClose={mockOnClose}
          comparison={comparisonWithSummary}
        />
      );

      // Should display executive summary
      expect(screen.getByText(/This is a test executive summary for display/)).toBeInTheDocument();
    });

    it('should handle empty data gracefully', () => {
      const emptyComparison = {
        id: 'empty-test',
        title: 'Empty Data Test',
        comparisonResults: {},
        metadata: {},
      };

      render(
        <ComparisonDetailModal isOpen={true} onClose={mockOnClose} comparison={emptyComparison} />
      );

      // Should render without crashing
      expect(screen.getByText('Empty Data Test')).toBeInTheDocument();

      // Should show all tabs even with empty data
      expect(screen.getAllByText('Executive Summary').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Detailed Analysis').length).toBeGreaterThan(0);
    });

    it('should work with visual data', () => {
      const comparisonWithVisuals = {
        id: 'visual-test',
        title: 'Visual Test',
        comparisonResults: {
          comparisonResults: {
            candidateAnalysis: {
              'Visual Candidate': {
                scores: { skills: 85 },
              },
            },
          },
        },
        visualData: {
          barChartData: {
            labels: ['Visual Candidate'],
            datasets: [{ data: [85], label: 'Test Score' }],
          },
        },
        metadata: {},
      };

      render(
        <ComparisonDetailModal
          isOpen={true}
          onClose={mockOnClose}
          comparison={comparisonWithVisuals}
        />
      );

      // Should show visual insights tab
      expect(screen.getAllByText('Visual Insights').length).toBeGreaterThan(0);

      // Navigate to visual insights
      fireEvent.click(screen.getAllByText('Visual Insights')[0]);

      // Should render visual content
      expect(screen.getAllByText('Visual Insights').length).toBeGreaterThan(0);
    });
  });
});
