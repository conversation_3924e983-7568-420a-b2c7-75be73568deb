import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ComparisonStats } from '../ComparisonStats';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock Chart.js components
jest.mock('react-chartjs-2', () => ({
  Bar: ({ data }: any) => <div data-testid="bar-chart">{data?.labels?.join(', ')}</div>,
  Doughnut: ({ data }: any) => <div data-testid="doughnut-chart">{data?.labels?.join(', ')}</div>,
  Radar: ({ data }: any) => <div data-testid="radar-chart">{data?.labels?.join(', ')}</div>,
}));

// Mock Chart.js registration
jest.mock('chart.js', () => ({
  Chart: {
    register: jest.fn(),
  },
  CategoryScale: jest.fn(),
  LinearScale: jest.fn(),
  BarElement: jest.fn(),
  Title: jest.fn(),
  Tooltip: jest.fn(),
  Legend: jest.fn(),
  ArcElement: jest.fn(),
  RadialLinearScale: jest.fn(),
  PointElement: jest.fn(),
  LineElement: jest.fn(),
  Filler: jest.fn(),
}));

jest.mock('chartjs-plugin-datalabels', () => ({}));

describe('ComparisonStats', () => {
  const mockApiChartData = {
    barChartData: {
      labels: ['Christian Malone', 'David Wallace'],
      datasets: [
        {
          data: [75, 71],
          label: 'Overall Score',
        },
      ],
    },
    radarChartData: {
      labels: ['Skills', 'Experience', 'Leadership', 'Cultural Fit', 'Availability'],
      datasets: [
        {
          data: [70, 80, 60, 75, 90],
          label: 'Christian Malone',
        },
        {
          data: [75, 70, 55, 70, 85],
          label: 'David Wallace',
        },
      ],
    },
    comparisonMatrix: {
      rows: [
        [
          'Christian Malone',
          'N/A',
          'Strong attention to detail, Experience in brand marketing',
          'Limited direct experience in art direction, Lower match score',
          'Strong background in brand marketing provides unique perspective.',
        ],
        [
          'David Wallace',
          'N/A',
          'Strong background in marketing and campaign management',
          'Less focus on brand marketing, Limited leadership experience',
          'Experience in content production offers practical skill set.',
        ],
      ],
      headers: ['Candidate', 'Match Score', 'Key Strengths', 'Main Concerns', 'Unique Value'],
    },
  };

  const mockCandidateAnalysis = {
    'David Wallace': {
      scores: {
        skills: 75,
        experience: 70,
        leadership: 55,
        culturalFit: 70,
        availability: 85,
      },
      keyDifferentiator: 'Content production expertise',
    },
    'Christian Malone': {
      scores: {
        skills: 70,
        experience: 80,
        leadership: 60,
        culturalFit: 75,
        availability: 90,
      },
      keyDifferentiator: 'Brand marketing background',
    },
  };

  const mockCandidateAnalysisWithIds = {
    'b322ae9f-d1b2-4c6b-861e-e8236adc2eff': {
      name: 'David Wallace',
      scores: {
        skills: 75,
        experience: 70,
        leadership: 55,
        culturalFit: 70,
        availability: 85,
      },
    },
    'bb002a51-ee2f-48cb-b775-fbda3605e839': {
      name: 'Christian Malone',
      scores: {
        skills: 70,
        experience: 80,
        leadership: 60,
        culturalFit: 75,
        availability: 90,
      },
    },
  };

  const mockMetadata = {
    jobTitle: 'Art Director',
    candidateNames: [
      { id: 'b322ae9f-d1b2-4c6b-861e-e8236adc2eff', name: 'David Wallace' },
      { id: 'bb002a51-ee2f-48cb-b775-fbda3605e839', name: 'Christian Malone' },
    ],
  };

  const mockHeadToHeadComparisons = [
    {
      candidate1: 'Christian Malone',
      candidate2: 'David Wallace',
      keyDifference: 'Christian has stronger brand marketing background',
      recommendation: 'Choose Christian for strategic work',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Chart Data Handling', () => {
    it('should render charts when API chart data is provided', () => {
      render(
        <ComparisonStats
          data={mockApiChartData}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
          headToHeadComparisons={mockHeadToHeadComparisons}
        />
      );

      expect(screen.getByText('Candidate Comparison')).toBeInTheDocument();
      expect(screen.getByText('Skills Comparison')).toBeInTheDocument();
      expect(screen.getByText('Key Insights')).toBeInTheDocument();

      // Should display candidate names
      expect(screen.getAllByText('Christian Malone').length).toBeGreaterThan(0);
      expect(screen.getAllByText('David Wallace').length).toBeGreaterThan(0);

      // Should display scores
      expect(screen.getAllByText('75%').length).toBeGreaterThan(0);
      expect(screen.getAllByText('71%').length).toBeGreaterThan(0);
    });

    it('should render radar chart with correct data', () => {
      render(
        <ComparisonStats
          data={mockApiChartData}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      const radarChart = screen.getByTestId('radar-chart');
      expect(radarChart).toHaveTextContent(
        'Skills, Experience, Leadership, Cultural Fit, Availability'
      );
    });

    it('should render comparison matrix insights', () => {
      render(
        <ComparisonStats
          data={mockApiChartData}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      expect(
        screen.getByText('Strong attention to detail, Experience in brand marketing')
      ).toBeInTheDocument();
      expect(
        screen.getByText('Strong background in marketing and campaign management')
      ).toBeInTheDocument();
      expect(
        screen.getByText('Limited direct experience in art direction, Lower match score')
      ).toBeInTheDocument();
    });
  });

  describe('Fallback to Legacy Data Structure', () => {
    it('should handle legacy candidateAnalysis data when no API chart data', () => {
      render(
        <ComparisonStats
          data={null}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      expect(screen.getByText('Candidate Comparison')).toBeInTheDocument();
      expect(screen.getByText('Skills Breakdown')).toBeInTheDocument();

      // Should calculate and display overall scores
      expect(screen.getAllByText('71%').length).toBeGreaterThan(0); // David's score
      expect(screen.getAllByText('75%').length).toBeGreaterThan(0); // Christian's score
    });

    it('should calculate overall scores correctly for legacy data', () => {
      render(
        <ComparisonStats
          data={null}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      // David: (75+70+55+70+85)/5 = 71%
      // Christian: (70+80+60+75+90)/5 = 75%
      expect(screen.getAllByText('71%').length).toBeGreaterThan(0);
      expect(screen.getAllByText('75%').length).toBeGreaterThan(0);
    });

    it('should display skills breakdown for legacy data', () => {
      render(
        <ComparisonStats
          data={null}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      // Should show individual skill percentages (text is rendered in lowercase)
      expect(screen.getAllByText('skills').length).toBeGreaterThan(0);
      expect(screen.getAllByText('experience').length).toBeGreaterThan(0);
      expect(screen.getAllByText('leadership').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Culture').length).toBeGreaterThan(0); // This one is capitalized
      expect(screen.getAllByText('availability').length).toBeGreaterThan(0);
    });
  });

  describe('Name Resolution', () => {
    it('should resolve names from analysis data', () => {
      render(
        <ComparisonStats
          data={null}
          candidateAnalysis={mockCandidateAnalysisWithIds}
          metadata={mockMetadata}
        />
      );

      expect(screen.getAllByText('David Wallace').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Christian Malone').length).toBeGreaterThan(0);
    });

    it('should format UUID keys when name resolution fails', () => {
      const uuidAnalysis = {
        'a1b2c3d4-e5f6-7890-abcd-ef1234567890': {
          scores: { skills: 80, experience: 75, leadership: 70, culturalFit: 65, availability: 85 },
        },
      };

      render(<ComparisonStats data={null} candidateAnalysis={uuidAnalysis} metadata={null} />);

      expect(screen.getAllByText('Candidate a1b2c3d4').length).toBeGreaterThan(0);
    });

    it('should format candidateX patterns correctly', () => {
      const candidatePatternAnalysis = {
        candidate1: {
          scores: { skills: 80, experience: 75, leadership: 70, culturalFit: 65, availability: 85 },
        },
      };

      render(
        <ComparisonStats data={null} candidateAnalysis={candidatePatternAnalysis} metadata={null} />
      );

      expect(screen.getAllByText('Candidate1').length).toBeGreaterThan(0);
    });

    it('should properly capitalize irregular names', () => {
      const irregularNamesAnalysis = {
        'john DOE': {
          scores: { skills: 80, experience: 75, leadership: 70, culturalFit: 65, availability: 85 },
        },
      };

      render(
        <ComparisonStats data={null} candidateAnalysis={irregularNamesAnalysis} metadata={null} />
      );

      expect(screen.getAllByText('John Doe').length).toBeGreaterThan(0);
    });
  });

  describe('Empty State Handling', () => {
    it('should show no visualization message when no data provided', () => {
      render(<ComparisonStats data={null} candidateAnalysis={null} metadata={null} />);

      expect(screen.getByText('No visualization data available')).toBeInTheDocument();
    });

    it('should show no visualization message when both data and candidateAnalysis are empty', () => {
      render(<ComparisonStats data={null} candidateAnalysis={{}} metadata={null} />);

      expect(screen.getByText('No visualization data available')).toBeInTheDocument();
    });

    it('should handle partial API data gracefully', () => {
      const partialData = {
        barChartData: mockApiChartData.barChartData,
        // Missing radarChartData and comparisonMatrix
      };

      render(
        <ComparisonStats
          data={partialData}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      // Should still render the bar chart section
      expect(screen.getByText('Candidate Comparison')).toBeInTheDocument();
      expect(screen.getByText('Christian Malone')).toBeInTheDocument();
    });
  });

  describe('Score Calculation', () => {
    it('should handle missing scores gracefully', () => {
      const analysisWithMissingScores = {
        'Test Candidate': {
          scores: { skills: 80 }, // Missing other scores
        },
      };

      render(
        <ComparisonStats
          data={null}
          candidateAnalysis={analysisWithMissingScores}
          metadata={null}
        />
      );

      // Should render without crashing and show calculated score
      expect(screen.getAllByText('Test Candidate').length).toBeGreaterThan(0);
      expect(screen.getAllByText('16%').length).toBeGreaterThan(0); // 80/5 = 16%
    });

    it('should handle completely missing scores', () => {
      const analysisWithoutScores = {
        'Test Candidate': {
          // No scores object
        },
      };

      render(
        <ComparisonStats data={null} candidateAnalysis={analysisWithoutScores} metadata={null} />
      );

      // Should render without crashing and show 0%
      expect(screen.getAllByText('Test Candidate').length).toBeGreaterThan(0);
      expect(screen.getAllByText('0%').length).toBeGreaterThan(0);
    });

    it('should calculate scores only when some scores are present', () => {
      const mixedScoreAnalysis = {
        'Candidate A': {
          scores: { skills: 80, experience: 70, leadership: 60, culturalFit: 75, availability: 85 },
        },
        'Candidate B': {
          scores: {}, // Empty scores
        },
      };

      render(
        <ComparisonStats data={null} candidateAnalysis={mixedScoreAnalysis} metadata={null} />
      );

      expect(screen.getAllByText('74%').length).toBeGreaterThan(0); // Candidate A's average
      expect(screen.getAllByText('0%').length).toBeGreaterThan(0); // Candidate B's score
    });
  });

  describe('Sorting and Ranking', () => {
    it('should sort candidates by overall score in descending order', () => {
      render(
        <ComparisonStats
          data={null}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      // Christian (75%) should appear before David (71%)
      const textContent = document.body.textContent || '';
      const christianIndex = textContent.indexOf('Christian Malone');
      const davidIndex = textContent.indexOf('David Wallace');

      expect(christianIndex).toBeLessThan(davidIndex);
    });

    it('should display star icon for top candidate', () => {
      render(
        <ComparisonStats
          data={mockApiChartData}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      // Should have star indicators for top candidates
      const candidateComparison = screen.getByText('Candidate Comparison').closest('div');
      expect(candidateComparison).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed data gracefully', () => {
      const malformedData = {
        barChartData: null,
        radarChartData: undefined,
        comparisonMatrix: 'invalid',
      };

      render(
        <ComparisonStats
          data={malformedData}
          candidateAnalysis={mockCandidateAnalysis}
          metadata={mockMetadata}
        />
      );

      // Should fall back to candidate analysis display
      expect(screen.getByText('Candidate Comparison')).toBeInTheDocument();
    });

    it('should handle null candidate analysis entries', () => {
      const analysisWithNulls = {
        'Valid Candidate': {
          scores: { skills: 80, experience: 75, leadership: 70, culturalFit: 65, availability: 85 },
        },
        'Invalid Candidate': null,
      };

      render(<ComparisonStats data={null} candidateAnalysis={analysisWithNulls} metadata={null} />);

      // Should render valid candidate and handle null gracefully
      expect(screen.getAllByText('Valid Candidate').length).toBeGreaterThan(0);
      expect(screen.getAllByText('Invalid Candidate').length).toBeGreaterThan(0);
    });
  });
});
