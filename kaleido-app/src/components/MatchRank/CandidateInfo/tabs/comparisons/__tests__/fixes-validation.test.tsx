import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ComparisonDetailModal } from '../ComparisonDetailModal';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock Chart.js and recharts
jest.mock('react-chartjs-2', () => ({
  Bar: ({ data }: any) => <div data-testid="bar-chart">{data?.labels?.join(', ')}</div>,
  Radar: ({ data }: any) => <div data-testid="radar-chart">{data?.labels?.join(', ')}</div>,
}));

jest.mock('recharts', () => ({
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
}));

jest.mock('chart.js', () => ({ Chart: { register: jest.fn() } }));
jest.mock('chartjs-plugin-datalabels', () => ({}));

describe('Comparison Fixes Validation', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Core Fix Validation', () => {
    it('should access nested candidate analysis data correctly', () => {
      // This is the actual data structure from the user's debug output - the core fix we implemented
      const realWorldNestedComparison = {
        id: 'c81b6e3d-0eab-400b-850a-079a85fb5e36',
        title: 'quick overview comparison for David, Christian',
        comparisonResults: {
          executiveSummary: 'Christian Malone and David Wallace both have marketing experience',
          // The actual data is nested here - this is what we fixed
          comparisonResults: {
            candidateAnalysis: {
              'David Wallace': {
                scores: {
                  skills: 75,
                  experience: 70,
                  leadership: 55,
                  culturalFit: 70,
                  availability: 85,
                },
                strengths: ['Strong background in marketing'],
                overallRank: 2,
              },
              'Christian Malone': {
                scores: {
                  skills: 70,
                  experience: 80,
                  leadership: 60,
                  culturalFit: 75,
                  availability: 90,
                },
                strengths: ['Experience in brand marketing'],
                overallRank: 1,
              },
            },
            recommendations: {
              hiringStrategy: 'Prioritize Christian for brand work',
            },
          },
          criticalConsiderations: ['Experience varies between candidates'],
        },
        metadata: {
          candidateNames: [
            { id: 'b322ae9f-d1b2-4c6b-861e-e8236adc2eff', name: 'David Wallace' },
            { id: 'bb002a51-ee2f-48cb-b775-fbda3605e839', name: 'Christian Malone' },
          ],
        },
      };

      render(
        <ComparisonDetailModal
          isOpen={true}
          onClose={mockOnClose}
          comparison={realWorldNestedComparison}
        />
      );

      // Should successfully render the modal with the title
      expect(
        screen.getByText('Quick Overview Comparison For David, Christian')
      ).toBeInTheDocument();

      // Should display the executive summary in the summary tab
      expect(
        screen.getByText(/Christian Malone and David Wallace both have marketing experience/)
      ).toBeInTheDocument();

      // Should display critical considerations
      expect(screen.getByText(/Experience varies between candidates/)).toBeInTheDocument();
    });

    it('should handle empty comparison results gracefully', () => {
      const emptyComparison = {
        id: 'empty-comparison',
        title: 'Empty Comparison',
        comparisonResults: {},
        metadata: {},
      };

      render(
        <ComparisonDetailModal isOpen={true} onClose={mockOnClose} comparison={emptyComparison} />
      );

      // Should render without crashing
      expect(screen.getByText('Empty Comparison')).toBeInTheDocument();
    });

    it('should show visual insights when data is available', () => {
      const comparisonWithVisuals = {
        id: 'visual-comparison',
        title: 'Visual Comparison',
        comparisonResults: {
          comparisonResults: {
            candidateAnalysis: {
              'John Doe': {
                scores: { skills: 80, experience: 75 },
              },
            },
          },
        },
        visualData: {
          barChartData: {
            labels: ['John Doe'],
            datasets: [{ data: [80], label: 'Overall Score' }],
          },
        },
        metadata: {},
      };

      render(
        <ComparisonDetailModal
          isOpen={true}
          onClose={mockOnClose}
          comparison={comparisonWithVisuals}
        />
      );

      // Should show visual insights tab
      expect(screen.getByText('Visual Insights')).toBeInTheDocument();
    });
  });

  describe('Practical Usage Scenarios', () => {
    it('should work with minimal comparison data', () => {
      const minimalComparison = {
        id: 'minimal',
        title: 'Minimal Comparison',
        comparisonResults: {
          executiveSummary: 'Basic comparison summary',
        },
        metadata: {
          candidateNames: [
            { id: '1', name: 'Candidate A' },
            { id: '2', name: 'Candidate B' },
          ],
        },
      };

      render(
        <ComparisonDetailModal isOpen={true} onClose={mockOnClose} comparison={minimalComparison} />
      );

      // Should render the basic content
      expect(screen.getByText('Minimal Comparison')).toBeInTheDocument();
      expect(screen.getByText(/Basic comparison summary/)).toBeInTheDocument();
    });

    it('should display recommendations when available', () => {
      const comparisonWithRecommendations = {
        id: 'recommendations',
        title: 'Comparison with Recommendations',
        comparisonResults: {
          comparisonResults: {
            recommendations: {
              hiringStrategy: 'Choose the best candidate for the role',
            },
          },
        },
        metadata: {},
      };

      render(
        <ComparisonDetailModal
          isOpen={true}
          onClose={mockOnClose}
          comparison={comparisonWithRecommendations}
        />
      );

      // Should show final summary tab
      expect(screen.getByText('Final Summary')).toBeInTheDocument();
      // Modal should render successfully with recommendations data
      expect(screen.getByText('Comparison With Recommendations')).toBeInTheDocument();
    });

    it('should handle both nested and top-level data correctly', () => {
      const mixedDataComparison = {
        id: 'mixed',
        title: 'Mixed Data Structure',
        comparisonResults: {
          executiveSummary: 'Top level summary',
          comparisonResults: {
            candidateAnalysis: {
              'Jane Smith': {
                scores: { skills: 85 },
                strengths: ['Great experience'],
              },
            },
          },
        },
        metadata: {
          candidateNames: [{ id: '1', name: 'Jane Smith' }],
        },
      };

      render(
        <ComparisonDetailModal
          isOpen={true}
          onClose={mockOnClose}
          comparison={mixedDataComparison}
        />
      );

      // Should use top-level data when available
      expect(screen.getByText(/Top level summary/)).toBeInTheDocument();

      // Should render successfully with mixed data structure
      expect(screen.getByText('Mixed Data Structure')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle null comparison gracefully', () => {
      render(
        <ComparisonDetailModal isOpen={true} onClose={mockOnClose} comparison={null as any} />
      );

      // Should not crash - component should handle null comparison
      expect(document.body).toBeInTheDocument();
    });

    it('should handle malformed data structures', () => {
      const malformedComparison = {
        id: 'malformed',
        title: 'Malformed Data',
        comparisonResults: {
          invalidField: 'invalid',
          comparisonResults: {
            candidateAnalysis: null,
          },
        },
        metadata: null,
      };

      render(
        <ComparisonDetailModal
          isOpen={true}
          onClose={mockOnClose}
          comparison={malformedComparison}
        />
      );

      // Should render title even with malformed data
      expect(screen.getByText('Malformed Data')).toBeInTheDocument();
    });
  });
});
