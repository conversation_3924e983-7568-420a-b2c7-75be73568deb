import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { showToast } from '@/components/Toaster';
import { EmptyState } from '@/components/ui/EmptyState';
import { ICandidate } from '@/entities/interfaces';
import { apiClient } from '@/lib/apiHelper';
import { AnimatePresence, motion } from 'framer-motion';
import { Calendar, CheckCircle, FileVideo, Mail, Play, Timer, Video } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface VideoIntroTabProps {
  candidate: ICandidate;
  jobId?: string;
}

interface VideoResponse {
  id: string;
  question: string;
  videoUrl: string;
  duration: number;
  recordedAt: Date | string;
  questionId?: string;
  status?: string;
  isExpired?: boolean;
}

interface CultureFitQuestion {
  id: string;
  question: string;
  duration: number;
}

interface VideoResponseData {
  job: {
    id: string;
    jobType: string;
    companyName: string;
    department: string;
    cultureFitQuestions: CultureFitQuestion[];
  };
  candidate: {
    id: string;
    fullName: string;
    jobTitle: string;
    location: string;
    currentCompany: string;
    yearsOfExperience: number;
    videoResponses: VideoResponse[];
    answeredQuestions: number;
    totalQuestions: number;
    status: string;
    videoIntroEmailSent?: boolean;
  };
}

export const VideoIntroTab: React.FC<VideoIntroTabProps> = ({ candidate, jobId }) => {
  // Determine if candidate is from upload or scout source (not job-seeker)
  const isUploadOrScout =
    candidate.source &&
    (candidate.source === 'RESUME_UPLOAD' ||
      candidate.source === 'LINKEDIN_SCOUT' ||
      candidate.source === 'scout' ||
      candidate.source === 'upload');
  const [videoResponses, setVideoResponses] = useState<VideoResponse[]>([]);
  const [cultureFitQuestions, setCultureFitQuestions] = useState<CultureFitQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedResponse, setSelectedResponse] = useState<VideoResponse | null>(null);
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [jobData, setJobData] = useState<any>(null);
  const [candidateData, setCandidateData] = useState<any>(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);

  // Format date with full date and time
  const formatDate = (date: Date | string) => {
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Format duration to minutes:seconds
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get relative time
  const getRelativeTime = (date: Date | string) => {
    const dateObj = new Date(date);
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  // Listen for culture fit updates
  useEffect(() => {
    const handleCultureFitUpdate = () => {
      // Trigger a refresh by updating the refresh key
      setRefreshKey(prev => prev + 1);
    };

    // Listen for a custom event that can be triggered after culture fit update
    window.addEventListener('cultureFitUpdated', handleCultureFitUpdate);

    return () => {
      window.removeEventListener('cultureFitUpdated', handleCultureFitUpdate);
    };
  }, []);

  // Fetch video responses from the API
  useEffect(() => {
    const fetchVideoResponses = async () => {
      // If we have jobId and candidate.id, fetch from the new endpoint
      if (jobId && candidate.id) {
        setIsLoading(true);
        const startTime = Date.now();

        try {
          const response = await apiClient.get<VideoResponseData>(
            `/jobs/${jobId}/candidates/${candidate.id}/video-responses`
          );

          // Set culture fit questions, job data, and candidate data
          if (response?.job?.cultureFitQuestions) {
            setCultureFitQuestions(response.job.cultureFitQuestions);
            setJobData(response.job);
          }

          // Set candidate data from response
          if (response?.candidate) {
            setCandidateData(response.candidate);
          }

          if (response?.candidate?.videoResponses && response.candidate.videoResponses.length > 0) {
            setVideoResponses(response.candidate.videoResponses);
            // Find the first question that has a response and select it
            for (const question of response.job.cultureFitQuestions || []) {
              const matchingResponse = response.candidate.videoResponses.find(
                r => r.question === question.question
              );
              if (matchingResponse) {
                setSelectedResponse(matchingResponse);
                break;
              }
            }
            // If no matching response found, just select the first video response
            if (!selectedResponse && response.candidate.videoResponses.length > 0) {
              setSelectedResponse(response.candidate.videoResponses[0]);
            }
          } else if (response?.job?.cultureFitQuestions?.length > 0) {
            // No video responses but we have questions
            setVideoResponses([]);
            setSelectedQuestionId(response.job.cultureFitQuestions[0].id);
          }
        } catch (error) {
          console.error('Error fetching video responses:', error);
          showToast({ message: 'Failed to load video responses', type: 'error' });
          // Fallback to using candidate's existing video responses
          if (candidate.videoResponses) {
            const uniqueResponses = getUniqueResponses(candidate.videoResponses);
            setVideoResponses(uniqueResponses);
            if (uniqueResponses.length > 0) {
              setSelectedResponse(uniqueResponses[0]);
            }
          }
        } finally {
          // Ensure minimum loading time of 500ms to prevent flicker
          const elapsedTime = Date.now() - startTime;
          const remainingTime = Math.max(500 - elapsedTime, 0);

          setTimeout(() => {
            setIsLoading(false);
            setInitialLoadComplete(true);
          }, remainingTime);
        }
      } else if (candidate.videoResponses) {
        // If no jobId, use candidate's existing video responses
        const uniqueResponses = getUniqueResponses(candidate.videoResponses);
        setVideoResponses(uniqueResponses);
        if (uniqueResponses.length > 0) {
          setSelectedResponse(uniqueResponses[0]);
        }
        setTimeout(() => {
          setIsLoading(false);
          setInitialLoadComplete(true);
        }, 300);
      } else {
        setTimeout(() => {
          setIsLoading(false);
          setInitialLoadComplete(true);
        }, 300);
      }
    };

    fetchVideoResponses();
  }, [candidate.id, jobId, refreshKey]);

  // Helper function to get unique responses
  const getUniqueResponses = (responses: VideoResponse[]): VideoResponse[] => {
    const uniqueMap = responses.reduce(
      (acc, response) => {
        if (
          !acc[response.question] ||
          new Date(response.recordedAt).getTime() >
            new Date(acc[response.question].recordedAt).getTime()
        ) {
          acc[response.question] = response;
        }
        return acc;
      },
      {} as Record<string, VideoResponse>
    );

    // Convert to array and sort by recordedAt
    const sorted = Object.values(uniqueMap);
    sorted.sort((a, b) => new Date(b.recordedAt).getTime() - new Date(a.recordedAt).getTime());
    return sorted;
  };

  if (isLoading) {
    return (
      <div className="w-full flex items-center justify-center min-h-[400px]">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <ColorfulSmokeyOrbLoader />
        </motion.div>
      </div>
    );
  }

  // Handle sending video intro email
  const handleSendVideoIntroEmail = async () => {
    // Check if candidate has email
    if (!candidate.email || candidate.email.trim() === '') {
      showToast({
        message: 'Candidate email is not available',
        type: 'error',
      });
      return;
    }

    if (!jobId || !jobData) {
      showToast({
        message: 'Job information is not available',
        type: 'error',
      });
      return;
    }

    setIsSendingEmail(true);
    try {
      const response = await apiClient.post('/email/invite-video-intro', {
        candidateEmail: candidate.email,
        candidateName: candidate.fullName,
        jobTitle: jobData.jobType || jobData.jobTitle || 'Position',
        jobId: jobId,
        candidateId: candidate.id,
      });

      if (response) {
        const isResend = candidate.videoIntroEmailSent;
        showToast({
          message: isResend
            ? 'Video introduction email resent successfully!'
            : 'Video introduction email sent successfully!',
          type: 'success',
        });
        // Trigger a refresh
        setRefreshKey(prev => prev + 1);
      }
    } catch (error: any) {
      console.error('Failed to send video intro email:', error);
      const errorMessage = error.message || error.response?.data?.message || 'Failed to send email';
      showToast({
        message: errorMessage,
        type: 'error',
      });
    } finally {
      setIsSendingEmail(false);
    }
  };

  // Check if email has been sent - check API response data first, then prop data
  const videoIntroEmailSent =
    candidateData?.videoIntroEmailSent ||
    candidate.videoIntroEmailSent ||
    candidate.emailCorrespondence?.some(
      email =>
        (email.type === 'SENT' && email.subject.includes('Video Introduction')) ||
        email.metadata?.emailType === 'video_intro_invitation'
    );

  // Only show empty state if we have no questions AND no responses
  if (
    (!videoResponses || videoResponses.length === 0) &&
    (!cultureFitQuestions || cultureFitQuestions.length === 0)
  ) {
    const hasEmail = candidate.email && candidate.email.trim() !== '';

    return (
      <motion.div
        className="w-full max-w-3xl mx-auto"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <EmptyState
          title="No Video Responses"
          description="This candidate hasn't submitted any video responses yet. Video introductions help you better understand the candidate's communication skills and personality."
          icon={Video}
          type="generic"
          showButton={false}
        />

        {/* Add email sending button if we have job context */}
        {jobId && (
          <div className="mt-6 flex flex-col items-center">
            <button
              type="button"
              onClick={handleSendVideoIntroEmail}
              disabled={isSendingEmail || !hasEmail}
              className={`group relative flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-all duration-300 ${
                isSendingEmail || !hasEmail ? 'cursor-not-allowed' : ''
              }`}
              style={{
                backgroundColor: !hasEmail
                  ? 'rgba(156, 163, 175, 0.1)' // Gray for disabled
                  : videoIntroEmailSent
                    ? 'rgba(251, 191, 36, 0.1)' // Amber for resend
                    : 'rgba(239, 68, 68, 0.15)', // Lighter red for first send
                backdropFilter: !hasEmail ? 'none' : 'blur(8px)',
                border: !hasEmail
                  ? '1px solid rgba(156, 163, 175, 0.2)'
                  : videoIntroEmailSent
                    ? '1px solid rgba(245, 158, 11, 0.3)'
                    : '1px solid rgba(239, 68, 68, 0.3)',
                color: !hasEmail ? '#9ca3af' : videoIntroEmailSent ? '#f59e0b' : '#ef4444',
              }}
            >
              {!hasEmail ? (
                <>
                  <Mail className="w-4 h-4 opacity-50" />
                  <span className="font-medium">Email not available</span>
                </>
              ) : isSendingEmail ? (
                <>
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span className="font-medium">Sending...</span>
                </>
              ) : (
                <>
                  {videoIntroEmailSent ? (
                    <>
                      <Mail className="w-4 h-4 transition-transform group-hover:scale-110" />
                      <span className="font-medium">
                        {isUploadOrScout ? 'Resend Interest Email' : 'Resend Video Intro Email'}
                      </span>
                    </>
                  ) : (
                    <>
                      {isUploadOrScout ? (
                        <Mail className="w-4 h-4 transition-transform group-hover:scale-110" />
                      ) : (
                        <Video className="w-4 h-4 transition-transform group-hover:scale-110" />
                      )}
                      <span className="font-medium">
                        {isUploadOrScout ? 'Send Interest Email' : 'Send Video Intro Email'}
                      </span>
                    </>
                  )}
                </>
              )}
            </button>

            {/* Email status indicator */}
            {videoIntroEmailSent && hasEmail && (
              <div className="mt-2 text-center">
                <p className="text-xs text-amber-400">
                  • Video intro email sent - awaiting response
                </p>
              </div>
            )}
          </div>
        )}
      </motion.div>
    );
  }

  return (
    <motion.div
      className="w-full max-w-7xl mx-auto px-2 sm:px-4 xl:px-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    >
      <div className="flex flex-col xl:flex-row gap-3 sm:gap-4 xl:gap-8">
        {/* Questions Sidebar */}
        <div className="xl:w-96 xl:flex-shrink-0 order-2 xl:order-1">
          <div className="xl:sticky xl:top-0">
            {/* Header */}
            <div className="mb-4 sm:mb-6">
              <div className="flex items-center mb-3 sm:mb-4">
                <Video className="w-5 h-5 sm:w-6 sm:h-6 text-muted-foreground mr-2 sm:mr-3" />
                <div className="flex-1 flex items-center justify-between">
                  <h3 className="text-base sm:text-lg font-medium text-muted-foreground tracking-wider">
                    Video Intro Response
                  </h3>
                  <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full font-medium">
                    {cultureFitQuestions.length > 0
                      ? cultureFitQuestions.length
                      : videoResponses.length}{' '}
                    Total
                  </span>
                </div>
              </div>
              <div className="h-px bg-gradient-to-r from-transparent via-muted-foreground/20 to-transparent" />
            </div>

            {/* Questions List */}
            <div className="space-y-1.5 sm:space-y-2">
              <AnimatePresence>
                {/* Show all culture fit questions with their responses */}
                {cultureFitQuestions.map((question: CultureFitQuestion, index: number) => {
                  // Match by question text since questionId might not match
                  const response = videoResponses.find(r => r.question === question.question);
                  const isSelected = response
                    ? selectedResponse?.id === response.id
                    : selectedQuestionId === question.id;

                  return (
                    <motion.button
                      key={question.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => {
                        if (response) {
                          setSelectedResponse(response);
                          setSelectedQuestionId(null);
                        } else {
                          setSelectedResponse(null);
                          setSelectedQuestionId(question.id);
                        }
                      }}
                      className={`w-full text-left transition-all duration-300 group relative ${
                        isSelected ? 'scale-[1.02]' : 'hover:scale-[1.01]'
                      }`}
                    >
                      <div className="absolute inset-0 rounded-lg sm:rounded-xl bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                      <div
                        className={`relative flex gap-2.5 sm:gap-3 p-2.5 sm:p-3 xl:p-4 rounded-lg sm:rounded-xl border transition-all duration-300 ${
                          isSelected ? '' : 'hover:bg-white/5 border-transparent'
                        }`}
                      >
                        {/* Glassmorphic gradient background for selected item */}
                        {isSelected && (
                          <>
                            <div className="absolute inset-0 rounded-lg sm:rounded-xl bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-purple-600/20 blur-xl" />
                            <div className="absolute inset-0 rounded-lg sm:rounded-xl bg-gradient-to-br from-purple-500/10 to-pink-500/10" />
                            <div className="absolute inset-0 rounded-lg sm:rounded-xl backdrop-blur-sm" />
                            <div className="absolute inset-0 rounded-lg sm:rounded-xl border border-purple-400/10" />
                          </>
                        )}
                        {/* Question Number */}
                        <div className="relative flex-shrink-0 w-8 h-8 sm:w-9 sm:h-9 xl:w-10 xl:h-10 rounded-full flex items-center justify-center font-bold transition-all duration-300 z-10">
                          {isSelected ? (
                            <>
                              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-600 to-pink-600 shadow-lg shadow-purple-500/30" />
                              <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/20 to-transparent" />
                              <span className="relative text-white text-xs sm:text-sm">
                                {(index + 1).toString().padStart(2, '0')}
                              </span>
                            </>
                          ) : (
                            <>
                              <div className="absolute inset-0 rounded-full bg-muted/50" />
                              <span className="relative text-muted-foreground group-hover:text-foreground text-xs sm:text-sm">
                                {(index + 1).toString().padStart(2, '0')}
                              </span>
                            </>
                          )}
                        </div>

                        {/* Question Content */}
                        <div className="flex-1 min-w-0 relative z-10">
                          <p
                            className={`font-medium mb-1.5 sm:mb-2 text-sm sm:text-base transition-colors duration-300 ${
                              isSelected
                                ? 'text-foreground'
                                : 'text-muted-foreground group-hover:text-foreground'
                            }`}
                          >
                            {question.question}
                          </p>

                          <div className="flex items-center gap-2 sm:gap-3 text-xs">
                            {response ? (
                              <>
                                <div
                                  className={`flex items-center gap-1 transition-colors duration-300 ${
                                    isSelected ? 'text-purple-400' : 'text-muted-foreground/70'
                                  }`}
                                >
                                  <Timer className="w-3 h-3" />
                                  <span>{formatDuration(response.duration)}</span>
                                </div>
                                <span className="w-1 h-1 bg-muted-foreground/30 rounded-full" />
                                <span
                                  className={`transition-colors duration-300 ${
                                    isSelected ? 'text-pink-400' : 'text-muted-foreground/70'
                                  }`}
                                >
                                  {getRelativeTime(response.recordedAt)}
                                </span>
                              </>
                            ) : (
                              <span className="text-muted-foreground/70 italic text-xs">
                                Candidate has not responded yet
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Play Icon or Status Icon */}
                        <div className="relative flex-shrink-0 w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center transition-all duration-300 z-10">
                          {response ? (
                            isSelected ? (
                              <>
                                {/* Glassmorphic play button */}
                                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-500/30 to-pink-500/30 backdrop-blur-sm" />
                                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/10 to-transparent" />
                                <div className="absolute inset-0 rounded-full border border-purple-400/20" />
                                <Play
                                  className="relative w-4 h-4 text-purple-300"
                                  fill="currentColor"
                                />
                              </>
                            ) : (
                              <>
                                <div className="absolute inset-0 rounded-full bg-muted/30 group-hover:bg-gradient-to-br group-hover:from-purple-500/20 group-hover:to-pink-500/20 transition-all duration-300" />
                                <Play
                                  className="relative w-4 h-4 text-muted-foreground group-hover:text-purple-400 transition-colors duration-300"
                                  fill="currentColor"
                                />
                              </>
                            )
                          ) : (
                            <>
                              <div className="absolute inset-0 rounded-full bg-muted/20" />
                              <Timer className="relative w-4 h-4 text-muted-foreground/50" />
                            </>
                          )}
                        </div>
                      </div>
                    </motion.button>
                  );
                })}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Video Player */}
        <div className="flex-1 min-w-0 order-1 xl:order-2">
          <AnimatePresence mode="wait">
            {selectedResponse ? (
              <motion.div
                key={selectedResponse.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="h-full"
              >
                <div className="relative h-full rounded-2xl sm:rounded-3xl overflow-hidden">
                  {/* Sophisticated Gradient Background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-purple-600/5" />
                  <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_var(--primary)/10,_transparent_40%)]" />
                  <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,_var(--purple-600)/10,_transparent_40%)]" />

                  <div className="relative h-full p-2 sm:p-4 xl:p-6 backdrop-blur-sm bg-white/5 dark:bg-black/5 border border-white/10">
                    {/* Video Container */}
                    <div className="h-full flex flex-col justify-center">
                      {/* Video Player with Enhanced Shadow */}
                      <div className="relative w-full max-w-4xl mx-auto rounded-lg sm:rounded-xl xl:rounded-2xl overflow-hidden bg-black shadow-2xl ring-1 ring-white/10">
                        <div className="aspect-video">
                          <video
                            key={selectedResponse.id}
                            src={selectedResponse.videoUrl}
                            controls
                            controlsList="nodownload"
                            className="w-full h-full object-contain"
                            playsInline
                          />
                        </div>

                        {/* Floating Info Overlay */}
                        <div className="absolute top-2 left-2 right-2 sm:top-3 sm:left-3 sm:right-3 xl:top-4 xl:left-4 xl:right-4 flex justify-between items-start pointer-events-none">
                          {/* Question Number Badge */}
                          <div className="bg-black/60 backdrop-blur-md px-2 py-1 sm:px-3 sm:py-1.5 xl:px-4 xl:py-2 rounded-full flex items-center gap-1 sm:gap-2">
                            <span className="text-white/70 text-xs sm:text-sm">Question</span>
                            <span className="text-white font-bold text-xs sm:text-sm">
                              {videoResponses.findIndex(r => r.id === selectedResponse.id) + 1}
                            </span>
                            <span className="text-white/70 text-xs sm:text-sm">
                              of {videoResponses.length}
                            </span>
                          </div>

                          {/* Duration Badge */}
                          <div className="bg-black/60 backdrop-blur-md px-2 py-1 sm:px-3 sm:py-1.5 xl:px-3 xl:py-2 rounded-full text-white text-xs sm:text-sm flex items-center gap-1 sm:gap-2">
                            <Timer className="w-3 h-3 sm:w-4 sm:h-4" />
                            <span className="font-medium">
                              {formatDuration(selectedResponse.duration)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Elegant Info Bar */}
                      <div className="mt-3 sm:mt-4 xl:mt-6 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4 px-1 sm:px-2 max-w-4xl mx-auto w-full">
                        {/* Status */}
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 sm:w-7 sm:h-7 xl:w-8 xl:h-8 rounded-full bg-green-500/10 flex items-center justify-center">
                            <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Status</p>
                            <p className="text-sm font-medium text-green-600 dark:text-green-400">
                              Completed
                            </p>
                          </div>
                        </div>

                        {/* Navigation Hints */}
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <div className="w-6 h-6 sm:w-7 sm:h-7 xl:w-8 xl:h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Recorded</p>
                            <p className="text-[8pt] font-sm">
                              {formatDate(selectedResponse.recordedAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ) : selectedQuestionId ? (
              // Show the selected question with no response
              <motion.div
                key={selectedQuestionId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="h-full"
              >
                <div className="h-full flex justify-center items-center p-2 sm:p-4 xl:p-6">
                  <div className="relative p-4 sm:p-6 xl:p-8 rounded-lg sm:rounded-xl xl:rounded-2xl overflow-hidden border border-white/10 backdrop-blur-sm max-w-lg w-full">
                    <motion.div
                      animate={{
                        rotate: [0, 360],
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 15,
                        repeat: Infinity,
                        ease: 'linear',
                      }}
                      className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 blur-2xl"
                    />
                    <motion.div
                      animate={{
                        x: [-40, 40, -40],
                        y: [-20, 20, -20],
                      }}
                      transition={{
                        duration: 8,
                        repeat: Infinity,
                        ease: 'easeInOut',
                      }}
                      className="absolute inset-0 bg-gradient-to-tr from-white/5 via-white/10 to-transparent blur-xl"
                    />

                    <div className="relative p-3 sm:p-4 xl:p-6">
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{
                          duration: 0.3,
                          ease: 'easeOut',
                        }}
                        className="flex justify-center mb-4 sm:mb-6 relative"
                      >
                        <motion.div
                          animate={{
                            opacity: [0.4, 0.8, 0.4],
                            scale: [0.95, 1.05, 0.95],
                          }}
                          transition={{
                            duration: 4,
                            repeat: Infinity,
                            ease: 'easeInOut',
                          }}
                          className="absolute inset-0 bg-gradient-to-r from-indigo-500/30 via-purple-500/30 to-pink-500/30 rounded-full blur-xl"
                        />
                        <div className="relative">
                          <motion.div
                            animate={{
                              opacity: [0.5, 1, 0.5],
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: 'easeInOut',
                            }}
                            className="absolute inset-0 bg-gradient-to-tr from-indigo-500 to-purple-500 rounded-full blur-md"
                          />
                          <Timer className="w-12 h-12 sm:w-14 sm:h-14 xl:w-16 xl:h-16 text-white relative z-10" />
                        </div>
                      </motion.div>

                      <motion.div
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                        className="text-center relative z-10"
                      >
                        <h3 className="text-lg sm:text-xl font-semibold text-white mb-2 sm:mb-3">
                          Awaiting Response
                        </h3>
                        <p className="text-white/60 max-w-md mx-auto leading-relaxed text-sm">
                          The candidate has not responded to this question yet. You'll be able to
                          view their video response once they submit it.
                        </p>
                        <div className="mt-3 sm:mt-4 text-white/40 text-xs">
                          Expected duration:{' '}
                          {cultureFitQuestions.find(q => q.id === selectedQuestionId)?.duration} min
                        </div>

                        {/* Add email sending button */}
                        {jobId && (
                          <div className="mt-4 sm:mt-6">
                            <button
                              type="button"
                              onClick={handleSendVideoIntroEmail}
                              disabled={isSendingEmail || !candidate.email}
                              className={`group relative inline-flex items-center justify-center gap-2 px-5 py-2.5 rounded-lg transition-all duration-300 ${
                                isSendingEmail || !candidate.email ? 'cursor-not-allowed' : ''
                              }`}
                              style={{
                                backgroundColor: !candidate.email
                                  ? 'rgba(156, 163, 175, 0.1)'
                                  : videoIntroEmailSent
                                    ? 'rgba(251, 191, 36, 0.1)'
                                    : 'rgba(239, 68, 68, 0.15)',
                                backdropFilter: !candidate.email ? 'none' : 'blur(8px)',
                                border: !candidate.email
                                  ? '1px solid rgba(156, 163, 175, 0.2)'
                                  : videoIntroEmailSent
                                    ? '1px solid rgba(245, 158, 11, 0.3)'
                                    : '1px solid rgba(239, 68, 68, 0.3)',
                                color: !candidate.email
                                  ? '#9ca3af'
                                  : videoIntroEmailSent
                                    ? '#f59e0b'
                                    : '#dc2626',
                              }}
                            >
                              {!candidate.email ? (
                                <>
                                  <Mail className="w-4 h-4 opacity-50" />
                                  <span className="font-medium text-sm">Email not available</span>
                                </>
                              ) : isSendingEmail ? (
                                <>
                                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                                  <span className="font-medium text-sm">Sending...</span>
                                </>
                              ) : (
                                <>
                                  {videoIntroEmailSent ? (
                                    <>
                                      <Mail className="w-4 h-4 transition-transform group-hover:scale-110" />
                                      <span className="font-medium text-sm">
                                        {isUploadOrScout
                                          ? 'Resend Interest Email'
                                          : 'Resend Video Intro Email'}
                                      </span>
                                    </>
                                  ) : (
                                    <>
                                      {isUploadOrScout ? (
                                        <Mail className="w-4 h-4 transition-transform group-hover:scale-110 text-red-300" />
                                      ) : (
                                        <Video className="w-4 h-4 transition-transform group-hover:scale-110 text-red-300" />
                                      )}
                                      <span className="font-medium text-sm text-red-300">
                                        {isUploadOrScout
                                          ? 'Send Interest Email'
                                          : 'Send Video Intro Email'}
                                      </span>
                                    </>
                                  )}
                                </>
                              )}
                            </button>

                            {videoIntroEmailSent && candidate.email && (
                              <p className="mt-2 text-xs text-amber-400">
                                • Video intro email sent - awaiting response
                              </p>
                            )}
                          </div>
                        )}
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="h-full flex items-center justify-center min-h-[350px] sm:min-h-[400px] xl:min-h-[500px] p-3 sm:p-4 xl:p-6"
              >
                <div className="text-center max-w-md">
                  <div className="relative inline-flex">
                    <div className="absolute inset-0 bg-primary/20 blur-xl rounded-full animate-pulse" />
                    <div className="relative p-3 sm:p-4 xl:p-6 rounded-full bg-gradient-to-br from-primary/10 to-purple-600/10 border border-white/10">
                      <FileVideo className="w-8 h-8 sm:w-10 sm:h-10 xl:w-12 xl:h-12 text-primary" />
                    </div>
                  </div>
                  <h3 className="mt-3 sm:mt-4 xl:mt-6 text-base sm:text-lg xl:text-xl font-semibold mb-2">
                    Select a Video Response
                  </h3>
                  <p className="text-muted-foreground text-sm sm:text-base max-w-sm mx-auto">
                    Choose a question from the sidebar to view the candidate's video response
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};
