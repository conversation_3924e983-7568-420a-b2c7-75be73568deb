'use client';

import { CreditButton } from '@/components/common/CreditButton';
import { showToast } from '@/components/Toaster';
import { ICandidate } from '@/entities/interfaces';
import { apiClient } from '@/lib/apiHelper';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { CandidateStatus } from '@/types/candidate.types';
import { CreditActionType } from '@/types/subscription';
import { useJobThresholds } from '@/hooks/useJobThresholds';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  AlignLeft,
  Briefcase,
  Download,
  Github,
  Globe,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  Search,
  Share2,
  Star,
  Unlock,
} from 'lucide-react';
import React, { useState } from 'react';
import AnimatedScoreCard from '../../AnimatedScoreCard';
import { VideoIntroButton } from '../VideoIntroButton';

interface OverviewTabContentProps {
  candidate: ICandidate;
  jobId: string;
  jobTitle?: string;
  onCandidateSelect?: (candidate: ICandidate) => void;
  onStatusUpdate?: () => void;
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;
}

const FaintLineSeparator = () => (
  <div className="h-[1px] bg-gradient-to-r from-transparent via-gray-200/20 to-transparent dark:via-gray-700/30"></div>
);

export const OverviewTabContent: React.FC<OverviewTabContentProps> = ({
  candidate,
  jobId,
  jobTitle,
  onCandidateSelect,
  onStatusUpdate,
  topCandidateThreshold: propTopThreshold,
  secondTierCandidateThreshold: propSecondThreshold,
}) => {
  const [isShortlisting, setIsShortlisting] = useState(false);
  const { updateCandidateData, updateCandidateStatus } = useMatchRankDetailsStore();
  const jobStore = useJobStore();

  // Get thresholds from the hook with fallback to props or defaults
  const { topCandidateThreshold, secondTierCandidateThreshold } = useJobThresholds({
    jobId,
    fallbackJob:
      propTopThreshold && propSecondThreshold
        ? {
            topCandidateThreshold: propTopThreshold,
            secondTierCandidateThreshold: propSecondThreshold,
          }
        : undefined,
  });

  // Get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate random background color for avatar
  const getRandomBgColor = (name: string) => {
    const colors = [
      'bg-blue-500',
      'bg-purple-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-red-500',
      'bg-teal-500',
    ];
    const safeName = name || 'Unknown';
    const index = safeName.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // Check if candidate is scouted
  const isScoutedCandidate =
    (candidate as any).source === 'LINKEDIN_SCOUT' ||
    (candidate as any).sourceType === 'LINKEDIN_SCOUT';

  // Check if email is locked (needs unlocking)
  const isEmailLocked =
    (candidate as any).email === '<EMAIL>' ||
    (candidate as any).email ===
      'Email available after profile enhancement - Contact via LinkedIn or enhance profile for direct email';

  const handleDownload = () => {
    showToast({ message: 'Download feature coming soon', type: 'info' });
  };

  const handleShare = () => {
    showToast({ message: 'Share feature coming soon', type: 'info' });
  };

  // Handle unlock candidate details
  const handleUnlockDetails = async () => {
    try {
      showToast({
        message: 'Unlocking candidate details...',
        type: 'info',
      });

      const response: any = await apiClient.post(`/candidates/${candidate.id}/enhance`);

      if (response && response.candidate) {
        const enhancedCandidate = response.candidate;

        // Create enhanced candidate data object
        const enhancedCandidateData = {
          ...(enhancedCandidate.email && { email: enhancedCandidate.email }),
          ...(enhancedCandidate.phone && { phone: enhancedCandidate.phone }),
          ...(enhancedCandidate.linkedinUrl && { linkedinUrl: enhancedCandidate.linkedinUrl }),
          ...(enhancedCandidate.summary && { summary: enhancedCandidate.summary }),
          ...(enhancedCandidate.experience && { experience: enhancedCandidate.experience }),
          ...(enhancedCandidate.skills && { skills: enhancedCandidate.skills }),
        };

        // Update the candidate data
        updateCandidateData(candidate.id, enhancedCandidateData);

        // Refresh data
        if (jobId) {
          await jobStore.fetchCandidateById(jobId, candidate.id);
          onStatusUpdate?.();
        }

        showToast({
          message: 'Candidate details unlocked successfully!',
          type: 'success',
        });
      }
    } catch (error: any) {
      console.error('Error unlocking candidate details:', error);
      showToast({
        message: 'Failed to unlock candidate details. Please try again.',
        type: 'error',
      });
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Candidate Header Card with Glassmorphic Effect - Matching Match Analysis Style */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative w-full"
      >
        {/* Glassmorphic background */}
        <div className="absolute inset-0 rounded-2xl overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-purple-500/10"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-transparent"></div>
          <div className="absolute inset-0 backdrop-blur-xl"></div>
        </div>

        {/* Content */}
        <div className="relative p-4 sm:p-6 lg:p-8">
          <div className="flex flex-col lg:flex-row items-start gap-6 lg:gap-8">
            {/* Avatar Section - Responsive sizing */}
            <div className="relative flex-shrink-0 self-center lg:self-start">
              {(candidate as any).myProfileImage ? (
                <Image
                  src={(candidate as any).myProfileImage}
                  alt={candidate.fullName}
                  width={192}
                  height={192}
                  className="w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 rounded-xl object-cover shadow-2xl"
                />
              ) : (
                <div
                  className={`w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 rounded-xl flex items-center justify-center text-white shadow-2xl ${getRandomBgColor(
                    candidate.fullName
                  )}`}
                >
                  <span className="text-3xl sm:text-4xl lg:text-6xl font-bold">
                    {getInitials(candidate.fullName)}
                  </span>
                </div>
              )}

              {/* Scouted Badge with Animation */}
              {isScoutedCandidate && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 200 }}
                  className="absolute -top-2 -right-2 flex items-center backdrop-blur-md gap-1 bg-gradient-to-r from-pink-600 to-purple-600 px-2 sm:px-3 py-1 rounded-full shadow-sm"
                >
                  <Search className="w-3 h-3 text-white" />
                  <span className="text-[10px] font-bold text-white">Scouted</span>
                </motion.div>
              )}
            </div>

            {/* Content Section - Better organized */}
            <div className="flex-1 w-full min-w-0">
              {/* Header Section with Name and Status */}
              <div className="mb-6">
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                  <div className="flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-3">
                      <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-white/90 bg-clip-text text-transparent">
                        {candidate.fullName}
                      </h1>
                      {(candidate as any).status === CandidateStatus.SHORTLISTED && (
                        <motion.div
                          initial={{ rotate: 0 }}
                          animate={{ rotate: [0, -10, 10, 0] }}
                          transition={{ duration: 0.5 }}
                          className="flex items-center gap-2 px-3 py-1.5 bg-yellow-500/20 rounded-full backdrop-blur-sm"
                        >
                          <Star className="w-4 h-4 text-yellow-500 fill-yellow-500" />
                          <span className="text-sm font-medium text-yellow-300">Shortlisted</span>
                        </motion.div>
                      )}
                    </div>
                    <p className="text-lg text-white/80 font-light mb-2">{candidate.jobTitle}</p>
                    <div className="flex items-center gap-2 text-pink-300/80">
                      <MapPin className="w-4 h-4" />
                      <span className="text-sm">
                        {candidate.location || 'Location not specified'}
                      </span>
                    </div>
                  </div>

                  {/* Quick Action Buttons - Better spacing */}
                  <div className="flex items-center gap-3">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleDownload}
                      className="p-3 rounded-xl bg-white/10 hover:bg-white/20 backdrop-blur-sm transition-all duration-200 group"
                      title="Download"
                    >
                      <Download className="w-5 h-5 text-white/80 group-hover:text-white transition-colors" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleShare}
                      className="p-3 rounded-xl bg-white/10 hover:bg-white/20 backdrop-blur-sm transition-all duration-200 group"
                      title="Share"
                    >
                      <Share2 className="w-5 h-5 text-white/80 group-hover:text-white transition-colors" />
                    </motion.button>
                  </div>
                </div>
              </div>

              {/* Action Buttons Section - Better layout */}
              <div className="flex flex-col sm:flex-row gap-3 max-w-2xl">
                {/* Shortlist Button - Full width on mobile, auto on desktop */}
                {(candidate as any).status !== CandidateStatus.SHORTLISTED && (
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={async () => {
                      if (isShortlisting) return;

                      setIsShortlisting(true);
                      try {
                        const newStatus = CandidateStatus.SHORTLISTED;
                        const notes = 'Status updated internally without email notification';

                        const response = await updateCandidateStatus(
                          candidate.id,
                          newStatus,
                          jobId,
                          notes
                        );

                        if (response) {
                          updateCandidateData(candidate.id, { status: newStatus });

                          showToast({
                            message: 'Added to shortlist',
                            type: 'success',
                          });

                          if (jobId) {
                            await jobStore.onCandidateStatusChange(jobId, candidate.id);
                          }

                          onStatusUpdate?.();
                        }
                      } catch (error) {
                        console.error('Error updating shortlist status:', error);
                        showToast({
                          message: 'Failed to update shortlist status',
                          type: 'error',
                        });
                      } finally {
                        setIsShortlisting(false);
                      }
                    }}
                    disabled={isShortlisting}
                    className={`flex items-center justify-center gap-3 px-6 py-3 rounded-xl backdrop-blur-sm transition-all duration-200 ${
                      isShortlisting
                        ? 'bg-white/5 opacity-50 cursor-not-allowed'
                        : 'bg-white/10 hover:bg-white/20 text-white/80 hover:text-white'
                    } sm:w-auto w-full`}
                  >
                    <Star className="w-5 h-5" />
                    <span className="font-medium">
                      {isShortlisting ? 'Adding to Shortlist...' : 'Add to Shortlist'}
                    </span>
                  </motion.button>
                )}

                {/* Unlock Details Button */}
                {isEmailLocked && (
                  <CreditButton
                    actionType={CreditActionType.UNLOCK_CANDIDATE_DETAILS}
                    onClick={handleUnlockDetails}
                    variant="rank"
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 sm:w-auto w-full justify-center"
                    showCreditCost={true}
                  >
                    <Unlock className="w-5 h-5 mr-2" />
                    Unlock Details
                  </CreditButton>
                )}

                {/* Video Intro Button */}
                {(candidate as any).email && !isEmailLocked && (
                  <div className="sm:w-auto w-full">
                    <VideoIntroButton
                      candidateId={candidate.id}
                      candidateName={candidate.fullName}
                      candidateEmail={(candidate as any).email}
                      candidateSource={candidate.source}
                      jobId={jobId}
                      jobTitle={jobTitle || 'Open Position'}
                      videoIntroEmailSent={(candidate as any).videoIntroEmailSent}
                      emailCorrespondence={(candidate as any).emailCorrespondence}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Information and Score Card with Glassmorphic Effect */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="relative rounded-lg border border-white/10 overflow-hidden"
      >
        {/* Gradient Reflection Background - More transparent */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/2"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/3 to-transparent"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,_white/5,_transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,_white/3,_transparent_40%)]"></div>

        {/* Content */}
        <div className="relative p-4 sm:p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Candidate Information */}
            <div className="lg:col-span-2 space-y-4">
              {/* Basic Information Grid */}
              <div className="space-y-3">
                {/* First Row - Email and Phone */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {/* Email */}
                  <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start gap-2.5">
                      <div className="p-1.5 bg-purple-500/10 rounded">
                        <Mail className="w-3.5 h-3.5 text-purple-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-purple-500 font-medium mb-0.5">Email</p>
                        {isEmailLocked ? (
                          <p className="text-sm text-muted-foreground/70 italic truncate">
                            Locked - Unlock to view
                          </p>
                        ) : (
                          <p className="text-sm font-medium truncate">
                            {(candidate as any).email || 'Not provided'}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Phone */}
                  <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start gap-2.5">
                      <div className="p-1.5 bg-blue-500/10 rounded">
                        <Phone className="w-3.5 h-3.5 text-blue-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-blue-500 font-medium mb-0.5">Phone</p>
                        <p className="text-sm font-medium truncate">
                          {(candidate as any).phone || 'Not provided'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Faint separator line */}
                <FaintLineSeparator />

                {/* Second Row - Experience and Location */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {/* Experience */}
                  <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start gap-2.5">
                      <div className="p-1.5 bg-green-500/10 rounded">
                        <Briefcase className="w-3.5 h-3.5 text-green-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-green-500 font-medium mb-0.5">Experience</p>
                        <p className="text-sm font-medium">
                          {(candidate as any).yearsOfExperience
                            ? `${(candidate as any).yearsOfExperience} Years`
                            : 'Not specified'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Location Preference */}
                  <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start gap-2.5">
                      <div className="p-1.5 bg-orange-500/10 rounded">
                        <MapPin className="w-3.5 h-3.5 text-orange-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-orange-500 font-medium mb-0.5">
                          Preferred Location
                        </p>
                        <p className="text-sm font-medium truncate">
                          {(candidate as any).preferredLocation ||
                            candidate.location ||
                            'Not specified'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Faint separator line */}
                <FaintLineSeparator />

                {/* Third Row - Remote and Contact Method */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {/* Remote Preference */}
                  <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start gap-2.5">
                      <div className="p-1.5 bg-indigo-500/10 rounded">
                        <Globe className="w-3.5 h-3.5 text-indigo-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-indigo-500 font-medium mb-0.5">Remote Only</p>
                        <p className="text-sm font-medium">
                          {(candidate as any).isRemoteOnly ? 'Yes' : 'No'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Contact Method */}
                  <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                    <div className="flex items-start gap-2.5">
                      <div className="p-1.5 bg-pink-500/10 rounded">
                        <Mail className="w-3.5 h-3.5 text-pink-500" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-pink-500 font-medium mb-0.5">Contact Method</p>
                        <p className="text-sm font-medium truncate">
                          {(candidate as any).contactMethod || 'Not specified'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Faint separator line before social profiles */}
              {((candidate as any).linkedinUrl ||
                (candidate as any).githubUrl ||
                (candidate as any).profileUrl) && (
                <>
                  <FaintLineSeparator />

                  {/* Social Profiles Row */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {/* LinkedIn */}
                    {(candidate as any).linkedinUrl && (
                      <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                        <a
                          href={
                            (candidate as any).linkedinUrl.startsWith('http')
                              ? (candidate as any).linkedinUrl
                              : `https://${(candidate as any).linkedinUrl}`
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-start gap-2.5"
                        >
                          <div className="p-1.5 bg-blue-600/10 rounded">
                            <Linkedin className="w-3.5 h-3.5 text-blue-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-xs text-blue-600 font-medium mb-0.5">LinkedIn</p>
                            <p className="text-sm font-medium truncate">View Profile</p>
                          </div>
                        </a>
                      </div>
                    )}

                    {/* GitHub */}
                    {(candidate as any).githubUrl && (
                      <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                        <a
                          href={
                            (candidate as any).githubUrl.startsWith('http')
                              ? (candidate as any).githubUrl
                              : `https://${(candidate as any).githubUrl}`
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-start gap-2.5"
                        >
                          <div className="p-1.5 bg-gray-700/10 rounded">
                            <Github className="w-3.5 h-3.5 text-gray-700" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-xs text-gray-700 font-medium mb-0.5">GitHub</p>
                            <p className="text-sm font-medium truncate">View Profile</p>
                          </div>
                        </a>
                      </div>
                    )}

                    {/* Portfolio/Website */}
                    {(candidate as any).profileUrl && (
                      <div className="bg-muted/30 rounded-lg p-3 hover:bg-muted/50 transition-colors">
                        <a
                          href={
                            (candidate as any).profileUrl.startsWith('http')
                              ? (candidate as any).profileUrl
                              : `https://${(candidate as any).profileUrl}`
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-start gap-2.5"
                        >
                          <div className="p-1.5 bg-green-600/10 rounded">
                            <Globe className="w-3.5 h-3.5 text-green-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-xs text-green-600 font-medium mb-0.5">Portfolio</p>
                            <p className="text-sm font-medium truncate">View Site</p>
                          </div>
                        </a>
                      </div>
                    )}
                  </div>
                </>
              )}

              <FaintLineSeparator />

              {/* Summary Section */}
              {(candidate as any).summary && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <div className="p-1.5 bg-teal-500/10 rounded">
                      <AlignLeft className="w-3.5 h-3.5 text-teal-500" />
                    </div>
                    <h3 className="text-sm font-semibold text-teal-500">Summary</h3>
                  </div>
                  <div className="bg-muted/30 rounded-lg p-3">
                    <p className="text-sm leading-relaxed">{(candidate as any).summary}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column - Match Score */}
            <div className="lg:col-span-1">
              <AnimatedScoreCard
                candidate={candidate as any}
                matchScore={candidate.evaluation?.matchScore}
                rank={candidate.evaluation?.rank}
                topCandidateThreshold={topCandidateThreshold}
                secondTierCandidateThreshold={secondTierCandidateThreshold}
              />
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};
