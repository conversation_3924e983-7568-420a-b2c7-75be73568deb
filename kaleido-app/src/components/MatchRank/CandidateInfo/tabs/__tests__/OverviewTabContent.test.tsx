import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { OverviewTabContent } from '../OverviewTabContent';
import { ICandidate } from '@/entities/interfaces';
import { CandidateStatus } from '@/types/candidate.types';
import { mockCandidateData } from '@/test-utils';

// Mock dependencies
jest.mock('@/components/common/CreditButton', () => {
  return function MockCreditButton({ children, onClick, className, ...props }: any) {
    return (
      <button onClick={onClick} className={className} data-testid="credit-button" {...props}>
        {children}
      </button>
    );
  };
});

jest.mock('@/components/Toaster', () => ({
  showToast: jest.fn(),
}));

jest.mock('@/lib/apiHelper', () => ({
  apiClient: {
    post: jest.fn(),
  },
}));

jest.mock('@/stores/unifiedJobStore', () => ({
  useJobStore: jest.fn(() => ({
    fetchCandidateById: jest.fn(),
  })),
}));

jest.mock('@/stores/matchrankDetailsStore', () => ({
  useMatchRankDetailsStore: jest.fn(() => ({
    updateCandidateData: jest.fn(),
    updateCandidateStatus: jest.fn(),
  })),
}));

jest.mock('@/hooks/useJobThresholds', () => ({
  useJobThresholds: jest.fn(() => ({
    topCandidateThreshold: 80,
    secondTierCandidateThreshold: 60,
  })),
}));

jest.mock('../../../AnimatedScoreCard', () => ({
  __esModule: true,
  default: ({ candidate, matchScore }: any) => (
    <div data-testid="animated-score-card">
      Score: {matchScore} for {candidate.fullName}
    </div>
  ),
}));

jest.mock('../../VideoIntroButton', () => ({
  VideoIntroButton: ({ candidateName }: any) => (
    <button data-testid="video-intro-button">Video Intro for {candidateName}</button>
  ),
}));

jest.mock('@/components/common/CreditButton', () => ({
  CreditButton: ({ children, ...props }: any) => (
    <button data-testid="credit-button" {...props}>
      {children}
    </button>
  ),
}));

jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    // eslint-disable-next-line @next/next/no-img-element
    return <img src={src} alt={alt} data-testid="profile-image" {...props} />;
  };
});

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  Download: () => <div data-testid="download-icon" />,
  Share2: () => <div data-testid="share-icon" />,
  Star: () => <div data-testid="star-icon" />,
  Search: () => <div data-testid="search-icon" />,
  MapPin: () => <div data-testid="map-pin-icon" />,
  Mail: () => <div data-testid="mail-icon" />,
  Phone: () => <div data-testid="phone-icon" />,
  Briefcase: () => <div data-testid="briefcase-icon" />,
  Globe: () => <div data-testid="globe-icon" />,
  Linkedin: () => <div data-testid="linkedin-icon" />,
  Github: () => <div data-testid="github-icon" />,
  AlignLeft: () => <div data-testid="align-left-icon" />,
  Unlock: () => <div data-testid="unlock-icon" />,
}));

const mockCandidate: ICandidate = {
  ...mockCandidateData,
  id: 'test-candidate-id',
  fullName: 'John Doe',
  jobTitle: 'Senior Software Engineer',
  location: 'San Francisco, CA',
  status: CandidateStatus.NEW,
  source: 'REGULAR', // Override the LINKEDIN_SCOUT from mockCandidateData
  sourceType: 'REGULAR',
  summary: undefined, // Override summary from mockCandidateData
  linkedinUrl: undefined, // Override linkedinUrl from mockCandidateData
  githubUrl: undefined,
  evaluation: {
    matchScore: 85,
    rank: 1,
    detailedScoreAnalysis: {
      specificCriteriaMatched: {
        skillsMatch: 0.9,
        experienceRelevance: 0.8,
        locationAndAvailability: 0.85,
      },
    },
  },
} as any;

const mockScoutedCandidate: ICandidate = {
  ...mockCandidate,
  source: 'LINKEDIN_SCOUT',
} as any;

const mockLockedCandidate: ICandidate = {
  ...mockCandidate,
  email: '<EMAIL>',
} as any;

const mockShortlistedCandidate: ICandidate = {
  ...mockCandidate,
  status: CandidateStatus.SHORTLISTED,
} as any;

const mockCandidateWithAllData: ICandidate = {
  ...mockCandidate,
  email: '<EMAIL>',
  phone: '******-123-4567',
  yearsOfExperience: 5,
  preferredLocation: 'Remote',
  isRemoteOnly: true,
  contactMethod: 'Email',
  linkedinUrl: 'https://linkedin.com/in/johndoe',
  githubUrl: 'https://github.com/johndoe',
  profileUrl: 'https://johndoe.dev',
  summary: 'Experienced software engineer with a passion for building scalable applications.',
} as any;

describe('OverviewTabContent', () => {
  const defaultProps = {
    candidate: mockCandidate,
    jobId: 'test-job-id',
    jobTitle: 'Software Engineer Position',
    onCandidateSelect: jest.fn(),
    onStatusUpdate: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders candidate information correctly', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Senior Software Engineer')).toBeInTheDocument();
      expect(screen.getByText('San Francisco, CA')).toBeInTheDocument();
    });

    it('renders animated score card', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.getByTestId('animated-score-card')).toBeInTheDocument();
      expect(screen.getByText('Score: 85 for John Doe')).toBeInTheDocument();
    });

    it('renders action buttons', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.getByTestId('download-icon')).toBeInTheDocument();
      expect(screen.getByTestId('share-icon')).toBeInTheDocument();
    });

    it('displays correct icons for different sections', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getAllByTestId('mail-icon').length).toBeGreaterThan(0);
      expect(screen.getByTestId('phone-icon')).toBeInTheDocument();
      expect(screen.getByTestId('briefcase-icon')).toBeInTheDocument();
      expect(screen.getAllByTestId('map-pin-icon').length).toBeGreaterThan(0);
    });
  });

  describe('Avatar Rendering', () => {
    it('renders profile image when available', () => {
      const candidateWithImage = {
        ...mockCandidate,
        myProfileImage: 'https://example.com/profile.jpg',
      } as any;

      render(<OverviewTabContent {...defaultProps} candidate={candidateWithImage} />);

      const profileImage = screen.getByTestId('profile-image');
      expect(profileImage).toHaveAttribute('src', 'https://example.com/profile.jpg');
      expect(profileImage).toHaveAttribute('alt', 'John Doe');
    });

    it('renders initials avatar when no profile image', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('generates consistent avatar colors based on name', () => {
      render(<OverviewTabContent {...defaultProps} />);

      const initialsElement = screen.getByText('JD');
      const avatarContainer = initialsElement.closest('div');
      expect(avatarContainer).toHaveClass(/bg-(blue|purple|green|yellow|pink|indigo|red|teal)-500/);
    });
  });

  describe('Scouted Candidate Badge', () => {
    it('displays scouted badge for LinkedIn scouted candidates', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockScoutedCandidate} />);

      expect(screen.getByText('Scouted')).toBeInTheDocument();
      expect(screen.getByTestId('search-icon')).toBeInTheDocument();
    });

    it('does not display scouted badge for regular candidates', () => {
      const regularCandidate = {
        ...mockCandidate,
        source: 'REGULAR',
        sourceType: 'REGULAR',
      };
      render(<OverviewTabContent {...defaultProps} candidate={regularCandidate} />);

      expect(screen.queryByText('Scouted')).not.toBeInTheDocument();
    });
  });

  describe('Shortlisted Status', () => {
    it('displays shortlisted badge for shortlisted candidates', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockShortlistedCandidate} />);

      expect(screen.getByText('Shortlisted')).toBeInTheDocument();
      expect(screen.getByTestId('star-icon')).toBeInTheDocument();
    });

    it('does not show shortlist button for already shortlisted candidates', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockShortlistedCandidate} />);

      expect(screen.queryByText('Add to Shortlist')).not.toBeInTheDocument();
    });

    it('shows shortlist button for non-shortlisted candidates', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.getByText('Add to Shortlist')).toBeInTheDocument();
    });
  });

  describe('Contact Information', () => {
    it('displays email when available and not locked', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('shows locked message for locked email', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockLockedCandidate} />);

      expect(screen.getByText('Locked - Unlock to view')).toBeInTheDocument();
    });

    it('displays phone number when available', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getByText('******-123-4567')).toBeInTheDocument();
    });

    it('shows "Not provided" for missing contact information', () => {
      const candidateWithoutContact = {
        ...mockCandidate,
        email: undefined,
        phone: undefined,
      };
      render(<OverviewTabContent {...defaultProps} candidate={candidateWithoutContact} />);

      expect(screen.getAllByText('Not provided')).toHaveLength(2); // Email and phone
    });
  });

  describe('Experience Information', () => {
    it('displays years of experience when available', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getByText('5 Years')).toBeInTheDocument();
    });

    it('shows "Not specified" when experience is missing', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.getByText('Not specified')).toBeInTheDocument();
    });

    it('displays preferred location', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getByText('Remote')).toBeInTheDocument();
    });

    it('displays remote preference', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getByText('Yes')).toBeInTheDocument(); // Remote Only
    });

    it('displays contact method', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getAllByText('Email').length).toBeGreaterThan(0);
    });
  });

  describe('Social Profiles', () => {
    it('renders LinkedIn profile link when available', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      const linkedinLink = screen.getByRole('link', { name: /linkedin/i });
      expect(linkedinLink).toHaveAttribute('href', 'https://linkedin.com/in/johndoe');
      expect(linkedinLink).toHaveAttribute('target', '_blank');
    });

    it('renders GitHub profile link when available', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      const githubLink = screen.getByRole('link', { name: /github/i });
      expect(githubLink).toHaveAttribute('href', 'https://github.com/johndoe');
      expect(githubLink).toHaveAttribute('target', '_blank');
    });

    it('renders portfolio link when available', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      const portfolioLink = screen.getByRole('link', { name: /portfolio/i });
      expect(portfolioLink).toHaveAttribute('href', 'https://johndoe.dev');
      expect(portfolioLink).toHaveAttribute('target', '_blank');
    });

    it('adds https protocol to URLs without protocol', () => {
      const candidateWithProtocollessUrls = {
        ...mockCandidate,
        linkedinUrl: 'linkedin.com/in/johndoe',
        githubUrl: 'github.com/johndoe',
        profileUrl: 'johndoe.dev',
      } as any;

      render(<OverviewTabContent {...defaultProps} candidate={candidateWithProtocollessUrls} />);

      const linkedinLink = screen.getByRole('link', { name: /linkedin/i });
      expect(linkedinLink).toHaveAttribute('href', 'https://linkedin.com/in/johndoe');
    });

    it('does not render social profiles section when no URLs available', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.queryByRole('link', { name: /linkedin/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /github/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('link', { name: /portfolio/i })).not.toBeInTheDocument();
    });
  });

  describe('Summary Section', () => {
    it('displays summary when available', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getByText('Summary')).toBeInTheDocument();
      expect(
        screen.getByText(
          'Experienced software engineer with a passion for building scalable applications.'
        )
      ).toBeInTheDocument();
    });

    it('does not render summary section when summary is not available', () => {
      render(<OverviewTabContent {...defaultProps} />);

      expect(screen.queryByText('Summary')).not.toBeInTheDocument();
    });
  });

  describe('Action Buttons Functionality', () => {
    it('shows toast message when download button is clicked', async () => {
      const { showToast } = require('@/components/Toaster');

      render(<OverviewTabContent {...defaultProps} />);

      const downloadButton = screen.getByTestId('download-icon').closest('button');
      fireEvent.click(downloadButton!);

      expect(showToast).toHaveBeenCalledWith({
        message: 'Download feature coming soon',
        type: 'info',
      });
    });

    it('shows toast message when share button is clicked', async () => {
      const { showToast } = require('@/components/Toaster');

      render(<OverviewTabContent {...defaultProps} />);

      const shareButton = screen.getByTestId('share-icon').closest('button');
      fireEvent.click(shareButton!);

      expect(showToast).toHaveBeenCalledWith({
        message: 'Share feature coming soon',
        type: 'info',
      });
    });
  });

  describe('Shortlist Functionality', () => {
    it('handles shortlist button click', async () => {
      const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');
      const mockUpdateCandidateStatus = jest.fn().mockResolvedValue(true);
      const mockUpdateCandidateData = jest.fn();

      useMatchRankDetailsStore.mockReturnValue({
        updateCandidateData: mockUpdateCandidateData,
        updateCandidateStatus: mockUpdateCandidateStatus,
      });

      render(<OverviewTabContent {...defaultProps} />);

      const shortlistButton = screen.getByText('Add to Shortlist');
      fireEvent.click(shortlistButton);

      await waitFor(() => {
        expect(mockUpdateCandidateStatus).toHaveBeenCalledWith(
          'test-candidate-id',
          CandidateStatus.SHORTLISTED,
          'test-job-id',
          'Status updated internally without email notification'
        );
      });
    });

    it('disables shortlist button while processing', async () => {
      const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');
      const mockUpdateCandidateStatus = jest
        .fn()
        .mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      useMatchRankDetailsStore.mockReturnValue({
        updateCandidateData: jest.fn(),
        updateCandidateStatus: mockUpdateCandidateStatus,
      });

      render(<OverviewTabContent {...defaultProps} />);

      const shortlistButton = screen.getByText('Add to Shortlist');
      fireEvent.click(shortlistButton);

      expect(screen.getByText('Adding to Shortlist...')).toBeInTheDocument();
      const buttonElement = screen.getByText('Adding to Shortlist...').closest('button');
      expect(buttonElement).toBeDisabled();
    });
  });

  describe('Unlock Details Functionality', () => {
    it('shows unlock details button for locked candidates', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockLockedCandidate} />);

      expect(screen.getByTestId('credit-button')).toBeInTheDocument();
      expect(screen.getByText('Unlock Details')).toBeInTheDocument();
    });

    it('does not show unlock details button for unlocked candidates', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.queryByText('Unlock Details')).not.toBeInTheDocument();
    });

    it('handles unlock details functionality', async () => {
      const { apiClient } = require('@/lib/apiHelper');
      const mockPost = jest.fn().mockResolvedValue({
        candidate: {
          email: '<EMAIL>',
          phone: '******-987-6543',
        },
      });
      apiClient.post = mockPost;

      render(<OverviewTabContent {...defaultProps} candidate={mockLockedCandidate} />);

      const unlockButton = screen.getByText('Unlock Details');
      fireEvent.click(unlockButton);

      await waitFor(() => {
        expect(mockPost).toHaveBeenCalledWith('/candidates/test-candidate-id/enhance');
      });
    });
  });

  describe('Video Intro Button', () => {
    it('shows video intro button for candidates with unlocked email', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockCandidateWithAllData} />);

      expect(screen.getByTestId('video-intro-button')).toBeInTheDocument();
      expect(screen.getByText('Video Intro for John Doe')).toBeInTheDocument();
    });

    it('does not show video intro button for candidates with locked email', () => {
      render(<OverviewTabContent {...defaultProps} candidate={mockLockedCandidate} />);

      expect(screen.queryByTestId('video-intro-button')).not.toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('applies responsive classes for mobile layout', () => {
      render(<OverviewTabContent {...defaultProps} />);

      const mainContainer = screen.getByText('John Doe').closest('[class*="max-w-7xl"]');
      expect(mainContainer).toHaveClass('max-w-7xl', 'mx-auto');
    });

    it('applies responsive padding to content sections', () => {
      render(<OverviewTabContent {...defaultProps} />);

      const contentSections = screen
        .getAllByText(/Email|Phone|Experience/)
        .map(el => el.closest('[class*="p-4"]'));

      contentSections.forEach(section => {
        expect(section).toHaveClass('p-4', 'sm:p-6');
      });
    });
  });

  describe('Error Handling', () => {
    it('handles missing candidate data gracefully', () => {
      const incompleteCandidate = {
        id: 'test-id',
        fullName: 'Test User',
      } as any;

      expect(() => {
        render(<OverviewTabContent {...defaultProps} candidate={incompleteCandidate} />);
      }).not.toThrow();
    });

    it('handles undefined evaluation gracefully', () => {
      const candidateWithoutEvaluation = {
        ...mockCandidate,
        evaluation: undefined,
      } as any;

      expect(() => {
        render(<OverviewTabContent {...defaultProps} candidate={candidateWithoutEvaluation} />);
      }).not.toThrow();
    });
  });
});
