import React, { useRef, useState } from 'react';

import { motion } from 'framer-motion';
import { ListFilter } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { CreditCoin } from '@/components/common/CreditCoin';
import { useJobStateStore } from '@/stores/unifiedJobStore';
import { useJobStore } from '@/stores/unifiedJobStore';
import {
  selectHasFormEdits,
  selectHasUnrankedCandidates,
  useMatchRankDetailsStore,
} from '@/stores/matchrankDetailsStore';
import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';

import { Particles } from './Particles';

interface RankNowButtonProps {
  jobId: string;
  candidateCount: number;
  isThresholdValid?: () => boolean;
  className?: string;
  disabled?: boolean;
  onRankClick?: () => Promise<void>;
  useJobEditStore?: boolean; // Whether to use jobEditStore as source of truth
  matchRankCost?: {
    success: boolean;
    creditCost: number;
    unevaluatedCandidatesCount: number;
    isValid: boolean;
    message: string;
    availableCredits: number;
  };
}

export const RankNowButton: React.FC<RankNowButtonProps> = ({
  jobId,
  candidateCount,
  isThresholdValid = () => true,
  className = '',
  disabled = false,
  onRankClick,
  useJobEditStore: useJobEditStoreFlag = false,
  matchRankCost,
}) => {
  const router = useRouter();
  // Get stores
  const matchRankStore = useMatchRankDetailsStore();
  const jobStore = useJobStore();

  // Use appropriate store based on flag
  const { isProcessing, handleRanking, viewMode, showUploader } = matchRankStore;

  // Check if there are any form edits or file uploads - use jobStore if specified
  const hasFormEdits = useJobEditStoreFlag
    ? jobStore.hasUnsavedChanges
    : useMatchRankDetailsStore(selectHasFormEdits);

  // Check if there are unranked candidates - use jobStore if specified
  const hasUnrankedCandidates = useJobEditStoreFlag
    ? (jobStore.candidates?.unranked?.length > 0 ?? false)
    : useMatchRankDetailsStore(selectHasUnrankedCandidates);

  // Check if MatchRankStatusManager has any active jobs
  const hasActiveMatchRankJobs = useMatchRankJobsStore(
    state =>
      state.activeJobs.length > 0 ||
      Object.values(state.jobs).some(job => job.status === 'queued' || job.status === 'active')
  );

  // Use backend-driven credit calculation instead of frontend logic
  const [backendCreditCost, setBackendCreditCost] = React.useState<{
    creditCost: number;
    unevaluatedCandidatesCount: number;
    isValid: boolean;
    message: string;
  } | null>(null);

  // Use jobStore for match rank cost if not provided as prop
  const fetchJobCriteria = jobStore.fetchJobCriteria;
  const storeMatchRankCost = jobStore.matchRankCost; // matchRankCost is at root level, not in currentJob

  // Subscribe to store updates for match rank cost
  React.useEffect(() => {
    if (!jobId) return;

    // If matchRankCost is provided as prop, use that instead of store
    if (matchRankCost) {
      return;
    }

    // Subscribe to store changes for real-time updates
    const unsubscribe = jobStore.subscribe(
      state => ({ matchRankCost: state.matchRankCost, stats: state.stats }),
      (current, previous) => {
        if (
          current.matchRankCost !== previous.matchRankCost ||
          current.stats?.totalCandidates !== previous.stats?.totalCandidates
        ) {
          console.log('[RankNowButton] Store matchRankCost updated:', current.matchRankCost);
        }
      }
    );

    // Initial fetch if we don't have data
    if (!storeMatchRankCost) {
      fetchJobCriteria(jobId, false).catch(error => {
        console.error('RankNowButton: Failed to fetch job criteria:', error);
      });
    }

    return () => unsubscribe();
  }, [jobId, matchRankCost, fetchJobCriteria]);

  // Use provided matchRankCost prop, then backend-calculated credit cost as the authoritative source
  const creditCost =
    matchRankCost?.creditCost ??
    backendCreditCost?.creditCost ??
    storeMatchRankCost?.creditCost ??
    0;
  const unevaluatedCandidatesCount =
    matchRankCost?.unevaluatedCandidatesCount ??
    backendCreditCost?.unevaluatedCandidatesCount ??
    candidateCount;
  const isValidCreditCheck = matchRankCost?.isValid ?? backendCreditCost?.isValid ?? true;
  const creditMessage = matchRankCost?.message ?? backendCreditCost?.message ?? '';
  const [isClicked, setIsClicked] = useState(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Check if we should enable the button based on context
  const shouldEnableForContext = () => {
    // If we have an onRankClick prop, it means the parent component is handling the logic
    if (onRankClick) {
      return true; // Let the parent component control via the disabled prop
    }

    // If using jobStore, check for actual pending changes
    if (useJobEditStoreFlag) {
      // Use the helper method from jobStore to determine if there are pending changes
      return jobStore.hasPendingChanges();
    }

    // For legacy MatchRank logic, check if we're in a context where ranking makes sense
    return hasFormEdits || hasUnrankedCandidates || showUploader;
  };

  // Debug logging to help identify why button might be disabled

  // Check if button should be enabled based on credit cost
  const shouldEnableForCreditCost = () => {
    // Enable for threshold changes even if creditCost is 0 (threshold changes are free)
    if (useJobEditStoreFlag) {
      const hasThresholdChanges = jobStore.hasThresholdChanges();
      return hasThresholdChanges || creditCost > 0;
    }

    // For legacy MatchRank logic, only enable if creditCost > 0
    return creditCost > 0;
  };

  const isButtonEnabled =
    !isProcessing &&
    !isClicked &&
    !hasActiveMatchRankJobs && // Disable if MatchRankStatusManager has active jobs
    candidateCount > 0 &&
    isThresholdValid() &&
    !disabled &&
    shouldEnableForContext() &&
    isValidCreditCheck && // Use the credit validation result
    shouldEnableForCreditCost() && // Only enable if creditCost > 0 or threshold changes
    (unevaluatedCandidatesCount > 0 || candidateCount > 0); // Enable if there are candidates to evaluate OR if we have candidates but no match rank data yet

  const { addJob } = useMatchRankJobsStore();

  const handleClick = async () => {
    // Prevent multiple clicks
    if (isClicked || isProcessing) {
      return;
    }

    // Set clicked state to prevent multiple clicks
    setIsClicked(true);

    try {
      // The job will be added to the match rank jobs store in the handleRanking method
      // with the correct queue job ID from the API response
      if (onRankClick) {
        await onRankClick();
      } else {
        await handleRanking(jobId);
      }

      // The handleRanking method already handles job updates and state management
      // We only need to dispatch a single targeted event to notify components
      // that match ranking has started (not completed - that's handled by MatchRankStatusManager)

      // Update the job state store to indicate match ranking has started
      useJobStateStore.getState().markJobAsUpdated(jobId);
    } catch (error) {
      // Only log errors in non-test environments
      if (process.env.NODE_ENV !== 'test') {
        console.error('Error during ranking process:', error);
      }

      // Check if this is a credit-related error
      // The apiHelper should have already shown the credit modal for 402 errors
      let errorMessage = 'Failed to start match and rank process';
      let isCreditError = false;

      if (error && typeof error === 'object') {
        // Check if it's an API error with a message
        if ('message' in error && typeof error.message === 'string') {
          errorMessage = error.message;

          // If it's a credit-related error, the modal should already be shown
          if (
            error.message.toLowerCase().includes('credit') ||
            error.message.toLowerCase().includes('insufficient')
          ) {
            isCreditError = true;
          }
        }

        // Check if it's a 402 error (Payment Required - credits)
        if ('status' in error && error.status === 402) {
          isCreditError = true;
        }
      }

      // Always add the job to the status manager to show the error
      // This ensures the MatchRankStatusManager is visible for both credit and non-credit errors

      addJob({
        jobId, // Use the original job ID since we don't have a queue job ID
        status: 'failed',
        message: isCreditError ? 'Insufficient credits to start match ranking' : errorMessage,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      // Reset the clicked state after a delay
      debounceTimerRef.current = setTimeout(() => {
        setIsClicked(false);
      }, 2000); // 2 second delay before allowing another click
    }
  };

  // Clean up debounce timer on unmount
  React.useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <div className={`relative group ${className}`}>
      {/* Background glow animation - enhanced for active state */}
      <motion.div
        animate={{
          scale: isButtonEnabled ? [1, 1.15, 1] : [1, 1.05, 1],
          opacity: isButtonEnabled ? [0.4, 0.8, 0.4] : [0.2, 0.3, 0.2],
        }}
        transition={{
          duration: isButtonEnabled ? 4 : 6,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute inset-0 rounded-md blur-lg"
        style={{
          background: isButtonEnabled ? 'var(--button-primary-bg)' : 'var(--button-secondary-bg)',
          opacity: isButtonEnabled ? 0.5 : 0.2,
        }}
      />

      <button
        type="button"
        onClick={handleClick}
        disabled={!isButtonEnabled}
        className={`relative px-6 py-2.5 flex items-center gap-2 text-gray-50 rounded-full
          transition-all duration-300 font-semibold text-lg overflow-hidden
          ${
            isButtonEnabled
              ? 'bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-500 hover:to-indigo-600 shadow-lg hover:shadow-xl scale-100 hover:scale-105'
              : 'bg-gray-500/50 text-gray-300 opacity-60 cursor-not-allowed'
          }`}
        title={
          candidateCount === 0
            ? 'No candidates available to rank'
            : !isThresholdValid()
              ? 'Both Top and 2nd-tier thresholds must be greater than 0 to rank candidates'
              : disabled
                ? 'Button disabled. Please wait for uploads to complete.'
                : hasActiveMatchRankJobs
                  ? 'Match & Rank is currently in progress. Please wait for it to complete.'
                  : useJobEditStoreFlag && !shouldEnableForContext()
                    ? 'No pending changes detected. Please edit thresholds, upload candidates, or make other changes before ranking.'
                    : !useJobEditStoreFlag && !hasFormEdits
                      ? 'No changes detected. Please edit the job form, thresholds, or upload files before ranking.'
                      : creditCost === 0
                        ? 'No changes that require re-ranking detected. Only threshold changes do not require credits.'
                        : isClicked
                          ? 'Processing ranking request...'
                          : isButtonEnabled
                            ? `Click to rank ${creditCost === candidateCount ? 'all' : 'new'} candidates (${creditCost} credits)`
                            : ''
        }
      >
        <motion.div
          initial="hidden"
          animate="visible"
          className="absolute inset-0"
          style={{ opacity: !isButtonEnabled ? 0.2 : 0.7 }}
        >
          <Particles />
        </motion.div>
        <ListFilter
          size={18}
          className="relative z-10"
          style={{
            color: !isButtonEnabled ? 'var(--button-secondary-text)' : 'var(--button-primary-text)',
          }}
        />
        <span className="relative z-10">
          {isProcessing || isClicked
            ? 'Processing...'
            : hasActiveMatchRankJobs
              ? 'Matching'
              : 'Rank Now'}
        </span>
        {/* Credit cost indicator */}
        {!isProcessing && !isClicked && creditCost > 0 && (
          <div className="relative z-10 flex items-center gap-1 ml-2 px-2 py-1 bg-white/20 rounded-full text-xs font-medium">
            <CreditCoin size={12} showValue={true} value={creditCost} />
          </div>
        )}
      </button>
    </div>
  );
};
