import {
  selectHasFormEdits,
  selectHasUnrankedCandidates,
  useMatchRankDetailsStore,
} from '@/stores/matchrankDetailsStore';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';

// Import after mocks
import apiHelper from '@/lib/apiHelper';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { useMatchRankJobsStore } from '@/stores/matchRankJobsStore';
import { RankNowButton } from './RankNowButton';

// Mock modules first
const mockMarkJobAsUpdated = jest.fn();

jest.mock('@/lib/apiHelper', () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

jest.mock('@/stores/unifiedJobStore', () => ({
  useJobsStore: jest.fn(),
  useJobStore: jest.fn(),
  useJobStateStore: {
    getState: () => ({
      markJobAsUpdated: mockMarkJobAsUpdated,
    }),
  },
}));

jest.mock('@/stores/matchrankDetailsStore', () => {
  const selectHasFormEdits = jest.fn(() => true);
  const selectHasUnrankedCandidates = jest.fn(() => true);
  return {
    useMatchRankDetailsStore: jest.fn(),
    selectHasFormEdits,
    selectHasUnrankedCandidates,
  };
});

jest.mock('@/stores/matchRankJobsStore', () => ({
  useMatchRankJobsStore: Object.assign(
    jest.fn(() => ({
      addJob: jest.fn(),
    })),
    {
      getState: jest.fn(() => ({
        jobs: {},
        activeJobs: [],
      })),
    }
  ),
}));

// Get the mocked functions
const mockUseJobsStore = useJobsStore as jest.MockedFunction<typeof useJobsStore>;
const mockUseMatchRankDetailsStore = useMatchRankDetailsStore as jest.MockedFunction<
  typeof useMatchRankDetailsStore
>;
const mockUseMatchRankJobsStore = useMatchRankJobsStore as jest.MockedFunction<
  typeof useMatchRankJobsStore
>;
// Remove this line as we'll mock directly in tests

describe('RankNowButton Optimizations', () => {
  const mockProps = {
    jobId: 'test-job-123',
    candidateCount: 25,
    isThresholdValid: () => true,
    className: '',
    disabled: false,
    // onRankClick is intentionally omitted so component uses handleRanking
  };

  // Mock functions that we'll use in tests
  const mockHandleRanking = jest.fn();
  const mockAddJob = jest.fn();
  const mockRefreshJobAndNotify = jest.fn();

  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Clear any timers that might be running
    jest.clearAllTimers();

    // Reset API mocks to ensure clean state
    (apiHelper.get as jest.Mock).mockReset();
    (apiHelper.post as jest.Mock).mockReset();

    // Mock API response for credit cost - return JobCriteriaData directly
    (apiHelper.get as jest.Mock).mockResolvedValue({
      jobId: 'test-job-123',
      topTierCandidateThreshold: 7000,
      secondTierCandidateThreshold: 5000,
      status: 'MATCHED',
      totalCandidates: 25,
      candidateStats: {
        totalCandidates: 25,
        topTierCount: 5,
        secondTierCount: 10,
        othersCount: 10,
      },
      candidates: [],
      matchRankCost: {
        success: true,
        creditCost: 2,
        unevaluatedCandidatesCount: 2,
        isValid: true,
        message: 'Valid operation',
        availableCredits: 50,
      },
      stats: {
        totalCandidates: 25,
        topTierCount: 5,
        secondTierCount: 10,
        othersCount: 10,
      },
    });

    // Setup store mocks with proper return values
    mockUseMatchRankDetailsStore.mockImplementation(selector => {
      if (selector) {
        // Handle specific selectors
        if (selector === selectHasFormEdits) return true;
        if (selector === selectHasUnrankedCandidates) return true;
        // Handle inline selector functions (like creditCost calculation)
        if (typeof selector === 'function') {
          // Return a mock state for the creditCost calculation
          return selector({
            // Core state
            isOpen: true,
            selectedJobId: mockProps.jobId,
            selectedJob: {
              id: mockProps.jobId,
              candidates: [{ id: 'candidate-1' } as any, { id: 'candidate-2' } as any],
              candidateEvaluations: [],
            } as any,
            viewMode: 'edit' as const,
            candidateCount: 25,
            isProcessing: false,
            selectedCandidate: null,
            showUploader: true, // Enable showUploader to make button enabled
            isUploadMode: false,
            hasFormChanges: true,
            hasFileUploads: false,

            // Credit calculation state
            lastRankedCandidateCount: 0,
            jobDescriptionChanged: true, // This will make creditCost > 0

            // Cache to prevent unnecessary API calls
            jobCache: {},
            lastJobDataRefreshTimestamps: {},
            lastJobsRefreshTimestamp: 0,

            // Actions - Mock functions
            setIsOpen: jest.fn(),
            setSelectedJob: jest.fn(),
            setViewMode: jest.fn(),
            updateCandidateCount: jest.fn(),
            setIsProcessing: jest.fn(),
            setSelectedCandidate: jest.fn(),
            setShowUploader: jest.fn(),
            setHasFormChanges: jest.fn(),
            setHasFileUploads: jest.fn(),
            setJobDescriptionChanged: jest.fn(),
            setLastRankedCandidateCount: jest.fn(),
            resetFormState: jest.fn(),
            getJobById: jest.fn(),
            cacheJob: jest.fn(),
            handleRanking: mockHandleRanking,
            refreshJobData: jest.fn(),
            refreshAfterScouting: jest.fn(),
            updateCandidateStatus: jest.fn(),
            updateCandidateData: jest.fn(),
            checkCulturalFitSetup: jest.fn(),
            handleEditClick: jest.fn(),
            handleUploaderClose: jest.fn(),
            openMatchRankDetails: jest.fn(),
            closeMatchRankDetails: jest.fn(),
            clearUrlParameter: jest.fn(),
            clearUrlParameters: jest.fn(),
          });
        }
        return 0;
      }
      // Default store state when no selector is provided
      return {
        isProcessing: false,
        handleRanking: mockHandleRanking,
        setSelectedJob: jest.fn(),
        viewMode: 'edit',
        showUploader: true, // Enable showUploader to make button enabled
        selectedJob: null,
        hasFormChanges: true,
        hasFileUploads: false,
        candidateCount: 25,
        lastRankedCandidateCount: 0,
        jobDescriptionChanged: true, // This will make creditCost > 0
      };
    });

    // Mock useJobCriteriaStore to return no cached data, forcing API call
    const mockFetchJobCriteria = jest.fn().mockImplementation(async (jobId, force) => {
      hasBeenCalled = true; // Mark as called to return cached data next time
      // Simulate the API call that the real fetchJobCriteria would make
      const result = await apiHelper.get(`/jobs/${jobId}/criteria`);
      return result;
    });

    // Create a mock that returns cached data after the first call
    let hasBeenCalled = false;
    const mockGetMatchRankCost = jest.fn(() => {
      if (hasBeenCalled) {
        return {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        };
      }
      return null; // No cached data initially
    });

    (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
      currentJob: null,
      stats: null,
      candidates: null,
      isLoading: false,
      hasUnsavedChanges: false,
      fetchJobCriteria: mockFetchJobCriteria,
      setSelectedJob: jest.fn(),
      updateFormData: jest.fn(),
      onWorkerComplete: jest.fn(),
      subscribe: jest.fn(() => jest.fn()), // Return unsubscribe function
      matchRankCost: null,
      hasPendingChanges: jest.fn(() => false),
      hasThresholdChanges: jest.fn(() => false),
    });

    mockUseMatchRankJobsStore.mockImplementation(selector => {
      if (selector) {
        // Handle selectors for active jobs
        return false; // No active jobs
      }
      return {
        addJob: mockAddJob,
        activeJobs: [],
        jobs: {},
        getState: jest.fn(() => ({
          jobs: {},
          activeJobs: [],
        })),
      };
    });

    // Mock successful ranking operation
    mockHandleRanking.mockResolvedValue(true);
  });

  describe('Simplified Event Dispatching', () => {
    it('should dispatch single targeted event for match rank start', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      // Advance timers to allow async operations to complete
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        expect(mockHandleRanking).toHaveBeenCalledTimes(1);
        expect(mockHandleRanking).toHaveBeenCalledWith(mockProps.jobId);
        expect(mockMarkJobAsUpdated).toHaveBeenCalledTimes(1);
        expect(mockMarkJobAsUpdated).toHaveBeenCalledWith(mockProps.jobId);
      });
    });

    it('should not dispatch multiple events for single ranking operation', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      await waitFor(() => {
        // Should only update job state once, not multiple times
        expect(mockMarkJobAsUpdated).toHaveBeenCalledTimes(1);
      });

      // Verify no additional API refresh calls
      expect(mockRefreshJobAndNotify).not.toHaveBeenCalled();
    });

    it('should let handleRanking method manage state updates', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      // Advance timers to allow the click handler to complete
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        // Should call handleRanking to manage state
        expect(mockHandleRanking).toHaveBeenCalledWith(mockProps.jobId);
      });

      // Should not make additional API calls beyond handleRanking
      expect(mockRefreshJobAndNotify).not.toHaveBeenCalled();
    });

    it('should use onRankClick when provided instead of handleRanking', async () => {
      const customOnRankClick = jest.fn().mockResolvedValue(true);
      const propsWithCustomHandler = {
        ...mockProps,
        onRankClick: customOnRankClick,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCustomHandler} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      await waitFor(() => {
        expect(customOnRankClick).toHaveBeenCalled();
        expect(mockHandleRanking).not.toHaveBeenCalled();
      });
    });
  });

  describe('API Refresh Optimization', () => {
    it('should not trigger API refresh calls after ranking starts', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      // Advance timers to allow the click handler to complete
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        expect(mockHandleRanking).toHaveBeenCalled();
      });

      // Should NOT call any refresh methods
      expect(mockRefreshJobAndNotify).not.toHaveBeenCalled();
    });

    it('should not make redundant API calls during ranking process', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      await waitFor(() => {
        expect(mockMarkJobAsUpdated).toHaveBeenCalled();
      });

      // Verify no additional API calls beyond the ranking operation itself
      expect(mockRefreshJobAndNotify).not.toHaveBeenCalled();
    });
  });

  describe('Button State Management', () => {
    it('should prevent multiple clicks during processing', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      // First click
      fireEvent.click(rankButton);

      // Advance timers slightly to allow first click to process
      jest.advanceTimersByTime(50);

      // Immediate second click should be ignored
      fireEvent.click(rankButton);

      // Advance timers to complete processing
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        // Should only call handleRanking once
        expect(mockHandleRanking).toHaveBeenCalledTimes(1);
      });
    });

    it('should disable button when processing', async () => {
      mockUseMatchRankDetailsStore.mockReturnValue({
        isProcessing: true,
        handleRanking: mockHandleRanking,
        setSelectedJob: jest.fn(),
        viewMode: 'edit',
        showUploader: false,
        selectedJob: null,
      });

      render(<RankNowButton {...mockProps} />);

      const rankButton = screen.getByRole('button');
      expect(rankButton).toBeDisabled();
    });

    it('should enable button when conditions are met', async () => {
      const enabledProps = {
        ...mockProps,
        candidateCount: 10,
        disabled: false,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...enabledProps} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();
    });

    it('should disable button when no candidates available', () => {
      const noCandidatesProps = {
        ...mockProps,
        candidateCount: 0,
      };

      render(<RankNowButton {...noCandidatesProps} />);

      const rankButton = screen.getByRole('button');
      expect(rankButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('should handle ranking errors gracefully', async () => {
      mockHandleRanking.mockRejectedValue(new Error('Ranking failed'));

      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      // Advance timers to allow the click handler to complete
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        expect(mockHandleRanking).toHaveBeenCalled();
      });

      // Advance timers to allow error handling to complete
      // The component has a 3-second timeout, so we need to advance past that
      jest.advanceTimersByTime(3100); // 3 seconds + buffer

      // Should not throw and should handle error gracefully
      // Button should become clickable again after error timeout
      await waitFor(() => {
        expect(rankButton).not.toBeDisabled();
      });
    });

    it('should handle credit-related errors appropriately', async () => {
      const creditError = new Error('Insufficient credits');
      (creditError as any).status = 402;
      mockHandleRanking.mockRejectedValue(creditError);

      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      // Advance timers to allow the click handler to complete
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        expect(mockHandleRanking).toHaveBeenCalled();
      });

      // Should handle credit errors without additional API calls
      expect(mockRefreshJobAndNotify).not.toHaveBeenCalled();
    });
  });

  describe('Event Data Structure', () => {
    it('should include correct event data structure', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      await waitFor(() => {
        expect(mockMarkJobAsUpdated).toHaveBeenCalledWith(mockProps.jobId);
      });
    });

    it('should update job state for match ranking', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      await waitFor(() => {
        expect(mockMarkJobAsUpdated).toHaveBeenCalledWith(mockProps.jobId);
      });
    });
  });

  describe('Integration with Job Management', () => {
    it('should work with match rank jobs store', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      // Advance timers to allow the click handler to complete
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        // Should integrate with the match rank jobs store through handleRanking
        expect(mockHandleRanking).toHaveBeenCalledWith(mockProps.jobId);
      });
    });

    it('should handle job ID correctly in all operations', async () => {
      const specificJobId = 'specific-job-456';
      const propsWithSpecificJob = {
        ...mockProps,
        jobId: specificJobId,
        matchRankCost: {
          success: true,
          creditCost: 2,
          unevaluatedCandidatesCount: 2,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithSpecificJob} />);

      const rankButton = screen.getByRole('button');

      // Button should be enabled immediately since we provided the cost as prop
      expect(rankButton).not.toBeDisabled();

      fireEvent.click(rankButton);

      // Advance timers to allow the click handler to complete
      jest.advanceTimersByTime(100);

      await waitFor(() => {
        expect(mockHandleRanking).toHaveBeenCalledWith(specificJobId);
        expect(mockMarkJobAsUpdated).toHaveBeenCalledWith(specificJobId);
      });
    });
  });

  describe('Backend Credit Calculation', () => {
    beforeEach(() => {
      // Mock API response for credit cost
      (apiHelper.get as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          matchRankCost: {
            creditCost: 2,
            unevaluatedCandidatesCount: 2,
            isValid: true,
            message: 'Valid operation',
            availableCredits: 50,
          },
        },
      });
    });

    it('should fetch credit cost from backend on mount', async () => {
      render(<RankNowButton {...mockProps} />);

      await waitFor(() => {
        expect(apiHelper.get).toHaveBeenCalledWith('/jobs/test-job-123/criteria');
      });
    });

    it('should handle API error gracefully', async () => {
      // Mock the fetchJobCriteria to handle the error gracefully
      const mockFetchJobCriteriaWithError = jest.fn().mockImplementation(async jobId => {
        try {
          return await apiHelper.get(`/jobs/${jobId}/criteria`);
        } catch (error) {
          // Handle error gracefully like the real implementation would
          return null;
        }
      });

      (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
        currentJob: null,
        stats: null,
        candidates: null,
        isLoading: false,
        hasUnsavedChanges: false,
        fetchJobCriteria: mockFetchJobCriteriaWithError,
        setSelectedJob: jest.fn(),
        updateFormData: jest.fn(),
        onWorkerComplete: jest.fn(),
        subscribe: jest.fn(() => jest.fn()), // Return unsubscribe function
        matchRankCost: null,
        hasPendingChanges: jest.fn(() => false),
        hasThresholdChanges: jest.fn(() => false),
      });

      (apiHelper.get as jest.Mock).mockRejectedValue(new Error('API Error'));

      render(<RankNowButton {...mockProps} />);

      await waitFor(() => {
        expect(mockFetchJobCriteriaWithError).toHaveBeenCalledWith('test-job-123', false);
      });

      // Should still render button even if API fails
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should show appropriate message when no credits are required', async () => {
      // Mock the store to return zero credit cost
      const mockGetMatchRankCost = jest.fn(() => ({
        creditCost: 0,
        unevaluatedCandidatesCount: 0,
        isValid: true,
        message: 'All candidates evaluated',
        availableCredits: 50,
      }));

      (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
        getMatchRankCost: mockGetMatchRankCost,
        fetchJobCriteria: jest.fn().mockResolvedValue(undefined),
        getCriteriaData: jest.fn(() => null),
        updateCriteriaData: jest.fn(),
        clearCriteriaData: jest.fn(),
        clearAllCriteriaData: jest.fn(),
        setError: jest.fn(),
        getCandidateStats: jest.fn(() => null),
        getCandidates: jest.fn(() => []),
        getTotalCandidates: jest.fn(() => 0),
        subscribe: jest.fn(() => jest.fn()), // Return unsubscribe function
        matchRankCost: null,
        hasPendingChanges: jest.fn(() => false),
        hasThresholdChanges: jest.fn(() => false),
      });

      (apiHelper.get as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          matchRankCost: {
            creditCost: 0,
            unevaluatedCandidatesCount: 0,
            isValid: true,
            message: 'All candidates evaluated',
            availableCredits: 50,
          },
        },
      });

      render(<RankNowButton {...mockProps} />);

      await waitFor(() => {
        const button = screen.getByRole('button');
        expect(button).toHaveAttribute(
          'title',
          'No changes that require re-ranking detected. Only threshold changes do not require credits.'
        );
      });
    });

    it('should refetch credit cost when job changes', async () => {
      // Create a mock fetchJobCriteria function to track calls
      const mockFetchJobCriteria = jest.fn().mockResolvedValue(undefined);

      // Mock the store to return null for any job ID (no cached data)
      (useJobStore as jest.MockedFunction<typeof useJobStore>).mockReturnValue({
        getMatchRankCost: jest.fn(() => null), // Always return null to trigger fetch
        fetchJobCriteria: mockFetchJobCriteria,
        getCriteriaData: jest.fn(() => null),
        updateCriteriaData: jest.fn(),
        clearCriteriaData: jest.fn(),
        clearAllCriteriaData: jest.fn(),
        setError: jest.fn(),
        getCandidateStats: jest.fn(() => null),
        getCandidates: jest.fn(() => []),
        getTotalCandidates: jest.fn(() => 0),
        subscribe: jest.fn(() => jest.fn()), // Return unsubscribe function
        matchRankCost: null,
        hasPendingChanges: jest.fn(() => false),
        hasThresholdChanges: jest.fn(() => false),
      });

      const { rerender } = render(<RankNowButton {...mockProps} />);

      // Wait for initial fetch
      await waitFor(() => {
        expect(mockFetchJobCriteria).toHaveBeenCalledWith('test-job-123', false);
      });

      // Clear previous calls to check for new ones
      mockFetchJobCriteria.mockClear();

      // Change job ID
      rerender(<RankNowButton {...mockProps} jobId="new-job-id" />);

      // Wait for fetch with new job ID
      await waitFor(() => {
        expect(mockFetchJobCriteria).toHaveBeenCalledWith('new-job-id', false);
      });
    });

    it('should display correct credit cost from backend', async () => {
      // Provide matchRankCost as prop to bypass store logic
      const propsWithCost = {
        ...mockProps,
        matchRankCost: {
          success: true,
          creditCost: 5,
          unevaluatedCandidatesCount: 5,
          isValid: true,
          message: 'Valid operation',
          availableCredits: 50,
        },
      };

      render(<RankNowButton {...propsWithCost} />);

      // Wait for component to render with credit cost
      await waitFor(() => {
        // The credit cost should be displayed, but it's shown in the CreditCoin component
        // Check if the button is rendered and enabled with the correct credit cost
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        expect(button).toHaveAttribute('title', expect.stringContaining('5 credits'));
      });
    });
  });
});
