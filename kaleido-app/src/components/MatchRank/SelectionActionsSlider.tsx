import { ICandidate } from '@/entities/interfaces';
import { AnimatePresence, motion } from 'framer-motion';
import {
  CheckCircle,
  Download,
  GitCompare,
  Mail,
  Settings,
  Share2,
  Trash2,
  <PERSON>r<PERSON>heck,
  Users,
  Video,
  X,
} from 'lucide-react';
import React from 'react';

interface SelectionActionsSliderProps {
  isVisible: boolean;
  selectedCandidates: ICandidate[];
  onClose: () => void;
  onCompare: () => void;
  onSendVideoIntro: () => void;
  onDelete?: () => void;
  onExport?: () => void;
  onShare?: () => void;
  className?: string;
}

export const SelectionActionsSlider: React.FC<SelectionActionsSliderProps> = ({
  isVisible,
  selectedCandidates,
  onClose,
  onCompare,
  onSendVideoIntro,
  onDelete,
  onExport,
  onShare,
  className = '',
}) => {
  const selectedCount = selectedCandidates.length;

  // Check if any selected candidates are from upload or scout sources
  const hasUploadOrScoutCandidates = selectedCandidates.some(
    candidate =>
      candidate.source &&
      (candidate.source === 'RESUME_UPLOAD' ||
        candidate.source === 'LINKEDIN_SCOUT' ||
        candidate.source === 'scout' ||
        candidate.source === 'upload')
  );

  const leftActions = [
    {
      id: 'compare',
      label: 'Compare Candidates',
      icon: GitCompare,
      onClick: onCompare,
      disabled: selectedCount < 2,
      tooltip: selectedCount < 2 ? 'Select at least 2 candidates to compare' : '',
      color: 'text-purple-400',
    },
    {
      id: 'video-intro',
      label: hasUploadOrScoutCandidates ? 'Send Interest Emails' : 'Send Video Intro Emails',
      icon: hasUploadOrScoutCandidates ? Mail : Video,
      onClick: onSendVideoIntro,
      disabled: selectedCount === 0,
      color: 'text-blue-400',
    },
  ];

  const rightActions = [
    {
      id: 'export',
      label: 'Export',
      icon: Download,
      onClick: onExport,
      disabled: selectedCount === 0,
      color: 'text-green-400',
    },
    {
      id: 'share',
      label: 'Share',
      icon: Share2,
      onClick: onShare,
      disabled: selectedCount === 0,
      color: 'text-indigo-400',
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: Trash2,
      onClick: onDelete,
      disabled: selectedCount === 0,
      color: 'text-red-400',
      danger: true,
    },
  ];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 120, opacity: 0, scale: 0.95 }}
          animate={{ y: 0, opacity: 1, scale: 1 }}
          exit={{ y: 120, opacity: 0, scale: 0.95 }}
          transition={{
            type: 'spring',
            damping: 20,
            stiffness: 300,
            opacity: { duration: 0.3 },
          }}
          className={`fixed bottom-0 left-0 right-0 z-50 ${className}`}
        >
          {/* Full width container with enhanced shadows */}
          <div className="relative">
            {/* Refined gradient shadows with slider color flow */}
            <div className="absolute inset-0 -top-16 bg-gradient-to-t from-slate-900/80 via-purple-950/60 to-transparent blur-2xl transform scale-110" />
            <div className="absolute inset-0 -top-12 bg-gradient-to-t from-purple-950/70 via-purple-800/40 to-transparent blur-xl" />
            <div className="absolute inset-0 -top-8 bg-gradient-to-t from-purple-900/60 via-purple-600/30 to-transparent blur-lg" />
            <div className="absolute inset-0 -top-4 bg-gradient-to-t from-purple-950/50 via-purple-500/20 to-transparent blur-md" />

            {/* Main glassmorphic container */}
            <div className="relative bg-gradient-to-r from-slate-900/95 via-purple-950/95 to-slate-900/95 backdrop-blur-xl border-t border-purple-400/30 shadow-2xl shadow-purple-500/40">
              {/* Top accent line - Enhanced */}
              <div className="absolute top-0 left-0 right-0 h-[2px] bg-gradient-to-r from-transparent via-purple-400/60 to-transparent" />
              <div className="absolute -top-1 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-pink-400/40 to-transparent" />

              {/* Content */}
              <div className="w-full px-8 py-8">
                <div className="flex items-center justify-between w-full">
                  {/* Left side - Selection info and actions */}
                  <motion.div
                    initial={{ x: -30, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1, duration: 0.4 }}
                    className="flex items-center space-x-4"
                  >
                    {/* Selection indicator */}
                    <div className="flex items-center space-x-3">
                      <div className="relative p-2.5 bg-purple-500/20 rounded-xl backdrop-blur-sm border border-purple-400/30">
                        <Users className="w-5 h-5 text-purple-400" />
                      </div>
                      <div>
                        <p className="text-white font-semibold text-sm">{selectedCount} Selected</p>
                      </div>
                    </div>

                    {/* Divider */}
                    <div className="h-12 w-px bg-white/10" />

                    {/* Left actions with section label */}
                    <div className="flex flex-col items-start space-y-3">
                      <div className="flex items-center gap-2 text-sm text-white/60">
                        <UserCheck className="w-4 h-4" />
                        <span className="font-medium">Candidate Actions</span>
                      </div>
                      <div className="flex items-center space-x-4">
                        {leftActions.map(action => (
                          <motion.button
                            key={action.id}
                            whileHover={{ scale: action.disabled ? 1 : 1.05 }}
                            whileTap={{ scale: action.disabled ? 1 : 0.95 }}
                            onClick={action.onClick}
                            disabled={action.disabled}
                            className={`
                              px-5 py-3 bg-white/10 border border-white/20
                              rounded-xl transition-all duration-200
                              flex items-center gap-2.5 text-base font-semibold backdrop-blur-sm
                              shadow-lg shadow-purple-500/10
                              ${
                                action.disabled
                                  ? 'opacity-50 cursor-not-allowed'
                                  : 'hover:bg-white/15 hover:border-white/30 hover:shadow-xl hover:shadow-purple-500/20'
                              }
                            `}
                            title={action.tooltip}
                          >
                            <action.icon className={`w-5 h-5 ${action.color}`} />
                            <span className="text-white/90 text-sm">{action.label}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </motion.div>

                  {/* Right side - Other actions and close */}
                  <motion.div
                    initial={{ x: 30, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.4 }}
                    className="flex items-center space-x-3"
                  >
                    {/* Right actions with section label */}
                    <div className="flex flex-col items-end space-y-3">
                      <div className="flex items-center gap-2 text-sm text-white/60">
                        <Settings className="w-4 h-4" />
                        <span className="font-medium">Actions</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        {rightActions.map(action => (
                          <motion.button
                            key={action.id}
                            whileHover={{ scale: action.disabled ? 1 : 1.05 }}
                            whileTap={{ scale: action.disabled ? 1 : 0.95 }}
                            onClick={action.onClick}
                            disabled={action.disabled}
                            className={`
                          ${
                            action.danger
                              ? 'px-4 py-2.5 border rounded-lg bg-red-500/10 border-red-500/30'
                              : 'p-2 rounded-lg hover:bg-white/10'
                          }
                          transition-all duration-200
                          flex items-center gap-2 text-sm font-medium backdrop-blur-sm
                          ${
                            action.disabled
                              ? 'opacity-50 cursor-not-allowed'
                              : action.danger
                                ? 'hover:bg-red-500/20 hover:border-red-500/40'
                                : 'hover:bg-white/15'
                          }
                        `}
                          >
                            <action.icon
                              className={`${action.danger ? 'w-4 h-4' : 'w-4 h-4'} ${action.color}`}
                            />
                            {action.danger && (
                              <span className="text-sm text-red-400">{action.label}</span>
                            )}
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    {/* Divider */}
                    <div className="h-12 w-px bg-white/10" />

                    {/* Close button */}
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={onClose}
                      className="p-2.5 bg-white/10 hover:bg-white/15 border border-white/20 hover:border-white/30 rounded-lg transition-all duration-200"
                    >
                      <X className="w-5 h-5 text-white/90" />
                    </motion.button>
                  </motion.div>
                </div>

                {/* Selected candidates preview */}
                {selectedCount > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="mt-6"
                  >
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-xs text-white/60 font-medium uppercase tracking-wider">
                        Selected Candidates
                      </span>
                    </div>
                    <div className="flex items-center gap-3 flex-wrap">
                      {selectedCandidates.slice(0, 3).map(candidate => {
                        // Convert to title case
                        const titleCase = candidate.fullName
                          .split(' ')
                          .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                          .join(' ');

                        return (
                          <div key={candidate.id} className="flex items-center gap-1.5">
                            <CheckCircle className="w-3.5 h-3.5 text-green-400" />
                            <span className="text-xs text-white/80">{titleCase}</span>
                          </div>
                        );
                      })}
                      {selectedCount > 3 && (
                        <span className="text-sm text-white/60">+{selectedCount - 3} more</span>
                      )}
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SelectionActionsSlider;
