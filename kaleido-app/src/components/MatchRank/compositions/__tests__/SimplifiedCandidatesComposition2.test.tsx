import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { SimplifiedCandidatesComposition2 } from '../SimplifiedCandidatesComposition2';
import { ICandidate } from '@/entities/interfaces';
import { CandidateStatus } from '@/types/candidate.types';
import { mockCandidateData } from '@/test-utils';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(),
}));

// Mock dependencies
jest.mock('@/stores/unifiedJobStore', () => ({
  useJobStore: jest.fn(() => ({
    currentJob: {
      topCandidateThreshold: 80,
      secondTierCandidateThreshold: 60,
      jobTitle: 'Software Engineer',
    },
    fetchCandidates: jest.fn(),
    stats: {
      totalCandidates: 50,
      shortlistedCount: 5,
    },
  })),
}));

jest.mock('@/stores/matchrankDetailsStore', () => ({
  useMatchRankDetailsStore: jest.fn(() => ({
    selectedCandidate: null,
    setSelectedCandidate: jest.fn(),
  })),
}));

jest.mock('@/components/common/BurgerMenu', () => ({
  NavigationBurgerMenu: ({ isOpen, onToggle, title, className }: any) => {
    return (
      <div className={className} data-testid="navigation-burger-menu">
        <button onClick={onToggle} data-testid="burger-toggle">
          {isOpen ? 'Close' : 'Open'} {title}
        </button>
      </div>
    );
  },
}));

jest.mock('../../CandidateListItem', () => ({
  CandidateListItem: ({ candidate, isSelected, onSelect, isShortlisted }: any) => {
    return (
      <div
        data-testid={`candidate-item-${candidate.id}`}
        className={isSelected ? 'selected' : ''}
        onClick={() => onSelect(candidate)}
      >
        {candidate.fullName} {isShortlisted && '(Shortlisted)'}
      </div>
    );
  },
}));

jest.mock('../../UnifiedCandidateView', () => ({
  UnifiedCandidateView: ({
    candidate,
    jobId,
    jobTitle,
    onCandidateSelect,
    onStatusUpdate,
  }: any) => {
    return (
      <div data-testid="unified-candidate-view">
        Viewing: {candidate.fullName} - Job: {jobTitle} ({jobId})
        <button onClick={() => onCandidateSelect(candidate)}>Select Candidate</button>
        <button onClick={onStatusUpdate}>Update Status</button>
      </div>
    );
  },
}));

jest.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: any) => children,
  Tooltip: ({ children }: any) => children,
  TooltipTrigger: ({ children, asChild }: any) => (asChild ? children : <div>{children}</div>),
  TooltipContent: ({ children, side }: any) => (
    <div data-testid="tooltip-content" data-side={side}>
      {children}
    </div>
  ),
}));

jest.mock('next/image', () => {
  return function MockImage({ src, alt, width, height, className }: any) {
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        className={className}
        data-testid="kaleido-logo"
      />
    );
  };
});

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  ArrowLeft: () => <div data-testid="arrow-left-icon" />,
  ChevronRight: () => <div data-testid="chevron-right-icon" />,
  ChevronLeft: () => <div data-testid="chevron-left-icon" />,
  Medal: () => <div data-testid="medal-icon" />,
  Star: () => <div data-testid="star-icon" />,
  Target: () => <div data-testid="target-icon" />,
  Trophy: () => <div data-testid="trophy-icon" />,
  Users: () => <div data-testid="users-icon" />,
  X: () => <div data-testid="x-icon" />,
  GitCompare: () => <div data-testid="git-compare-icon" />,
  AlertCircle: () => <div data-testid="alert-circle-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
}));

jest.mock('framer-motion', () => ({
  AnimatePresence: ({ children }: any) => children,
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

const createMockCandidate = (
  id: string,
  name: string,
  status: CandidateStatus = CandidateStatus.NEW
): ICandidate =>
  ({
    ...mockCandidateData,
    id,
    fullName: name,
    status,
    evaluation: {
      matchScore: 75 + Math.random() * 20,
      rank: parseInt(id),
    },
  }) as any;

const mockCandidates = [
  createMockCandidate('1', 'John Doe'),
  createMockCandidate('2', 'Jane Smith', CandidateStatus.SHORTLISTED),
  createMockCandidate('3', 'Bob Wilson'),
  createMockCandidate('4', 'Alice Brown'),
];

const mockGroupedCandidates = {
  shortlisted: [createMockCandidate('2', 'Jane Smith', CandidateStatus.SHORTLISTED)],
  topTier: [createMockCandidate('1', 'John Doe'), createMockCandidate('3', 'Bob Wilson')],
  secondTier: [createMockCandidate('4', 'Alice Brown')],
  others: [],
  unranked: [],
};

const mockLargeCandidateSet = {
  shortlisted: Array.from({ length: 3 }, (_, i) =>
    createMockCandidate(`s-${i}`, `Shortlisted ${i}`, CandidateStatus.SHORTLISTED)
  ),
  topTier: Array.from({ length: 8 }, (_, i) => createMockCandidate(`t-${i}`, `Top Tier ${i}`)),
  secondTier: Array.from({ length: 12 }, (_, i) =>
    createMockCandidate(`st-${i}`, `Second Tier ${i}`)
  ),
  others: Array.from({ length: 5 }, (_, i) => createMockCandidate(`o-${i}`, `Other ${i}`)),
  unranked: Array.from({ length: 3 }, (_, i) => createMockCandidate(`u-${i}`, `Unranked ${i}`)),
};

const mockPushFn = jest.fn();
const mockSetSelectedCandidate = jest.fn();
const mockFetchCandidates = jest.fn();

describe('SimplifiedCandidatesComposition2', () => {
  const defaultProps = {
    candidates: mockGroupedCandidates,
    currentPage: 0,
    onPageChange: jest.fn(),
    isAtsJob: false,
    jobId: 'test-job-id',
    fetchCandidateById: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPushFn,
    });
    (require('next/navigation').usePathname as jest.Mock).mockReturnValue('/');

    const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');
    useMatchRankDetailsStore.mockReturnValue({
      selectedCandidate: mockCandidates[0],
      setSelectedCandidate: mockSetSelectedCandidate,
    });

    const { useJobStore } = require('@/stores/unifiedJobStore');
    useJobStore.mockReturnValue({
      currentJob: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
        jobTitle: 'Software Engineer',
      },
      fetchCandidates: mockFetchCandidates,
      stats: {
        totalCandidates: 50,
        shortlistedCount: 5,
      },
    });
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);
      expect(screen.getByTestId('unified-candidate-view')).toBeInTheDocument();
    });

    it('renders mobile navigation bar on small screens', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      expect(screen.getByTestId('navigation-burger-menu')).toBeInTheDocument();
      expect(screen.getByText('4 candidates')).toBeInTheDocument();
    });

    it('renders back to jobs button', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      expect(screen.getByText('Back to Jobs')).toBeInTheDocument();
      expect(screen.getByTestId('arrow-left-icon')).toBeInTheDocument();
    });

    it('renders Kaleido logo', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      expect(screen.getByTestId('kaleido-logo')).toBeInTheDocument();
    });
  });

  describe('Candidate Grouping', () => {
    it('renders all candidate sections', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      expect(screen.getByText('Shortlisted')).toBeInTheDocument();
      expect(screen.getByText('Top Candidates')).toBeInTheDocument();
      expect(screen.getByText('Second Tier')).toBeInTheDocument();
    });

    it('shows correct candidate counts for each section', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Count badges should be visible
      expect(screen.getAllByText('1')).toHaveLength(2); // Shortlisted count (1) and Second tier count (1)
      expect(screen.getByText('2')).toBeInTheDocument(); // Top tier count
    });

    it('filters out shortlisted candidates from other sections', () => {
      const candidatesWithDuplicateShortlisted = {
        ...mockGroupedCandidates,
        topTier: [
          ...mockGroupedCandidates.topTier,
          createMockCandidate('5', 'Duplicate Shortlisted', CandidateStatus.SHORTLISTED),
        ],
      };

      render(
        <SimplifiedCandidatesComposition2
          {...defaultProps}
          candidates={candidatesWithDuplicateShortlisted}
        />
      );

      // The duplicate shortlisted candidate should not appear in top tier section
      const topTierSection = screen.getByText('Top Candidates').closest('[class*="mb-2"]');
      expect(topTierSection).toBeInTheDocument();
    });

    it('handles empty candidate groups gracefully', () => {
      const emptyCandidates = {
        shortlisted: [],
        topTier: [],
        secondTier: [],
        others: [],
        unranked: [],
      };

      const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');
      useMatchRankDetailsStore.mockReturnValue({
        selectedCandidate: null,
        setSelectedCandidate: mockSetSelectedCandidate,
      });

      render(<SimplifiedCandidatesComposition2 {...defaultProps} candidates={emptyCandidates} />);

      expect(screen.getByText('No candidates available')).toBeInTheDocument();
    });
  });

  describe('Candidate Section Expansion', () => {
    it('expands shortlisted section by default', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      expect(screen.getByTestId('candidate-item-2')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith (Shortlisted)')).toBeInTheDocument();
    });

    it('expands section when header is clicked', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const topTierHeader = screen.getByText('Top Candidates');
      fireEvent.click(topTierHeader);

      await waitFor(() => {
        expect(screen.getByTestId('candidate-item-1')).toBeInTheDocument();
        expect(screen.getByTestId('candidate-item-3')).toBeInTheDocument();
      });
    });

    it('collapses section when header is clicked again', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const shortlistedHeader = screen.getByText('Shortlisted');
      fireEvent.click(shortlistedHeader); // Collapse

      await waitFor(() => {
        expect(screen.queryByTestId('candidate-item-2')).not.toBeInTheDocument();
      });
    });

    it('shows correct chevron rotation when section is expanded', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const topTierHeader = screen.getByText('Top Candidates');
      fireEvent.click(topTierHeader);

      await waitFor(() => {
        // Check that the section is expanded by looking for candidate items
        expect(screen.getByTestId('candidate-item-1')).toBeInTheDocument();
        expect(screen.getByTestId('candidate-item-3')).toBeInTheDocument();
      });
    });
  });

  describe('Candidate Selection', () => {
    it('calls setSelectedCandidate when candidate is clicked', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const candidateItem = screen.getByTestId('candidate-item-2');
      fireEvent.click(candidateItem);

      expect(mockSetSelectedCandidate).toHaveBeenCalled();
    });

    it('fetches full candidate details when fetchCandidateById is provided', async () => {
      const mockFetchCandidateById = jest.fn().mockResolvedValue(mockCandidates[0]);

      render(
        <SimplifiedCandidatesComposition2
          {...defaultProps}
          fetchCandidateById={mockFetchCandidateById}
        />
      );

      const candidateItem = screen.getByTestId('candidate-item-2');
      fireEvent.click(candidateItem);

      await waitFor(() => {
        expect(mockFetchCandidateById).toHaveBeenCalledWith('test-job-id', '2');
      });
    });

    it('highlights selected candidate', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // First expand the shortlisted section to see the selected candidate
      const shortlistedCandidate = screen.getByTestId('candidate-item-2');
      expect(shortlistedCandidate).toBeInTheDocument();
    });
  });

  describe('Sidebar Functionality', () => {
    it('collapses sidebar when collapse button is clicked', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const collapseButton = screen.getByTestId('chevron-left-icon').closest('button');
      fireEvent.click(collapseButton!);

      await waitFor(() => {
        expect(screen.getByTestId('chevron-right-icon')).toBeInTheDocument();
      });
    });

    it('expands sidebar when expand button is clicked', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // First collapse
      const collapseButton = screen.getByTestId('chevron-left-icon').closest('button');
      fireEvent.click(collapseButton!);

      await waitFor(() => {
        const expandButton = screen.getByTestId('chevron-right-icon').closest('button');
        fireEvent.click(expandButton!);
      });

      await waitFor(() => {
        expect(screen.getByText('Back to Jobs')).toBeInTheDocument();
      });
    });

    it('shows tooltips in collapsed mode', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Collapse sidebar first
      const collapseButton = screen.getByTestId('chevron-left-icon').closest('button');
      fireEvent.click(collapseButton!);

      await waitFor(() => {
        // In collapsed mode, there should be tooltips for navigation and candidate sections
        const tooltips = screen.getAllByTestId('tooltip-content');
        expect(tooltips.length).toBeGreaterThanOrEqual(2);
      });
    });

    it('adjusts logo size in collapsed mode', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const logo = screen.getByTestId('kaleido-logo');
      expect(logo).toHaveAttribute('width', '56');

      // Collapse sidebar
      const collapseButton = screen.getByTestId('chevron-left-icon').closest('button');
      fireEvent.click(collapseButton!);

      await waitFor(() => {
        expect(logo).toHaveAttribute('width', '40');
      });
    });
  });

  describe('Mobile Responsiveness', () => {
    it('opens mobile sidebar when burger menu is clicked', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const burgerToggle = screen.getByTestId('burger-toggle');
      fireEvent.click(burgerToggle);

      // Mobile sidebar should be accessible
      await waitFor(() => {
        expect(screen.getByText('Candidate Rankings')).toBeInTheDocument();
      });
    });

    it('closes mobile sidebar when candidate is selected', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Open mobile sidebar first
      const burgerToggle = screen.getByTestId('burger-toggle');
      fireEvent.click(burgerToggle);

      // Select a candidate
      const candidateItem = screen.getByTestId('candidate-item-2');
      fireEvent.click(candidateItem);

      // This would close the mobile sidebar in the actual implementation
      // The test verifies the interaction occurs
      expect(mockSetSelectedCandidate).toHaveBeenCalled();
    });

    it('closes mobile sidebar when overlay is clicked', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Open mobile sidebar
      const burgerToggle = screen.getByTestId('burger-toggle');
      fireEvent.click(burgerToggle);

      // Click overlay (this would be implemented in the actual component)
      const overlay = document.querySelector('[class*="bg-black/50"]');
      if (overlay) {
        fireEvent.click(overlay);
      }

      // Verify interaction occurred
      expect(burgerToggle).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('navigates to jobs page when back button is clicked', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const backButton = screen.getByText('Back to Jobs').closest('button');
      fireEvent.click(backButton!);

      expect(mockPushFn).toHaveBeenCalledWith('/jobs');
    });

    it('navigates to jobs page when back icon is clicked in collapsed mode', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Collapse sidebar first
      const collapseButton = screen.getByTestId('chevron-left-icon').closest('button');
      fireEvent.click(collapseButton!);

      await waitFor(() => {
        const backIcon = screen.getByTestId('arrow-left-icon').closest('button');
        fireEvent.click(backIcon!);
      });

      expect(mockPushFn).toHaveBeenCalledWith('/jobs');
    });
  });

  describe('Status Updates', () => {
    it('calls fetchCandidates when status is updated', async () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const updateStatusButton = screen.getByText('Update Status');
      fireEvent.click(updateStatusButton);

      expect(mockFetchCandidates).toHaveBeenCalledWith('test-job-id');
    });

    it('handles status update errors gracefully', async () => {
      mockFetchCandidates.mockRejectedValueOnce(new Error('Network error'));

      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      const updateStatusButton = screen.getByText('Update Status');
      fireEvent.click(updateStatusButton);

      // Should not crash on error
      expect(screen.getByTestId('unified-candidate-view')).toBeInTheDocument();
    });
  });

  describe('Threshold Handling', () => {
    it('uses job thresholds from store', () => {
      const { useJobStore } = require('@/stores/unifiedJobStore');
      useJobStore.mockReturnValue({
        currentJob: {
          topCandidateThreshold: '90',
          secondTierCandidateThreshold: '70',
          jobTitle: 'Senior Engineer',
        },
        fetchCandidates: mockFetchCandidates,
        stats: { totalCandidates: 50, shortlistedCount: 5 },
      });

      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Thresholds should be parsed correctly and passed to UnifiedCandidateView
      expect(screen.getByTestId('unified-candidate-view')).toBeInTheDocument();
    });

    it('falls back to default thresholds when job thresholds are missing', () => {
      const { useJobStore } = require('@/stores/unifiedJobStore');
      useJobStore.mockReturnValue({
        currentJob: null,
        fetchCandidates: mockFetchCandidates,
        stats: { totalCandidates: 50, shortlistedCount: 5 },
      });

      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      expect(screen.getByTestId('unified-candidate-view')).toBeInTheDocument();
    });
  });

  describe('Auto-selection Logic', () => {
    it('auto-selects highest ranking candidate when no candidate is selected', () => {
      const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');
      useMatchRankDetailsStore.mockReturnValue({
        selectedCandidate: null,
        setSelectedCandidate: mockSetSelectedCandidate,
      });

      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      expect(mockSetSelectedCandidate).toHaveBeenCalled();
    });

    it('prioritizes shortlisted candidates for auto-selection', () => {
      const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');
      useMatchRankDetailsStore.mockReturnValue({
        selectedCandidate: null,
        setSelectedCandidate: mockSetSelectedCandidate,
      });

      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Should select the shortlisted candidate first
      expect(mockSetSelectedCandidate).toHaveBeenCalledWith(
        expect.objectContaining({ status: CandidateStatus.SHORTLISTED })
      );
    });

    it('updates selection when selected candidate no longer exists', () => {
      // This test verifies the auto-selection logic when the selected candidate is no longer in the list
      const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');

      useMatchRankDetailsStore.mockReturnValue({
        selectedCandidate: { ...mockCandidates[0], id: 'non-existent' },
        setSelectedCandidate: mockSetSelectedCandidate,
      });

      render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // The component should call setSelectedCandidate to update to a valid candidate
      expect(mockSetSelectedCandidate).toHaveBeenCalled();
    });
  });

  describe('Performance and Memoization', () => {
    it('does not re-render unnecessarily with same props', () => {
      const { rerender } = render(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Re-render with the same props
      rerender(<SimplifiedCandidatesComposition2 {...defaultProps} />);

      // Component should still be rendered
      expect(screen.getByTestId('unified-candidate-view')).toBeInTheDocument();
    });
  });

  describe('Large Dataset Handling', () => {
    it('handles large number of candidates efficiently', () => {
      render(
        <SimplifiedCandidatesComposition2 {...defaultProps} candidates={mockLargeCandidateSet} />
      );

      expect(screen.getByText('31 candidates')).toBeInTheDocument();
      expect(screen.getByText('Shortlisted')).toBeInTheDocument();
      expect(screen.getByText('Top Candidates')).toBeInTheDocument();
      expect(screen.getByText('Second Tier')).toBeInTheDocument();
      expect(screen.getByText('Other Candidates')).toBeInTheDocument();
      expect(screen.getByText('Unranked')).toBeInTheDocument();
    });
  });

  describe('Error Boundary and Edge Cases', () => {
    it('handles undefined candidates gracefully', () => {
      const { useMatchRankDetailsStore } = require('@/stores/matchrankDetailsStore');
      useMatchRankDetailsStore.mockReturnValue({
        selectedCandidate: null,
        setSelectedCandidate: mockSetSelectedCandidate,
      });

      expect(() => {
        render(
          <SimplifiedCandidatesComposition2 {...defaultProps} candidates={undefined as any} />
        );
      }).not.toThrow();
    });

    it('handles flat array candidates (non-grouped)', () => {
      render(<SimplifiedCandidatesComposition2 {...defaultProps} candidates={mockCandidates} />);

      // Should still render without crashing
      expect(screen.getByTestId('unified-candidate-view')).toBeInTheDocument();
    });
  });
});
