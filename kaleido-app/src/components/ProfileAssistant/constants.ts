import { AssistantMood, StepMessage } from './types';

export const assistantMoods: Record<string, AssistantMood> = {
  sleeping: { 
    icon: '😴', 
    message: 'Click to wake me up!', 
    color: 'from-gray-400 to-gray-500' 
  },
  happy: { 
    icon: '😊', 
    message: 'Going great!', 
    color: 'from-green-400 to-emerald-500' 
  },
  excited: { 
    icon: '🤩', 
    message: "You're on fire!", 
    color: 'from-yellow-400 to-orange-500' 
  },
  proud: { 
    icon: '🥳', 
    message: 'Almost there!', 
    color: 'from-purple-400 to-pink-500' 
  },
  celebrating: { 
    icon: '🎉', 
    message: 'Profile hero!', 
    color: 'from-blue-400 to-indigo-500' 
  },
  thinking: { 
    icon: '🤔', 
    message: 'Need help?', 
    color: 'from-amber-400 to-yellow-500' 
  },
  encouraging: { 
    icon: '💪', 
    message: 'You got this!', 
    color: 'from-red-400 to-pink-500' 
  }
};

export const assistantMessages: Record<string, StepMessage> = {
  import: {
    greeting: "🎉 Welcome aboard! Let's build your amazing profile!",
    mandatory: "No pressure here - this step is totally optional!",
    tip: "💡 Pro tip: Uploading a resume saves you 5 minutes of typing!",
    encouragement: "You're doing great! This is the easy part 😊"
  },
  essentials: {
    greeting: "👋 Let's start with the basics - just 3 quick things!",
    mandatory: "I just need your name and email to get started - that's it!",
    fields: {
      display: "📝 Quick checklist:\n✓ First name\n✓ Last name\n✓ Email address",
      fun: "Think of it as your digital handshake! 🤝"
    },
    completion: "Awesome! You're already 30% done! 🚀"
  },
  professional: {
    greeting: "💼 Time to showcase your superpowers!",
    mandatory: "Just add 1 skill and you're good to go! (But feel free to add more - you're talented!)",
    fields: {
      display: "🎯 Required: At least one skill",
      fun: "What makes you awesome? Share at least one superpower!"
    },
    encouragement: "Employers love seeing your skills! Each one increases matches by 15%! 📈"
  },
  preferences: {
    greeting: "🎯 Let's find your dream job together!",
    mandatory: "This is where the magic happens - tell me what you're looking for!",
    fields: {
      display: "📋 Your wish list:\n• Job types you want\n• Where you'd like to work\n• Remote or office?\n• Your worth (salary range)",
      fun: "Think of this as your job shopping list! 🛒"
    },
    tip: "Being specific here means better matches - no more irrelevant jobs! 🎯"
  },
  additional: {
    greeting: "✨ Want to stand out? Add some sparkle! (Totally optional though!)",
    mandatory: "Nothing required here - add what feels right for you!",
    encouragement: "Profiles with these extras get 40% more views! But no pressure 😊"
  },
  verification: {
    greeting: "🌟 Level up with verification! (Optional but powerful!)",
    mandatory: "Skip this if you want - but verified profiles get 3x more responses!",
    benefits: "🏆 Get a verified badge\n👀 3x more profile views\n💼 Priority in search results"
  },
  privacy: {
    greeting: "🎊 Final stop! You're almost there!",
    mandatory: "Just review and hit launch - you've got this!",
    celebration: "🎉 Amazing job! Your profile is ready to shine!"
  }
};

export const mandatoryFieldsByStep: Record<string, string[]> = {
  essentials: ['firstName', 'lastName', 'email'],
  professional: ['skills'],
  preferences: ['jobTypes', 'locations', 'remotePreference', 'desiredSalary'],
};

export const fieldFriendlyNames: Record<string, string> = {
  firstName: 'First Name',
  lastName: 'Last Name',
  email: 'Email Address',
  skills: 'Skills (at least 1)',
  jobTypes: 'Job Types',
  locations: 'Preferred Locations',
  remotePreference: 'Remote Work Preference',
  desiredSalary: 'Salary Expectations'
};

export const floatingAnimation = {
  initial: { y: 100, opacity: 0, scale: 0.8 },
  animate: { 
    y: 0, 
    opacity: 1, 
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 260,
      damping: 20
    }
  },
  hover: { scale: 1.05 },
  tap: { scale: 0.95 }
};