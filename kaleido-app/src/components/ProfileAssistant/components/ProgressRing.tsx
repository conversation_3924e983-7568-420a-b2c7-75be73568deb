'use client';

import React from 'react';
import { ProgressRingProps } from '../types';

export const ProgressRing: React.FC<ProgressRingProps> = ({ 
  value, 
  label, 
  color, 
  size = 'small', 
  testId 
}) => {
  const colors = {
    red: 'text-red-400',
    blue: 'text-blue-400',
    green: 'text-green-400',
    gray: 'text-gray-400',
    indigo: 'text-indigo-400'
  };

  const sizes = {
    small: { container: 'w-12 h-12', text: 'text-xs' },
    medium: { container: 'w-16 h-16', text: 'text-sm' },
    large: { container: 'w-20 h-20', text: 'text-base' }
  };

  const radius = size === 'small' ? 16 : size === 'medium' ? 20 : 24;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (value / 100) * circumference;

  return (
    <div className="flex flex-col items-center" data-testid={testId} data-progress={value}>
      <div className={`relative ${sizes[size].container}`}>
        <svg className="transform -rotate-90 w-full h-full">
          {/* Background circle */}
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            stroke="currentColor"
            strokeWidth="3"
            fill="none"
            className="text-gray-200"
          />
          {/* Progress circle */}
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            stroke="currentColor"
            strokeWidth="3"
            fill="none"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            className={`${colors[color]} transition-all duration-500 ease-in-out`}
            strokeLinecap="round"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={`${sizes[size].text} font-bold text-gray-700`}>
            {value}%
          </span>
        </div>
      </div>
      <span className="text-xs text-gray-600 mt-0.5 font-medium">{label}</span>
    </div>
  );
};