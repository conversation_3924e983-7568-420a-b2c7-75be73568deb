'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { HelpCir<PERSON>, Ski<PERSON>Forward, Zap, ChevronRight } from 'lucide-react';

interface QuickActionsProps {
  onGetHelp?: () => void;
  onSkipOptional?: () => void;
  canSkip: boolean;
  hasOptionalContent: boolean;
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  onGetHelp,
  onSkipOptional,
  canSkip,
  hasOptionalContent
}) => {
  if (!onGetHelp && !onSkipOptional) return null;

  return (
    <motion.div 
      className="mt-3 flex flex-wrap gap-2"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      {/* Help button */}
      {onGetHelp && (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="text-xs text-gray-600 hover:text-gray-900 hover:bg-gray-50 px-2 py-1 rounded-md transition-colors flex items-center gap-1 group"
          onClick={onGetHelp}
          data-testid="assistant-why-needed"
        >
          <HelpCircle className="w-3 h-3 group-hover:rotate-12 transition-transform" />
          <span>Why needed?</span>
        </motion.button>
      )}
      
      {/* Skip optional button */}
      {onSkipOptional && canSkip && (
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="text-xs text-gray-600 hover:text-gray-900 hover:bg-gray-50 px-2 py-1 rounded-md transition-colors flex items-center gap-1 group"
          onClick={onSkipOptional}
          data-testid="assistant-skip-optional"
        >
          <SkipForward className="w-3 h-3 group-hover:translate-x-0.5 transition-transform" />
          <span>Skip optional</span>
        </motion.button>
      )}
      
      {/* Quick fill suggestion (if optional content available) */}
      {hasOptionalContent && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-xs text-amber-600 flex items-center gap-1 ml-auto"
        >
          <Zap className="w-3 h-3" />
          <span>Optional fields available</span>
          <ChevronRight className="w-3 h-3 animate-pulse" />
        </motion.div>
      )}
    </motion.div>
  );
};