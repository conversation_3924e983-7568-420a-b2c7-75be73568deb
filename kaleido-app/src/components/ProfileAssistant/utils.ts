import { ProgressData } from './types';

export const calculateProgress = (
  currentStep: string,
  formData: any,
  mandatoryFieldsByStep: Record<string, string[]>
): ProgressData => {
  const mandatoryFields = mandatoryFieldsByStep[currentStep] || [];
  const completed: string[] = [];
  const missing: string[] = [];

  mandatoryFields.forEach(field => {
    let isComplete = false;
    
    switch (field) {
      case 'firstName':
        isComplete = !!(formData.firstName?.trim());
        break;
      case 'lastName':
        isComplete = !!(formData.lastName?.trim());
        break;
      case 'email':
        isComplete = !!(formData.email?.trim() && 
                       /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email));
        break;
      case 'skills':
        isComplete = formData.skills && formData.skills.length > 0;
        break;
      case 'jobTypes':
        isComplete = formData.preferences?.jobTypes && 
                    formData.preferences.jobTypes.length > 0;
        break;
      case 'locations':
        isComplete = formData.preferences?.locations && 
                    formData.preferences.locations.length > 0;
        break;
      case 'remotePreference':
        isComplete = !!formData.preferences?.remotePreference;
        break;
      case 'desiredSalary':
        isComplete = !!(
          formData.preferences?.desiredSalary?.min &&
          formData.preferences?.desiredSalary?.max &&
          formData.preferences?.desiredSalary?.currency &&
          formData.preferences?.desiredSalary?.period
        );
        break;
      default:
        isComplete = !!(formData as any)[field];
    }

    if (isComplete) {
      completed.push(field);
    } else {
      missing.push(field);
    }
  });

  // Calculate mandatory progress
  const mandatoryPct = mandatoryFields.length > 0 
    ? Math.round((completed.length / mandatoryFields.length) * 100)
    : 100;

  // Calculate optional fields completion (simplified)
  let optionalPct = 0;
  
  // Check various optional fields based on step
  if (currentStep === 'essentials') {
    // Optional: phone, location
    if (formData.phone) optionalPct += 50;
    if (formData.location) optionalPct += 50;
  } else if (currentStep === 'professional') {
    // Optional: summary, experience, education
    if (formData.summary) optionalPct += 33;
    if (formData.experience?.length > 0) optionalPct += 33;
    if (formData.education?.length > 0) optionalPct += 34;
  } else if (currentStep === 'preferences') {
    // Optional: industries
    if (formData.preferences?.industries?.length > 0) optionalPct = 100;
  } else if (currentStep === 'additional') {
    // All fields are optional
    let additionalComplete = 0;
    if (formData.myValues?.length > 0) additionalComplete += 33;
    if (formData.availability) additionalComplete += 33;
    if (formData.portfolio?.length > 0) additionalComplete += 34;
    optionalPct = additionalComplete;
  } else if (currentStep === 'verification') {
    // Optional: video, ID
    let verificationComplete = 0;
    if (formData.videoIntroduction) verificationComplete += 50;
    if (formData.idVerification) verificationComplete += 50;
    optionalPct = verificationComplete;
  }

  // Overall progress (weighted average)
  const overallPct = Math.round((mandatoryPct * 0.7) + (optionalPct * 0.3));

  return {
    mandatoryProgress: mandatoryPct,
    optionalProgress: Math.round(optionalPct),
    overallProgress: overallPct,
    completedFields: completed,
    missingFields: missing
  };
};

export const getFieldValidationStatus = (
  field: string,
  value: any
): { isValid: boolean; errorMessage?: string } => {
  switch (field) {
    case 'firstName':
    case 'lastName':
      return {
        isValid: !!(value?.trim()),
        errorMessage: !value?.trim() ? 'This field is required' : undefined
      };
    
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const isValidEmail = !!(value?.trim() && emailRegex.test(value));
      return {
        isValid: isValidEmail,
        errorMessage: !value?.trim() 
          ? 'Email is required' 
          : !isValidEmail 
            ? 'Please enter a valid email address' 
            : undefined
      };
    
    case 'skills':
      return {
        isValid: value?.length > 0,
        errorMessage: !value?.length ? 'Please add at least one skill' : undefined
      };
    
    case 'jobTypes':
      return {
        isValid: value?.length > 0,
        errorMessage: !value?.length ? 'Please select at least one job type' : undefined
      };
    
    case 'locations':
      return {
        isValid: value?.length > 0,
        errorMessage: !value?.length ? 'Please add at least one location' : undefined
      };
    
    case 'remotePreference':
      return {
        isValid: !!value,
        errorMessage: !value ? 'Please select your remote work preference' : undefined
      };
    
    case 'desiredSalary':
      const hasAllFields = !!(
        value?.min && 
        value?.max && 
        value?.currency && 
        value?.period
      );
      const isValidRange = value?.min && value?.max ? value.min <= value.max : true;
      
      return {
        isValid: hasAllFields && isValidRange,
        errorMessage: !hasAllFields 
          ? 'Please complete all salary fields' 
          : !isValidRange 
            ? 'Maximum salary must be greater than minimum' 
            : undefined
      };
    
    default:
      return {
        isValid: !!value,
        errorMessage: !value ? 'This field is required' : undefined
      };
  }
};