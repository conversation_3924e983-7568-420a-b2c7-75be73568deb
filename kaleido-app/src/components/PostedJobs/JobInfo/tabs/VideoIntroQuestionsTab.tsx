import CulturalFitCard from '@/components/CultureFit/CulturalFitCard';
import QuestionSuggestions from '@/components/CultureFit/QuestionSuggestions';
import { showToast } from '@/components/Toaster';
import { SaveChangesSlider } from '@/components/common';
import { IJob } from '@/entities/interfaces';
import { CultureFitQuestions } from '@/entities/Job.entities';
import apiHelper from '@/lib/apiHelper';
import { jobDataToIJob, useJobsStore, useJobStateStore } from '@/stores/unifiedJobStore';
import { Slider } from '@mui/material';
import { AnimatePresence, motion } from 'framer-motion';
import {
  ChevronLeft,
  ChevronRight,
  Clock,
  Edit2,
  Loader2,
  Medal,
  Plus,
  Save,
  Sparkles,
  Target,
  Timer,
  Trash2,
  Trophy,
  UserCheck,
  Users2,
  Video,
  X,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface VideoIntroQuestionsTabProps {
  job: IJob;
}

export const VideoIntroQuestionsTab: React.FC<VideoIntroQuestionsTabProps> = ({
  job: initialJob,
}) => {
  const [job, setJob] = useState<IJob>(initialJob);
  const [questions, setQuestions] = useState<CultureFitQuestions[]>(
    initialJob.cultureFitQuestions?.length
      ? initialJob.cultureFitQuestions
      : [{ id: '1', question: '', duration: 1 }]
  );
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [initialQuestions, setInitialQuestions] = useState<CultureFitQuestions[]>([]);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const candidatesPerPage = 5;
  const [activePopupIndex, setActivePopupIndex] = useState<number | null>(null);
  const [editingQuestions, setEditingQuestions] = useState<Set<number>>(new Set());

  // Subscribe to job state changes
  useEffect(() => {
    const unsubscribe = useJobStateStore.subscribe(
      state => state.jobs,
      jobs => {
        const updatedJob = jobs.find(j => j.id === job.id);
        if (updatedJob) {
          const currentJobKey = JSON.stringify({
            cultureFitQuestions: job.cultureFitQuestions,
            candidates: job.candidates?.length,
          });

          const updatedJobKey = JSON.stringify({
            cultureFitQuestions: updatedJob.cultureFitQuestions,
            candidates: updatedJob.candidates?.length,
          });

          if (currentJobKey !== updatedJobKey) {
            setJob(jobDataToIJob(updatedJob));
            setQuestions(
              updatedJob.cultureFitQuestions?.length
                ? Array.isArray(updatedJob.cultureFitQuestions)
                  ? (updatedJob.cultureFitQuestions.filter(
                      q => typeof q === 'object' && 'id' in q
                    ) as CultureFitQuestions[])
                  : [{ id: '1', question: '', duration: 1 }]
                : [{ id: '1', question: '', duration: 1 }]
            );
          }
        }
      }
    );

    return () => {
      unsubscribe();
    };
  }, [job.id]);

  // Initialize and fetch culture fit details
  useEffect(() => {
    const initializeJob = async () => {
      if (job.id) {
        setIsLoadingDetails(true);

        try {
          const cultureFitData = await apiHelper.get(`/jobs/${job.id}/culture-fit-details`);

          if (cultureFitData) {
            const updatedJob = {
              ...job,
              ...cultureFitData.job,
              candidates: cultureFitData.candidates,
            };

            setJob(updatedJob);

            if (cultureFitData.job.cultureFitQuestions?.length) {
              setQuestions(cultureFitData.job.cultureFitQuestions);
              setInitialQuestions(cultureFitData.job.cultureFitQuestions);
              // Initially, all non-empty questions are in read mode
              const initialEditingSet = new Set<number>();
              cultureFitData.job.cultureFitQuestions.forEach(
                (q: CultureFitQuestions, index: number) => {
                  if (!q.question.trim()) {
                    initialEditingSet.add(index);
                  }
                }
              );
              setEditingQuestions(initialEditingSet);
            }
          }
        } catch (error) {
          console.error('Error fetching culture fit details:', error);
        } finally {
          setIsLoadingDetails(false);
        }
      }
    };

    initializeJob();
  }, [job.id]);

  // Track changes
  useEffect(() => {
    const hasChanges = JSON.stringify(questions) !== JSON.stringify(initialQuestions);
    setHasUnsavedChanges(hasChanges);
  }, [questions, initialQuestions]);

  // Validation
  const validationErrors = React.useMemo(() => {
    const errors: Record<string, string> = {};
    const validQuestions = questions.filter(q => q.question.trim() !== '');
    const hasEmptyQuestions = questions.some(q => q.question.trim() === '');

    if (validQuestions.length === 0) {
      errors.questions = 'At least one question must have content';
    }

    if (hasEmptyQuestions && questions.length > 1) {
      errors.emptyQuestions = 'Please fill in all questions or remove empty ones';
    }

    return errors;
  }, [questions]);

  const handleSave = async () => {
    setIsSaving(true);

    try {
      const validQuestions = questions.filter(q => q.question.trim() !== '');

      const { updateJobCultureFit } = useJobsStore.getState();
      const response = await updateJobCultureFit(job.id, {
        cultureFitQuestions: validQuestions,
        cultureFitDescription: job.cultureFitDescription || '',
      });

      if (response && response.success) {
        // Get the updated job data with new culture fit questions
        const updatedJobData = response.job || response;

        // Update local state with the new questions
        const newQuestions = updatedJobData.cultureFitQuestions?.length
          ? updatedJobData.cultureFitQuestions
          : [{ id: '1', question: '', duration: 1 }];

        setQuestions(newQuestions);
        setInitialQuestions(newQuestions);
        setHasUnsavedChanges(false);

        // Update the job with the new culture fit questions
        setJob(prevJob => ({
          ...prevJob,
          cultureFitQuestions: newQuestions,
          cultureFitDescription:
            updatedJobData.cultureFitDescription || prevJob.cultureFitDescription,
        }));

        // Mark job as updated and trigger refresh event
        useJobStateStore.getState().markJobAsUpdated(job.id);

        // Dispatch event to notify VideoIntroTab
        window.dispatchEvent(
          new CustomEvent('cultureFitUpdated', {
            detail: {
              jobId: job.id,
              cultureFitQuestions: newQuestions,
            },
          })
        );

        showToast({
          message: 'Video intro questions updated successfully',
          isSuccess: true,
        });
      } else {
        showToast({
          message: response?.message || 'Failed to update questions',
          isSuccess: false,
        });
      }
    } catch (error) {
      console.error('Error saving questions:', error);
      showToast({
        message: 'Error updating questions',
        isSuccess: false,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleQuestionChange = (
    index: number,
    field: keyof CultureFitQuestions,
    value: string | number
  ) => {
    const updatedQuestions = questions.map((q, i) => (i === index ? { ...q, [field]: value } : q));
    setQuestions(updatedQuestions);
  };

  // QuestionSuggestionsPopup component
  const QuestionSuggestionsPopup = ({
    isOpen,
    onClose,
    onSelectQuestion,
    triggerIndex,
  }: {
    isOpen: boolean;
    onClose: () => void;
    onSelectQuestion: (question: string) => void;
    triggerIndex: number;
  }) => {
    return (
      <AnimatePresence>
        {isOpen && (
          <div className="absolute bottom-full left-0 right-0 mb-4 z-[9999]">
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ type: 'spring', duration: 0.3, stiffness: 200 }}
              className="relative border border-white/10 rounded-xl shadow-2xl max-h-[50vh] overflow-hidden"
            >
              {/* Glassmorphism background layers */}
              <div
                className="absolute inset-0 rounded-xl"
                style={{
                  background: 'rgba(17, 24, 39, 0.98)',
                  backdropFilter: 'blur(40px) saturate(150%)',
                  WebkitBackdropFilter: 'blur(40px) saturate(150%)',
                }}
              />
              <div
                className="absolute inset-0 rounded-xl"
                style={{
                  background:
                    'linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%)',
                }}
              />

              {/* Content */}
              <div
                className="relative z-10 p-6 overflow-y-auto max-h-[50vh]"
                style={{
                  boxShadow: 'inset 0 0 0 1px rgba(255, 255, 255, 0.1)',
                }}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-purple-400" />
                    <h3 className="text-sm font-medium text-white">Suggested Questions</h3>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={onClose}
                    className="p-1 rounded bg-gray-800/50 hover:bg-gray-700/50 text-gray-400 hover:text-white transition-all"
                  >
                    <X className="w-4 h-4" />
                  </motion.button>
                </div>

                <div className="overflow-hidden">
                  <QuestionSuggestions
                    size="medium"
                    onSelectQuestion={question => {
                      onSelectQuestion(question);
                      onClose();
                    }}
                  />
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    );
  };

  if (isLoadingDetails) {
    return (
      <div className="w-full">
        {/* Hero Header Section Skeleton */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="relative overflow-hidden mb-8 -mt-8 -mx-8"
        >
          <div className="relative h-[280px] overflow-hidden">
            {/* Background placeholder */}
            <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 via-indigo-900/30 to-indigo-900/50 animate-pulse" />

            {/* Content skeleton */}
            <div className="relative h-full flex items-center px-8 lg:px-12">
              <div className="max-w-7xl mx-auto w-full">
                <div className="text-center">
                  {/* Icon skeleton */}
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 bg-white/20 rounded-2xl animate-pulse" />
                  </div>

                  {/* Title skeleton */}
                  <div className="h-10 w-80 bg-white/20 rounded-lg mx-auto mb-3 animate-pulse" />

                  {/* Subtitle skeleton */}
                  <div className="h-6 w-96 bg-white/10 rounded-lg mx-auto mb-6 animate-pulse" />

                  {/* Stats skeleton */}
                  <div className="flex justify-center gap-6">
                    {[1, 2, 3, 4].map(i => (
                      <div key={i} className="text-center">
                        <div className="flex items-center justify-center gap-1.5 mb-1">
                          <div className="w-4 h-4 bg-white/20 rounded animate-pulse" />
                          <div className="h-7 w-8 bg-white/30 rounded animate-pulse" />
                        </div>
                        <div className="h-3 w-20 bg-white/10 rounded animate-pulse mx-auto" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Main Content Skeleton */}
        <div className="max-w-7xl mx-auto px-8 pb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Questions Section Skeleton */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-6 border border-white/[0.05]">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-indigo-500/20 rounded-xl animate-pulse" />
                  <div>
                    <div className="h-7 w-48 bg-white/20 rounded animate-pulse mb-2" />
                    <div className="h-4 w-64 bg-gray-300/10 rounded animate-pulse" />
                  </div>
                </div>

                {/* Question Cards */}
                <div className="space-y-4">
                  {[1, 2, 3].map(i => (
                    <div
                      key={i}
                      className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-5 border border-white/[0.05]"
                    >
                      <div className="flex justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-lg animate-pulse flex items-center justify-center">
                            <span className="text-white/30 font-semibold text-sm">{i}</span>
                          </div>
                          <div className="h-5 w-24 bg-muted/20 rounded animate-pulse" />
                        </div>
                        <div className="w-8 h-8 bg-muted/20 rounded-lg animate-pulse" />
                      </div>

                      {/* Textarea skeleton */}
                      <div className="w-full min-h-[100px] p-4 rounded-xl bg-muted/10 border border-muted/20 animate-pulse" />

                      {/* Get Question Suggestions button skeleton (shows when empty) */}
                      {i === 1 && (
                        <div className="mt-4 flex justify-center">
                          <div className="px-6 py-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl animate-pulse flex items-center gap-3">
                            <div className="w-5 h-5 bg-purple-300/20 rounded animate-pulse" />
                            <div className="h-4 w-40 bg-purple-300/20 rounded animate-pulse" />
                            <div className="w-4 h-4 bg-purple-300/20 rounded animate-pulse" />
                          </div>
                        </div>
                      )}

                      {/* Duration slider skeleton */}
                      <div className="mt-6 space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-indigo-400/20 rounded animate-pulse" />
                            <div className="h-4 w-32 bg-muted/20 rounded animate-pulse" />
                          </div>
                          <div className="h-6 w-20 bg-indigo-500/10 rounded-lg animate-pulse px-3 py-1" />
                        </div>
                        <div className="h-2 bg-muted/10 rounded-full animate-pulse">
                          <div className="h-2 w-1/3 bg-indigo-500/20 rounded-full animate-pulse" />
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Add button skeleton */}
                  <div className="w-full py-4 rounded-xl flex items-center justify-center gap-3 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 text-muted/30 border border-indigo-500/20 animate-pulse">
                    <div className="w-5 h-5 bg-muted/20 rounded animate-pulse" />
                    <div className="h-5 w-36 bg-muted/20 rounded animate-pulse" />
                  </div>

                  {/* Save button skeleton */}
                  <div className="flex justify-center pt-6">
                    <div className="px-10 py-4 rounded-xl flex items-center gap-3 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 animate-pulse">
                      <div className="w-5 h-5 bg-white/20 rounded animate-pulse" />
                      <div className="h-5 w-24 bg-white/20 rounded animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Tips skeleton */}
              <div className="bg-gradient-to-br from-white/[0.01] to-transparent backdrop-blur-sm rounded-lg p-4 border border-white/[0.03]">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <div className="w-3.5 h-3.5 bg-blue-400/30 rounded animate-pulse" />
                    <div className="h-3 w-48 bg-gray-500/20 rounded animate-pulse" />
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3.5 h-3.5 bg-purple-400/30 rounded animate-pulse" />
                    <div className="h-3 w-48 bg-gray-500/20 rounded animate-pulse" />
                  </div>
                </div>
              </div>
            </div>

            {/* Candidates Section Skeleton */}
            <div className="lg:col-span-1">
              <div className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-6 border border-white/[0.05] h-[calc(100vh-320px)] flex flex-col">
                {/* Header */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-xl animate-pulse" />
                  <div>
                    <div className="h-6 w-40 bg-white/20 rounded animate-pulse mb-2" />
                    <div className="h-4 w-32 bg-gray-300/10 rounded animate-pulse" />
                  </div>
                </div>

                {/* Candidate cards skeleton */}
                <div className="flex-1 space-y-3">
                  {[1, 2, 3, 4].map(i => (
                    <div key={i} className="backdrop-blur-xl rounded-lg p-4 border border-white/5">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="h-5 w-32 bg-white/20 rounded animate-pulse mb-2" />
                          <div className="h-4 w-24 bg-gray-400/20 rounded animate-pulse mb-2" />
                          <div className="flex items-center gap-1">
                            <div className="w-3 h-3 bg-gray-400/20 rounded animate-pulse" />
                            <div className="h-3 w-40 bg-gray-400/20 rounded animate-pulse" />
                          </div>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <div className="h-6 w-20 bg-gray-500/20 rounded-full animate-pulse" />
                          <div className="h-6 w-12 bg-gray-500/20 rounded-full animate-pulse" />
                        </div>
                      </div>
                      <div className="h-10 bg-muted/10 rounded-lg animate-pulse border border-muted/20" />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Hero Header Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative overflow-hidden mb-8 -mt-8 -mx-8"
      >
        {/* Background with image and overlay */}
        <div className="relative h-[280px] overflow-hidden">
          {/* Background Image */}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url('/images/insights/professional_training_revised.png')`,
              backgroundPosition: 'center 20%',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
            }}
          />

          {/* Gradient overlay - transparent at top, darker at bottom */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-indigo-900/50 to-indigo-900/80" />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-transparent to-transparent" />

          {/* Content */}
          <div className="relative h-full flex items-center px-4 sm:px-6 lg:px-12">
            <div className="max-w-7xl mx-auto w-full">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-center"
              >
                <div className="flex justify-center mb-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: 'spring', stiffness: 200 }}
                    className="p-3 bg-white/20 backdrop-blur-md rounded-2xl border border-white/30"
                  >
                    <Video className="w-10 h-10 text-white" />
                  </motion.div>
                </div>

                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-3"
                >
                  Video Intro Questions
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                  className="text-sm sm:text-base lg:text-lg text-white/90 max-w-2xl mx-auto mb-6 px-4"
                >
                  Craft personalized video questions to find candidates aligned with your values
                </motion.p>

                {/* Stats */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="flex flex-wrap justify-center gap-3 sm:gap-4"
                >
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5">
                      <Users2 className="w-4 h-4 text-white/70" />
                      <span className="text-sm font-semibold text-white">
                        {job.candidates?.filter(
                          candidate =>
                            candidate.hasCompletedVideoInterview &&
                            candidate.videoResponses &&
                            candidate.videoResponses.length > 0
                        ).length || 0}
                      </span>
                    </div>
                    <p className="text-[10px] text-white/50">Completed Responses</p>
                  </div>

                  <div className="w-px h-7 bg-white/15" />

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5">
                      <UserCheck className="w-4 h-4 text-white/70" />
                      <span className="text-sm font-semibold text-white">
                        {job.candidates?.length || 0}
                      </span>
                    </div>
                    <p className="text-[10px] text-white/50">Total Candidates</p>
                  </div>

                  <div className="w-px h-7 bg-white/15" />

                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1.5">
                      <Timer className="w-4 h-4 text-white/70" />
                      <span className="text-sm font-semibold text-white">
                        {Math.round(questions.reduce((sum, q) => sum + q.duration, 0) * 10) / 10}
                      </span>
                    </div>
                    <p className="text-[10px] text-white/50">Total Minutes</p>
                  </div>

                  {job.topCandidateThreshold && (
                    <>
                      <div className="w-px h-7 bg-white/15" />

                      <div className="text-center">
                        <div className="flex items-center justify-center gap-1.5">
                          <Trophy className="w-4 h-4 text-green-400" />
                          <span className="text-sm font-semibold text-white">
                            {typeof job.topCandidateThreshold === 'number'
                              ? job.topCandidateThreshold
                              : parseFloat(job.topCandidateThreshold || '0')}
                            %
                          </span>
                        </div>
                        <p className="text-[10px] text-white/50">Top Threshold</p>
                      </div>
                    </>
                  )}

                  {job.secondTierCandidateThreshold && (
                    <>
                      <div className="w-px h-7 bg-white/15" />

                      <div className="text-center">
                        <div className="flex items-center justify-center gap-1.5">
                          <Medal className="w-4 h-4 text-yellow-400" />
                          <span className="text-sm font-semibold text-white">
                            {typeof job.secondTierCandidateThreshold === 'number'
                              ? job.secondTierCandidateThreshold
                              : parseFloat(job.secondTierCandidateThreshold || '0')}
                            %
                          </span>
                        </div>
                        <p className="text-[10px] text-white/50">Second Tier</p>
                      </div>
                    </>
                  )}
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
          {/* Questions Section */}
          <div className="lg:col-span-2 space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-6 border border-white/[0.05]"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-indigo-500/20 rounded-xl">
                  <Sparkles className="w-6 h-6 text-indigo-400" />
                </div>
                <div>
                  <h3 className="text-2xl font-semibold text-white">Craft Your Questions</h3>
                  <p className="text-gray-300 text-sm mt-1">
                    Create engaging questions to understand candidates better
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                {questions.map((q, index) => {
                  const isEditing = editingQuestions.has(index) || !q.question.trim();

                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 + index * 0.1 }}
                      className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-5 border border-white/[0.05] hover:border-white/[0.08] transition-all duration-300 relative group"
                    >
                      {isEditing ? (
                        // Edit Mode
                        <>
                          <div className="flex justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-semibold text-sm">
                                {index + 1}
                              </div>
                              <h4 className="text-lg font-medium text-white">
                                Question {index + 1}
                              </h4>
                            </div>
                            <div className="flex items-center gap-2">
                              {q.question.trim() && (
                                <motion.button
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  onClick={() => {
                                    const newEditingSet = new Set(editingQuestions);
                                    newEditingSet.delete(index);
                                    setEditingQuestions(newEditingSet);
                                  }}
                                  className="p-2 rounded-lg transition-all text-green-400 hover:bg-green-500/20 hover:text-green-300"
                                  title="Save and switch to read mode"
                                >
                                  <Save className="w-4 h-4" />
                                </motion.button>
                              )}
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => {
                                  const newQuestions = questions.filter((_, i) => i !== index);
                                  setQuestions(newQuestions);
                                  const newEditingSet = new Set(editingQuestions);
                                  newEditingSet.delete(index);
                                  // Adjust indices in editing set
                                  const adjustedSet = new Set<number>();
                                  newEditingSet.forEach(i => {
                                    if (i > index) adjustedSet.add(i - 1);
                                    else if (i < index) adjustedSet.add(i);
                                  });
                                  setEditingQuestions(adjustedSet);
                                }}
                                disabled={questions.length === 1}
                                className={`p-2 rounded-lg transition-all ${
                                  questions.length === 1
                                    ? 'text-gray-500 cursor-not-allowed opacity-50'
                                    : 'text-red-400 hover:bg-red-500/20 hover:text-red-300'
                                }`}
                              >
                                <Trash2 className="w-4 h-4" />
                              </motion.button>
                            </div>
                          </div>

                          <textarea
                            value={q.question}
                            onChange={e => handleQuestionChange(index, 'question', e.target.value)}
                            placeholder={`Enter an engaging question for candidates to answer...`}
                            className="w-full min-h-[100px] p-4 rounded-xl bg-gray-900/50 border border-gray-700/50 text-gray-200 resize-y focus:outline-none focus:border-indigo-500/50 transition-all duration-300 placeholder:text-gray-500"
                            autoFocus={editingQuestions.has(index)}
                          />

                          {!q.question.trim() && (
                            <div className="mt-4 flex justify-center relative">
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() =>
                                  setActivePopupIndex(activePopupIndex === index ? null : index)
                                }
                                className="px-6 py-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-xl flex items-center gap-3 text-purple-300 hover:text-purple-200 hover:border-purple-400/50 transition-all duration-300 group"
                              >
                                <motion.div
                                  animate={{ rotate: [0, 10, -10, 0] }}
                                  transition={{ duration: 2, repeat: Infinity }}
                                >
                                  <Sparkles className="w-5 h-5" />
                                </motion.div>
                                <span className="font-medium">Get Question Suggestions</span>
                                <motion.div className="w-0 group-hover:w-4 overflow-hidden transition-all duration-300">
                                  <ChevronRight className="w-4 h-4" />
                                </motion.div>
                              </motion.button>

                              <QuestionSuggestionsPopup
                                isOpen={activePopupIndex === index}
                                onClose={() => setActivePopupIndex(null)}
                                triggerIndex={index}
                                onSelectQuestion={question => {
                                  handleQuestionChange(index, 'question', question);
                                  setActivePopupIndex(null);
                                }}
                              />
                            </div>
                          )}

                          <div className="mt-6 space-y-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Clock className="w-4 h-4 text-indigo-400" />
                                <span className="text-sm text-gray-300">Response Duration</span>
                              </div>
                              <span className="text-sm font-medium text-white bg-indigo-500/20 px-3 py-1 rounded-lg">
                                {q.duration} {q.duration === 1 ? 'minute' : 'minutes'}
                              </span>
                            </div>

                            <Slider
                              value={q.duration}
                              onChange={(_, value) =>
                                handleQuestionChange(index, 'duration', value as number)
                              }
                              step={0.5}
                              marks
                              min={0.5}
                              max={3}
                              valueLabelDisplay="auto"
                              sx={{
                                color: '#6366f1',
                                height: 8,
                                '& .MuiSlider-track': {
                                  border: 'none',
                                  height: 8,
                                  borderRadius: 4,
                                  background: 'linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%)',
                                },
                                '& .MuiSlider-thumb': {
                                  height: 20,
                                  width: 20,
                                  backgroundColor: '#fff',
                                  border: '2px solid #6366f1',
                                  '&:hover': {
                                    boxShadow: '0 0 0 8px rgba(99, 102, 241, 0.16)',
                                  },
                                },
                                '& .MuiSlider-rail': {
                                  height: 8,
                                  borderRadius: 4,
                                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                },
                                '& .MuiSlider-mark': {
                                  backgroundColor: 'rgba(255, 255, 255, 0.3)',
                                  height: 12,
                                  width: 2,
                                  borderRadius: 1,
                                },
                                '& .MuiSlider-markActive': {
                                  backgroundColor: '#fff',
                                },
                              }}
                            />
                          </div>
                        </>
                      ) : (
                        // Read-Only Mode
                        <>
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex items-start gap-3 flex-1">
                              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-semibold text-sm flex-shrink-0">
                                {index + 1}
                              </div>
                              <div className="flex-1">
                                <p className="text-gray-200 leading-relaxed">{q.question}</p>
                                <div className="flex items-center gap-2 mt-2">
                                  <Clock className="w-3 h-3 text-indigo-400" />
                                  <span className="text-xs text-gray-400">
                                    {q.duration} {q.duration === 1 ? 'minute' : 'minutes'} response
                                    time
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-1 ml-4">
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => {
                                  const newEditingSet = new Set(editingQuestions);
                                  newEditingSet.add(index);
                                  setEditingQuestions(newEditingSet);
                                }}
                                className="p-1.5 rounded-lg transition-all text-blue-400 hover:bg-blue-500/20 hover:text-blue-300"
                                title="Edit question"
                              >
                                <Edit2 className="w-3.5 h-3.5" />
                              </motion.button>
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => {
                                  const newQuestions = questions.filter((_, i) => i !== index);
                                  setQuestions(newQuestions);
                                  const newEditingSet = new Set(editingQuestions);
                                  newEditingSet.delete(index);
                                  // Adjust indices in editing set
                                  const adjustedSet = new Set<number>();
                                  newEditingSet.forEach(i => {
                                    if (i > index) adjustedSet.add(i - 1);
                                    else if (i < index) adjustedSet.add(i);
                                  });
                                  setEditingQuestions(adjustedSet);
                                }}
                                disabled={questions.length === 1}
                                className={`p-1.5 rounded-lg transition-all ${
                                  questions.length === 1
                                    ? 'text-gray-500 cursor-not-allowed opacity-50'
                                    : 'text-red-400 hover:bg-red-500/20 hover:text-red-300'
                                }`}
                                title="Delete question"
                              >
                                <Trash2 className="w-3.5 h-3.5" />
                              </motion.button>
                            </div>
                          </div>
                        </>
                      )}
                    </motion.div>
                  );
                })}

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    const newIndex = questions.length;
                    setQuestions([
                      ...questions,
                      {
                        id: `${questions.length + 1}`,
                        question: '',
                        duration: 1,
                      },
                    ]);
                    // Add new question to editing set
                    const newEditingSet = new Set(editingQuestions);
                    newEditingSet.add(newIndex);
                    setEditingQuestions(newEditingSet);
                  }}
                  disabled={questions.some(q => q.question.trim() === '')}
                  className={`w-full py-4 rounded-xl flex items-center justify-center gap-3 font-medium transition-all ${
                    questions.some(q => q.question.trim() === '')
                      ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-white hover:from-indigo-500/30 hover:to-purple-500/30 border border-indigo-500/30'
                  }`}
                >
                  <Plus size={20} />
                  Add Another Question
                </motion.button>

                {/* Removed inline save button - using SaveChangesSlider instead */}
              </div>
            </motion.div>

            {/* Tips Section - Smaller */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-br from-white/[0.01] to-transparent backdrop-blur-sm rounded-lg p-4 border border-white/[0.03]"
            >
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-6 text-xs text-gray-500">
                <div className="flex items-center gap-2">
                  <Target className="w-3.5 h-3.5 text-blue-400/60 flex-shrink-0" />
                  <span>
                    <strong className="text-gray-400">Culture Fit</strong>: Questions that reveal
                    values alignment
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <UserCheck className="w-3.5 h-3.5 text-purple-400/60 flex-shrink-0" />
                  <span>
                    <strong className="text-gray-400">Personal Touch</strong>: Open-ended for
                    authentic responses
                  </span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Candidates Section */}
          <div className="lg:col-span-1 mt-6 lg:mt-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-br from-white/[0.02] to-transparent backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-white/[0.05] min-h-[500px] lg:min-h-[calc(100vh-320px)] lg:max-h-[calc(100vh-320px)] flex flex-col"
            >
              <div className="flex items-center gap-3 mb-4 lg:mb-6">
                <div className="p-1.5 sm:p-2 bg-purple-500/20 rounded-xl">
                  <Users2 className="w-5 h-5 sm:w-6 sm:h-6 text-purple-400" />
                </div>
                <div className="min-w-0">
                  <h3 className="text-lg sm:text-xl font-semibold text-white truncate">
                    Candidate Responses
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-300 mt-1">
                    {job.candidates?.filter(
                      candidate =>
                        candidate.hasCompletedVideoInterview &&
                        candidate.videoResponses &&
                        candidate.videoResponses.length > 0
                    ).length || 0}{' '}
                    of {job.candidates?.length || 0}{' '}
                    {job.candidates?.length === 1 ? 'candidate' : 'candidates'} completed
                  </p>
                </div>
              </div>

              {/* Pagination Controls at Top */}
              {job.candidates && job.candidates.length > candidatesPerPage && (
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between pb-3 mb-3 lg:pb-4 lg:mb-4 border-b border-white/[0.05] gap-2">
                  <p className="text-xs text-gray-400">
                    Showing {(currentPage - 1) * candidatesPerPage + 1}-
                    {Math.min(currentPage * candidatesPerPage, job.candidates.length)} of{' '}
                    {job.candidates.length}
                  </p>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className={`p-1.5 rounded transition-all ${
                        currentPage === 1
                          ? 'text-gray-600 cursor-not-allowed'
                          : 'text-gray-400 hover:text-white hover:bg-white/[0.05]'
                      }`}
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </button>
                    <span className="text-sm text-gray-400">
                      {currentPage} / {Math.ceil(job.candidates.length / candidatesPerPage)}
                    </span>
                    <button
                      onClick={() =>
                        setCurrentPage(
                          Math.min(
                            Math.ceil(job.candidates.length / candidatesPerPage),
                            currentPage + 1
                          )
                        )
                      }
                      disabled={
                        currentPage === Math.ceil(job.candidates.length / candidatesPerPage)
                      }
                      className={`p-1.5 rounded transition-all ${
                        currentPage === Math.ceil(job.candidates.length / candidatesPerPage)
                          ? 'text-gray-600 cursor-not-allowed'
                          : 'text-gray-400 hover:text-white hover:bg-white/[0.05]'
                      }`}
                    >
                      <ChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              )}

              <div className="flex-1 overflow-y-auto pr-0 sm:pr-2 custom-scrollbar space-y-3">
                {job.candidates && job.candidates.length > 0 ? (
                  job.candidates
                    .slice((currentPage - 1) * candidatesPerPage, currentPage * candidatesPerPage)
                    .map((candidate, index) => (
                      <motion.div
                        key={candidate.id}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <CulturalFitCard
                          candidate={candidate}
                          job={job}
                          questionLength={job.cultureFitQuestions?.length || 0}
                          onVideoModalOpen={() => {}}
                          onVideoModalClose={() => {}}
                        />
                      </motion.div>
                    ))
                ) : !isLoadingDetails ? (
                  <div className="text-center py-12">
                    <div className="p-4 bg-gray-800/30 rounded-full inline-block mb-4">
                      <Video className="w-12 h-12 text-gray-500" />
                    </div>
                    <p className="text-gray-400 font-medium">No candidate responses yet</p>
                    <p className="text-gray-500 text-sm mt-2">
                      Responses will appear here as candidates submit their videos
                    </p>
                  </div>
                ) : null}
              </div>
            </motion.div>
          </div>
        </div>

        <style jsx>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        `}</style>
      </div>

      {/* Save Changes Slider */}
      <SaveChangesSlider
        isVisible={hasUnsavedChanges && Object.keys(validationErrors).length === 0}
        onSave={handleSave}
        onCancel={() => {
          setQuestions(initialQuestions);
          setHasUnsavedChanges(false);
        }}
        isSaving={isSaving}
        saveText="Save Questions"
      />
    </div>
  );
};
