import { AnimatePresence, motion } from 'framer-motion';
import {
  Badge<PERSON>heck,
  BookOpen,
  Briefcase,
  Building,
  Calendar,
  ChevronDown,
  Clock,
  DollarSign,
  Edit,
  Facebook,
  GraduationCap,
  HeartHandshake,
  Instagram,
  Languages,
  LayoutList,
  Lightbulb,
  Linkedin,
  ListChecks,
  MapPin,
  Save,
  ShieldCheck,
  TrendingUp,
  Twitter,
  Users,
  X,
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import CulturalFitDetailsDrawer from '@/components/CultureFit/CulturalFitDetailsDrawer';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { preserveTabParameter } from '@/contexts/jobs/JobsContext';
import apiHelper from '@/lib/apiHelper';
import { useSocialConnectors } from '@/stores/socialConnectorStore';
import { useJobsStore, useJobStateStore, useJobStore } from '@/stores/unifiedJobStore';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import ReactDOM from 'react-dom';
import { showToast } from '../Toaster';
import VideoJDDetailsSettings from '../VideoJD/VideoJDDetailsSettings';
import GenerateContentButton from './GenerateContentButton';
import JobEditView from './JobEditView';
import PublishDropdownPortal from './PublishDropdownPortal';

// Define fields to exclude from editing
const EXCLUDED_FIELDS = [
  'id',
  'createdAt',
  'updatedAt',
  'candidates',
  'videoResponses',
  'status',
  'cultureFitQuestions',
];

// Platform type definition
interface PublishPlatform {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  disabled?: boolean;
}

export const PostedJobDetails = ({ job, onClose, showEditOptions = true }) => {
  const [editMode, setEditMode] = useState(false);
  const [editedJob, setEditedJob] = useState(job);
  const [isSaving, setIsSaving] = useState(false);
  const [publishDropdownOpen, setPublishDropdownOpen] = useState(false);
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  const [fullJobData, setFullJobData] = useState(null);
  const [isLoadingJobDetails, setIsLoadingJobDetails] = useState(false);

  // Use the job store for publishing state
  const { selectedPlatforms, isPublishing, isUnpublishing, togglePlatformSelection } =
    useJobStore();

  // Use the social connector store
  const { connectors: connectorStatuses, fetchConnectorStatuses: fetchConnectorStatusesFromStore } =
    useSocialConnectors();
  const publishDropdownRef = useRef<HTMLButtonElement>(null);

  // Safely handle router - might not be available in all contexts
  let router, searchParams;
  try {
    router = useRouter();
    searchParams = useSearchParams();
  } catch (error) {
    console.warn('Router not available in this context:', error);
    router = null;
    searchParams = null;
  }

  const {
    updateJob,
    setSelectedJobId,
    selectedJob,
    publishJob: publishJobFromStore,
    unpublishJob: unpublishJobFromStore,
  } = useJobsStore();

  // Use fullJobData if available, otherwise selectedJob from store, otherwise use prop
  const currentJob = fullJobData || selectedJob || job;

  // Extract jobId from URL parameters (moved up for better access)
  const jobId = searchParams?.get('jobId');

  // Determine if we're in a loading state
  const isWaitingForJobData = jobId && !currentJob;

  // Fetch full job details when component mounts
  useEffect(() => {
    const fetchFullJobDetails = async () => {
      if (!currentJob?.id) return;

      // If we already have full job data (check for required fields), don't fetch again
      if (currentJob.jobResponsibilities && currentJob.skills && currentJob.benefits) {
        setFullJobData(currentJob);
        return;
      }

      try {
        setIsLoadingJobDetails(true);
        const fullJob = await apiHelper.get(`/jobs/${currentJob.id}`);
        setFullJobData(fullJob);
        setEditedJob(fullJob); // Update edited job with full data
      } catch (error) {
        console.error('Error fetching full job details:', error);
        showToast({
          message: 'Failed to load complete job details',
          isSuccess: false,
        });
        // Fall back to using the partial job data
        setFullJobData(currentJob);
      } finally {
        setIsLoadingJobDetails(false);
      }
    };

    fetchFullJobDetails();
  }, [currentJob?.id]);

  // Create portal container on mount
  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof document !== 'undefined') {
      // Create portal container if it doesn't exist
      let container = document.getElementById('job-details-portal');
      if (!container) {
        container = document.createElement('div');
        container.id = 'job-details-portal';
        document.body.appendChild(container);
      }
      setPortalContainer(container);

      // Cleanup on unmount
      return () => {
        if (container && container.parentNode && container.childElementCount === 0) {
          container.parentNode.removeChild(container);
        }
      };
    }
  }, []);

  // Initialize job in store when component mounts
  useEffect(() => {
    if (currentJob?.id) {
      // Set the current job in the store to sync platform selection
      useJobStore.getState().setSelectedJob(currentJob.id);
    }
  }, [currentJob?.id]);

  const showVideoJd = searchParams?.get('showVideoJd') === 'true';
  const showCultureFit = searchParams?.get('showCultureFit') === 'true';
  const mode = searchParams?.get('mode');

  // Helper function to safely check connector status
  const isConnectorEnabled = (platform: string): boolean => {
    if (!Array.isArray(connectorStatuses)) return false;
    const connector = connectorStatuses.find(c => c?.platform === platform);
    return Boolean(connector?.isConnected);
  };

  // Define available platforms with dynamic status based on connectors
  const platforms: PublishPlatform[] = [
    {
      id: 'jobboard',
      name: 'Our Job Board',
      icon: <LayoutList className="w-4 h-4" />,
      color: '#6366F1',
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: <Linkedin className="w-4 h-4" />,
      color: '#0A66C2',
      disabled: !isConnectorEnabled('linkedin'),
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: <Instagram className="w-4 h-4" />,
      color: '#E1306C',
      disabled: !isConnectorEnabled('instagram'),
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: <Facebook className="w-4 h-4" />,
      color: '#1877F2',
      disabled: !isConnectorEnabled('facebook'),
    },
    {
      id: 'twitter',
      name: 'Twitter/X',
      icon: <Twitter className="w-4 h-4" />,
      color: '#1DA1F2',
      disabled: !isConnectorEnabled('twitter'),
    },
  ];

  const handleFieldChange = (field: string, value: any) => {
    setEditedJob(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      // Update the job
      const response: any = await updateJob(currentJob.id, editedJob);

      if (response) {
        showToast({
          message: 'Job updated successfully',
          isSuccess: true,
        });

        // Exit edit mode
        setEditMode(false);

        // Update the job state store to notify other components of the update
        useJobStateStore.getState().updateJob(currentJob.id, { ...currentJob, ...editedJob });

        // Invalidate unified job store caches for immediate refresh
        const { useUnifiedJobStore } = await import('@/stores/unifiedJobStore');
        const jobStore = useUnifiedJobStore.getState();
        if (jobStore.selectedJobId === currentJob.id) {
          jobStore.invalidateCache(`job-${currentJob.id}`);
          jobStore.invalidateCache(`criteria-${currentJob.id}`);
        }
      } else {
        showToast({
          message: response.message || 'Failed to update job',
          isSuccess: false,
        });
      }
    } catch (error) {
      console.error('Error saving job:', error);
      showToast({
        message: 'Failed to save changes',
        isSuccess: false,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    try {
      await publishJobFromStore(currentJob.id, selectedPlatforms);
      showToast({
        message: 'Job published successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error publishing job:', error);
      showToast({
        message: 'Failed to publish job',
        isSuccess: false,
      });
    }
  };

  const handleUnpublish = async () => {
    try {
      await unpublishJobFromStore(currentJob.id, selectedPlatforms);
      showToast({
        message: 'Job unpublished successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error unpublishing job:', error);
      showToast({
        message: 'Failed to unpublish job',
        isSuccess: false,
      });
    }
  };

  // We're handling click outside in the portal component

  const handleClose = () => {
    setSelectedJobId(null);
    onClose();
  };

  if (!currentJob) return null;

  // If showVideoJd is true, only render the VideoJDDetailsSettings component
  if (showVideoJd) {
    return (
      <VideoJDDetailsSettings
        job={currentJob}
        onCloseOverride={() => {
          if (router && searchParams) {
            preserveTabParameter(router, searchParams);
          }
          // Also call the original onClose function
          handleClose();
        }}
      />
    );
  }

  // If showCultureFit is true, only render the CulturalFitDetailsDrawer component
  if (showCultureFit) {
    return (
      <CulturalFitDetailsDrawer
        job={currentJob}
        onClose={() => {
          if (router && searchParams) {
            preserveTabParameter(router, searchParams);
          }
          handleClose();
        }}
      />
    );
  }

  // If portal container is not available yet, return null
  if (!portalContainer) return null;

  // Content to be rendered in the portal
  const content = (
    <AnimatePresence mode="wait">
      <motion.div
        className="fixed inset-0 z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2, ease: 'easeInOut' }}
      >
        {/* Animated backdrop */}
        <motion.div
          className="fixed inset-0 backdrop-blur-sm z-40 pointer-events-auto"
          style={{ backgroundColor: 'var(--sidebar-bg)' }}
          onClick={handleClose}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2, ease: 'easeInOut' }}
        />

        {/* Animated panel */}
        <motion.div
          className={`fixed inset-y-0 right-0 border-l z-50 backdrop-blur-xl pointer-events-auto ${
            editMode ? 'w-[65vw]' : 'w-full lg:max-w-[50vw]'
          }`}
          style={{
            backgroundColor: 'var(--card-bg)',
            borderColor: 'var(--card-border)',
            willChange: 'transform',
          }}
          initial={{ x: '100%' }}
          animate={{ x: 0 }}
          exit={{ x: '100%' }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
            mass: 0.8,
          }}
        >
          {/* Header */}
          <div
            className="flex flex-col md:flex-row md:items-center justify-between p-4 md:p-6 border-b backdrop-blur-sm"
            style={{ borderColor: 'var(--card-border)' }}
          >
            <div className="flex items-center gap-2 mb-4 md:mb-0">
              <Briefcase className="w-6 h-6" style={{ color: 'var(--info-color)' }} />
              <h2 className="text-xl font-semibold" style={{ color: 'var(--foreground-color)' }}>
                Job Details
              </h2>
            </div>
            <div className="flex flex-wrap items-center gap-3 md:gap-4">
              {showEditOptions && (
                <>
                  {/* Left side buttons */}
                  <div className="flex flex-wrap items-center gap-2">
                    <div className="relative">
                      <button
                        id="publish-dropdown-button"
                        data-publish-button="true"
                        ref={publishDropdownRef}
                        onClick={async e => {
                          e.stopPropagation(); // Prevent event bubbling

                          // Only fetch connector statuses when opening the dropdown
                          if (!publishDropdownOpen && connectorStatuses.length === 0) {
                            await fetchConnectorStatusesFromStore();
                          }

                          setPublishDropdownOpen(!publishDropdownOpen);
                        }}
                        className="group relative flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ease-out disabled:opacity-50 disabled:cursor-not-allowed"
                        style={{
                          background: 'var(--button-primary-bg)',
                          color: 'var(--button-primary-text)',
                        }}
                      >
                        <span className="font-medium">
                          {currentJob?.isPublished ? 'Manage Publishing' : 'Publish to'}
                        </span>
                        <ChevronDown
                          className={`w-4 h-4 transition-transform duration-200 ${publishDropdownOpen ? 'rotate-180' : ''}`}
                        />
                      </button>

                      {/* Publish dropdown */}
                      <PublishDropdownPortal
                        isOpen={publishDropdownOpen}
                        platforms={platforms}
                        selectedPlatforms={selectedPlatforms}
                        togglePlatformSelection={togglePlatformSelection}
                        handlePublish={handlePublish}
                        handleUnpublish={handleUnpublish}
                        isPublishing={isPublishing}
                        isUnpublishing={isUnpublishing}
                        isPublished={currentJob?.isPublished}
                        buttonRef={publishDropdownRef}
                        onClose={() => setPublishDropdownOpen(false)}
                      />
                    </div>

                    {/* Generate Content Button */}
                    <GenerateContentButton
                      jobId={currentJob.id}
                      initialContent={currentJob.finalDraft || ''}
                      buttonLabel="Generate JD"
                    />
                  </div>

                  {/* Separator */}
                  <div className="h-6 w-px" style={{ backgroundColor: 'var(--card-border)' }} />

                  {/* Right side buttons */}
                  <div className="flex items-center gap-2">
                    {editMode ? (
                      <button
                        onClick={handleSave}
                        className="flex items-center gap-1 px-3 py-2 rounded-md transition-colors disabled:opacity-50"
                        style={{
                          background: 'var(--button-primary-bg)',
                          color: 'var(--button-primary-text)',
                        }}
                        disabled={isSaving}
                      >
                        {isSaving ? (
                          <>
                            <span className="animate-spin">⏳</span>
                            <span>Saving...</span>
                          </>
                        ) : (
                          <>
                            <Save className="w-4 h-4" />
                            <span>Save</span>
                          </>
                        )}
                      </button>
                    ) : (
                      <button
                        onClick={() => {
                          // Get the job ID to edit
                          const jobId = fullJobData?.id || currentJob?.id;

                          if (jobId && router) {
                            // Navigate to unified job view
                            router.push(`/jobs/${jobId}/manage`);
                          } else if (!router) {
                            showToast({
                              message: 'Navigation not available in this context',
                              isSuccess: false,
                            });
                          } else {
                            showToast({
                              message: 'Unable to edit job: Job ID not found',
                              isSuccess: false,
                            });
                          }
                        }}
                        className="flex items-center gap-1 px-3 py-2 rounded-md transition-colors"
                        style={{
                          backgroundColor: 'var(--button-secondary-bg)',
                          color: 'var(--button-secondary-text)',
                        }}
                      >
                        <Edit className="w-4 h-4" />
                        <span>Edit</span>
                      </button>
                    )}
                  </div>
                </>
              )}

              {/* Close button - always visible */}
              <button
                title=""
                onClick={handleClose}
                className="p-2 rounded-lg transition-colors"
                style={{
                  backgroundColor: 'var(--button-secondary-bg)',
                  color: 'var(--button-secondary-text)',
                }}
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="overflow-y-auto h-[calc(100vh-80px)] p-4 md:p-6">
            {/* Show loader while fetching full job details or waiting for job data */}
            {isLoadingJobDetails || isWaitingForJobData ? (
              <div className="h-full flex items-center justify-center">
                <ColorfulSmokeyOrbLoader text="Loading job details..." />
              </div>
            ) : editMode ? (
              <JobEditView job={editedJob} onFieldChange={handleFieldChange} />
            ) : (
              <>
                {/* Company Info */}
                {currentJob?.companyName && (
                  <div className="flex flex-wrap items-center gap-4 mb-8">
                    <div
                      className="rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold"
                      style={{
                        backgroundColor: 'var(--button-secondary-bg)',
                        color: 'var(--foreground-color)',
                      }}
                    >
                      {currentJob.companyName.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <h1
                        className="text-2xl font-bold mb-1"
                        style={{ color: 'var(--foreground-color)' }}
                      >
                        {currentJob.jobType}
                      </h1>
                      <p style={{ color: 'var(--foreground-color)', opacity: 0.7 }}>
                        {currentJob.companyName}
                      </p>
                    </div>
                  </div>
                )}

                {/* Job Details Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 mb-8">
                  <div className="space-y-1">
                    <h3
                      className="text-sm font-medium flex items-center gap-1"
                      style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                    >
                      <MapPin className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                      Location
                    </h3>
                    <p style={{ color: 'var(--foreground-color)' }}>
                      {currentJob.location && Array.isArray(currentJob.location)
                        ? currentJob.location
                            .map(loc =>
                              loc
                                .split(' ')
                                .map(
                                  word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                                )
                                .join(' ')
                            )
                            .join(', ')
                        : 'Not specified'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h3
                      className="text-sm font-medium flex items-center gap-1"
                      style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                    >
                      <DollarSign className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                      Salary Range
                    </h3>
                    <p style={{ color: 'var(--foreground-color)' }}>
                      {currentJob?.salaryRange || 'Not specified'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h3
                      className="text-sm font-medium flex items-center gap-1"
                      style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                    >
                      <Calendar className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                      Experience
                    </h3>
                    <p style={{ color: 'var(--foreground-color)' }}>
                      {currentJob?.experience
                        ? currentJob.experience.charAt(0).toUpperCase() +
                          currentJob.experience.slice(1).toLowerCase()
                        : 'Not specified'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h3
                      className="text-sm font-medium flex items-center gap-1"
                      style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                    >
                      <Users className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                      Type of Hiring
                    </h3>
                    <p style={{ color: 'var(--foreground-color)' }}>
                      {currentJob?.typeOfHiring
                        ? currentJob.typeOfHiring
                            .toLowerCase()
                            .split('_')
                            .map(
                              (word: string) =>
                                word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                            )
                            .join(' ')
                        : 'Not specified'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h3
                      className="text-sm font-medium flex items-center gap-1"
                      style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                    >
                      <Clock className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                      Type of Job
                    </h3>
                    <p style={{ color: 'var(--foreground-color)' }}>
                      {currentJob.typeOfJob
                        ?.toLowerCase()
                        .split('_')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                        .join(' ')}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <h3
                      className="text-sm font-medium flex items-center gap-1"
                      style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                    >
                      <Briefcase className="w-4 h-4" style={{ color: 'var(--info-color)' }} />
                      Industry
                    </h3>
                    <p style={{ color: 'var(--foreground-color)' }}>
                      {currentJob.department
                        ?.split(' ')
                        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                        .join(' ')}
                    </p>
                  </div>
                </div>

                {/* Description */}
                {currentJob?.companyDescription && (
                  <div className="mb-8">
                    <h3
                      className="text-lg font-semibold mb-3 flex items-center gap-2"
                      style={{ color: 'var(--foreground-color)' }}
                    >
                      <Building className="w-5 h-5" style={{ color: 'var(--info-color)' }} />
                      About the Company
                    </h3>
                    <p style={{ color: 'var(--foreground-color)', opacity: 0.8 }}>
                      {currentJob.companyDescription}
                    </p>
                  </div>
                )}

                {/* Responsibilities */}
                {currentJob?.jobResponsibilities &&
                  Array.isArray(currentJob.jobResponsibilities) &&
                  currentJob.jobResponsibilities.length > 0 && (
                    <div className="mb-8">
                      <h3
                        className="text-lg font-semibold mb-3 flex items-center gap-2"
                        style={{ color: 'var(--foreground-color)' }}
                      >
                        <LayoutList className="w-5 h-5" style={{ color: 'var(--info-color)' }} />
                        Responsibilities
                      </h3>
                      <ul className="space-y-2">
                        {currentJob.jobResponsibilities.map(
                          (responsibility: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-2"
                              style={{ color: 'var(--foreground-color)', opacity: 0.8 }}
                            >
                              <ListChecks className="w-4 h-4 mt-0.5" style={{ opacity: 0.5 }} />
                              <span>
                                {responsibility?.charAt(0).toUpperCase() + responsibility?.slice(1)}
                              </span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  )}

                {/* Skills */}
                {currentJob?.skills &&
                  Array.isArray(currentJob.skills) &&
                  currentJob.skills.length > 0 && (
                    <div className="mb-8">
                      <h3
                        className="text-lg font-semibold mb-3 flex items-center gap-2"
                        style={{ color: 'var(--foreground-color)' }}
                      >
                        <BadgeCheck className="w-5 h-5" style={{ color: 'var(--info-color)' }} />
                        Required Skills
                      </h3>
                      <ul className="space-y-2">
                        {currentJob.skills.map((skill: string, index: number) => (
                          <li
                            key={index}
                            className="flex items-start gap-2"
                            style={{ color: 'var(--foreground-color)', opacity: 0.8 }}
                          >
                            <ShieldCheck className="w-4 h-4 mt-0.5" style={{ opacity: 0.5 }} />
                            <span>{skill}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {/* Education & Languages */}
                {((currentJob?.education &&
                  Array.isArray(currentJob.education) &&
                  currentJob.education.length > 0) ||
                  (currentJob?.language &&
                    Array.isArray(currentJob.language) &&
                    currentJob.language.length > 0)) && (
                  <div className="mb-8">
                    <h3
                      className="text-lg font-semibold mb-3 flex items-center gap-2"
                      style={{ color: 'var(--foreground-color)' }}
                    >
                      <BookOpen className="w-5 h-5" style={{ color: 'var(--info-color)' }} />
                      Education & Languages
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {currentJob?.education &&
                        Array.isArray(currentJob.education) &&
                        currentJob.education.length > 0 && (
                          <div>
                            <h4
                              className="text-sm font-medium mb-2 flex items-center gap-1"
                              style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                            >
                              <GraduationCap
                                className="w-4 h-4"
                                style={{ color: 'var(--info-color)' }}
                              />
                              Education
                            </h4>
                            <ul className="space-y-1">
                              {currentJob.education.map((edu: string, index: number) => (
                                <li
                                  key={index}
                                  className="flex items-start gap-2"
                                  style={{ color: 'var(--foreground-color)', opacity: 0.8 }}
                                >
                                  <span style={{ opacity: 0.5 }}>•</span>
                                  {edu}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      {currentJob?.language &&
                        Array.isArray(currentJob.language) &&
                        currentJob.language.length > 0 && (
                          <div>
                            <h4
                              className="text-sm font-medium mb-2 flex items-center gap-1"
                              style={{ color: 'var(--foreground-color)', opacity: 0.7 }}
                            >
                              <Languages
                                className="w-4 h-4"
                                style={{ color: 'var(--info-color)' }}
                              />
                              Languages
                            </h4>
                            <ul className="space-y-1">
                              {currentJob.language.map((lang: string, index: number) => (
                                <li
                                  key={index}
                                  className="flex items-start gap-2"
                                  style={{ color: 'var(--foreground-color)', opacity: 0.8 }}
                                >
                                  <span style={{ opacity: 0.5 }}>•</span>
                                  {lang}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                    </div>
                  </div>
                )}

                {/* Soft Skills */}
                {currentJob?.softSkills &&
                  Array.isArray(currentJob.softSkills) &&
                  currentJob.softSkills.length > 0 && (
                    <div className="mb-8">
                      <h3
                        className="text-lg font-semibold mb-3 flex items-center gap-2"
                        style={{ color: 'var(--foreground-color)' }}
                      >
                        <Lightbulb className="w-5 h-5" style={{ color: 'var(--info-color)' }} />
                        Soft Skills
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {currentJob.softSkills.map((skill: string, index: number) => (
                          <span
                            key={index}
                            className="px-3 py-1 rounded-full text-sm"
                            style={{
                              backgroundColor: 'var(--button-secondary-bg)',
                              color: 'var(--button-secondary-text)',
                            }}
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                {/* Benefits */}
                {currentJob?.benefits &&
                  Array.isArray(currentJob.benefits) &&
                  currentJob.benefits.length > 0 && (
                    <div className="mb-8">
                      <h3
                        className="text-lg font-semibold mb-3 flex items-center gap-2"
                        style={{ color: 'var(--foreground-color)' }}
                      >
                        <HeartHandshake
                          className="w-5 h-5"
                          style={{ color: 'var(--info-color)' }}
                        />
                        Benefits
                      </h3>
                      <ul className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {currentJob.benefits.map((benefit: string, index: number) => (
                          <li
                            key={index}
                            className="flex items-center gap-2"
                            style={{ color: 'var(--foreground-color)', opacity: 0.8 }}
                          >
                            <ShieldCheck className="w-4 h-4" style={{ opacity: 0.5 }} />
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {/* Career Growth */}
                {currentJob?.careerGrowth &&
                  Array.isArray(currentJob.careerGrowth) &&
                  currentJob.careerGrowth.length > 0 && (
                    <div className="mb-8">
                      <h3
                        className="text-lg font-semibold mb-3 flex items-center gap-2"
                        style={{ color: 'var(--foreground-color)' }}
                      >
                        <TrendingUp className="w-5 h-5" style={{ color: 'var(--info-color)' }} />
                        Career Growth
                      </h3>
                      <ul className="space-y-2">
                        {currentJob.careerGrowth.map((item: string, index: number) => (
                          <li
                            key={index}
                            className="flex items-start gap-2"
                            style={{ color: 'var(--foreground-color)', opacity: 0.8 }}
                          >
                            <TrendingUp className="w-4 h-4 mt-0.5" style={{ opacity: 0.5 }} />
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
              </>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );

  // Use ReactDOM.createPortal to render the content to the portal container
  return ReactDOM.createPortal(content, portalContainer);
};

export default PostedJobDetails;
