'use client';

import React, { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

import { SIDEBAR } from '@/constants/layout';
import SocialMediaLinks, { SocialMediaLink } from './SocialMediaLinks';

// Map of page paths to images
const pageImageConfigs: Record<string, string> = {
  '/jobs': '/images/landing/open-jobs/open-jobs-7.webp',
  '/talent-hub': '/images/insights/team_collaboration.png',
  '/company-settings': '/images/insights/innovation_technology_revised.png',
  '/marketplace': '/images/landing/open-jobs/open-jobs-2.webp',
  default: '/images/landing/open-jobs/open-jobs-5.webp',
};

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  className?: string;
  // New props for the image variant
  variant?: 'default' | 'image' | 'fullwidth';
  imageSrc?: string;
  imageHeight?: string;
  overlayColor?: string;
  // Social media links
  socialLinks?: SocialMediaLink[];
  // For custom content like stats
  children?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  icon: Icon,
  className = '',
  variant = 'default',
  imageSrc,
  imageHeight = 'h-48',
  overlayColor = 'from-purple-900/80',
  socialLinks = [],
  children,
}) => {
  const pathname = usePathname();
  const [currentImage, setCurrentImage] = useState(
    imageSrc || pageImageConfigs[pathname] || pageImageConfigs.default
  );
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Get sidebar state from localStorage and listen for changes
  useEffect(() => {
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarExpanded');
      if (savedState !== null) {
        setSidebarExpanded(JSON.parse(savedState));
      }
    };

    // Initial check
    checkSidebarState();

    // Listen for storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sidebarExpanded') {
        checkSidebarState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also check periodically for same-tab changes
    const interval = setInterval(checkSidebarState, 100);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  // Handle image transitions based on pathname
  useEffect(() => {
    if (variant === 'fullwidth') {
      const newImage = imageSrc || pageImageConfigs[pathname] || pageImageConfigs.default;

      if (newImage !== currentImage) {
        setIsTransitioning(true);
        setTimeout(() => {
          setCurrentImage(newImage);
          setIsTransitioning(false);
        }, 300);
      }
    }
  }, [pathname, imageSrc, currentImage, variant]);

  // Render social media links
  const renderSocialLinks = () => {
    if (!socialLinks || socialLinks.length === 0) return null;

    return <SocialMediaLinks links={socialLinks} size="md" variant="rounded" className="mt-2" />;
  };

  // Default header variant
  if (variant === 'default') {
    return (
      <div className={`mb-6 w-full max-w-7xl mx-auto ${className}`}>
        <div className="flex items-center">
          {Icon && <Icon className="w-4 h-4 text-pink-400 mr-2" />}
          <h1 className="text-md font-medium text-white/90 mb-1">{title}</h1>
        </div>
        {description && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
            className="text-sm text-white/70"
          >
            {description}
          </motion.p>
        )}
        {renderSocialLinks()}
      </div>
    );
  }

  // Original image variant (contained)
  if (variant === 'image') {
    return (
      <div className={`mb-6 w-full max-w-7xl mx-auto overflow-hidden rounded-xl ${className}`}>
        <div className={`relative ${imageHeight} w-full group`}>
          {currentImage && (
            <Image
              src={currentImage}
              alt={title}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
            />
          )}
          <div className={`absolute inset-0 bg-gradient-to-t ${overlayColor} to-transparent`}></div>

          <div className="absolute bottom-0 left-0 right-0 p-4 z-10 text-over-image">
            <div className="flex items-center">
              {Icon && <Icon className="w-5 h-5 text-pink-400 mr-2" />}
              <h1 className="text-md font-medium text-white-force mb-1">{title}</h1>
            </div>
            {description && (
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="text-sm text-white-force-80 max-w-3xl"
              >
                {description}
              </motion.p>
            )}
            {renderSocialLinks()}
          </div>
        </div>
      </div>
    );
  }

  // Full-width header variant (like referral partner)
  return (
    <div className={`fixed top-0 left-0 right-0 h-[280px] overflow-hidden ${className} z-0`}>
      {/* Background image with transitions */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentImage}
          initial={{ opacity: 0, scale: 1.05 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className="absolute inset-0 z-0"
        >
          <Image
            src={currentImage}
            alt="Hero Background"
            fill
            priority
            quality={100}
            className="object-cover object-[center_25%]"
            sizes="100vw"
          />
        </motion.div>
      </AnimatePresence>

      {/* Very faint overlay */}
      <div className="absolute inset-0 z-5 bg-black/10" />

      {/* Additional decorative layers */}
      <div className="absolute inset-0 z-10">
        {/* New gradient: teal with purple tinge -> teal -> purple-teal blend -> transparent/5 -> transparent */}
        <div className="absolute inset-0 bg-gradient-to-t from-teal-950/80 via-15% via-teal-800/60 via-30% via-purple-700/40 via-45% via-cyan-600/20 via-60% via-transparent/5 via-75% to-transparent" />

        {/* Dynamic corner accents - teal and purple blend */}
        <motion.div
          className="absolute top-0 right-0 w-64 h-64 bg-gradient-radial from-purple-600/15 via-transparent to-transparent blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />

        <motion.div
          className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-radial from-teal-600/15 via-transparent to-transparent blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />
      </div>

      {/* Feathered blend divider at bottom */}
      <div className="absolute bottom-0 left-0 right-0 h-24 z-15 pointer-events-none">
        {/* Multiple gradient layers for smooth feathering */}
        <div className="absolute inset-0 bg-gradient-to-t from-background via-background/95 via-30% via-background/80 via-50% via-background/50 via-70% to-transparent" />
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent via-50% to-background/30" />
        {/* Noise texture overlay for smoother blend */}
        <div className="absolute inset-0 opacity-[0.015] mix-blend-soft-light">
          <svg width="100%" height="100%">
            <filter id="noiseFilter">
              <feTurbulence
                type="turbulence"
                baseFrequency="0.9"
                numOctaves="4"
                stitchTiles="stitch"
              />
            </filter>
            <rect width="100%" height="100%" filter="url(#noiseFilter)" />
          </svg>
        </div>
      </div>

      {/* Content overlay */}
      <div className="relative z-20 w-full h-full flex items-end">
        <div
          className="w-full px-4 sm:px-6 lg:px-8 pb-6"
          style={{
            paddingLeft:
              typeof window !== 'undefined' && window.innerWidth >= 1024
                ? `calc(1rem + ${sidebarExpanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH})`
                : '1rem',
          }}
        >
          <div className="flex items-end justify-between">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex-1"
            >
              <div className="flex items-center mb-2">
                {Icon && <Icon className="w-8 h-8 text-pink-400 mr-3" />}
                <h1 className="text-3xl sm:text-4xl font-bold text-white">{title}</h1>
              </div>
              {description && <p className="text-lg text-white/80 max-w-3xl">{description}</p>}
              {renderSocialLinks()}
            </motion.div>

            {/* Custom content (stats, buttons, etc.) */}
            {children && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="hidden lg:block ml-8"
              >
                {children}
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageHeader;
