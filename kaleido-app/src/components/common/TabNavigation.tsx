'use client';

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

export interface TabItem {
  id: string;
  label: string;
  icon?: React.ReactElement | LucideIcon;
}

interface TabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: 'default' | 'gradient';
  gradientColors?: {
    from: string;
    to: string;
  };
  className?: string;
  updateUrl?: boolean;
  queryParamName?: string;
  showBorder?: boolean;
  showSeparators?: boolean;
  mobileCollapse?: boolean;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  variant = 'gradient',
  gradientColors = {
    from: 'from-purple-600',
    to: 'to-pink-600',
  },
  className = '',
  updateUrl = false,
  queryParamName = 'tab',
  showBorder = true,
  showSeparators = true,
  mobileCollapse = true,
}) => {
  const router = useRouter();

  const handleTabChange = (tabId: string) => {
    onTabChange(tabId);

    if (updateUrl && typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set(queryParamName, tabId);
      const newUrl = `${currentPath}?${searchParams.toString()}`;
      window.history.replaceState(null, '', newUrl);
    }
  };

  const getTabClasses = (isActive: boolean) => {
    const baseClasses = `
      relative px-2 sm:px-4 h-full flex items-center gap-1 sm:gap-2 
      text-xs sm:text-sm font-medium transition-all whitespace-nowrap flex-shrink-0
    `;

    if (isActive) {
      return `${baseClasses} text-primary`;
    }

    return `${baseClasses} text-muted-foreground hover:text-foreground hover:bg-muted/50`;
  };

  const renderIcon = (icon: React.ReactElement | LucideIcon | undefined) => {
    if (!icon) return null;

    if (React.isValidElement(icon)) {
      return React.cloneElement(icon as React.ReactElement, {
        className: 'w-3.5 sm:w-4 h-3.5 sm:h-4',
      });
    }

    const IconComponent = icon as LucideIcon;
    return <IconComponent className="w-3.5 sm:w-4 h-3.5 sm:h-4" />;
  };

  return (
    <div className={`flex items-center h-full ${className}`}>
      <div className="flex overflow-x-auto h-full [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        {tabs.map((tab, index) => (
          <React.Fragment key={tab.id}>
            {showSeparators && index > 0 && (
              <div className="w-px bg-gradient-to-b from-transparent via-gray-300/5 to-transparent self-center h-8 hidden sm:block" />
            )}
            <button
              onClick={() => handleTabChange(tab.id)}
              className={getTabClasses(activeTab === tab.id)}
            >
              {/* Gradient background for active tab */}
              {variant === 'gradient' && activeTab === tab.id && (
                <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-600/10 to-pink-600/20" />
              )}

              {/* Content */}
              <span className="relative z-10 flex items-center gap-1 sm:gap-2">
                {renderIcon(tab.icon)}
                {mobileCollapse ? (
                  <>
                    <span className="hidden sm:inline">{tab.label}</span>
                    <span className="sm:hidden">
                      {activeTab === tab.id
                        ? tab.label
                        : tab.label
                            .split(' ')
                            .map(word => word[0])
                            .join('')}
                    </span>
                  </>
                ) : (
                  <span>{tab.label}</span>
                )}
              </span>

              {/* Bottom border for active tab */}
              {showBorder && activeTab === tab.id && (
                <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-600 to-pink-600" />
              )}
            </button>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};
