'use client';

import React from 'react';
import { User, Mail, Phone, MapPin, Globe, Linkedin, CheckCircle2 } from 'lucide-react';

interface ProfileReadOnlyViewProps {
  formData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedinUrl?: string;
    portfolioUrl?: string;
    [key: string]: any;
  };
}

interface FieldCardProps {
  label: string;
  value: any;
  icon: React.ReactNode;
  required?: boolean;
  isEmpty?: boolean;
  colSpan?: number;
}

const FieldCard: React.FC<FieldCardProps> = ({ 
  label, 
  value, 
  icon, 
  required, 
  isEmpty,
  colSpan = 1 
}) => {
  const displayValue = isEmpty ? null : value;
  
  return (
    <div className={`group relative bg-gradient-to-br from-gray-50/50 to-white rounded-xl p-4 border border-gray-100 hover:border-purple-200 hover:shadow-sm transition-all duration-200 ${colSpan === 2 ? 'md:col-span-2' : ''}`}>
      <div className="flex items-start gap-3">
        {icon && (
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-indigo-100 flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-200">
            <div className="text-purple-600">
              {React.cloneElement(icon as React.ReactElement, { className: "w-5 h-5" })}
            </div>
          </div>
        )}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <p className="text-[10px] font-semibold text-purple-600 uppercase tracking-wider">
              {label}
            </p>
            {required && !isEmpty && (
              <CheckCircle2 className="w-3 h-3 text-purple-500" />
            )}
          </div>
          {displayValue ? (
            <p className="text-sm font-medium text-gray-900 break-words">
              {displayValue}
            </p>
          ) : (
            <p className="text-sm text-gray-400 italic">Not provided</p>
          )}
        </div>
      </div>
    </div>
  );
};

export const ProfileReadOnlyView: React.FC<ProfileReadOnlyViewProps> = ({ formData }) => {
  // Format URL display
  const formatUrl = (url: string) => {
    if (!url) return null;
    // For LinkedIn URLs, show as "Connected"
    if (url.includes('linkedin.com')) return 'Connected';
    // For portfolio URLs, show as "Available"
    return 'Available';
  };

  return (
    <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Name Fields - Same Row */}
        <FieldCard
          label="First Name"
          value={formData.firstName}
          icon={<User />}
          required
          isEmpty={!formData.firstName}
        />
        <FieldCard
          label="Last Name"
          value={formData.lastName}
          icon={<User />}
          required
          isEmpty={!formData.lastName}
        />
        
        {/* Email - Full Width */}
        <FieldCard
          label="Email Address"
          value={formData.email}
          icon={<Mail />}
          required
          isEmpty={!formData.email}
          colSpan={2}
        />
        
        {/* Phone & Location - Same Row */}
        <FieldCard
          label="Phone Number"
          value={formData.phone}
          icon={<Phone />}
          isEmpty={!formData.phone}
        />
        <FieldCard
          label="Location"
          value={formData.location}
          icon={<MapPin />}
          isEmpty={!formData.location}
        />
        
        {/* Social Links - Same Row */}
        <FieldCard
          label="LinkedIn Profile"
          value={formatUrl(formData.linkedinUrl)}
          icon={<Linkedin />}
          isEmpty={!formData.linkedinUrl}
        />
        <FieldCard
          label="Portfolio/Website"
          value={formatUrl(formData.portfolioUrl)}
          icon={<Globe />}
          isEmpty={!formData.portfolioUrl}
        />
      </div>
    </div>
  );
};

export default ProfileReadOnlyView;