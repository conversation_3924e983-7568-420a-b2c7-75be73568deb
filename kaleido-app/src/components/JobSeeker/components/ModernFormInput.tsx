'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle2, AlertCircle, Eye, EyeOff } from 'lucide-react';

interface ModernFormInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  type?: 'text' | 'email' | 'tel' | 'url' | 'password';
  placeholder?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  success?: boolean;
  className?: string;
  disabled?: boolean;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  validation?: (value: string) => string | null;
  size?: 'sm' | 'md' | 'lg';
}

export const ModernFormInput: React.FC<ModernFormInputProps> = ({
  label,
  value,
  onChange,
  type = 'text',
  placeholder,
  required = false,
  error,
  hint,
  success,
  className = '',
  disabled = false,
  prefix,
  suffix,
  validation,
  size = 'md',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    if (validation) {
      const validationError = validation(newValue);
      setLocalError(validationError);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    if (validation && value) {
      const validationError = validation(value);
      setLocalError(validationError);
    }
  };

  const displayError = error || localError;
  const isValid = success || (value && !displayError && !localError);
  const inputType = type === 'password' && showPassword ? 'text' : type;
  
  // Size classes
  const sizeClasses = {
    sm: {
      label: 'text-xs',
      input: 'py-2 text-sm',
      icon: 'w-3.5 h-3.5',
      message: 'text-xs'
    },
    md: {
      label: 'text-sm',
      input: 'py-3 text-base',
      icon: 'w-4 h-4',
      message: 'text-sm'
    },
    lg: {
      label: 'text-base',
      input: 'py-4 text-lg',
      icon: 'w-5 h-5',
      message: 'text-base'
    }
  };
  
  const currentSize = sizeClasses[size];

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label */}
      <div className="flex items-center justify-between">
        <label className={`block ${currentSize.label} font-medium text-gray-700`}>
          {label}
        </label>
        {required && (
          <span className={`${size === 'sm' ? 'text-[10px]' : 'text-xs'} text-purple-400 font-normal`}>required</span>
        )}
      </div>

      {/* Input Container */}
      <div className="relative">
        <div
          className={`relative flex items-center border rounded-lg transition-all duration-200 ${
            isFocused
              ? 'border-purple-500 ring-2 ring-purple-100'
              : displayError
              ? 'border-red-500'
              : isValid
              ? 'border-purple-400'
              : 'border-gray-300 hover:border-gray-400'
          } ${
            disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'
          }`}
        >
          {/* Prefix */}
          {prefix && (
            <div className={`pl-3 pr-2 text-gray-500`}>
              {React.cloneElement(prefix as React.ReactElement, { className: currentSize.icon })}
            </div>
          )}

          {/* Input */}
          <input
            type={inputType}
            value={value}
            onChange={handleChange}
            onFocus={() => setIsFocused(true)}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            className={`flex-1 px-3 ${currentSize.input} bg-transparent focus:outline-none text-gray-900 placeholder-gray-400 ${
              prefix ? 'pl-0' : ''
            } ${
              suffix || type === 'password' || isValid ? 'pr-10' : ''
            }`}
          />

          {/* Password Toggle */}
          {type === 'password' && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              {showPassword ? (
                <EyeOff className={currentSize.icon} />
              ) : (
                <Eye className={currentSize.icon} />
              )}
            </button>
          )}

          {/* Validation Icon */}
          {!type.includes('password') && (suffix || displayError || isValid) && (
            <div className="absolute right-3 flex items-center">
              {suffix || (
                <>
                  {displayError ? (
                    <AlertCircle className={`${currentSize.icon} text-red-500`} />
                  ) : isValid ? (
                    <CheckCircle2 className={`${currentSize.icon} text-purple-400`} />
                  ) : null}
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Messages */}
      <AnimatePresence>
        {(displayError || hint) && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {displayError ? (
              <p className={`${currentSize.message} text-red-600 flex items-center gap-2`}>
                <AlertCircle className={`${size === 'sm' ? 'w-3 h-3' : 'w-4 h-4'}`} />
                {displayError}
              </p>
            ) : hint ? (
              <p className={`${currentSize.message} text-gray-500`}>{hint}</p>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export const ModernTextArea: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  rows?: number;
  maxLength?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({
  label,
  value,
  onChange,
  placeholder,
  required = false,
  error,
  hint,
  rows = 4,
  maxLength,
  className = '',
  size = 'md',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const characterCount = value.length;
  const isNearLimit = maxLength && characterCount > maxLength * 0.8;
  
  // Size classes
  const sizeClasses = {
    sm: {
      label: 'text-xs',
      textarea: 'py-2 text-sm',
      message: 'text-xs'
    },
    md: {
      label: 'text-sm',
      textarea: 'py-3 text-base',
      message: 'text-sm'
    },
    lg: {
      label: 'text-base',
      textarea: 'py-4 text-lg',
      message: 'text-base'
    }
  };
  
  const currentSize = sizeClasses[size];

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label */}
      {label && (
        <div className="flex items-center justify-between">
          <label className={`block ${currentSize.label} font-medium text-gray-700`}>
            {label}
          </label>
          {required && (
            <span className={`${size === 'sm' ? 'text-[10px]' : 'text-xs'} text-purple-400 font-normal`}>required</span>
          )}
        </div>
      )}

      {/* TextArea */}
      <div
        className={`relative border rounded-lg transition-all duration-200 ${
          isFocused
            ? 'border-purple-500 ring-2 ring-purple-100'
            : error
            ? 'border-red-500'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          rows={rows}
          maxLength={maxLength}
          className={`w-full px-3 ${currentSize.textarea} bg-transparent focus:outline-none text-gray-900 placeholder-gray-400 resize-none`}
        />
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between">
        <div>
          <AnimatePresence>
            {(error || hint) && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                {error ? (
                  <p className={`${currentSize.message} text-red-600 flex items-center gap-2`}>
                    <AlertCircle className={`${size === 'sm' ? 'w-3 h-3' : 'w-4 h-4'}`} />
                    {error}
                  </p>
                ) : hint ? (
                  <p className={`${currentSize.message} text-gray-500`}>{hint}</p>
                ) : null}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        {maxLength && (
          <span
            className={`text-xs ${
              isNearLimit
                ? characterCount >= maxLength
                  ? 'text-red-500'
                  : 'text-amber-500'
                : 'text-gray-400'
            }`}
          >
            {characterCount}/{maxLength}
          </span>
        )}
      </div>
    </div>
  );
};

export default ModernFormInput;