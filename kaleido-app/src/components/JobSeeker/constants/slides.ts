import React from 'react';

import {
  Briefcase,
  Calendar,
  Camera,
  FileText,
  FileUp,
  Globe,
  GraduationCap,
  Heart,
  Lock,
  Shield,
  Target,
  User,
} from 'lucide-react';

import VideoComponent from '../../VideoComponent';
import {
  EducationStep,
  ExperienceStep,
  IdVerificationStep,
  PersonalInformationStep,
  PortfolioStep,
  PreferencesStep,
  PrivacySettingsStep,
  ProfessionalSummaryStep,
  ResumeUploadStep,
  ValuesStep,
  WorkAvailabilityStep,
} from '../steps';
import { BaseStepProps } from '../types';

interface StepComponentProps extends BaseStepProps {
  uploading?: boolean;
  setUploading?: (uploading: boolean) => void;
}

type StepComponent = React.ComponentType<StepComponentProps>;

export const setupSlides = [
  {
    id: 'resume',
    title: 'Upload Your CV/Resume',
    subtitle: 'Let us help you fill out your profile automatically',
    component: ResumeUploadStep as unknown as StepComponent,
    image: '/images/job-seeker-setup/new/profile-1.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(168, 85, 247, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: FileUp,
    imgIcon: '/images/job-seeker-setup/icons/document.png',
    funFact:
      '75% of recruiters use Applicant Tracking Systems that scan CVs - making a properly formatted resume crucial for getting past the first screening.',
  },
  {
    id: 'basics',
    title: 'Personal Information',
    subtitle: 'Review and complete your details',
    component: PersonalInformationStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-2.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(236, 72, 153, 0.3), rgba(219, 39, 119, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: User,
    imgIcon: '/images/job-seeker-setup/icons/personal-info.png',
    funFact:
      'Your email address is often your first impression - 76% of resumes are rejected due to unprofessional email addresses.',
  },
  {
    id: 'values',
    title: 'Personal Values',
    subtitle: 'Share what matters most to you',
    component: ValuesStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-3.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(139, 69, 19, 0.3), rgba(245, 101, 101, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: Heart,
    imgIcon: '/images/job-seeker-setup/icons/personal-info.webp',
    funFact:
      '89% of hiring managers say that culture fit is as important as skills when making hiring decisions - sharing your values helps find the right match.',
  },
  {
    id: 'professional',
    title: 'Professional Summary',
    subtitle: 'Share your professional background',
    component: ProfessionalSummaryStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-4.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(5, 150, 105, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: FileText,
    imgIcon: '/images/job-seeker-setup/icons/document.png',
    funFact:
      'Recruiters spend an average of 7.4 seconds reviewing a resume - a compelling professional summary can make those seconds count!',
  },
  {
    id: 'experience',
    title: 'Work Experience',
    subtitle: 'Tell us about your work history',
    component: ExperienceStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-5.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: Briefcase,
    imgIcon: '/images/job-seeker-setup/icons/experience.webp',
    funFact:
      'Using action verbs to describe your experience can increase interview chances by 140%. Words like "achieved," "improved," and "developed" grab attention.',
  },
  {
    id: 'education',
    title: 'Education & Certifications',
    subtitle: 'Share your educational background',
    component: EducationStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-6.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(168, 85, 247, 0.3), rgba(147, 51, 234, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: GraduationCap,
    imgIcon: '/images/job-seeker-setup/icons/job-seeker-bg-2.webp',
    funFact:
      'Recent studies show that 86% of employers believe professional certifications are a key differentiator when choosing between similar candidates.',
  },
  {
    id: 'portfolio',
    title: 'Portfolio & Links',
    subtitle: 'Showcase your work (optional)',
    component: PortfolioStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-7.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(245, 158, 11, 0.3), rgba(217, 119, 6, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: Globe,
    imgIcon: '/images/job-seeker-setup/icons/links.png',
    isOptional: true,
    funFact:
      'Candidates with online portfolios or professional social media profiles are 71% more likely to get called for an interview.',
  },
  {
    id: 'preferences',
    title: 'Job Preferences',
    subtitle: `Tell us what you're looking for`,
    component: PreferencesStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-8.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(239, 68, 68, 0.3), rgba(220, 38, 38, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: Target,
    imgIcon: '/images/job-seeker-setup/icons/nav.png',
    funFact:
      'Being clear about your preferences can save time - 83% of recruiters say finding candidates with matching preferences is their biggest challenge.',
  },
  {
    id: 'availability',
    title: 'Work Availability',
    subtitle: 'Let employers know when you can start',
    component: WorkAvailabilityStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-9.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(14, 165, 233, 0.3), rgba(2, 132, 199, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: Calendar,
    imgIcon: '/images/job-seeker-setup/icons/document.png',
    funFact:
      'Companies take an average of 24 days to hire - knowing your availability helps them plan their recruitment timeline better.',
  },
  {
    id: 'videoIntro',
    title: 'Video Introduction',
    subtitle: 'Introduce yourself with a short video',
    component: VideoComponent as StepComponent,
    image: '/images/job-seeker-setup/new/profile-10.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(124, 58, 237, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: Camera,
    imgIcon: '/images/job-seeker-setup/icons/video.png',
    funFact:
      'Profiles with video introductions get 34% more views and candidates are 2x more likely to be contacted by employers.',
  },
  {
    id: 'idVerification',
    title: 'Identity Verification',
    subtitle: 'Upload a valid government-issued photo ID',
    component: IdVerificationStep as unknown as StepComponent,
    image: '/images/job-seeker-setup/new/profile-11.webp',
    icon: Shield,
    imgIcon: '/images/job-seeker-setup/icons/id-verify.webp',
    acceptedDocuments: [
      {
        type: 'passport',
        label: 'Passport',
        description: 'Current and valid passport',
      },
      {
        type: 'driverLicense',
        label: "Driver's License",
        description: "Valid driver's license with photo",
      },
      {
        type: 'nationalId',
        label: 'National ID Card',
        description: 'Government-issued ID card',
      },
      {
        type: 'residencePermit',
        label: 'Residence Permit',
        description: 'Valid residence or work permit',
      },
    ],
    funFact:
      'Verified profiles are 3x more likely to be contacted by employers and have a 70% higher chance of getting interviewed. ID verification helps prevent fraud and builds trust.',
  },
  {
    id: 'privacy',
    title: 'Privacy Settings',
    subtitle: 'Control who can see your profile',
    component: PrivacySettingsStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-12.webp',
    icon: Lock,
    imgIcon: '/images/job-seeker-setup/icons/lock-2.png',
    funFact:
      '92% of employers use social media to screen candidates - managing your privacy settings helps you control your professional image.',
  },
] as const;

export type SlideId = (typeof setupSlides)[number]['id'];

export const verificationPhrases = [
  'I confirm this is my authentic application',
  'My name is [Name] and I am seeking employment',
  'I verify that all information provided is accurate',
  'Today is [Current Date] and I am applying for jobs',
  'I am actively looking for new opportunities',
  'I certify that my documents are genuine',
  'This video confirms my identity verification',
  'I am available for employment opportunities',
  'I authorize verification of my credentials',
  'My profile represents my true professional background',
  'I am ready to start my job search journey',
  'I confirm my willingness to work',
  'This is my official job seeker verification',
  'I am excited to explore new career paths',
  'I verify my commitment to finding employment',
  'I am prepared for new job opportunities',
  'This verification is done on [Current Date]',
  'I confirm my job search is active',
  'I am seeking professional growth opportunities',
  'My career journey starts with this verification',
  'I am ready for my next career challenge',
  'This confirms my job seeker status',
  'I verify my professional credentials',
  'I am pursuing new career opportunities',
  'My job search begins with this verification',
  'I confirm my employment availability',
  'This verifies my professional identity',
  'I am seeking career advancement',
  'My skills and experience are accurately represented',
  'I verify my readiness for employment',
  'This confirms my job application process',
  'I am actively pursuing job opportunities',
  'My professional profile is genuine',
  'I confirm my career search is active',
  'This verifies my employment intentions',
  'I am ready for professional opportunities',
  'My verification is completed today',
  'I confirm my job seeker status',
  'This validates my career aspirations',
  'I am prepared for employment',
  'My professional journey continues here',
  'I verify my employment goals',
  'This confirms my career intentions',
  'I am seeking new professional challenges',
  'My commitment to finding work is genuine',
  'I confirm my professional objectives',
  'This verifies my career commitment',
  'I am ready for my next role',
  'My professional verification is complete',
  'I confirm my employment search',
] as const;

export const getRandomVerificationPhrase = () => {
  const randomIndex = Math.floor(Math.random() * verificationPhrases.length);
  return verificationPhrases[randomIndex];
};
