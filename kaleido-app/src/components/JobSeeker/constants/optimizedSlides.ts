import React from 'react';

import { Briefcase, FileUp, Lock, Shield, Sparkles, Target, User } from 'lucide-react';

// Import optimized step components (we'll create these next)
import {
  AdditionalDetailsStep,
  EssentialInfoStep,
  JobPreferencesStep,
  PrivacyLaunchStep,
  ProfessionalProfileStep,
  SmartImportStep,
  VerificationMediaStep,
} from '../steps/optimized';

import { BaseStepProps } from '../types';

interface StepComponentProps extends BaseStepProps {
  uploading?: boolean;
  setUploading?: (uploading: boolean) => void;
}

type StepComponent = React.ComponentType<StepComponentProps>;

export interface OptimizedSlide {
  id: string;
  title: string;
  subtitle: string;
  component: StepComponent;
  image: string;
  overlay?: {
    gradient: string;
    blend: string;
    opacity: number;
  };
  icon: React.ComponentType<any>;
  imgIcon?: string;
  funFact: string;
  isOptional?: boolean;
  mandatoryFields?: string[];
  validation?: string;
  canSkip?: boolean;
  isFinal?: boolean;
}

export const optimizedSetupSlides: OptimizedSlide[] = [
  {
    id: 'import',
    title: 'Import Your Profile',
    subtitle: 'Speed up setup with resume or LinkedIn (optional)',
    component: SmartImportStep as unknown as StepComponent,
    image: '/images/job-seeker-setup/new/profile-3.webp',
    icon: FileUp,
    imgIcon: '/images/job-seeker-setup/icons/document.png',
    funFact:
      'Smart import can auto-fill up to 80% of your profile in seconds, saving you 10-15 minutes of manual typing!',
    isOptional: true,
    canSkip: true,
  },
  {
    id: 'essentials',
    title: 'Essential Information',
    subtitle: 'Basic details about you',
    component: EssentialInfoStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-7.webp',
    icon: User,
    imgIcon: '/images/job-seeker-setup/icons/personal-info.png',
    funFact:
      'Profiles with complete contact information are 3x more likely to be contacted by employers within the first week!',
    mandatoryFields: ['firstName', 'lastName', 'email'],
    validation: 'basicInfo',
  },
  {
    id: 'professional',
    title: 'Professional Profile',
    subtitle: 'Your skills and experience',
    component: ProfessionalProfileStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-3.webp',
    icon: Briefcase,
    imgIcon: '/images/job-seeker-setup/icons/experience.webp',
    funFact:
      'Listing at least 5 relevant skills increases your chances of being discovered by recruiters by 40%!',
    mandatoryFields: ['skills'],
    validation: 'professionalInfo',
  },
  {
    id: 'preferences',
    title: 'Job Preferences',
    subtitle: "What you're looking for",
    component: JobPreferencesStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-4.webp',
    icon: Target,
    imgIcon: '/images/job-seeker-setup/icons/nav.png',
    funFact:
      'Clear job preferences help us match you with relevant opportunities, reducing irrelevant applications by 60%!',
    mandatoryFields: ['jobTypes', 'locations', 'remotePreference', 'desiredSalary'],
    validation: 'preferences',
  },
  {
    id: 'additional',
    title: 'Additional Details',
    subtitle: 'Enhance your profile (optional)',
    component: AdditionalDetailsStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-5.webp',
    overlay: {
      gradient: 'linear-gradient(135deg, rgba(139, 69, 19, 0.3), rgba(245, 101, 101, 0.2))',
      blend: 'overlay',
      opacity: 0.7,
    },
    icon: Sparkles,
    imgIcon: '/images/job-seeker-setup/icons/personal-info.webp',
    funFact:
      'Candidates who share their values and include portfolio links receive 2.5x more interview invitations!',
    isOptional: true,
    canSkip: true,
  },
  {
    id: 'verification',
    title: 'Verification & Media',
    subtitle: 'Build trust with employers (optional)',
    component: VerificationMediaStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-6.webp',
    icon: Shield,
    imgIcon: '/images/job-seeker-setup/icons/id-verify.webp',
    funFact:
      'Verified profiles with video introductions get 3x more views and are prioritized in search results!',
    isOptional: true,
    canSkip: true,
  },
  {
    id: 'privacy',
    title: 'Privacy & Launch',
    subtitle: 'Final settings and review',
    component: PrivacyLaunchStep as StepComponent,
    image: '/images/job-seeker-setup/new/profile-7.webp',
    icon: Lock,
    imgIcon: '/images/job-seeker-setup/icons/lock-2.png',
    funFact:
      "You're joining thousands of professionals who found their dream job through our platform. Welcome aboard!",
    isFinal: true,
  },
];

export type OptimizedSlideId = (typeof optimizedSetupSlides)[number]['id'];

// Validation helper for mandatory fields
export const validateMandatoryFields = (step: string, formData: any) => {
  const mandatoryValidation: Record<string, () => boolean> = {
    essentials: () => {
      return !!(
        formData.firstName?.trim() &&
        formData.lastName?.trim() &&
        formData.email?.trim() &&
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
      );
    },
    professional: () => {
      return formData.skills?.length > 0;
    },
    preferences: () => {
      return !!(
        formData.preferences?.jobTypes?.length > 0 &&
        formData.preferences?.locations?.length > 0 &&
        formData.preferences?.remotePreference &&
        formData.preferences?.desiredSalary?.min &&
        formData.preferences?.desiredSalary?.max &&
        formData.preferences?.desiredSalary?.currency &&
        formData.preferences?.desiredSalary?.period
      );
    },
  };

  const validator = mandatoryValidation[step];
  return validator ? validator() : true;
};

// Get missing mandatory fields for a step
export const getMissingMandatoryFields = (step: string, formData: any): string[] => {
  const missing: string[] = [];

  switch (step) {
    case 'essentials':
      if (!formData.firstName?.trim()) missing.push('First Name');
      if (!formData.lastName?.trim()) missing.push('Last Name');
      if (!formData.email?.trim()) missing.push('Email');
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) missing.push('Valid Email');
      break;

    case 'professional':
      if (!formData.skills || formData.skills.length === 0) missing.push('At least 1 Skill');
      break;

    case 'preferences':
      if (!formData.preferences?.jobTypes?.length) missing.push('Job Types');
      if (!formData.preferences?.locations?.length) missing.push('Preferred Locations');
      if (!formData.preferences?.remotePreference) missing.push('Remote Preference');
      if (!formData.preferences?.desiredSalary?.min || !formData.preferences?.desiredSalary?.max) {
        missing.push('Salary Range');
      }
      if (!formData.preferences?.desiredSalary?.currency) missing.push('Currency');
      if (!formData.preferences?.desiredSalary?.period) missing.push('Salary Period');
      break;
  }

  return missing;
};

// Get validation message for a step
export const getOptimizedValidationMessage = (step: string, formData: any): string => {
  const missing = getMissingMandatoryFields(step, formData);

  if (missing.length === 0) return '';

  if (missing.length === 1) {
    return `Please complete: ${missing[0]}`;
  }

  return `Please complete ${missing.length} required fields: ${missing.join(', ')}`;
};

// Progress calculation helpers
export const calculateStepProgress = (step: string, formData: any): number => {
  const slide = optimizedSetupSlides.find(s => s.id === step);
  if (!slide || !slide.mandatoryFields) return 100;

  const mandatoryFields = slide.mandatoryFields;
  let completed = 0;

  mandatoryFields.forEach(field => {
    switch (field) {
      case 'firstName':
        if (formData.firstName?.trim()) completed++;
        break;
      case 'lastName':
        if (formData.lastName?.trim()) completed++;
        break;
      case 'email':
        if (formData.email?.trim() && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email))
          completed++;
        break;
      case 'skills':
        if (formData.skills?.length > 0) completed++;
        break;
      case 'jobTypes':
        if (formData.preferences?.jobTypes?.length > 0) completed++;
        break;
      case 'locations':
        if (formData.preferences?.locations?.length > 0) completed++;
        break;
      case 'remotePreference':
        if (formData.preferences?.remotePreference) completed++;
        break;
      case 'desiredSalary':
        if (
          formData.preferences?.desiredSalary?.min &&
          formData.preferences?.desiredSalary?.max &&
          formData.preferences?.desiredSalary?.currency &&
          formData.preferences?.desiredSalary?.period
        )
          completed++;
        break;
    }
  });

  return Math.round((completed / mandatoryFields.length) * 100);
};

export const calculateOverallProgress = (formData: any): number => {
  const stepsWithMandatory = optimizedSetupSlides.filter(s => s.mandatoryFields);
  const totalProgress = stepsWithMandatory.reduce((acc, slide) => {
    return acc + calculateStepProgress(slide.id, formData);
  }, 0);

  return Math.round(totalProgress / stepsWithMandatory.length);
};
