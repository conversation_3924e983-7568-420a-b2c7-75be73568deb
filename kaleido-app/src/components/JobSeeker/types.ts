import {
  Achievement,
  CandidateExperience,
  Certification,
  Compensation,
  Education,
  JobPreferences,
  JobSeekerMetadata,
  Recommendation,
  SocialProfile,
  UserRole,
  Verifications,
} from '@/shared/types';
import { StandardizedProfile } from '@/shared/types/profile.types';
import { Experience, JobSeekerProfile } from '@/types/jobSeeker';

export type { StandardizedProfile };

export interface ExtendedWorkAvailability {
  immediatelyAvailable?: boolean;
  noticePeriod?: number;
  willingToRelocate?: boolean;
  immediateStart?: boolean;
  preferredWorkType?: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT';
}

export interface UIJobSeekerProfile
  extends Omit<StandardizedProfile, 'workAvailability' | 'onboardingProgress'> {
  workAvailability?: ExtendedWorkAvailability;
  onboardingProgress?: {
    basicInfo: {
      completed: boolean;
      completedAt: string;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    professionalInfo: {
      completed: boolean;
      completedAt: string;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    preferences: {
      completed: boolean;
      completedAt: string;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    additionalInfo: {
      completed: boolean;
      completedAt: string;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    overall: {
      completed: boolean;
      completedAt: string;
      percentage: number;
    };
    lastUpdated: string;
  };
  linkedInProfile?: any;
  linkedInImportDate?: Date;
  experience?: Experience[];
}

export interface JobSeekerFullProfileProps {
  profile: UIJobSeekerProfile;
  onClose?: () => void;
}

export interface PrivacySettings {
  profileVisibility?: 'PUBLIC' | 'PRIVATE' | 'CONNECTIONS_ONLY';
  showContact?: boolean;
  showContactInfo?: boolean;
  showSalary?: boolean;
  allowMessages?: boolean;
  allowNotifications?: boolean;
  emailPreferences?: {
    jobAlerts?: boolean;
    messages?: boolean;
    updates?: boolean;
    marketing?: boolean;
  };
}

export interface IJobSeekerProfile {
  id?: string;
  userId?: string;
  clientId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location?: string;
  myProfileImage?: string;
  summary?: string;
  skills: string[];
  experience: CandidateExperience[];
  resumeUrl?: string;
  resumeTemplate?: 'modern' | 'professional' | 'creative';
  resumeHtml?: string;
  linkedinUrl?: string;
  linkedIn?: string;
  githubUrl?: string;
  github?: string;
  website?: string;
  role?: UserRole;
  education: Education[];
  certifications?: Certification[];
  languages?: string[];
  myValues?: string[];
  portfolioUrl?: string;
  portfolio?: any[];
  videoIntroUrl?: string;
  videoIntroduction?: string;
  availability?: any;
  idVerification?: any;
  preferences?: JobPreferences;
  passportId?: string;
  achievements?: Achievement[];
  projects?: any[];
  recommendations?: Recommendation[];
  workAvailability?: ExtendedWorkAvailability;
  compensation?: Compensation;
  privacySettings?: PrivacySettings;
  socialProfiles?: SocialProfile[];
  metadata?: JobSeekerMetadata;
  hasCompletedOnboarding?: boolean;
  linkedInProfile?: any;
  onboardingProgress?: {
    basicInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    professionalInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    preferences: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    additionalInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    overall: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
    };
    lastUpdated: Date | null;
  };
  verifications?: Verifications;
  isImportedFromLinkedIn?: boolean;
  linkedInImportDate?: Date;
  resumeData?: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    location?: string;
    summary?: string;
    skills: string[];
    experience: CandidateExperience[];
    education: Education[];
    certifications?: Certification[];
    languages?: string[];
    portfolioUrl?: string;
    resumeUrl: string;
  };
}

export interface BaseStepProps {
  formData: IJobSeekerProfile;
  onUpdate: (data: Partial<IJobSeekerProfile>) => void;
  onNext?: () => void;
  onValidityChange?: (isValid: boolean) => void;
  uploading?: boolean;
  setUploading?: (uploading: boolean) => void;
  linkedInData?: any;
}

export interface StepComponentProps extends BaseStepProps {
  onNext?: () => void;
}

export type StepComponent = React.ComponentType<StepComponentProps>;

// Extend CandidateExperience for local use
export interface ExtendedCandidateExperience extends CandidateExperience {
  location: string; // Make location required
}

// Extended profile properties that might be in the data but not in the type
export interface ExtendedJobSeekerProfile
  extends Omit<JobSeekerProfile, 'workAvailability' | 'onboardingProgress'> {
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  myValues?: string[];
  hasCompletedOnboarding?: boolean;
  resumeUrl?: string;
  workAvailability?: {
    immediatelyAvailable?: boolean;
    willingToRelocate?: boolean;
    noticePeriod?: string;
    immediateStart?: boolean;
    preferredWorkType?: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT';
  };
  preferences?: {
    jobTypes?: string[];
    locations?: string[];
    industries?: string[];
    desiredSalary: {
      min: number;
      max: number;
      currency: string;
      period: string;
    };
    remotePreference?: string;
  };
  onboardingProgress?: {
    overall?: {
      completed?: boolean;
      percentage?: number;
    };
  };
  verifications?: {
    email: boolean;
    phone: boolean;
    identity?: {
      status: string;
      isVerified: boolean;
      verifiedAt: string;
      documentUrl?: string;
      documentType?: string;
    };
    video?: {
      status: string;
      videoUrl: string;
      isVerified: boolean;
      verifiedAt: string;
      verificationPhrase?: string;
    };
    education: boolean;
    employment: boolean;
    skills: boolean;
  };
}

export interface JobSeekersValidationResponse {
  isValid: boolean;
  missingFields: string[];
  mandatoryMissingFields: string[];
  hasCompletedOnboarding: boolean;
  completion: {
    overall: number;
    sections: {
      basicInfo: number;
      professionalInfo: number;
      preferences: number;
      additionalInfo: number;
    };
  };
  record: IJobSeekerProfile;
  onboardingProgress?: {
    basicInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    professionalInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    preferences: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    additionalInfo: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
      requiredFields: string[];
      completedFields: string[];
    };
    overall: {
      completed: boolean;
      completedAt: Date | null;
      percentage: number;
    };
    lastUpdated: Date | null;
  };
}

export interface JobSeekerSetupSliderProps {
  onComplete: (data: IJobSeekerProfile) => void;
  onClose: () => void;
  initialData: Partial<IJobSeekerProfile> & {
    record?: IJobSeekerProfile;
    linkedInProfile?: any;
  };
  initialStep?: string;
  validationResponse?: JobSeekersValidationResponse;
}

export interface CompletionState {
  show: boolean;
  animate: boolean;
}
