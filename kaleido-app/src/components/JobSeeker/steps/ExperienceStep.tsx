import { Briefcase, Building, Calendar, ChevronDown, MapPin, Plus, X } from 'lucide-react';
import React, { useState } from 'react';

import { ArrayFieldPagination } from '@/components/common/ArrayFieldPagination';
import { Button } from '@/components/ui/button';
import { ModernFormInput, ModernTextArea } from '../components/ModernFormInput';
import { CandidateExperience } from '@/shared/types';
import { BaseStepProps } from '../types';

const emptyExperience: CandidateExperience = {
  company: '',
  title: '',
  location: '',
  startDate: new Date(),
  endDate: null,
  current: false,
  description: '',
  skills: [],
  achievements: undefined,
};

export const ExperienceStep: React.FC<BaseStepProps> = ({ formData, onUpdate }) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [currentExperience, setCurrentExperience] = useState<CandidateExperience>(emptyExperience);
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Set<number>>(new Set());
  const itemsPerPage = 3;
  const MAX_DESCRIPTION_LENGTH = 150;

  const handleAddExperience = () => {
    const updatedExperience = [...(formData.experience || [])];
    if (editingIndex !== null) {
      updatedExperience[editingIndex] = currentExperience;
    } else {
      updatedExperience.push(currentExperience);
    }

    const update = { experience: updatedExperience };
    onUpdate(update);
    setEditingIndex(null);
    setCurrentExperience(emptyExperience);
  };

  const handleRemoveExperience = (index: number) => {
    const updatedExperience = (formData.experience || []).filter((_, i) => i !== index);
    const update = { experience: updatedExperience };
    onUpdate(update);
  };

  const handleEditExperience = (index: number) => {
    setEditingIndex(index);
    // Ensure location is set to a string value for Experience type
    const experience = formData.experience?.[index];
    if (experience) {
      const expToEdit = {
        ...experience,
        location: experience.location || '',
      };
      setCurrentExperience(expToEdit as CandidateExperience);
    }
  };

  const handleExperienceChange = (field: keyof CandidateExperience, value: any) => {
    setCurrentExperience(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSkillAdd = (e: any) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const value = e.target.value.trim();
      if (value && !currentExperience.skills?.includes(value)) {
        setCurrentExperience(prev => ({
          ...prev,
          skills: [...(prev.skills || []), value],
        }));
        e.target.value = '';
      }
    }
  };

  const handleRemoveSkill = (index: number) => {
    setCurrentExperience(prev => ({
      ...prev,
      skills: (prev.skills || []).filter((_, i) => i !== index),
    }));
  };

  const toggleDescription = (index: number) => {
    setExpandedDescriptions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const totalPages = Math.ceil((formData.experience?.length || 0) / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const visibleExperience = formData.experience?.slice(startIndex, startIndex + itemsPerPage) || [];

  return (
    <div className="space-y-6 md:w-full mx-auto">
      {editingIndex !== null || !formData.experience || formData.experience.length === 0 ? (
        <div className="space-y-6 p-6 bg-white/50 rounded-xl border border-gray-200 shadow-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModernFormInput
              label="Company"
              value={currentExperience.company}
              onChange={(value) => handleExperienceChange('company', value)}
              placeholder="Enter company name"
              prefix={<Building className="w-3.5 h-3.5" />}
              size="sm"
            />
            <ModernFormInput
              label="Job Title"
              value={currentExperience.title}
              onChange={(value) => handleExperienceChange('title', value)}
              placeholder="Enter your job title"
              prefix={<Briefcase className="w-3.5 h-3.5" />}
              size="sm"
            />
          </div>

          <ModernFormInput
            label="Location"
            value={currentExperience.location}
            onChange={(value) => handleExperienceChange('location', value)}
            placeholder="City, Country"
            prefix={<MapPin className="w-3.5 h-3.5" />}
            size="sm"
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModernFormInput
              label="Start Date"
              type="text"
              value={
                currentExperience.startDate
                  ? new Date(currentExperience.startDate).toISOString().split('T')[0]
                  : ''
              }
              onChange={(value) => handleExperienceChange('startDate', value ? new Date(value) : null)}
              placeholder="YYYY-MM-DD"
              prefix={<Calendar className="w-3.5 h-3.5" />}
              size="sm"
            />
            <ModernFormInput
              label="End Date"
              type="text"
              value={
                currentExperience.endDate
                  ? new Date(currentExperience.endDate).toISOString().split('T')[0]
                  : ''
              }
              onChange={(value) =>
                handleExperienceChange('endDate', value ? new Date(value) : null)
              }
              disabled={currentExperience.current}
              placeholder="YYYY-MM-DD or leave empty for current"
              prefix={<Calendar className="w-3.5 h-3.5" />}
              size="sm"
            />
          </div>

          <ModernTextArea
            label="Description"
            value={currentExperience.description}
            onChange={(value) => handleExperienceChange('description', value)}
            placeholder="Describe your responsibilities and achievements..."
            rows={4}
            size="sm"
          />

          <div className="space-y-3">
            <label className="text-sm font-medium text-gray-900">Skills Used</label>
            <div className="flex items-center gap-2">
              <input
                type="text"
                placeholder="Type a skill and press Enter"
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                onKeyDown={handleSkillAdd}
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  const input = document.querySelector(
                    'input[placeholder="Type a skill and press Enter"]'
                  ) as HTMLInputElement;
                  if (input?.value) {
                    handleSkillAdd({ key: 'Enter', target: input } as any);
                  }
                }}
              >
                Add
              </Button>
            </div>

            <div className="flex flex-wrap gap-2">
              {currentExperience?.skills?.map((skill, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 px-3 py-1 bg-purple-50 text-purple-700 rounded-full group"
                >
                  <span className="text-sm">{skill}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveSkill(index)}
                    className="p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-purple-100"
                    aria-label="Remove skill"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={() => {
                setEditingIndex(null);
                setCurrentExperience(emptyExperience);
              }}
              className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-all text-sm font-medium"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAddExperience}
              className="px-4 py-2 rounded-lg bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 transition-all text-sm font-medium shadow-sm hover:shadow-md"
            >
              {editingIndex !== null ? 'Save Changes' : 'Add Experience'}
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {visibleExperience.map((exp, index) => (
            <div
              key={startIndex + index}
              className="p-6 rounded-lg bg-white/50 border border-gray-200 shadow-sm relative group hover:shadow-md transition-shadow duration-200"
            >
              <div className="absolute right-3 top-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  type="button"
                  onClick={() => handleEditExperience(startIndex + index)}
                  className="p-1.5 rounded-full bg-purple-50 text-purple-500 hover:bg-purple-100"
                  aria-label="Edit experience"
                >
                  <Briefcase className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={() => handleRemoveExperience(startIndex + index)}
                  className="p-1.5 rounded-full bg-red-50 text-red-500 hover:bg-red-100"
                  aria-label="Remove experience"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">{exp.title}</h4>
                  <p className="text-base font-medium text-purple-600">{exp.company}</p>
                </div>

                <div className="flex items-center gap-3 text-sm text-purple-800/60">
                  <div className="flex items-center gap-1.5">
                    <MapPin className="w-4 h-4" />
                    <span>{exp.location}</span>
                  </div>
                  <span className="text-gray-300">•</span>
                  <div className="flex items-center gap-1.5">
                    <Calendar className="w-4 h-4 text-primary-400" />
                    <span>
                      {exp.startDate
                        ? new Date(exp.startDate).toLocaleDateString('en-US', {
                            month: 'short',
                            year: 'numeric',
                          })
                        : 'N/A'}{' '}
                      -{' '}
                      {exp.endDate
                        ? new Date(exp.endDate).toLocaleDateString('en-US', {
                            month: 'short',
                            year: 'numeric',
                          })
                        : 'Present'}
                    </span>
                  </div>
                </div>

                {exp.description && (
                  <div className="relative">
                    <p
                      className="text-sm text-gray-600 whitespace-pre-wrap cursor-pointer hover:text-gray-800 transition-colors duration-200"
                      onClick={() => toggleDescription(startIndex + index)}
                    >
                      {expandedDescriptions.has(startIndex + index)
                        ? exp.description
                        : exp.description.length > MAX_DESCRIPTION_LENGTH
                          ? `${exp.description.slice(0, MAX_DESCRIPTION_LENGTH)}...`
                          : exp.description}
                    </p>
                    {exp.description.length > MAX_DESCRIPTION_LENGTH && (
                      <button
                        type="button"
                        onClick={() => toggleDescription(startIndex + index)}
                        className="flex items-center gap-1 mt-1 text-purple-600 hover:text-purple-700 text-sm font-medium transition-colors duration-200"
                      >
                        {expandedDescriptions.has(startIndex + index) ? 'Show less' : 'Show more'}
                        <ChevronDown
                          className={`w-4 h-4 transition-transform duration-200 ${
                            expandedDescriptions.has(startIndex + index)
                              ? 'transform rotate-180'
                              : ''
                          }`}
                        />
                      </button>
                    )}
                  </div>
                )}

                {exp.skills?.length > 0 && (
                  <div className="flex flex-wrap gap-2 pt-2">
                    {exp.skills.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          <ArrayFieldPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />

          <button
            type="button"
            onClick={() => setEditingIndex(null)}
            className="w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg bg-gradient-to-br from-purple-50/80 via-indigo-50/50 to-pink-50/80 border border-purple-200 text-purple-700 hover:from-purple-100 hover:via-indigo-100 hover:to-pink-100 hover:border-purple-300 transition-all text-sm font-medium"
          >
            <Plus className="w-4 h-4" />
            Add Experience
          </button>
        </div>
      )}
    </div>
  );
};
