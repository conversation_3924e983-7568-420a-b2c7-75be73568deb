import { CandidateExperience, ResumeUploadResponse } from '@/shared/types';
import { AnimatePresence, motion } from 'framer-motion';
import {
  CheckCircle,
  CloudUpload,
  Eye,
  FileCheck,
  FileText,
  HardDrive,
  Lightbulb,
  LucideProps,
  Shield,
  Upload,
  X,
} from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';

import TabComponent from '@/components/common/TabComponent';
import { getMessagesByTag } from '@/components/FileUploader/LoadingMessages';
import { showToast } from '@/components/Toaster';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import useUser from '@/hooks/useUser';
import apiHelper from '@/lib/apiHelper';
import { useDropzone } from 'react-dropzone';
import { LinkedInImportSummary } from '../components/LinkedInImportSummary';
import { ResumeImportSummary } from '../components/ResumeImportSummary';
import { BaseStepProps } from '../types';

interface Auth0Metadata {
  auth0?: {
    provider: string;
    picture?: string;
  };
}

// Custom Resume Loading Display component
const ResumeLoadingDisplay: React.FC<{ isProcessing: boolean }> = ({ isProcessing }) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const resumeMessages = getMessagesByTag('resume_upload');

  useEffect(() => {
    if (isProcessing) {
      const interval = setInterval(() => {
        setCurrentMessageIndex(prev => (prev === resumeMessages.length - 1 ? 0 : prev + 1));
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isProcessing, resumeMessages.length]);

  if (!isProcessing) return null;

  const currentMessage = resumeMessages[currentMessageIndex];
  const IconComponent = currentMessage.icon as React.FC<LucideProps>;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/15 backdrop-blur-[6px] animate-in fade-in duration-300 z-50">
      <div className="relative w-[520px] h-[320px] flex items-center justify-center">
        {/* Smoky outer glow with softer edges */}
        <div
          className="
            absolute
            inset-0
            rounded-[40px]
            pointer-events-none
            bg-[radial-gradient(circle_at_center,_rgba(147,51,234,0.12),_rgba(59,130,246,0.08),_rgba(6,182,212,0.06),transparent_70%)]
            blur-[120px]
            animate-pulse
            opacity-80
            [animation-duration:8s]
          "
        />

        {/* Animated inner glow with smoky effect */}
        <div
          className="
            absolute
            inset-0
            pointer-events-none
            rounded-[40px]
            bg-[radial-gradient(circle_at_center,_rgba(147,51,234,0.18),_rgba(59,130,246,0.14),_rgba(6,182,212,0.12),transparent_75%)]
            blur-[80px]
            opacity-90
            scale-95
          "
        >
          <motion.div
            className="absolute inset-0 bg-[radial-gradient(circle_at_center,_rgba(147,51,234,0.2),transparent_70%)] rounded-[40px]"
            animate={{
              scale: [1, 1.05, 1],
              opacity: [0.7, 0.9, 0.7],
            }}
            transition={{
              duration: 5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        </div>

        {/* Content area with smoky effect but no container look */}
        <div className="relative w-full h-full rounded-[30px] overflow-hidden">
          {/* Smoky gradient overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/5 to-transparent opacity-70" />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-500/8 to-transparent opacity-80" />

          {/* Animated smoky wisps */}
          <motion.div
            className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_rgba(147,51,234,0.1),transparent_70%)] opacity-60"
            animate={{
              opacity: [0.4, 0.7, 0.4],
              rotate: [0, 5, 0],
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />

          <motion.div
            className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,_rgba(59,130,246,0.1),transparent_70%)] opacity-60"
            animate={{
              opacity: [0.5, 0.8, 0.5],
              rotate: [0, -5, 0],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 1,
            }}
          />

          <div className="relative h-full flex items-center justify-center px-12">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentMessageIndex}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="flex flex-col items-center w-full"
              >
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.8, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col items-center gap-6"
                >
                  <div className="relative">
                    {/* Improved icon glow effect */}
                    <motion.div
                      className={`absolute inset-0 ${currentMessage.iconColor} blur-2xl opacity-70`}
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.5, 0.7, 0.5],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: 'easeInOut',
                      }}
                    >
                      <IconComponent size={56} />
                    </motion.div>
                    <IconComponent
                      className={`${currentMessage.iconColor} relative`}
                      size={56}
                      strokeWidth={1.5}
                    />
                  </div>
                  <p className="text-white/90 text-xl font-medium text-center tracking-wide max-w-sm">
                    {currentMessage.text}
                  </p>
                </motion.div>
                <div className="relative mt-8 w-32">
                  <motion.div
                    className="h-1 bg-white/5 rounded-full overflow-hidden"
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: 1 }}
                    transition={{
                      duration: 3,
                      ease: 'linear',
                      repeat: Infinity,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/80 via-blue-500/80 to-cyan-500/80 rounded-full blur-sm" />
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 rounded-full" />
                  </motion.div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Helper function to check if user logged in with LinkedIn
 */
const isLinkedInLogin = (user: any): boolean => {
  if (!user) return false;

  // Check if the user's sub contains 'linkedin'
  if (user.sub && user.sub.includes('linkedin')) return true;

  // Check if the provider field indicates LinkedIn
  if (user.provider === 'linkedin') return true;

  // Check if there's LinkedIn identity in the identities array
  if (user.identities && Array.isArray(user.identities)) {
    return user.identities.some((identity: any) => identity.provider === 'linkedin');
  }

  // Check if there's LinkedIn profile data (additional safety check)
  if (user.linkedInProfile) return true;

  return false;
};

interface ResumeUploadStepProps extends BaseStepProps {
  uploading?: boolean;
  setUploading?: (uploading: boolean) => void;
  hideImportSummaries?: boolean;
}

export const ResumeUploadStep: React.FC<ResumeUploadStepProps> = ({
  formData,
  onUpdate,
  uploading,
  setUploading,
  hideImportSummaries = false,
}) => {
  const { user } = useUser();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  // Determine which import methods are available
  const metadata = formData.metadata as Auth0Metadata;

  const hasResumeData = !!formData.resumeUrl;
  const hasLinkedInData =
    metadata?.auth0?.provider === 'linkedin' ||
    formData.linkedInProfile?.provider === 'linkedin' ||
    (formData.clientId && formData.clientId.startsWith('linkedin|')) ||
    // Check if we have any LinkedIn-specific data
    !!formData.linkedInProfile ||
    // Check if we have data that likely came from LinkedIn
    (formData.firstName && formData.lastName && formData.linkedinUrl);
  const [activeTab, setActiveTab] = useState(() => {
    // Set default tab based on available data and login method
    if (hasLinkedInData && isLinkedInLogin(user)) return 'linkedin';
    if (hasResumeData) return 'resume';
    // If user logged in with LinkedIn but no data yet, show LinkedIn tab
    if (isLinkedInLogin(user)) return 'linkedin';
    // Otherwise default to resume tab
    return 'resume';
  });

  const tabs = [
    // Only show LinkedIn tab if user logged in with LinkedIn
    ...(isLinkedInLogin(user)
      ? [
          {
            id: 'linkedin',
            label: 'LinkedIn Import',
            icon: <FileText className="w-4 h-4" />,
          },
        ]
      : []),
    ...(hasResumeData
      ? [
          {
            id: 'resume',
            label: 'Resume Import',
            icon: <FileText className="w-4 h-4" />,
          },
        ]
      : []),
  ];

  // Update active tab when user data changes
  useEffect(() => {
    // If current tab is linkedin but user didn't log in with LinkedIn, switch to resume
    if (activeTab === 'linkedin' && !isLinkedInLogin(user)) {
      setActiveTab('resume');
    }
    // If no active tab is set and user logged in with LinkedIn, set to linkedin
    else if (!activeTab && isLinkedInLogin(user)) {
      setActiveTab('linkedin');
    }
  }, [user, activeTab]);

  const handleUpdate = (data: Partial<typeof formData>) => {
    onUpdate(data);
  };

  const processResumeResponse = async (response: ResumeUploadResponse) => {
    try {
      // Convert dates in education and experience arrays
      const convertEducationDates = (education: any[]) =>
        education.map(edu => ({
          ...edu,
          startDate: edu.startDate ? new Date(edu.startDate) : null,
          endDate: edu.endDate ? new Date(edu.endDate) : null,
        }));

      const convertExperienceDates = (experience: any[]) =>
        experience.map(exp => ({
          ...exp,
          startDate: exp.startDate ? new Date(exp.startDate) : null,
          endDate: exp.endDate ? new Date(exp.endDate) : null,
          current: !exp.endDate || new Date(exp.endDate) > new Date(), // Set current based on end date
        })) as CandidateExperience[];

      const convertCertificationDates = (certifications: any[]) =>
        certifications.map(cert => ({
          ...cert,
          issueDate: cert.issueDate ? new Date(cert.issueDate) : null,
          expiryDate: cert.expiryDate ? new Date(cert.expiryDate) : null,
        }));

      // Ensure we're using the extracted data for firstName, lastName, and email
      // instead of keeping the placeholder "New User" data
      const updatedProfile = {
        ...formData,
        ...response,
        // Explicitly prioritize the extracted data for these fields
        firstName: response.firstName || formData.firstName,
        lastName: response.lastName || formData.lastName,
        email: response.email || formData.email,
        phone: response.phone || formData.phone,
        location: response.location || formData.location,
        summary: response.summary || formData.summary,
        education: response.education
          ? convertEducationDates(response.education)
          : formData.education,
        experience: response.experience
          ? convertExperienceDates(response.experience)
          : formData.experience,
        certifications: response.certifications
          ? convertCertificationDates(response.certifications)
          : formData.certifications,
        portfolioUrl: response.portfolioUrl || formData.portfolioUrl,
        resumeUrl: response.resumeUrl,
        role: formData.role,
        videoIntroUrl: formData.videoIntroUrl,
        passportId: formData.passportId,
        verifications: formData.verifications,
        workAvailability: {
          immediatelyAvailable: true,
          ...formData.workAvailability,
          ...response.workAvailability,
        },
        privacySettings: {
          profileVisibility: 'PUBLIC',
          showContact: true,
          showSalary: false,
          allowMessages: true,
          ...formData.privacySettings,
          ...response.privacySettings,
        },
        // Store the resume data for the import summary
        resumeData: {
          firstName: response.firstName,
          lastName: response.lastName,
          email: response.email,
          phone: response.phone,
          location: response.location,
          summary: response.summary,
          skills: response.skills || [],
          experience: response.experience ? convertExperienceDates(response.experience) : [],
          education: response.education ? convertEducationDates(response.education) : [],
          certifications: response.certifications
            ? convertCertificationDates(response.certifications)
            : [],
          languages: response.languages || [],
          portfolioUrl: response.portfolioUrl,
          resumeUrl: response.resumeUrl,
        },
      };

      // Update the profile directly
      handleUpdate(updatedProfile);

      // Also update the profile in the backend to ensure the extracted data is saved
      try {
        if (formData.id) {
          // If we have an ID, update the existing profile
          await apiHelper.put(`/job-seekers/${formData.id}`, {
            ...updatedProfile,
            // Ensure these fields are explicitly set from the extracted data
            firstName: response.firstName || formData.firstName,
            lastName: response.lastName || formData.lastName,
            email: response.email || formData.email,
          });
        } else {
          // If we don't have an ID, create a new profile
          const newProfile = await apiHelper.post('/job-seekers', {
            ...updatedProfile,
            // Ensure these fields are explicitly set from the extracted data
            firstName: response.firstName || formData.firstName,
            lastName: response.lastName || formData.lastName,
            email: response.email || formData.email,
          });
          // Update the form data with the new profile ID
          if (newProfile && newProfile.id) {
            handleUpdate({ ...updatedProfile, id: newProfile.id });
          }
        }
      } catch (backendError) {
        console.error('Error updating profile in backend:', backendError);
        // Continue with local updates even if backend update fails
      }

      showToast({
        message: 'Resume uploaded and processed successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error processing resume response:', error);
      showToast({
        message: 'Failed to process resume data. Please try filling in the details manually.',
        isSuccess: false,
      });
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setSelectedFile(file);

        // Set both local and parent uploading states immediately
        setIsUploading(true);
        if (setUploading) setUploading(true);
        setError(null);

        // Start progress simulation
        let progress = 0;
        const interval = setInterval(() => {
          progress = Math.min(progress + 10, 90); // Cap at 90% until actual completion
          setUploadProgress(progress);
        }, 200);

        try {
          // First try the SLM approach
          const formData = new FormData();
          formData.append('file', file);

          try {
            // Attempt to use the SLM endpoint first
            const response = await apiHelper.post<ResumeUploadResponse>(
              '/job-seekers/upload/resume',
              formData
            );

            clearInterval(interval);
            setUploadProgress(100);
            await processResumeResponse(response);
          } catch (slmError) {
            console.warn(
              'SLM resume upload failed, falling back to local implementation:',
              slmError
            );

            // Fallback to the local implementation
            const localFormData = new FormData();
            localFormData.append('files', file);
            localFormData.append('jobId', 'temp-job-id');

            try {
              const result = await apiHelper.post('/resumes/upload', localFormData);

              if (!result.success || result.candidates.length === 0) {
                throw new Error('Failed to parse resume data');
              }

              // Extract candidate data from the response
              const candidate = result.candidates[0];
              const responseData: ResumeUploadResponse = {
                firstName: candidate.firstName || '',
                lastName: candidate.lastName || '',
                email: candidate.email || '',
                phone: candidate.phone || '',
                location: candidate.location || '',
                summary: candidate.summary || '',
                skills: candidate.skills || [],
                experience: candidate.experience || [],
                education: [],
                portfolioUrl: candidate.githubUrl || '',
                linkedinUrl: candidate.linkedinUrl || '',
                resumeUrl: URL.createObjectURL(file), // Create a temporary URL for preview
                workAvailability: {},
                privacySettings: {},
                preferences: {},
                id: '',
                languages: [],
              };

              clearInterval(interval);
              setUploadProgress(100);
              await processResumeResponse(responseData);
            } catch (localError) {
              console.error('Local upload failed:', localError);
              throw localError;
            }
          }
        } catch (error) {
          console.error('All upload methods failed:', error);
          setError('Failed to upload resume. Please try again.');
          setUploadProgress(0);
        } finally {
          // Reset both uploading states
          setIsUploading(false);
          if (setUploading) setUploading(false);
        }
      }
    },
    [setUploading]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    maxFiles: 1,
    disabled: uploading || isUploading,
  });

  const removeResume = () => {
    handleUpdate({ resumeUrl: null });
    setSelectedFile(null);
    setUploadProgress(0);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Tabs Section - Only show if not hidden */}
      {!hideImportSummaries && tabs.length > 0 && (
        <>
          <TabComponent
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            className="text-purple-800"
            showDescription={true}
            // activeTextColor="text-purple-800"
            inactiveTextColor="text-purple-800"
          />

          {activeTab === 'linkedin' && isLinkedInLogin(user) && (
            <LinkedInImportSummary
              formData={formData}
              linkedInData={{
                ...formData.linkedInProfile,
                metadata: formData.metadata,
                firstName: formData.firstName,
                lastName: formData.lastName,
                email: formData.email,
                location: formData.location,
                profilePicture: formData.myProfileImage || metadata?.auth0?.picture,
                skills: formData.skills,
                positions: formData.experience,
                educations: formData.education,
                publicProfileUrl: formData.linkedinUrl,
                // Add these fields to ensure isFieldFromLinkedIn works correctly
                name: `${formData.firstName} ${formData.lastName}`,
                headline: formData.summary,
                pictureUrl: formData.myProfileImage,
              }}
              jobSeekerId={formData.id}
            />
          )}

          {activeTab === 'resume' && hasResumeData && (
            <>
              <ResumeImportSummary
                formData={formData}
                resumeData={
                  formData.resumeData || {
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    email: formData.email,
                    phone: formData.phone,
                    location: formData.location,
                    summary: formData.summary,
                    skills: formData.skills,
                    experience: formData.experience,
                    education: formData.education,
                    certifications: formData.certifications,
                    languages: formData.languages,
                    portfolioUrl: formData.portfolioUrl,
                    resumeUrl: formData.resumeUrl,
                  }
                }
                jobSeekerId={formData.id}
              />
            </>
          )}
        </>
      )}
      {/* Upload Section - Always Visible */}
      <div className="mb-6">
        {formData.resumeUrl ? (
          <Card className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                <div className="p-2 bg-blue-50 rounded">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Resume</h3>
                  <p className="text-sm text-gray-500">
                    {selectedFile ? (
                      <>
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB •{' '}
                        {new Date().toLocaleDateString()}
                      </>
                    ) : (
                      'Uploaded resume'
                    )}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={removeResume}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
          </Card>
        ) : (
          <div
            {...getRootProps()}
            className={`relative border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300 group
              ${
                isDragActive
                  ? 'border-primary bg-gradient-to-br from-primary/10 to-primary/5 shadow-lg scale-[1.02]'
                  : 'border-gray-300 hover:border-primary/50 hover:bg-gradient-to-br hover:from-primary/5 hover:to-transparent hover:shadow-md'
              }
              ${uploading || isUploading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-[1.01] hover:shadow-md'}
              backdrop-blur-sm bg-white/80`}
          >
            <input {...getInputProps()} disabled={uploading || isUploading} />

            {/* Background gradient effect */}
            <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-primary/5 via-transparent to-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            <div className="relative space-y-6">
              <motion.div
                className="mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center shadow-lg"
                animate={isDragActive ? { scale: [1, 1.1, 1] } : {}}
                transition={{ duration: 0.5, repeat: isDragActive ? Infinity : 0 }}
              >
                {uploading || isUploading ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                  >
                    <CloudUpload className="h-8 w-8 text-primary" />
                  </motion.div>
                ) : (
                  <Upload className="h-8 w-8 text-primary group-hover:scale-110 transition-transform duration-200" />
                )}
              </motion.div>

              <div className="space-y-2">
                <p className="text-lg font-medium text-gray-800">
                  {isDragActive
                    ? 'Drop your resume here'
                    : uploading || isUploading
                      ? 'Processing your resume...'
                      : 'Drag & drop your resume here, or click to select'}
                </p>
                <p className="text-sm text-gray-500 flex items-center justify-center gap-2">
                  <FileCheck className="w-4 h-4" />
                  PDF, DOC, or DOCX (max 10MB)
                </p>
              </div>
            </div>
          </div>
        )}

        {(uploading || isUploading) && <ResumeLoadingDisplay isProcessing={true} />}

        {uploadProgress > 0 && uploadProgress < 100 && !(uploading || isUploading) && (
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Uploading...</span>
              <span>{uploadProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              {/* eslint-disable-next-line react/forbid-dom-props */}
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}
        {!hasResumeData && (
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Resume Tips */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Lightbulb className="w-4 h-4 text-amber-600" />
                <h4 className="text-sm font-medium text-gray-700">Resume Tips</h4>
              </div>
              <ul className="space-y-2 text-xs text-gray-600">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-3 h-3 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span>Keep it clear and concise</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-3 h-3 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span>Highlight relevant skills</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-3 h-3 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span>Use bullet points</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-3 h-3 text-amber-600 mt-0.5 flex-shrink-0" />
                  <span>Include achievements</span>
                </li>
              </ul>
            </div>

            {/* Requirements */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <FileCheck className="w-4 h-4 text-blue-600" />
                <h4 className="text-sm font-medium text-gray-700">Requirements</h4>
              </div>
              <ul className="space-y-2 text-xs text-gray-600">
                <li className="flex items-start gap-2">
                  <FileText className="w-3 h-3 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>PDF, DOC, DOCX files</span>
                </li>
                <li className="flex items-start gap-2">
                  <HardDrive className="w-3 h-3 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>Max 20MB file size</span>
                </li>
                <li className="flex items-start gap-2">
                  <Eye className="w-3 h-3 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>Clearly readable text</span>
                </li>
                <li className="flex items-start gap-2">
                  <Shield className="w-3 h-3 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span>No password protection</span>
                </li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
