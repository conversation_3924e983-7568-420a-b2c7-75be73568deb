'use client';

import React from 'react';
import { User, Mail, Phone, MapPin, Globe, Linkedin, Github, CheckCircle2, TrendingUp } from 'lucide-react';
import { BaseStepProps } from '../../types';
import { ModernFormInput } from '../../components/ModernFormInput';
import { UnifiedStepLayout, FieldCard } from '../../components/UnifiedStepLayout';

const EssentialInfoStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  // Validation functions
  const validateEmail = (email: string) => {
    if (!email) return 'Email is required';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) ? null : 'Please enter a valid email address';
  };

  const validateName = (name: string) => {
    if (!name?.trim()) return 'This field is required';
    if (name.trim().length < 2) return 'Must be at least 2 characters';
    return null;
  };

  const validatePhone = (phone: string) => {
    if (!phone) return null; // Optional field
    const phoneRegex = /^[\+]?[1-9][\d\s\-\(\)]{7,}$/;
    return phoneRegex.test(phone.replace(/\s/g, '')) ? null : 'Please enter a valid phone number';
  };

  // Section configuration
  const sectionConfig = {
    title: 'Complete your essential information',
    subtitle: 'Why this matters',
    icon: <User className="w-4 h-4" />,
    description: 'Complete profiles receive 5x more interview requests',
    benefits: [
      { icon: <CheckCircle2 className="w-3.5 h-3.5" />, text: 'All fields completed' },
      { icon: <TrendingUp className="w-3.5 h-3.5" />, text: '40% more visibility' }
    ],
    requiredFieldLabels: ['First Name', 'Last Name', 'Email']
  };

  // Mandatory fields for this section
  const mandatoryFields = ['firstName', 'lastName', 'email'];

  // Format URL display for read-only view
  const formatUrl = (url: string) => {
    if (!url) return null;
    if (url.includes('linkedin.com')) return 'Connected';
    return 'Available';
  };

  // Render read-only view
  const renderReadOnlyView = () => (
    <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Name Fields - Same Row */}
        <FieldCard
          label="FIRST NAME"
          value={formData.firstName}
          icon={<User />}
          required
          isEmpty={!formData.firstName}
        />
        <FieldCard
          label="LAST NAME"
          value={formData.lastName}
          icon={<User />}
          required
          isEmpty={!formData.lastName}
        />
        
        {/* Email - Full Width */}
        <FieldCard
          label="EMAIL ADDRESS"
          value={formData.email}
          icon={<Mail />}
          required
          isEmpty={!formData.email}
          colSpan={2}
        />
        
        {/* Phone & Location - Same Row */}
        <FieldCard
          label="PHONE NUMBER"
          value={formData.phone}
          icon={<Phone />}
          isEmpty={!formData.phone}
        />
        <FieldCard
          label="LOCATION"
          value={formData.location}
          icon={<MapPin />}
          isEmpty={!formData.location}
        />
        
        {/* Social Links - Full Width for better visibility */}
        <FieldCard
          label="PORTFOLIO/WEBSITE"
          value={formatUrl(formData.portfolioUrl)}
          icon={<Globe />}
          isEmpty={!formData.portfolioUrl}
          colSpan={2}
        />
        
        {/* Professional Links - Same Row */}
        <FieldCard
          label="LINKEDIN PROFILE"
          value={formatUrl(formData.linkedinUrl)}
          icon={<Linkedin />}
          isEmpty={!formData.linkedinUrl}
        />
        <FieldCard
          label="GITHUB PROFILE"
          value={formatUrl(formData.githubUrl)}
          icon={<Github />}
          isEmpty={!formData.githubUrl}
        />
      </div>
    </div>
  );

  // Render edit form
  const renderEditForm = () => (
    <div className="space-y-4">
      {/* Required Information */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Required Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <ModernFormInput
            label="First Name"
            value={formData.firstName || ''}
            onChange={(value) => onUpdate({ firstName: value })}
            placeholder="Enter your first name"
            required
            validation={validateName}
            prefix={<User className="w-3.5 h-3.5" />}
            size="sm"
          />
          <ModernFormInput
            label="Last Name"
            value={formData.lastName || ''}
            onChange={(value) => onUpdate({ lastName: value })}
            placeholder="Enter your last name"
            required
            validation={validateName}
            prefix={<User className="w-3.5 h-3.5" />}
            size="sm"
          />
        </div>
        <div className="mt-3">
          <ModernFormInput
            label="Email Address"
            type="email"
            value={formData.email || ''}
            onChange={(value) => onUpdate({ email: value })}
            placeholder="<EMAIL>"
            required
            validation={validateEmail}
            prefix={<Mail className="w-3.5 h-3.5" />}
            size="sm"
          />
        </div>
      </div>

      {/* Optional Information */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Additional Information (Optional)</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <ModernFormInput
            label="Phone Number"
            type="tel"
            value={formData.phone || ''}
            onChange={(value) => onUpdate({ phone: value })}
            placeholder="+****************"
            validation={validatePhone}
            prefix={<Phone className="w-3.5 h-3.5" />}
            size="sm"
          />
          
          <ModernFormInput
            label="Location"
            value={formData.location || ''}
            onChange={(value) => onUpdate({ location: value })}
            placeholder="City, State/Country"
            prefix={<MapPin className="w-3.5 h-3.5" />}
            size="sm"
          />
        </div>
        
        <div className="space-y-3 mt-3">
          <ModernFormInput
            label="Portfolio/Website"
            type="url"
            value={formData.portfolioUrl || ''}
            onChange={(value) => onUpdate({ portfolioUrl: value })}
            placeholder="yourwebsite.com"
            prefix={<Globe className="w-3.5 h-3.5" />}
            size="sm"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <ModernFormInput
              label="LinkedIn"
              type="url"
              value={formData.linkedinUrl || ''}
              onChange={(value) => onUpdate({ linkedinUrl: value })}
              placeholder="linkedin.com/in/yourprofile"
              prefix={<Linkedin className="w-3.5 h-3.5" />}
              size="sm"
            />
            
            <ModernFormInput
              label="GitHub"
              type="url"
              value={formData.githubUrl || ''}
              onChange={(value) => onUpdate({ githubUrl: value })}
              placeholder="github.com/yourusername"
              prefix={<Github className="w-3.5 h-3.5" />}
              size="sm"
            />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <UnifiedStepLayout
      formData={formData}
      sectionId="essentials"
      sectionConfig={sectionConfig}
      mandatoryFields={mandatoryFields}
      renderReadOnlyView={renderReadOnlyView}
    >
      {renderEditForm()}
    </UnifiedStepLayout>
  );
};


export default EssentialInfoStep;