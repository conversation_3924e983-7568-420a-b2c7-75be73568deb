'use client';

import React, { useState, useCallback } from 'react';
import { Video, Shield, CheckCircle2, ChevronDown, ChevronUp, Camera, Upload, FileText, X } from 'lucide-react';
import { BaseStepProps } from '../../types';
import { UnifiedStepLayout, FieldCard } from '../../components/UnifiedStepLayout';
import { VideoIntroUpload } from '../../VideoIntroUpload';
import VideoComponent from '@/components/VideoComponent';
import { motion, AnimatePresence } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import StyledCheckboxGroup from '@/components/common/styledInputs/StyledCheckboxGroup';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import apiHelper from '@/lib/apiHelper';

// Document type options
const DOCUMENT_TYPES = ['passport', 'driverLicense', 'nationalId', 'residencePermit'].map(type => ({
  value: type,
  label: type
    .replace(/([A-Z])/g, ' $1')
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' '),
}));

// Helper function to convert display names to values
const getDocumentTypeValue = (displayName: string | null | undefined): string | null => {
  if (!displayName) return null;
  if (DOCUMENT_TYPES.some(dt => dt.value === displayName)) return displayName;
  const matchedType = DOCUMENT_TYPES.find(dt => dt.label === displayName);
  if (matchedType) return matchedType.value;
  const camelCaseValue = displayName
    .split(' ')
    .map((word, index) =>
      index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    .join('');
  if (DOCUMENT_TYPES.some(dt => dt.value === camelCaseValue)) return camelCaseValue;
  return null;
};

// Verification phrases for video recording
const VERIFICATION_PHRASES = [
  'I confirm my identity for job application verification',
  'I am verifying my identity on [Current Date]',
  'This video confirms my identity verification',
  'I am recording this for identity verification purposes',
  'I verify my identity for employment verification',
];

const getRandomVerificationPhrase = () => {
  const randomIndex = Math.floor(Math.random() * VERIFICATION_PHRASES.length);
  return VERIFICATION_PHRASES[randomIndex];
};

const VerificationMediaStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext, uploading, setUploading }) => {
  const [expandedSections, setExpandedSections] = useState<{ [key: string]: boolean }>({ 
    video: false, 
    idVerification: false 
  });
  const [showVideoComponent, setShowVideoComponent] = useState(false);
  const [showVideoRecording, setShowVideoRecording] = useState(false);
  const [verificationPhrase] = useState(getRandomVerificationPhrase());
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  // Get the document type value based on what's in formData
  const documentTypeValue = getDocumentTypeValue(
    formData.verifications?.identity?.documentType
  );
  const [selectedDocType, setSelectedDocType] = useState<string[]>(
    documentTypeValue ? [documentTypeValue] : []
  );
  
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };
  // Section configuration
  const sectionConfig = {
    title: 'Verify your profile',
    subtitle: 'Build trust',
    icon: <Shield className="w-4 h-4" />,
    description: 'Verified profiles get 3x responses',
    benefits: [
      { icon: <Video className="w-3.5 h-3.5" />, text: 'Video engagement' },
      { icon: <CheckCircle2 className="w-3.5 h-3.5" />, text: 'Instant credibility' }
    ],
    requiredFieldLabels: [] // All fields are optional
  };

  // No mandatory fields for this section (all optional)
  const mandatoryFields: string[] = [];

  // Helper to get verification status
  const getVerificationStatus = () => {
    const hasVideo = !!formData.videoIntroUrl || !!formData.verifications?.video?.videoUrl;
    const hasIdDoc = !!formData.verifications?.identity?.documentUrl;
    const idDocType = formData.verifications?.identity?.documentType;
    const verificationPending = formData.verifications?.identity?.status === 'PENDING' || formData.verifications?.video?.status === 'PENDING';
    
    return {
      video: hasVideo ? 'Video uploaded' : 'Not started',
      idVerification: hasIdDoc ? `${idDocType || 'Document'} uploaded` : 'Not started',
      overall: (hasVideo ? 1 : 0) + (hasIdDoc ? 1 : 0),
      isPending: verificationPending
    };
  };
  
  const verificationStatus = getVerificationStatus();
  
  // Render read-only view
  const renderReadOnlyView = () => (
    <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Video Introduction */}
        <FieldCard
          label="VIDEO INTRODUCTION"
          value={verificationStatus.video}
          icon={<Video />}
          isEmpty={verificationStatus.video === 'Not started'}
        />
        
        {/* ID Verification */}
        <FieldCard
          label="ID VERIFICATION"
          value={verificationStatus.idVerification}
          icon={<Shield />}
          isEmpty={verificationStatus.idVerification === 'Not started'}
        />
        
        {/* Verification Status - Full Width */}
        {verificationStatus.isPending && (
          <div className="md:col-span-2">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse" />
                <p className="text-sm text-amber-700 font-medium">Verification Pending</p>
              </div>
              <p className="text-xs text-amber-600 mt-1">
                Your documents are being reviewed. This usually takes 24-48 hours.
              </p>
            </div>
          </div>
        )}
        
        {/* Completion Status - Full Width */}
        {verificationStatus.overall === 2 && !verificationStatus.isPending && (
          <div className="md:col-span-2">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <CheckCircle2 className="w-5 h-5 text-green-500" />
                <p className="text-sm text-green-700 font-medium">Verification Complete</p>
              </div>
              <p className="text-xs text-green-600 mt-1">
                Your profile is fully verified and trusted by employers.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Collapsible Section Component
  const CollapsibleSection = ({ title, icon, children, sectionKey, status }: any) => (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center">
            {icon}
          </div>
          <div className="text-left">
            <h4 className="text-sm font-medium text-gray-900">{title}</h4>
            <p className="text-xs text-gray-500">{status}</p>
          </div>
        </div>
        {expandedSections[sectionKey] ? (
          <ChevronUp className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        )}
      </button>
      <AnimatePresence>
        {expandedSections[sectionKey] && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t border-gray-200"
          >
            <div className="p-4">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
  
  // Render edit form
  const renderEditForm = () => (
    <div className="space-y-4">
      {/* Video Introduction Section */}
      <CollapsibleSection
        title="Video Introduction"
        icon={<Video className="w-4 h-4 text-purple-500" />}
        sectionKey="video"
        status={verificationStatus.video}
      >
        <div className="space-y-3">
          <p className="text-sm text-gray-600 mb-3">
            Record or upload a short video introduction (max 2 minutes) to stand out to employers.
          </p>
          
          {/* Show video recording component if activated */}
          {showVideoRecording ? (
            <VideoComponent
              type="intro"
              onClose={(videoUrl) => {
                setShowVideoRecording(false);
                if (videoUrl) {
                  onUpdate({ videoIntroUrl: videoUrl });
                }
              }}
              storageKey="jobseeker-verification-draft"
            />
          ) : (
            <div className="space-y-4">
              {/* Existing video display */}
              {(formData.videoIntroUrl || formData.verifications?.video?.videoUrl) && (
                <Card className="p-4 border-2 border-primary/20 bg-primary/5">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-primary/10 rounded">
                        <Video className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Video Introduction</h4>
                        <p className="text-xs text-gray-500 mt-1">Video uploaded successfully</p>
                        <a
                          href={formData.videoIntroUrl || formData.verifications?.video?.videoUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-primary hover:underline mt-2 inline-block"
                        >
                          View Video
                        </a>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => {
                        onUpdate({ videoIntroUrl: null });
                        if (formData.verifications?.video) {
                          onUpdate({
                            verifications: {
                              ...formData.verifications,
                              video: null
                            }
                          });
                        }
                      }}
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </Card>
              )}
              
              {/* Action buttons */}
              <div className="flex gap-3">
                <Button
                  onClick={() => setShowVideoRecording(true)}
                  className="flex-1 bg-primary hover:bg-primary/90"
                >
                  <Camera className="w-4 h-4 mr-2" />
                  {formData.videoIntroUrl ? 'Re-record Video' : 'Record Video'}
                </Button>
                <div className="flex-1">
                  <VideoIntroUpload
                    userId={formData.id || ''}
                    currentVideoUrl={formData.videoIntroUrl || formData.verifications?.video?.videoUrl}
                    onUploadComplete={(videoUrl) => onUpdate({ videoIntroUrl: videoUrl })}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </CollapsibleSection>

      {/* ID Verification Section */}
      <CollapsibleSection
        title="ID Verification"
        icon={<Shield className="w-4 h-4 text-purple-500" />}
        sectionKey="idVerification"
        status={verificationStatus.idVerification}
      >
        <div className="space-y-3">
          <p className="text-sm text-gray-600 mb-3">
            Upload your ID document for verification. This helps build trust with employers.
          </p>
          
          {/* Show video component for verification if ID is uploaded */}
          {showVideoComponent ? (
            <VideoComponent
              type="verification"
              verificationPhrase={verificationPhrase}
              onClose={(videoUrl) => {
                setShowVideoComponent(false);
                if (videoUrl) {
                  onUpdate({
                    verifications: {
                      ...formData.verifications,
                      video: {
                        videoUrl,
                        isVerified: false,
                        verifiedAt: null,
                        verificationPhrase,
                        status: 'PENDING',
                      },
                    },
                  });
                }
              }}
              storageKey="jobseeker-verification-draft"
            />
          ) : (
            <div className="space-y-4">
              {/* Document Type Selection */}
              <StyledCheckboxGroup
                label="Select Document Type"
                options={DOCUMENT_TYPES.map(dt => dt.label)}
                values={selectedDocType.map(
                  v => DOCUMENT_TYPES.find(dt => dt.value === v)?.label || v
                )}
                onChange={(values) => handleDocumentTypeChange(values)}
                variant="light"
                allowCustomValues={false}
                tooltip="Select the type of identification document you want to upload"
              />
              
              {/* Verification Status Cards */}
              {formData.verifications?.identity?.documentUrl && (
                <Card className="p-4 border-2 border-primary/20 bg-primary/5">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-primary/10 rounded">
                        <FileText className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">
                          {formData.verifications?.identity?.documentType || 'ID Document'}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">Document uploaded</p>
                        <span className="inline-flex items-center px-2 py-1 mt-2 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full">
                          Verification Pending
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeDocument()}
                      className="text-gray-400 hover:text-gray-500"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </Card>
              )}
              
              {/* Document Upload Area */}
              {!formData.verifications?.identity?.documentUrl && (
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
                    ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300'}
                    ${uploading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <input {...getInputProps()} disabled={uploading} />
                  <div className="space-y-4">
                    <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                      {uploading ? (
                        <FileText className="h-6 w-6 text-primary animate-pulse" />
                      ) : (
                        <Upload className="h-6 w-6 text-primary" />
                      )}
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">
                        {isDragActive
                          ? 'Drop your document here'
                          : uploading
                            ? 'Uploading...'
                            : selectedDocType.length
                              ? 'Drag & drop your ID document here, or click to select'
                              : 'Please select a document type above before uploading'}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">PDF, JPG, or PNG (max 10MB)</p>
                    </div>
                  </div>
                </div>
              )}
              
              {uploadProgress > 0 && uploadProgress < 100 && (
                <div className="mt-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Uploading...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                </div>
              )}
              
              {error && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}
            </div>
          )}
        </div>
      </CollapsibleSection>

      {/* Progress Summary */}
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h5 className="text-sm font-medium text-gray-900">Verification Progress</h5>
          <span className="text-sm font-semibold text-purple-600">
            {verificationStatus.overall}/2 Completed
          </span>
        </div>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <CheckCircle2 className={`w-4 h-4 ${verificationStatus.video !== 'Not started' ? 'text-green-500' : 'text-gray-300'}`} />
            <span className="text-xs text-gray-600">Video Introduction</span>
            {verificationStatus.video !== 'Not started' && (
              <span className="text-xs text-green-600 ml-auto">✓ Uploaded</span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle2 className={`w-4 h-4 ${verificationStatus.idVerification !== 'Not started' ? 'text-green-500' : 'text-gray-300'}`} />
            <span className="text-xs text-gray-600">ID Verification</span>
            {verificationStatus.idVerification !== 'Not started' && (
              <span className="text-xs text-green-600 ml-auto">✓ Uploaded</span>
            )}
          </div>
        </div>
        
        {verificationStatus.isPending && (
          <div className="mt-3 pt-3 border-t border-purple-200">
            <p className="text-xs text-amber-600 flex items-center gap-1">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse" />
              Verification pending - usually takes 24-48 hours
            </p>
          </div>
        )}
        
        {verificationStatus.overall === 0 && (
          <p className="text-xs text-gray-500 mt-3">
            Add verification to build trust and stand out to employers.
          </p>
        )}
        
        {verificationStatus.overall === 1 && (
          <p className="text-xs text-gray-500 mt-3">
            Complete one more verification to maximize your profile visibility.
          </p>
        )}
        
        {verificationStatus.overall === 2 && !verificationStatus.isPending && (
          <p className="text-xs text-green-600 mt-3 font-medium">
            ✨ All verifications complete! Your profile is now more trusted.
          </p>
        )}
      </div>
    </div>
  );

  // Handle document type change
  const handleDocumentTypeChange = (values: string[]) => {
    const docType = values[values.length - 1] as
      | 'passport'
      | 'driverLicense'
      | 'nationalId'
      | 'residencePermit'
      | null;
    setSelectedDocType(docType ? [docType] : []);

    onUpdate({
      verifications: {
        ...formData.verifications,
        identity: {
          ...formData.verifications?.identity,
          documentType: docType
            ? DOCUMENT_TYPES.find(dt => dt.value === docType)?.label
            : null,
        },
      },
    });
  };
  
  // Handle document upload
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (!selectedDocType.length) {
        setError('Please select a document type before uploading');
        return;
      }

      if (acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setSelectedFile(file);
        if (setUploading) setUploading(true);

        try {
          const uploadFormData = new FormData();
          uploadFormData.append('file', file);
          uploadFormData.append(
            'documentType',
            DOCUMENT_TYPES.find(dt => dt.value === selectedDocType[0])?.label ||
              selectedDocType[0]
          );

          // Upload to Digital Ocean Spaces via backend
          const response = await apiHelper.post<{ documentUrl: string }>(
            '/job-seekers/upload/identity',
            uploadFormData
          );

          // Update verification data
          onUpdate({
            verifications: {
              ...formData.verifications,
              identity: {
                documentUrl: response.documentUrl,
                documentType:
                  DOCUMENT_TYPES.find(dt => dt.value === selectedDocType[0])?.label ||
                  selectedDocType[0],
                isVerified: false,
                verifiedAt: null,
                status: 'PENDING',
              },
            },
          });

          // Show video component after successful ID upload
          setShowVideoComponent(true);
        } catch (error) {
          console.error('Upload failed:', error);
          setError('Failed to upload document. Please try again.');
        } finally {
          if (setUploading) setUploading(false);
        }
      }
    },
    [setUploading, selectedDocType, formData, onUpdate]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
    },
    maxFiles: 1,
  });

  // Remove document function
  const removeDocument = () => {
    onUpdate({
      verifications: {
        ...formData.verifications,
        identity: {
          documentUrl: null,
          documentType: null,
          isVerified: false,
          verifiedAt: null,
          status: 'PENDING',
        },
      },
    });
    setSelectedFile(null);
    setUploadProgress(0);
    setSelectedDocType([]);
    setShowVideoComponent(false);
  };

  return (
    <UnifiedStepLayout
      formData={formData}
      sectionId="verification"
      sectionConfig={sectionConfig}
      mandatoryFields={mandatoryFields}
      renderReadOnlyView={renderReadOnlyView}
    >
      {renderEditForm()}
    </UnifiedStepLayout>
  );
};

export default VerificationMediaStep;