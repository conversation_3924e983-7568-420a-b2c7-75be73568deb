'use client';

import React from 'react';
import { Heart, Clock, Globe, Languages, Award, Sparkles } from 'lucide-react';
import { BaseStepProps } from '../../types';
import { UnifiedStepLayout, FieldCard } from '../../components/UnifiedStepLayout';
import { ModernFormInput, ModernTextArea } from '../../components/ModernFormInput';
import { ModernSelector } from '../../components/ModernSelector';

// Common values options
const VALUES_OPTIONS = [
  { value: 'Innovation', label: 'Innovation', icon: <Sparkles className="w-4 h-4" /> },
  { value: 'Collaboration', label: 'Collaboration', icon: <Heart className="w-4 h-4" /> },
  { value: 'Integrity', label: 'Integrity', icon: <Heart className="w-4 h-4" /> },
  { value: 'Excellence', label: 'Excellence', icon: <Award className="w-4 h-4" /> },
  { value: 'Diversity', label: 'Diversity', icon: <Globe className="w-4 h-4" /> },
  { value: 'Growth', label: 'Growth', icon: <Sparkles className="w-4 h-4" /> },
  { value: 'Work-Life Balance', label: 'Work-Life Balance', icon: <Clock className="w-4 h-4" /> },
  { value: 'Sustainability', label: 'Sustainability', icon: <Globe className="w-4 h-4" /> },
];

// Common languages
const LANGUAGE_OPTIONS = [
  { value: 'English', label: 'English', icon: <Languages className="w-4 h-4" /> },
  { value: 'Spanish', label: 'Spanish', icon: <Languages className="w-4 h-4" /> },
  { value: 'French', label: 'French', icon: <Languages className="w-4 h-4" /> },
  { value: 'German', label: 'German', icon: <Languages className="w-4 h-4" /> },
  { value: 'Chinese', label: 'Chinese', icon: <Languages className="w-4 h-4" /> },
  { value: 'Arabic', label: 'Arabic', icon: <Languages className="w-4 h-4" /> },
  { value: 'Hindi', label: 'Hindi', icon: <Languages className="w-4 h-4" /> },
  { value: 'Portuguese', label: 'Portuguese', icon: <Languages className="w-4 h-4" /> },
];

const AdditionalDetailsStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  // Section configuration
  const sectionConfig = {
    title: 'Add additional details',
    subtitle: 'Stand out',
    icon: <Sparkles className="w-4 h-4" />,
    description: 'Additional details improve matches',
    benefits: [
      { icon: <Languages className="w-3.5 h-3.5" />, text: 'Global opportunities' },
      { icon: <Award className="w-3.5 h-3.5" />, text: 'Validated expertise' }
    ],
    requiredFieldLabels: [] // All fields are optional
  };

  // No mandatory fields for this section (all optional)
  const mandatoryFields: string[] = [];

  // Format availability display
  const formatAvailability = () => {
    if (formData.workAvailability?.immediatelyAvailable) {
      return 'Immediately Available';
    }
    if (formData.workAvailability?.noticePeriod) {
      return `${formData.workAvailability.noticePeriod} days notice`;
    }
    return null;
  };

  // Render read-only view
  const renderReadOnlyView = () => (
    <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Languages - Full Width */}
        <FieldCard
          label="LANGUAGES"
          value={formData.languages?.length > 0 ? formData.languages.join(', ') : null}
          icon={<Languages />}
          isEmpty={!formData.languages?.length}
          colSpan={2}
        />
        
        {/* My Values - Full Width */}
        <FieldCard
          label="MY VALUES"
          value={formData.myValues?.length > 0 ? formData.myValues.join(', ') : null}
          icon={<Heart />}
          isEmpty={!formData.myValues?.length}
          colSpan={2}
        />
        
        {/* Certifications & Work Availability - Same Row */}
        <FieldCard
          label="CERTIFICATIONS"
          value={formData.certifications?.length ? `${formData.certifications.length} certification${formData.certifications.length > 1 ? 's' : ''}` : null}
          icon={<Award />}
          isEmpty={!formData.certifications?.length}
        />
        <FieldCard
          label="WORK AVAILABILITY"
          value={formatAvailability()}
          icon={<Clock />}
          isEmpty={!formData.workAvailability}
        />
      </div>
    </div>
  );

  // Render edit form
  const renderEditForm = () => (
    <div className="space-y-4">
      {/* Languages - Optional */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Languages <span className="text-xs text-gray-500">(Optional)</span>
        </h4>
        <ModernSelector
          label=""
          options={LANGUAGE_OPTIONS}
          selectedValues={formData.languages || []}
          onChange={(values) => onUpdate({ languages: values })}
          multiple
          placeholder="Select languages you speak"
          size="sm"
        />
      </div>

      {/* Certifications - Optional */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Certifications <span className="text-xs text-gray-500">(Optional)</span>
        </h4>
        <div className="space-y-3">
          {formData.certifications?.map((cert: any, index: number) => (
            <div key={index} className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium text-sm">{cert.name}</p>
                  {cert.issuer && <p className="text-xs text-gray-600">{cert.issuer}</p>}
                  {cert.date && <p className="text-xs text-gray-500">{cert.date}</p>}
                </div>
                <button
                  onClick={() => {
                    const updated = [...(formData.certifications || [])];
                    updated.splice(index, 1);
                    onUpdate({ certifications: updated });
                  }}
                  className="text-red-500 hover:text-red-700 text-sm"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
          <button
            onClick={() => {
              const newCert = { name: '', issuer: '', issueDate: new Date() } as any;
              onUpdate({ certifications: [...(formData.certifications || []), newCert] });
            }}
            className="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-purple-400 hover:text-purple-600 transition-colors text-sm"
          >
            + Add Certification
          </button>
        </div>
      </div>

      {/* Values - Optional */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          My Values <span className="text-xs text-gray-500">(Optional)</span>
        </h4>
        <ModernSelector
          label=""
          options={VALUES_OPTIONS}
          selectedValues={formData.myValues || []}
          onChange={(values) => onUpdate({ myValues: values })}
          multiple
          placeholder="Select your core values"
          size="sm"
        />
      </div>

      {/* Work Availability - Optional */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Work Availability <span className="text-xs text-gray-500">(Optional)</span>
        </h4>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="immediately-available"
              checked={formData.workAvailability?.immediatelyAvailable || false}
              onChange={(e) => onUpdate({
                workAvailability: {
                  ...formData.workAvailability,
                  immediatelyAvailable: e.target.checked
                }
              })}
              className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
            />
            <label htmlFor="immediately-available" className="text-sm text-gray-700">
              I am immediately available
            </label>
          </div>
          
          {!formData.workAvailability?.immediatelyAvailable && (
            <ModernFormInput
              label="Notice Period (days)"
              value={formData.workAvailability?.noticePeriod?.toString() || ''}
              onChange={(value) => onUpdate({
                workAvailability: {
                  ...formData.workAvailability,
                  noticePeriod: parseInt(value) || 0
                }
              })}
              placeholder="30"
              prefix={<Clock className="w-3.5 h-3.5" />}
              size="sm"
            />
          )}
          
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="willing-to-relocate"
              checked={formData.workAvailability?.willingToRelocate || false}
              onChange={(e) => onUpdate({
                workAvailability: {
                  ...formData.workAvailability,
                  willingToRelocate: e.target.checked
                }
              })}
              className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
            />
            <label htmlFor="willing-to-relocate" className="text-sm text-gray-700">
              Willing to relocate
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <UnifiedStepLayout
      formData={formData}
      sectionId="additional"
      sectionConfig={sectionConfig}
      mandatoryFields={mandatoryFields}
      renderReadOnlyView={renderReadOnlyView}
    >
      {renderEditForm()}
    </UnifiedStepLayout>
  );
};

export default AdditionalDetailsStep;