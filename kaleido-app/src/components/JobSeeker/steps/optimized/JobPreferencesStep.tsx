'use client';

import React from 'react';
import { Briefcase, MapPin, Home, DollarSign, X, Building, Target, Globe, CheckCircle2 } from 'lucide-react';
import { BaseStepProps } from '../../types';
import { ModernSelector } from '../../components/ModernSelector';
import { ModernFormInput } from '../../components/ModernFormInput';
import { UnifiedStepLayout, FieldCard } from '../../components/UnifiedStepLayout';

// Common job types with descriptions - matching backend format
const JOB_TYPES = [
  { value: 'Full-time', label: 'Full-time', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'Part-time', label: 'Part-time', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'Contract', label: 'Contract', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'Freelance', label: 'Freelance', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'Internship', label: 'Internship', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'Back-end Developer', label: 'Back-end Developer', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'Front-end Developer', label: 'Front-end Developer', icon: <Briefcase className="w-4 h-4" /> },
  { value: 'Fullstack Engineer', label: 'Fullstack Engineer', icon: <Briefcase className="w-4 h-4" /> },
];

const REMOTE_PREFERENCES = [
  { value: 'remote', label: 'Remote Only', icon: <Home className="w-4 h-4" /> },
  { value: 'hybrid', label: 'Hybrid', icon: <Home className="w-4 h-4" /> },
  { value: 'onsite', label: 'On-site', icon: <MapPin className="w-4 h-4" /> },
  { value: 'flexible', label: 'Flexible', icon: <Home className="w-4 h-4" /> },
];

const POPULAR_LOCATIONS = [
  { value: 'New York', label: 'New York', icon: <MapPin className="w-4 h-4" /> },
  { value: 'San Francisco', label: 'San Francisco', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Los Angeles', label: 'Los Angeles', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Chicago', label: 'Chicago', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Boston', label: 'Boston', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Seattle', label: 'Seattle', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Austin', label: 'Austin', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Denver', label: 'Denver', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Dubai', label: 'Dubai', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Abu Dhabi', label: 'Abu Dhabi', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Riyadh', label: 'Riyadh', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Singapore', label: 'Singapore', icon: <MapPin className="w-4 h-4" /> },
  { value: 'London', label: 'London', icon: <MapPin className="w-4 h-4" /> },
  { value: 'Manchester', label: 'Manchester', icon: <MapPin className="w-4 h-4" /> },
];

const INDUSTRIES = [
  { value: 'Artificial Intelligence', label: 'Artificial Intelligence', icon: <Building className="w-4 h-4" /> },
  { value: 'Cybersecurity', label: 'Cybersecurity', icon: <Building className="w-4 h-4" /> },
  { value: 'Aerospace', label: 'Aerospace', icon: <Building className="w-4 h-4" /> },
  { value: 'Technology', label: 'Technology', icon: <Building className="w-4 h-4" /> },
  { value: 'Finance', label: 'Finance', icon: <Building className="w-4 h-4" /> },
  { value: 'Healthcare', label: 'Healthcare', icon: <Building className="w-4 h-4" /> },
  { value: 'E-commerce', label: 'E-commerce', icon: <Building className="w-4 h-4" /> },
  { value: 'Education', label: 'Education', icon: <Building className="w-4 h-4" /> },
  { value: 'Entertainment', label: 'Entertainment', icon: <Building className="w-4 h-4" /> },
  { value: 'Manufacturing', label: 'Manufacturing', icon: <Building className="w-4 h-4" /> },
];

const CURRENCIES = [
  { value: 'USD', label: 'USD ($)', icon: <DollarSign className="w-4 h-4" /> },
  { value: 'EUR', label: 'EUR (€)', icon: <DollarSign className="w-4 h-4" /> },
  { value: 'GBP', label: 'GBP (£)', icon: <DollarSign className="w-4 h-4" /> },
  { value: 'CAD', label: 'CAD (C$)', icon: <DollarSign className="w-4 h-4" /> },
];

const PERIODS = [
  { value: 'yearly', label: 'Per Year', icon: <DollarSign className="w-4 h-4" /> },
  { value: 'monthly', label: 'Per Month', icon: <DollarSign className="w-4 h-4" /> },
  { value: 'hourly', label: 'Per Hour', icon: <DollarSign className="w-4 h-4" /> },
  { value: 'bi-weekly', label: 'Bi-weekly', icon: <DollarSign className="w-4 h-4" /> },
  { value: 'weekly', label: 'Weekly', icon: <DollarSign className="w-4 h-4" /> },
];

const JobPreferencesStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  // Ensure preferences object exists
  const preferences = formData.preferences || {};
  
  // Section configuration
  const sectionConfig = {
    title: 'Define your job preferences',
    subtitle: 'Why this matters',
    icon: <Target className="w-4 h-4" />,
    description: '3x better job matches with clear preferences',
    benefits: [
      { icon: <DollarSign className="w-3.5 h-3.5" />, text: 'Salary clarity' },
      { icon: <Globe className="w-3.5 h-3.5" />, text: 'Remote options' }
    ],
    requiredFieldLabels: ['Job Types', 'Locations', 'Remote', 'Salary']
  };

  // Mandatory fields for this section
  const mandatoryFields = [
    'preferences.jobTypes',
    'preferences.locations',
    'preferences.remotePreference',
    'preferences.desiredSalary'
  ];

  const updatePreferences = (updates: any) => {
    onUpdate({
      preferences: {
        ...preferences,
        ...updates,
      },
    });
  };

  const formatSalary = () => {
    if (!preferences.desiredSalary?.min || !preferences.desiredSalary?.max) {
      return null;
    }
    const { min, max, currency = 'USD', period = 'yearly' } = preferences.desiredSalary;
    return `${currency} ${min.toLocaleString()}-${max.toLocaleString()} ${period}`;
  };

  // Render read-only view
  const renderReadOnlyView = () => (
    <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Job Types - Full Width */}
        <FieldCard
          label="JOB TYPES"
          value={preferences.jobTypes?.length > 0 ? preferences.jobTypes.join(', ') : null}
          icon={<Briefcase />}
          required
          isEmpty={!preferences.jobTypes?.length}
          colSpan={2}
        />
        
        {/* Preferred Locations - Full Width */}
        <FieldCard
          label="PREFERRED LOCATIONS"
          value={preferences.locations?.length > 0 ? preferences.locations.join(', ') : null}
          icon={<MapPin />}
          required
          isEmpty={!preferences.locations?.length}
          colSpan={2}
        />
        
        {/* Industries - Full Width */}
        <FieldCard
          label="INDUSTRIES"
          value={preferences.industries?.length > 0 ? preferences.industries.join(', ') : null}
          icon={<Building />}
          isEmpty={!preferences.industries?.length}
          colSpan={2}
        />
        
        {/* Remote Preference & Salary - Same Row */}
        <FieldCard
          label="REMOTE PREFERENCE"
          value={preferences.remotePreference}
          icon={<Globe />}
          required
          isEmpty={!preferences.remotePreference}
        />
        <FieldCard
          label="DESIRED SALARY"
          value={formatSalary()}
          icon={<DollarSign />}
          required
          isEmpty={!formatSalary()}
        />
      </div>
    </div>
  );

  // Render edit form
  const renderEditForm = () => (
    <div className="space-y-4">
      {/* Job Types - Required */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Job Types <span className="text-purple-500">*</span>
        </h4>
        <ModernSelector
          label=""
          options={JOB_TYPES}
          selectedValues={preferences.jobTypes || []}
          onChange={(values) => updatePreferences({ jobTypes: values })}
          multiple
          placeholder="Select job types"
          size="sm"
        />
        {(!preferences.jobTypes || preferences.jobTypes.length === 0) && (
          <p className="text-xs text-amber-600 mt-1">Select at least one job type</p>
        )}
      </div>

      {/* Preferred Locations - Required */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Preferred Locations <span className="text-purple-500">*</span>
        </h4>
        <ModernSelector
          label=""
          options={POPULAR_LOCATIONS}
          selectedValues={preferences.locations || []}
          onChange={(values) => updatePreferences({ locations: values })}
          multiple
          placeholder="Select locations"
          size="sm"
        />
        {(!preferences.locations || preferences.locations.length === 0) && (
          <p className="text-xs text-amber-600 mt-1">Add at least one location</p>
        )}
      </div>

      {/* Remote Preference - Required */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Work Style <span className="text-purple-500">*</span>
        </h4>
        <ModernSelector
          label=""
          options={REMOTE_PREFERENCES}
          selectedValues={preferences.remotePreference ? [preferences.remotePreference] : []}
          onChange={(values) => updatePreferences({ remotePreference: values[0] })}
          placeholder="Select work style preference"
          size="sm"
        />
        {!preferences.remotePreference && (
          <p className="text-xs text-amber-600 mt-1">Select your work style preference</p>
        )}
      </div>

      {/* Industries - Optional */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Preferred Industries <span className="text-xs text-gray-500">(Optional)</span>
        </h4>
        <ModernSelector
          label=""
          options={INDUSTRIES}
          selectedValues={preferences.industries || []}
          onChange={(values) => updatePreferences({ industries: values })}
          multiple
          placeholder="Select industries"
          size="sm"
        />
      </div>

      {/* Compensation - Required */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">
          Compensation Expectations <span className="text-purple-500">*</span>
        </h4>
        <div className="grid grid-cols-2 gap-3">
          <ModernFormInput
            label="Minimum"
            value={preferences.desiredSalary?.min?.toString() || ''}
            onChange={(value) => updatePreferences({
              desiredSalary: {
                ...preferences.desiredSalary,
                min: parseInt(value) || 0,
              }
            })}
            placeholder="50000"
            prefix={<DollarSign className="w-3.5 h-3.5" />}
            size="sm"
          />
          <ModernFormInput
            label="Maximum"
            value={preferences.desiredSalary?.max?.toString() || ''}
            onChange={(value) => updatePreferences({
              desiredSalary: {
                ...preferences.desiredSalary,
                max: parseInt(value) || 0,
              }
            })}
            placeholder="80000"
            prefix={<DollarSign className="w-3.5 h-3.5" />}
            size="sm"
          />
        </div>
        <div className="grid grid-cols-2 gap-3 mt-3">
          <ModernSelector
            label="Currency"
            options={CURRENCIES}
            selectedValues={preferences.desiredSalary?.currency ? [preferences.desiredSalary.currency] : ['USD']}
            onChange={(values) => updatePreferences({
              desiredSalary: {
                ...preferences.desiredSalary,
                currency: values[0] || 'USD',
              }
            })}
            size="sm"
          />
          <ModernSelector
            label="Period"
            options={PERIODS}
            selectedValues={preferences.desiredSalary?.period ? [preferences.desiredSalary.period] : ['yearly']}
            onChange={(values) => updatePreferences({
              desiredSalary: {
                ...preferences.desiredSalary,
                period: values[0] || 'yearly',
              }
            })}
            size="sm"
          />
        </div>
        {(!preferences.desiredSalary?.min || !preferences.desiredSalary?.max) && (
          <p className="text-xs text-amber-600 mt-1">Enter your salary expectations</p>
        )}
      </div>
    </div>
  );

  return (
    <UnifiedStepLayout
      formData={formData}
      sectionId="preferences"
      sectionConfig={sectionConfig}
      mandatoryFields={mandatoryFields}
      renderReadOnlyView={renderReadOnlyView}
    >
      {renderEditForm()}
    </UnifiedStepLayout>
  );
};


export default JobPreferencesStep;