'use client';

import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, CheckCircle2, Eye, Lock, Rocket, Users, Shield, Globe, Star, Sparkles, TrendingUp, Award, Briefcase, GraduationCap, Video, FileText, Mail, Wallet } from 'lucide-react';
import React from 'react';
import { BaseStepProps } from '../../types';
import { UnifiedStepLayout, FieldCard } from '../../components/UnifiedStepLayout';
import { ModernSelector } from '../../components/ModernSelector';

const PrivacyLaunchStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {

  // Privacy setting options
  const visibilityOptions = [
    { value: 'Public', label: 'Public', description: 'Visible to all employers' },
    { value: 'Private', label: 'Private', description: 'Only visible to selected employers' },
    { value: 'Connections', label: 'Connections Only', description: 'Visible to your network' },
  ];

  // Handle privacy settings update
  const handlePrivacyUpdate = (field: string, value: any) => {
    onUpdate({
      privacySettings: {
        ...formData.privacySettings,
        [field]: value,
      },
    });
  };

  // Calculate profile completion
  const mandatoryComplete = !!(
    formData.firstName &&
    formData.lastName &&
    formData.email &&
    formData.skills?.length > 0 &&
    formData.preferences?.jobTypes?.length > 0 &&
    formData.preferences?.locations?.length > 0 &&
    formData.preferences?.remotePreference &&
    formData.preferences?.desiredSalary?.min &&
    formData.preferences?.desiredSalary?.max
  );

  const profileSections = {
    required: {
      essentials: { 
        label: 'Essential Info', 
        complete: !!(formData.firstName && formData.lastName && formData.email),
        icon: <Users className="w-4 h-4" />
      },
      skills: { 
        label: 'Skills', 
        complete: formData.skills?.length > 0,
        icon: <Star className="w-4 h-4" />
      },
      preferences: { 
        label: 'Job Preferences', 
        complete: !!(formData.preferences?.jobTypes?.length > 0 && formData.preferences?.locations?.length > 0),
        icon: <Briefcase className="w-4 h-4" />
      },
    },
    optional: {
      summary: { 
        label: 'Professional Summary', 
        complete: !!formData.summary?.trim(),
        icon: <FileText className="w-4 h-4" />
      },
      experience: { 
        label: 'Work Experience', 
        complete: formData.experience?.length > 0,
        icon: <Briefcase className="w-4 h-4" />
      },
      education: { 
        label: 'Education', 
        complete: formData.education?.length > 0,
        icon: <GraduationCap className="w-4 h-4" />
      },
      portfolio: { 
        label: 'Portfolio & Links', 
        complete: !!(formData.portfolioUrl || formData.linkedinUrl || formData.githubUrl),
        icon: <Globe className="w-4 h-4" />
      },
      video: { 
        label: 'Video Introduction', 
        complete: !!formData.videoIntroUrl,
        icon: <Video className="w-4 h-4" />
      },
      verification: { 
        label: 'ID Verification', 
        complete: !!formData.verifications?.identity?.documentUrl,
        icon: <Shield className="w-4 h-4" />
      },
    }
  };

  const completedRequired = Object.values(profileSections.required).filter(item => item.complete).length;
  const totalRequired = Object.keys(profileSections.required).length;
  const completedOptional = Object.values(profileSections.optional).filter(item => item.complete).length;
  const totalOptional = Object.keys(profileSections.optional).length;
  const overallCompletion = Math.round(
    ((completedRequired + completedOptional) / (totalRequired + totalOptional)) * 100
  );

  // Section configuration
  const sectionConfig = {
    title: 'Review and launch your profile',
    subtitle: 'Final step',
    icon: <Rocket className="w-4 h-4" />,
    description: 'Complete profiles get 5x more views',
    benefits: [
      { icon: <CheckCircle2 className="w-3.5 h-3.5" />, text: `${overallCompletion}% complete` },
      { icon: <TrendingUp className="w-3.5 h-3.5" />, text: 'Ready to launch' }
    ],
    requiredFieldLabels: []
  };

  // No mandatory fields for this review section
  const mandatoryFields: string[] = [];

  // Render read-only view
  const renderReadOnlyView = () => (
    <div className="space-y-6">
      {/* Profile Completion Card */}
      <div className="bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 rounded-xl p-6 border border-purple-100">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Profile Strength</h3>
            <p className="text-sm text-gray-600 mt-1">
              {mandatoryComplete ? 'Your profile is ready to go live' : 'Complete required sections to activate'}
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              {overallCompletion}%
            </div>
            <p className="text-xs text-gray-500">Complete</p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-white/50 rounded-full h-3 mb-6">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${overallCompletion}%` }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="h-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 shadow-sm"
          />
        </div>
        
        {/* Sections Grid */}
        <div className="space-y-4">
          {/* Required Sections */}
          <div>
            <p className="text-xs font-semibold text-gray-700 uppercase tracking-wider mb-2">Required Sections</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {Object.entries(profileSections.required).map(([key, section]) => (
                <div
                  key={key}
                  className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
                    section.complete 
                      ? 'bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200' 
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    section.complete ? 'bg-white text-purple-600' : 'bg-gray-100 text-gray-400'
                  }`}>
                    {section.icon}
                  </div>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${
                      section.complete ? 'text-purple-900' : 'text-gray-600'
                    }`}>
                      {section.label}
                    </p>
                  </div>
                  {section.complete && (
                    <CheckCircle2 className="w-4 h-4 text-purple-500" />
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Optional Sections */}
          <div>
            <p className="text-xs font-semibold text-gray-700 uppercase tracking-wider mb-2">Optional Enhancements</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {Object.entries(profileSections.optional).map(([key, section]) => (
                <div
                  key={key}
                  className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
                    section.complete 
                      ? 'bg-gradient-to-r from-purple-100 to-pink-100 border border-purple-200' 
                      : 'bg-white border border-gray-200'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    section.complete ? 'bg-white text-purple-600' : 'bg-gray-100 text-gray-400'
                  }`}>
                    {section.icon}
                  </div>
                  <div className="flex-1">
                    <p className={`text-sm font-medium ${
                      section.complete ? 'text-purple-900' : 'text-gray-600'
                    }`}>
                      {section.label}
                    </p>
                  </div>
                  {section.complete && (
                    <CheckCircle2 className="w-4 h-4 text-purple-500" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* Privacy Settings Summary */}
      <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FieldCard
            label="PROFILE VISIBILITY"
            value={formData.privacySettings?.profileVisibility || 'Public'}
            icon={<Eye />}
            isEmpty={!formData.privacySettings?.profileVisibility}
          />
          <FieldCard
            label="CONTACT INFO"
            value={formData.privacySettings?.showContact ? 'Visible' : 'Hidden'}
            icon={<Mail />}
            isEmpty={formData.privacySettings?.showContact === undefined}
          />
          <FieldCard
            label="SALARY EXPECTATIONS"
            value={formData.privacySettings?.showSalary ? 'Visible' : 'Hidden'}
            icon={<Wallet />}
            isEmpty={formData.privacySettings?.showSalary === undefined}
          />
          <FieldCard
            label="MESSAGES"
            value={formData.privacySettings?.allowMessages ? 'Enabled' : 'Disabled'}
            icon={<Shield />}
            isEmpty={formData.privacySettings?.allowMessages === undefined}
          />
        </div>
      </div>
    </div>
  );

  // Render edit form
  const renderEditForm = () => (
    <div className="space-y-6">
      {/* Privacy Settings Section */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Privacy Settings</h4>
        
        {/* Profile Visibility */}
        <div className="space-y-4">
          <ModernSelector
            label="Profile Visibility"
            options={visibilityOptions}
            selectedValues={formData.privacySettings?.profileVisibility ? [formData.privacySettings.profileVisibility] : ['Public']}
            onChange={(values) => handlePrivacyUpdate('profileVisibility', values[0])}
            placeholder="Select visibility level"
            multiple={false}
            size="sm"
          />
          
          {/* Checkboxes for other settings */}
          <div className="space-y-3 mt-4">
            <label className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-purple-200 transition-colors cursor-pointer">
              <input
                type="checkbox"
                checked={formData.privacySettings?.showContact ?? true}
                onChange={(e) => handlePrivacyUpdate('showContact', e.target.checked)}
                className="w-4 h-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <div className="flex items-center gap-2 flex-1">
                <Mail className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Show Contact Information</p>
                  <p className="text-xs text-gray-500">Allow employers to see your email and phone</p>
                </div>
              </div>
            </label>
            
            <label className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-purple-200 transition-colors cursor-pointer">
              <input
                type="checkbox"
                checked={formData.privacySettings?.showSalary ?? true}
                onChange={(e) => handlePrivacyUpdate('showSalary', e.target.checked)}
                className="w-4 h-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <div className="flex items-center gap-2 flex-1">
                <Wallet className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Show Salary Expectations</p>
                  <p className="text-xs text-gray-500">Display your desired salary range to employers</p>
                </div>
              </div>
            </label>
            
            <label className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-purple-200 transition-colors cursor-pointer">
              <input
                type="checkbox"
                checked={formData.privacySettings?.allowMessages ?? true}
                onChange={(e) => handlePrivacyUpdate('allowMessages', e.target.checked)}
                className="w-4 h-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <div className="flex items-center gap-2 flex-1">
                <Shield className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Allow Messages</p>
                  <p className="text-xs text-gray-500">Let employers send you direct messages</p>
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>
      
      {/* Profile Completion Tips */}
      {completedOptional < totalOptional && (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-start gap-3">
            <Sparkles className="w-5 h-5 text-purple-600 mt-0.5" />
            <div className="flex-1">
              <h5 className="text-sm font-medium text-purple-900 mb-1">Boost Your Profile</h5>
              <p className="text-xs text-purple-700 mb-2">
                Complete {totalOptional - completedOptional} more section{totalOptional - completedOptional > 1 ? 's' : ''} to increase visibility by {Math.round((totalOptional - completedOptional) * 15)}%
              </p>
              <div className="space-y-1">
                {Object.entries(profileSections.optional).filter(([_, section]) => !section.complete).map(([key, section]) => (
                  <div key={key} className="flex items-center gap-2 text-xs text-purple-600">
                    <div className="w-1.5 h-1.5 rounded-full bg-purple-400" />
                    <span>Add {section.label.toLowerCase()}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Launch Status */}
      <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 border border-gray-100 text-center">
        <Rocket className={`w-12 h-12 mx-auto mb-3 ${
          mandatoryComplete ? 'text-purple-500' : 'text-gray-400'
        }`} />
        <h3 className="text-base font-semibold text-gray-900 mb-1">
          {mandatoryComplete ? 'Ready to Launch!' : 'Almost There'}
        </h3>
        <p className="text-sm text-gray-600">
          {mandatoryComplete 
            ? 'Your profile is complete and ready to be discovered'
            : `Complete ${totalRequired - completedRequired} required section${totalRequired - completedRequired > 1 ? 's' : ''} to activate`
          }
        </p>
      </div>
    </div>
  );

  return (
    <UnifiedStepLayout
      formData={formData}
      sectionId="privacy-launch"
      sectionConfig={sectionConfig}
      mandatoryFields={mandatoryFields}
      renderReadOnlyView={renderReadOnlyView}
    >
      {renderEditForm()}
    </UnifiedStepLayout>
  );
};

export default PrivacyLaunchStep;
