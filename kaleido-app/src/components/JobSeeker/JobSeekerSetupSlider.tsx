'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import {
  CompletionState,
  IJobSeekerProfile,
  JobSeekerSetupSliderProps,
  StepComponentProps,
} from './types';

import { getValidationMessage, validateSlide } from './utils/validation';

import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/shared/types';
import { AlertTriangle, Info, X } from 'lucide-react';
// import Image from 'next/image'; // Using regular img tags for better compatibility
import Confetti from 'react-confetti';
import { CompletionView } from './components/CompletionView';
import { ConfirmationDialog } from './components/ConfirmationDialog';
import LinkedInProfileHandler from './components/LinkedInProfileHandler';
import <PERSON>UrlHandler from './components/StepUrlHandler';
import ValidationHandler from './components/ValidationHandler';
// Import our new utilities and components
import useUser from '@/hooks/useUser';
import { useJobSeekerStore } from '@/stores/jobSeekerStore';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { showToast } from '../Toaster';
import { setupSlides } from './constants/slides';
import { fetchJobSeekerProfile } from './utils/profileUtils';
// Import optimized flow components
import { ProfileAssistant } from '../ProfileAssistant';
import {
  calculateOverallProgress,
  getOptimizedValidationMessage,
  optimizedSetupSlides,
  validateMandatoryFields,
} from './constants/optimizedSlides';

export const JobSeekerSetupSliderInner: React.FC<JobSeekerSetupSliderProps> = ({
  onComplete,
  onClose,
  initialData,
  initialStep,
  validationResponse,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [completion, setCompletion] = useState<CompletionState>({
    show: false,
    animate: false,
  });
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [useOptimizedFlow, setUseOptimizedFlow] = useState(true); // Toggle for optimized flow
  // Get user data - will be null on holding pages before authentication
  const { user } = useUser();
  const { formData, updateFormData, setFormData, saveStep, isLoading, setLinkedInData } =
    useJobSeekerStore();
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  // Helper function to refresh data instead of full page reload
  const handleDataRefresh = () => {
    invalidateJobsCache();
    refreshJobs(true);

    // Clear any cached data
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(
        key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
      );
      cacheKeys.forEach(key => localStorage.removeItem(key));
    }
  };

  // Initialize store with initial data
  useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      // Use setFormData directly with the initial data - don't merge with current formData
      // as it might be empty at this point
      setFormData(initialData as IJobSeekerProfile);
    }
  }, [initialData, setFormData]);

  // Only fetch profile if we don't have initial data AND don't have existing form data
  useEffect(() => {
    // Check if we already have profile data in the store
    const hasExistingData = formData?.id || formData?.email || formData?.firstName;

    if (user?.sub && (!initialData || Object.keys(initialData).length === 0) && !hasExistingData) {
      // Pass true to skipAutoCreate to prevent automatic profile creation on 404
      fetchJobSeekerProfile(user, updateFormData, true);
    }
  }, [user, initialData, formData?.id]);

  // ValidationHandler component now handles this logic

  // Use our extracted components for validation and LinkedIn profile handling
  useEffect(() => {
    if (validationResponse) {
      // This will be handled by ValidationHandler component
    }
  }, [validationResponse]);

  // Handle LinkedIn profile data if available
  useEffect(() => {
    if (initialData?.linkedInProfile) {
      // This will be handled by LinkedInProfileHandler component
    }
  }, [initialData]);

  // Handle URL step parameters
  useEffect(() => {
    // This will be handled by StepUrlHandler component
  }, [searchParams, initialStep]);

  const handleUpdate = (data: Partial<IJobSeekerProfile>) => {
    updateFormData(data);
  };

  // Handle skip optional for ProfileAssistant
  const handleSkipOptional = () => {
    const slides = useOptimizedFlow ? optimizedSetupSlides : setupSlides;
    const currentSlide = slides[currentSlideIndex];
    if (useOptimizedFlow && 'canSkip' in currentSlide && currentSlide.canSkip) {
      handleNext();
    } else if (useOptimizedFlow && 'isOptional' in currentSlide && currentSlide.isOptional) {
      handleNext();
    }
  };

  // Handle get help for ProfileAssistant
  const handleGetHelp = () => {
    showToast({
      message: 'Help documentation coming soon!',
      isSuccess: true,
    });
  };

  const handleNext = async () => {
    const slides = useOptimizedFlow ? optimizedSetupSlides : setupSlides;
    const currentSlide = slides[currentSlideIndex];

    // Check if current slide is valid before proceeding
    if (!isCurrentSlideValid()) {
      showToast({
        message: 'Please complete all required fields before continuing.',
        isSuccess: false,
      });
      return;
    }

    try {
      if (currentSlideIndex === slides.length - 1) {
        // On the last step, skip saveStep and go directly to handleComplete
        // handleComplete will do its own saving with proper data sanitization

        // Dispatch profile completion event for success modal
        const event = new CustomEvent('profileSetupCompleted', {
          detail: {
            action: 'profileSetup',
            actionType: 'completed',
            status: 'success',
            title: 'Profile Setup Complete!',
            description:
              'Your profile has been completed successfully. You can now apply for jobs and connect with employers.',
            stats: {
              'Profile Completion': '100%',
              'Steps Completed': slides.length,
            },
            closeManager: () => {
              // Refresh data when modal is closed to show updated profile state
              handleDataRefresh();
            },
          },
        });
        window.dispatchEvent(event);

        // Automatically complete the onboarding
        await handleComplete();
      } else {
        // For non-final steps, save current step data to backend
        await saveStep(currentSlide.id);

        // Small delay to ensure state propagation
        await new Promise(resolve => setTimeout(resolve, 50));
        const nextIndex = currentSlideIndex + 1;
        const nextSlide = slides[nextIndex];

        // Update the URL and navigate to the next step
        const params = new URLSearchParams(searchParams.toString());
        params.set('step', nextSlide.id);

        // Set the slide index first to ensure state is ready
        setCurrentSlideIndex(nextIndex);

        // Then update the URL
        router.push(`?${params.toString()}`);
      }
    } catch (error) {
      console.error('Error saving step:', error);
      showToast({
        message: 'Failed to save progress. Please try again.',
        isSuccess: false,
      });
    }
  };

  const handleBack = () => {
    if (currentSlideIndex === 0) {
      handleCloseAttempt();
    } else {
      const slides = useOptimizedFlow ? optimizedSetupSlides : setupSlides;
      const prevIndex = currentSlideIndex - 1;
      const prevSlide = slides[prevIndex];

      const params = new URLSearchParams(searchParams.toString());
      params.set('step', prevSlide.id);
      router.push(`?${params.toString()}`);

      setCurrentSlideIndex(prevIndex);
    }
  };

  const handleCloseAttempt = () => {
    // Refresh data when closing the slider
    handleDataRefresh();
    if (onClose) {
      onClose();
    }
  };

  const isCurrentSlideValid = () => {
    const slides = useOptimizedFlow ? optimizedSetupSlides : setupSlides;
    const currentSlide = slides[currentSlideIndex];

    // Check if currentSlide exists
    if (!currentSlide) {
      return false;
    }

    // For optimized flow, use new validation
    if (useOptimizedFlow) {
      // Optional steps are always valid
      if ('isOptional' in currentSlide && currentSlide.isOptional) {
        return true;
      }
      if ('canSkip' in currentSlide && currentSlide.canSkip) {
        return true;
      }
      return validateMandatoryFields(currentSlide.id, formData);
    }

    // For legacy flow
    if (currentSlide.id === 'portfolio') {
      return true;
    }
    // Type assertion needed as optimized slides have different IDs
    return validateSlide(currentSlide.id as any, formData);
  };

  const handleComplete = async () => {
    try {
      const jobSeekerId = searchParams.get('jobSeekerId');

      // Deeply sanitize education entries to ensure no null fields
      const sanitizedEducation = formData.education.map(edu => ({
        ...edu,
        degree: typeof edu.degree === 'string' && edu.degree ? edu.degree : 'Not specified',
        field: typeof edu.field === 'string' && edu.field ? edu.field : 'Not specified',
        institution:
          typeof edu.institution === 'string' && edu.institution
            ? edu.institution
            : 'Not specified',
      }));

      // Sanitize preferences data
      let sanitizedPreferences = formData.preferences;
      if (sanitizedPreferences) {
        // Ensure desiredSalary is a string
        if (
          sanitizedPreferences.desiredSalary &&
          sanitizedPreferences.desiredSalary.min &&
          sanitizedPreferences.desiredSalary.max
        ) {
          const min = sanitizedPreferences.desiredSalary.min;
          const max = sanitizedPreferences.desiredSalary.max;
          const currency = sanitizedPreferences.desiredSalary.currency || 'USD';
          const period = sanitizedPreferences.desiredSalary.period || 'yearly';

          sanitizedPreferences = {
            ...sanitizedPreferences,
            desiredSalary: {
              min,
              max,
              currency,
              period,
            },
          };

          // Remove the internal desiredSalary
          delete sanitizedPreferences.desiredSalary;
        } else if (typeof sanitizedPreferences.desiredSalary === 'object') {
          // Make sure the object has all required fields
          if (sanitizedPreferences.desiredSalary?.min && sanitizedPreferences.desiredSalary?.max) {
            const min = sanitizedPreferences.desiredSalary.min;
            const max = sanitizedPreferences.desiredSalary.max;
            const currency = sanitizedPreferences.desiredSalary.currency || 'USD';
            const period = sanitizedPreferences.desiredSalary.period || 'yearly';

            sanitizedPreferences = {
              ...sanitizedPreferences,
              desiredSalary: {
                min,
                max,
                currency,
                period,
              },
            };
          } else {
            // If incomplete, remove it
            delete sanitizedPreferences.desiredSalary;
          }
        }
      }

      // Sanitize location field - convert object to string if needed
      let sanitizedLocation = formData.location;
      if (typeof sanitizedLocation === 'object' && sanitizedLocation !== null) {
        const loc = sanitizedLocation as any;
        if (loc.country) {
          sanitizedLocation = loc.country;
        } else {
          sanitizedLocation = '';
        }
      } else if (!sanitizedLocation || typeof sanitizedLocation !== 'string') {
        sanitizedLocation = '';
      }

      const updatedFormData = {
        ...formData,
        projects: formData.projects.map(project => {
          const { startDate, endDate, ...rest } = project;
          return rest;
        }),
        // Replace education with sanitized version
        education: sanitizedEducation,
        // Set sanitized preferences
        preferences: sanitizedPreferences,
        // Set sanitized location
        location: sanitizedLocation,
        id: jobSeekerId,
        clientId: user?.sub,
        hasCompletedOnboarding: true,
        updatedAt: new Date().toISOString(),
        myValues: formData.myValues || [],
      };

      if (jobSeekerId) {
        await apiHelper.put(`/job-seekers/${jobSeekerId}`, updatedFormData);
      } else {
        await apiHelper.post('/job-seekers', updatedFormData);
      }

      // Check for pending job application and redirect back to the job
      try {
        const pendingJobId = localStorage.getItem('headstart_pending_job_application');
        const redirectUrl = localStorage.getItem('headstart_redirect_after_auth');

        if (pendingJobId && redirectUrl) {
          // Clear the stored application intent
          localStorage.removeItem('headstart_pending_job_application');
          localStorage.removeItem('headstart_redirect_after_auth');

          // Redirect back to the job page
          window.location.href = redirectUrl;
          return;
        }
      } catch (error) {
        console.error('Error checking pending job application:', error);
      }

      onComplete(updatedFormData);
    } catch (error) {
      console.error('Error completing setup:', error);
      showToast({
        message: 'Failed to save profile. Please try again.',
        isSuccess: false,
      });
    }
  };

  const completionPercentage = useMemo(() => {
    if (useOptimizedFlow) {
      // For optimized flow, calculate based on mandatory fields
      return calculateOverallProgress(formData);
    }

    // Use onboardingProgress data if available for more accurate progress
    if (validationResponse?.onboardingProgress) {
      return validationResponse.onboardingProgress.overall.percentage;
    }

    const slides = useOptimizedFlow ? optimizedSetupSlides : setupSlides;
    // Fall back to slide-based percentage
    return ((currentSlideIndex + 1) / slides.length) * 100;
  }, [currentSlideIndex, validationResponse, formData, useOptimizedFlow]);

  // Get section completion data
  const getSectionCompletionStatus = (sectionId: string) => {
    if (useOptimizedFlow) {
      // For optimized flow, calculate based on mandatory fields
      const slide = optimizedSetupSlides.find(s => s.id === sectionId);
      if (!slide || !slide.mandatoryFields) return null;

      const completed = slide.mandatoryFields.filter(field => {
        if (field === 'skills') return formData.skills?.length > 0;
        if (field === 'jobTypes') return formData.preferences?.jobTypes?.length > 0;
        if (field === 'locations') return formData.preferences?.locations?.length > 0;
        if (field === 'remotePreference') return !!formData.preferences?.remotePreference;
        if (field === 'desiredSalary') {
          return !!(
            formData.preferences?.desiredSalary?.min && formData.preferences?.desiredSalary?.max
          );
        }
        return !!(formData as any)[field];
      });

      return {
        percentage: Math.round((completed.length / slide.mandatoryFields.length) * 100),
        requiredFields: slide.mandatoryFields,
        completedFields: completed,
      };
    }

    if (!validationResponse?.onboardingProgress) return null;

    switch (sectionId) {
      case 'basics':
      case 'essentials':
        return validationResponse.onboardingProgress.basicInfo;
      case 'professional':
        return validationResponse.onboardingProgress.professionalInfo;
      case 'preferences':
        return validationResponse.onboardingProgress.preferences;
      default:
        return null;
    }
  };

  // Check if current step has missing mandatory fields
  const hasMissingMandatoryFields = () => {
    const slides = useOptimizedFlow ? optimizedSetupSlides : setupSlides;
    const currentSlide = slides[currentSlideIndex];

    if (!useOptimizedFlow) return false;
    if (!('mandatoryFields' in currentSlide) || !currentSlide.mandatoryFields) return false;

    return currentSlide.mandatoryFields.some(field => {
      const fieldPath = field.split('.');
      let value: any = formData;

      for (const path of fieldPath) {
        value = value?.[path];
      }

      if (Array.isArray(value)) return value.length === 0;
      return !value;
    });
  };

  // Get missing fields for current section
  const getMissingSectionFields = (sectionId: string) => {
    if (useOptimizedFlow) {
      const slide = optimizedSetupSlides.find(s => s.id === sectionId);
      if (!slide || !slide.mandatoryFields) return [];

      return slide.mandatoryFields.filter(field => {
        if (field === 'skills') return !(formData.skills?.length > 0);
        if (field === 'jobTypes') return !(formData.preferences?.jobTypes?.length > 0);
        if (field === 'locations') return !(formData.preferences?.locations?.length > 0);
        if (field === 'remotePreference') return !formData.preferences?.remotePreference;
        if (field === 'desiredSalary') {
          return !(
            formData.preferences?.desiredSalary?.min && formData.preferences?.desiredSalary?.max
          );
        }
        return !(formData as any)[field];
      });
    }

    const sectionData = getSectionCompletionStatus(sectionId);
    if (!sectionData) return [];

    return sectionData.requiredFields.filter(field => !sectionData.completedFields.includes(field));
  };

  // StepUrlHandler component now handles this logic

  return (
    <>
      {/* Handle validation and step navigation */}
      <ValidationHandler
        validationResponse={validationResponse}
        setCurrentSlideIndex={setCurrentSlideIndex}
        formData={formData}
        updateFormData={updateFormData}
      />

      {/* Handle LinkedIn profile data if available */}
      {initialData?.linkedInProfile && (
        <LinkedInProfileHandler
          linkedInProfile={initialData.linkedInProfile}
          formData={formData}
          user={user}
          updateFormData={updateFormData}
        />
      )}

      {/* Handle URL step parameters */}
      <StepUrlHandler setCurrentSlideIndex={setCurrentSlideIndex} initialStep={initialStep} />

      {/* Profile Assistant Widget for Optimized Flow */}
      {useOptimizedFlow && optimizedSetupSlides[currentSlideIndex] && (
        <ProfileAssistant
          currentStep={optimizedSetupSlides[currentSlideIndex].id}
          formData={formData}
          onSkipOptional={handleSkipOptional}
          onGetHelp={handleGetHelp}
        />
      )}

      <div
        data-testid="jobseeker-setup-slider"
        className="fixed inset-0 z-50 bg-gradient-to-br from-gray-50 via-white to-purple-50/10"
      >
        {completion.show && <Confetti numberOfPieces={200} recycle={false} />}

        <AnimatePresence mode="wait">
          {completion.show ? (
            <CompletionView
              image={
                (useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                  (useOptimizedFlow ? optimizedSetupSlides : setupSlides).length - 1
                ].image
              }
              onComplete={handleComplete}
              onClose={onClose}
              formData={formData}
              setCompletion={setCompletion}
              onboardingProgress={validationResponse?.onboardingProgress}
            />
          ) : (
            <motion.div
              key={currentSlideIndex}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="h-screen"
            >
              {/* Modern Split Layout */}
              <div className="flex flex-col lg:flex-row w-full h-full">
                {/* Left Panel - Form (55% of screen with comfortable padding) */}
                <div className="w-full lg:w-[55%] flex flex-col bg-white shadow-xl overflow-hidden">
                  {/* Modern Compact Header */}
                  <div className="px-6 lg:px-8 py-3 bg-white border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <h2 className="text-lg font-semibold text-gray-900">
                          {(useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                            currentSlideIndex
                          ]?.title || 'Setup'}
                        </h2>
                        <span className="text-sm text-gray-500">
                          Step {currentSlideIndex + 1} of{' '}
                          {(useOptimizedFlow ? optimizedSetupSlides : setupSlides).length}
                        </span>
                      </div>

                      {/* Always show close button in header to close the slider */}
                      <button
                        type="button"
                        onClick={handleCloseAttempt}
                        className="p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all"
                        aria-label="Close setup"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  </div>

                  {/* Content - Clean and modern */}
                  <div className="flex-1 overflow-y-auto">
                    <div className="px-6 lg:px-8 py-4">
                      {/* Step Progress Bar */}
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs text-gray-500">Mandatory Fields</span>
                          <span className="text-xs font-medium text-purple-600">
                            {Math.round(completionPercentage)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1.5">
                          <div
                            className="bg-gradient-to-r from-purple-500 to-pink-500 h-1.5 rounded-full transition-all duration-500"
                            style={{ width: `${completionPercentage}%` }}
                          />
                        </div>
                      </div>

                      {/* For import step, show title and tip */}
                      {(useOptimizedFlow ? optimizedSetupSlides : setupSlides)[currentSlideIndex]
                        ?.id === 'import' && (
                        <>
                          <div className="mb-4">
                            <h1 className="text-xl font-bold text-gray-900 mb-1">
                              {
                                (useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                                  currentSlideIndex
                                ]?.subtitle
                              }
                            </h1>
                            <p className="text-xs text-gray-500">
                              Complete this section to enhance your profile visibility
                            </p>
                          </div>

                          <div className="space-y-2 mb-4">
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-100 rounded-lg p-3"
                            >
                              <div className="flex items-start gap-2">
                                <Info className="w-4 h-4 text-indigo-500 mt-0.5 flex-shrink-0" />
                                <div className="flex-1">
                                  <p className="text-xs font-medium text-indigo-900 mb-0.5">
                                    Why this matters
                                  </p>
                                  <p className="text-xs text-indigo-700 leading-relaxed">
                                    Save 15 minutes by importing your existing profile. We'll
                                    extract information automatically.
                                  </p>
                                </div>
                              </div>
                            </motion.div>
                          </div>
                        </>
                      )}

                      {/* Validation Alert - Only show when needed and not handled by CompactProfileView */}
                      {!isCurrentSlideValid() &&
                        ((useOptimizedFlow ? optimizedSetupSlides : setupSlides)[currentSlideIndex]
                          ?.id === 'import' ||
                          !useOptimizedFlow) && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4"
                          >
                            <div className="flex items-start gap-2">
                              <AlertTriangle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                              <div className="flex-1">
                                <p className="text-xs text-amber-800">
                                  {useOptimizedFlow
                                    ? getOptimizedValidationMessage(
                                        (useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                                          currentSlideIndex
                                        ]?.id || '',
                                        formData
                                      )
                                    : getValidationMessage(
                                        ((useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                                          currentSlideIndex
                                        ]?.id as any) || '',
                                        formData
                                      )}
                                </p>
                              </div>
                            </div>
                          </motion.div>
                        )}

                      {/* Form Component - Direct rendering for all steps */}
                      {(useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                        currentSlideIndex
                      ] && (
                        <div>
                          {React.createElement(
                            (useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                              currentSlideIndex
                            ].component,
                            {
                              formData,
                              onUpdate: handleUpdate,
                              onNext: handleNext,
                              ...((useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                                currentSlideIndex
                              ]?.id === 'resume' ||
                              (useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                                currentSlideIndex
                              ]?.id === 'import'
                                ? { uploading, setUploading }
                                : {}),
                            } as StepComponentProps
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Clean Navigation Footer */}
                  <div className="px-6 lg:px-8 py-4 bg-white border-t border-gray-100">
                    <div className="flex items-center justify-between">
                      <button
                        onClick={handleBack}
                        className="flex items-center gap-2 px-4 py-2.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-all text-sm font-medium"
                      >
                        {currentSlideIndex === 0 ? (
                          <span>Cancel</span>
                        ) : (
                          <>
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M15 19l-7-7 7-7"
                              />
                            </svg>
                            Previous
                          </>
                        )}
                      </button>

                      {/* Optional fields skip button */}
                      {useOptimizedFlow &&
                        optimizedSetupSlides[currentSlideIndex] &&
                        (('canSkip' in optimizedSetupSlides[currentSlideIndex] &&
                          optimizedSetupSlides[currentSlideIndex].canSkip) ||
                          ('isOptional' in optimizedSetupSlides[currentSlideIndex] &&
                            optimizedSetupSlides[currentSlideIndex].isOptional)) && (
                          <button
                            onClick={handleSkipOptional}
                            className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
                          >
                            Skip for now
                          </button>
                        )}

                      <button
                        onClick={handleNext}
                        disabled={isLoading || uploading || !isCurrentSlideValid()}
                        className="flex items-center gap-2 px-6 py-2.5 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:from-gray-300 disabled:to-gray-300 disabled:cursor-not-allowed transition-all text-sm shadow-sm hover:shadow-md"
                      >
                        {isLoading || uploading ? (
                          <span className="flex items-center">
                            <svg className="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24">
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                                fill="none"
                              />
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              />
                            </svg>
                            Processing...
                          </span>
                        ) : currentSlideIndex ===
                          (useOptimizedFlow ? optimizedSetupSlides : setupSlides).length - 1 ? (
                          <>
                            Complete Setup
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          </>
                        ) : (
                          <>
                            Continue
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M9 5l7 7-7 7"
                              />
                            </svg>
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Right Panel - Image & Info (45% of screen) */}
                <div className="hidden lg:flex lg:w-[45%] relative overflow-hidden">
                  {/* Background Image - Changes with each section */}
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={`bg-${currentSlideIndex}`}
                      initial={{ opacity: 0, scale: 1.05 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.98 }}
                      transition={{ duration: 0.6, ease: 'easeInOut' }}
                      className="absolute inset-0"
                    >
                      {(useOptimizedFlow ? optimizedSetupSlides : setupSlides)[currentSlideIndex]
                        ?.image && (
                        <>
                          <img
                            src={
                              (useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                                currentSlideIndex
                              ]?.image
                            }
                            alt={`Step ${currentSlideIndex + 1} background`}
                            className="w-full h-full object-cover"
                          />
                          {/* Purple to pink gradient overlay only at the bottom */}
                          <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-t from-purple-900/70 via-purple-600/50 via-pink-400/30 to-transparent" />
                        </>
                      )}
                    </motion.div>
                  </AnimatePresence>

                  {/* Bottom Section - Pro Tip and Progress */}
                  <div className="absolute bottom-8 left-8 right-8 z-10 flex items-end justify-between gap-8">
                    {/* Pro Tip Section - Left Side */}
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={`pro-tip-${currentSlideIndex}`}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.4, delay: 0.2, ease: 'easeOut' }}
                        className="max-w-xl"
                      >
                        {/* Pro Tip with icon and text inline */}
                        <div className="flex items-start gap-3">
                          <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-sm flex items-center justify-center flex-shrink-0">
                            <svg
                              className="w-6 h-6 text-white"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path d="M12 2C8.13 2 5 5.13 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.87-3.13-7-7-7zm2.85 11.1l-.85.6V16h-4v-2.3l-.85-.6C7.8 12.16 7 10.63 7 9c0-2.76 2.24-5 5-5s5 2.24 5 5c0 1.63-.8 3.16-2.15 4.1z" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-white text-sm font-semibold mb-1 drop-shadow-md">
                              Pro Tip
                            </p>
                            <p className="text-white/95 text-sm lg:text-base leading-relaxed drop-shadow-md">
                              {
                                (useOptimizedFlow ? optimizedSetupSlides : setupSlides)[
                                  currentSlideIndex
                                ]?.funFact
                              }
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    </AnimatePresence>

                    {/* Progress Indicator - Right Side */}
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={`progress-${currentSlideIndex}`}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        transition={{ duration: 0.3, ease: 'easeOut' }}
                        className="flex-shrink-0"
                      >
                        {/* Progress Circle */}
                        <div className="relative">
                          <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                            {/* Background track */}
                            <path
                              d="m18,2.0845 a 15.9155,15.9155 0 0,1 0,31.831 a 15.9155,15.9155 0 0,1 0,-31.831"
                              fill="none"
                              stroke="rgba(255,255,255,0.25)"
                              strokeWidth="2"
                            />
                            {/* Progress arc */}
                            <path
                              d="m18,2.0845 a 15.9155,15.9155 0 0,1 0,31.831 a 15.9155,15.9155 0 0,1 0,-31.831"
                              fill="none"
                              stroke="white"
                              strokeWidth="2.5"
                              strokeLinecap="round"
                              strokeDasharray={`${completionPercentage}, 100`}
                              className="transition-all duration-700 ease-out filter drop-shadow-sm"
                            />
                          </svg>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="text-center">
                              <div className="text-2xl font-bold text-white drop-shadow-md">
                                {Math.round(completionPercentage)}%
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    </AnimatePresence>
                  </div>
                  {/* Progress Dots - Bottom Center */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
                    {(useOptimizedFlow ? optimizedSetupSlides : setupSlides).map((_, index) => (
                      <div
                        key={index}
                        className={`h-2 rounded-full transition-all duration-300 ${
                          index === currentSlideIndex ? 'w-6 bg-white shadow-lg' : 'w-2 bg-white/50'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <ConfirmationDialog
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={() => {
          setShowConfirmation(false);
          // Refresh data when confirming close
          handleDataRefresh();
          if (onClose) {
            onClose();
          }
        }}
      />
    </>
  );
};

// Main component that uses Zustand store
export const JobSeekerSetupSlider: React.FC<JobSeekerSetupSliderProps> = props => {
  return <JobSeekerSetupSliderInner {...props} />;
};

// Authenticated version that checks user roles
export const AuthenticatedJobSeekerSetupSlider: React.FC<JobSeekerSetupSliderProps> = props => {
  const { user, isLoading } = useUser();

  // Check for user role in all possible locations
  const isEmployer = useMemo(() => {
    // If user is loading or doesn't exist, allow the component to render
    // This handles the pre-authentication case on holding pages
    if (isLoading || !user) return false;

    // Check direct role property
    if (user.role === UserRole.EMPLOYER) return true;

    // Check localStorage for cached role
    try {
      if (user.sub) {
        const cachedRoleData = localStorage.getItem(`userRole_${user.sub}`);
        if (cachedRoleData) {
          const { role } = JSON.parse(cachedRoleData);
          if (role === UserRole.EMPLOYER) return true;
        }
      }
    } catch (error) {
      console.error('Error checking cached role:', error);
    }

    return false;
  }, [user, isLoading]);

  // Don't render the component if the user role is "employer"
  if (isEmployer) {
    return null;
  }

  return <JobSeekerSetupSliderInner {...props} />;
};
