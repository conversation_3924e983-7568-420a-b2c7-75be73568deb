'use client';

import React, { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowLeft,
  ArrowRight,
  Building2,
  Globe,
  Info,
  Mail,
  MapPin,
  Upload,
  User,
  Users,
  Wand2,
  X,
  CheckCircle,
  Clock,
  Briefcase,
  Phone,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import Confetti from 'react-confetti';

import useUser from '@/hooks/useUser';
import apiHelper from '@/lib/apiHelper';

import { showToast } from '../Toaster';
import { Button } from '../ui/button';
import { PhoneInput } from '../ui/PhoneInput';
import { ModernCard, CompactCard, InfoTip } from './components/ModernCard';
import { ModernFormInput, ModernTextArea } from './components/ModernFormInput';
import { ModernSelector } from './components/ModernSelector';

interface CompanySetupSliderProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (companyData: FormData) => void;
  hasError?: boolean;
  errorMessage?: string;
  initialData?: Partial<FormData>;
}

interface Slide {
  id: string;
  title: string;
  subtitle: string;
  fields: Array<keyof FormData>;
  image: string;
  component?: React.ComponentType<any>;
  icon?: React.ReactNode;
}

interface CompanySummaryResponse {
  summary: string;
}

// Sample company values
const companyValuesOptions = [
  'Innovation',
  'Teamwork',
  'Customer Focus',
  'Integrity',
  'Excellence',
  'Diversity & Inclusion',
  'Work-Life Balance',
  'Sustainability',
  'Transparency',
  'Accountability',
  'Growth Mindset',
  'Social Responsibility',
];

// Sample cultural fit options
const culturalFitOptions = [
  'Collaborative',
  'Fast-paced',
  'Results-oriented',
  'Creative',
  'Detail-oriented',
  'Self-motivated',
  'Adaptable',
  'Analytical',
  'Entrepreneurial',
];

const slides: Slide[] = [
  {
    id: 'basics',
    title: 'Company Basics',
    subtitle: 'Tell us about your company',
    fields: ['companyName', 'companyWebsite', 'description', 'logo'],
    image: '/images/company-setup/full/company-full-1.webp',
    icon: <Building2 className="w-5 h-5" />,
  },
  {
    id: 'details',
    title: 'Company Details',
    subtitle: 'Help us understand your company better',
    fields: ['size', 'industry', 'cultureFitDescription', 'companyValues'],
    image: '/images/company-setup/full/company-full-2.webp',
    icon: <Briefcase className="w-5 h-5" />,
  },
  {
    id: 'contact',
    title: 'Contact Information',
    subtitle: 'Your contact information',
    fields: ['contactName', 'contactEmail', 'phoneNumber'],
    image: '/images/company-setup/full/company-full-3.webp',
    icon: <Phone className="w-5 h-5" />,
  },
];

const companySizes = [
  '1-10 employees',
  '11-50 employees',
  '51-200 employees',
  '201-500 employees',
  '501-1000 employees',
  '1000+ employees',
];

const industries = [
  'Technology',
  'Healthcare',
  'Finance & Banking',
  'Education',
  'Retail & E-commerce',
  'Manufacturing',
  'Real Estate',
  'Consulting',
  'Marketing & Advertising',
  'Transportation & Logistics',
  'Hospitality & Tourism',
  'Media & Entertainment',
  'Energy & Utilities',
  'Non-profit',
  'Government',
  'Agriculture',
  'Construction',
  'Telecommunications',
  'Pharmaceuticals',
  'Aerospace & Defense',
  'Other',
];

interface CompletionState {
  show: boolean;
  animate: boolean;
}

interface FormData {
  companyName: string;
  companyWebsite: string;
  industry: string;
  size: string;
  location: string;
  contactName: string;
  contactEmail: string;
  phoneNumber: string;
  logo: string;
  description: string;
  cultureFitDescription?: string;
  companyValues?: string[];
}

export const CompanySetupSlider: React.FC<CompanySetupSliderProps> = ({
  onClose,
  onSubmit,
  initialData = {},
  hasError = false,
  errorMessage,
}) => {
  const { user } = useUser();
  const router = useRouter();
  const clientId = user?.userId || user?.sub || user?.clientId || '';
  const userEmail = user?.email || '';

  const [formData, setFormData] = useState<FormData>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('companySetupData');
      const savedData = saved ? JSON.parse(saved) : {};

      return {
        companyName: '',
        companyWebsite: '',
        industry: '',
        size: '',
        location: '',
        contactName: '',
        contactEmail: savedData.contactEmail || userEmail || '',
        phoneNumber: '',
        logo: '',
        description: '',
        cultureFitDescription: '',
        companyValues: [],
        ...savedData,
        ...initialData,
        clientId,
      };
    }
    return {
      companyName: '',
      companyWebsite: '',
      industry: '',
      size: '',
      location: '',
      contactName: '',
      contactEmail: userEmail || '',
      phoneNumber: '',
      logo: '',
      description: '',
      cultureFitDescription: '',
      companyValues: [],
      ...initialData,
      clientId,
    };
  });

  const [currentSlide, setCurrentSlide] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('companySetupSlide');
      return saved ? parseInt(saved, 10) : 0;
    }
    return 0;
  });

  const [emailError, setEmailError] = useState<string | null>(null);
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [completion, setCompletion] = useState<CompletionState>({
    show: false,
    animate: false,
  });
  const [isFetchingSummary, setIsFetchingSummary] = useState(false);
  const [isValidatingEmail, setIsValidatingEmail] = useState(false);

  // List of common public email domains that should be rejected
  // DISABLED: Email domain restrictions have been removed
  // const publicEmailDomains = [
  //   'gmail.com',
  //   'yahoo.com',
  //   'hotmail.com',
  //   'outlook.com',
  //   'aol.com',
  //   'icloud.com',
  //   'mail.com',
  //   'protonmail.com',
  //   'zoho.com',
  //   'yandex.com',
  //   'gmx.com',
  //   'live.com',
  //   'msn.com',
  //   'me.com',
  //   'inbox.com',
  //   'mail.ru',
  //   'hotmail.co.uk',
  //   'hotmail.fr',
  //   'yahoo.co.uk',
  //   'yahoo.fr',
  //   'googlemail.com',
  // ];

  // Save form data to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('companySetupData', JSON.stringify(formData));
    }
  }, [formData]);

  // Save current slide to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('companySetupSlide', currentSlide.toString());
    }
  }, [currentSlide]);

  // Function to validate if an email is a work email (not a public email domain)
  // DISABLED: Company email validation has been disabled to allow all email domains
  const isWorkEmail = (email: string): boolean => {
    if (!email || !email.includes('@')) return false;

    // Validation disabled - accepting all valid email formats
    return true;
  };

  const handleInputChange = (field: keyof FormData, value: string | string[]) => {
    if (field && value !== undefined) {
      // Clear errors when any input changes
      setEmailError(null);
      setPhoneError(null);

      // If the field is contactEmail, validate that it's a work email
      // DISABLED: Email validation check has been removed to allow all email domains

      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  // Handle phone number validation
  const handlePhoneValidation = (validation: { isValid: boolean; error?: string }) => {
    if (!validation.isValid && validation.error) {
      setPhoneError(validation.error);
    } else {
      setPhoneError(null);
    }
  };

  // Initialize form data with initial values
  useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
      }));
    }
  }, [initialData]);

  // Pre-populate email when user data becomes available
  useEffect(() => {
    if (userEmail && !formData.contactEmail) {
      setFormData(prev => ({
        ...prev,
        contactEmail: userEmail,
      }));
    }
  }, [userEmail, formData.contactEmail]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    const formDataObj = new FormData();
    formDataObj.append('file', file);

    try {
      const { imageUrl } = await apiHelper.post('/companies/upload-logo', formDataObj);
      handleInputChange('logo', imageUrl);
      showToast({
        message: 'Company logo uploaded successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      showToast({
        message: 'Failed to upload company logo. Please try again.',
        isSuccess: false,
      });
    } finally {
      setUploading(false);
    }
  };

  const handleFetchSummary = async () => {
    if (!formData.companyWebsite) {
      showToast({
        message: 'Please enter a company website URL first',
        isSuccess: false,
      });
      return;
    }

    try {
      setIsFetchingSummary(true);
      const response = await apiHelper.post<CompanySummaryResponse>('/jobs/summarize', {
        url: formData.companyWebsite,
      });

      if (response.summary) {
        handleInputChange('description', response.summary);
        showToast({
          message: 'Company description fetched successfully',
          isSuccess: true,
        });
      }
    } catch (error) {
      console.error('Error fetching summary:', error);
      showToast({
        message: 'Failed to fetch company description. Please try again.',
        isSuccess: false,
      });
    } finally {
      setIsFetchingSummary(false);
    }
  };

  const handleNext = async () => {
    // If we're on the last slide, submit the company data
    if (currentSlide === slides.length - 1) {
      try {
        // Final validation of email before submission
        // DISABLED: Email validation has been removed to allow all email domains

        setIsValidatingEmail(true);
        await onSubmit(formData);
        setCompletion({ show: true, animate: true });
      } catch (error: any) {
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to create company. Please try again.';

        // If the error is related to email, set the email error and go back to the contact slide
        if (errorMessage.toLowerCase().includes('email')) {
          setEmailError(errorMessage);
          // Find the index of the slide containing contactEmail
          const contactSlideIndex = slides.findIndex(slide =>
            slide.fields.includes('contactEmail')
          );
          setCurrentSlide(contactSlideIndex);
        } else {
          showToast({
            message: errorMessage,
            isSuccess: false,
          });
        }
        setCompletion({ show: false, animate: false });
        return;
      } finally {
        setIsValidatingEmail(false);
      }
    } else {
      // Simply move to the next slide without updating the company data
      // The data will be saved when the user completes the entire form
      setCurrentSlide(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (currentSlide > 0) {
      setCurrentSlide(prev => prev - 1);
    }
  };

  const handleComplete = async () => {
    // If there's an error, go back to the contact slide
    if (hasError) {
      // Find the index of the contact slide
      const contactSlideIndex = slides.findIndex(slide => slide.id === 'contact');
      setCurrentSlide(contactSlideIndex);
      return;
    }

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('companySetupData');
      localStorage.removeItem('companySetupSlide');
    }
    setCompletion({ show: false, animate: false });
    onClose();
    router.replace('/dashboard');
  };

  const isCurrentSlideValid = () => {
    // If there's an error and we're on the last slide, prevent moving forward
    if (hasError && currentSlide === slides.length - 1) {
      return false;
    }

    // If there's an email or phone error, the slide is not valid
    if (emailError || phoneError) {
      return false;
    }

    const currentFields = slides[currentSlide].fields;

    return currentFields.every(field => {
      // Fields that are optional
      if (field === 'logo' || field === 'cultureFitDescription' || field === 'companyValues')
        return true;

      // Special validation for contactEmail
      if (field === 'contactEmail') {
        const email = formData[field] as string;
        // DISABLED: Email validation - just check if email exists
        return !!email;
      }

      // Special validation for phoneNumber
      if (field === 'phoneNumber') {
        const phone = formData[field] as string;
        return phone && !phoneError; // Valid if has value and no error
      }

      const value = formData[field];
      if (Array.isArray(value)) {
        return true; // Arrays are always valid (can be empty)
      }

      return value ? true : false;
    });
  };

  // Calculate progress based on completed fields
  const calculateProgress = () => {
    const requiredFields = [
      'companyName', 'companyWebsite', 'description',
      'size', 'industry',
      'contactName', 'contactEmail', 'phoneNumber'
    ];
    
    const completedFields = requiredFields.filter(field => 
      formData[field as keyof FormData] && formData[field as keyof FormData] !== ''
    );
    
    return Math.round((completedFields.length / requiredFields.length) * 100);
  };

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-gray-50 via-white to-pink-50/20">
      {completion.show && <Confetti numberOfPieces={200} recycle={false} />}

      <AnimatePresence mode="wait">
        {completion.show ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 z-50"
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, ease: 'easeOut' }}
              className="absolute inset-0 bg-cover bg-center"
              style={{
                backgroundImage: `url(${slides[slides.length - 1].image})`,
              }}
            >
              <div className="absolute inset-0 bg-black/50" />

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="absolute inset-0 flex flex-col items-center justify-center text-white"
              >
                <div className="max-w-2xl mx-auto text-center px-4 flex flex-col items-center justify-center">
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.3 }}
                    className="w-20 h-20 rounded-full bg-white flex items-center justify-center mb-6 shadow-lg"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="40"
                      height="40"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-pink-600"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </motion.div>
                  <h1 className="flex flex-col items-center gap-2">
                    <span className="text-2xl font-medium text-white text-center">Welcome to</span>
                    <span className="text-5xl font-bold text-white relative text-center">
                      Kaleido Talent
                    </span>
                  </h1>
                  <p className="text-sm md:text-base text-white/90 mt-6 mb-8 text-center max-w-lg">
                    Your company profile has been set up successfully. Get ready to transform your
                    hiring process with AI-powered tools and insights.
                  </p>
                  <div className="flex justify-center">
                    <Button
                      onClick={handleComplete}
                      className="flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-pink-600 hover:bg-pink-700 text-white transition-all"
                    >
                      <span>Let's Get Started</span>
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key={currentSlide}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="relative h-screen w-full"
          >
            {/* Full-screen background image */}
            <div className="absolute inset-0 w-full h-full">
              <div
                className="absolute inset-0 bg-cover bg-center w-full h-full"
                style={{
                  backgroundImage: `url(${slides[currentSlide].image})`,
                  backgroundColor: 'rgb(55, 65, 81)',
                }}
              >
                <div className="absolute inset-0 bg-black/30" />
              </div>
            </div>

            {/* Close button */}
            <button
              onClick={() => {
                onClose();
                router.replace('/dashboard');
              }}
              className="absolute top-4 right-4 z-50 p-2 rounded-full bg-white/90 hover:bg-white transition-all shadow-lg"
            >
              <X className="w-5 h-5 text-gray-700" />
            </button>

            {/* Form Panel - Modern design */}
            <div className="relative w-full md:w-[520px] h-full overflow-hidden flex flex-col z-10 bg-white shadow-2xl">
              {/* Header with progress */}
              <div className="px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-white to-pink-50/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-pink-100 flex items-center justify-center">
                      {slides[currentSlide].icon || <Building2 className="w-5 h-5 text-pink-600" />}
                    </div>
                    <div>
                      <h2 className="text-base font-semibold text-gray-900">
                        {slides[currentSlide].title}
                      </h2>
                      <p className="text-xs text-gray-500 mt-0.5">
                        {slides[currentSlide].subtitle}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <p className="text-xs text-gray-500">Progress</p>
                      <p className="text-sm font-semibold text-gray-900">{calculateProgress()}%</p>
                    </div>
                    <div className="w-12 h-12 relative">
                      <svg className="w-12 h-12 transform -rotate-90">
                        <circle
                          cx="24"
                          cy="24"
                          r="20"
                          stroke="currentColor"
                          strokeWidth="3"
                          fill="none"
                          className="text-gray-200"
                        />
                        <circle
                          cx="24"
                          cy="24"
                          r="20"
                          stroke="currentColor"
                          strokeWidth="3"
                          fill="none"
                          strokeDasharray={`${calculateProgress() * 1.26} 126`}
                          className="text-pink-500 transition-all duration-500"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-6">
                <div className="max-w-full">
                  {/* Step indicator */}
                  <div className="flex items-center gap-2 mb-6">
                    {slides.map((slide, index) => (
                      <div key={slide.id} className="flex items-center">
                        <button
                          onClick={() => index <= currentSlide && setCurrentSlide(index)}
                          disabled={index > currentSlide}
                          className={`flex items-center justify-center w-8 h-8 rounded-full transition-all ${
                            index === currentSlide
                              ? 'bg-pink-500 text-white'
                              : index < currentSlide
                              ? 'bg-green-500 text-white cursor-pointer hover:bg-green-600'
                              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                          }`}
                        >
                          {index < currentSlide ? (
                            <CheckCircle className="w-4 h-4" />
                          ) : (
                            <span className="text-xs font-medium">{index + 1}</span>
                          )}
                        </button>
                        {index < slides.length - 1 && (
                          <div
                            className={`w-12 h-0.5 transition-all ${
                              index < currentSlide ? 'bg-green-500' : 'bg-gray-200'
                            }`}
                          />
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Display error message if provided */}
                  {hasError && errorMessage && (
                    <InfoTip
                      variant="warning"
                      title="Action Required"
                      description={errorMessage}
                    />
                  )}

                  {/* Helpful tips for current step */}
                  {currentSlide === 0 && (
                    <InfoTip
                      variant="info"
                      title="Quick Setup Tip"
                      description="Complete your company profile to unlock all features and start connecting with top talent."
                    />
                  )}
                  
                  <div className="space-y-4 mt-6">
                    {slides[currentSlide].component ? (
                      <div>
                        {React.createElement(slides[currentSlide].component as React.ComponentType)}
                      </div>
                    ) : (
                      slides[currentSlide].fields.map(field => {
                        if (field === 'logo') {
                          return (
                            <ModernCard key={field} variant="default" className="p-4">
                              <label className="block text-sm font-medium text-gray-700 mb-3">
                                Company Logo
                              </label>
                              <input
                                type="file"
                                name="file"
                                accept="image/*"
                                onChange={handleFileUpload}
                                className="hidden"
                                id="logo-upload"
                                disabled={uploading}
                              />
                              <label
                                htmlFor="logo-upload"
                                className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg hover:border-pink-400 transition-all cursor-pointer bg-gray-50 hover:bg-pink-50/30 group"
                              >
                                {formData[field] ? (
                                  <div className="relative w-full h-full rounded-lg overflow-hidden">
                                    <Image
                                      src={formData[field]}
                                      alt="Company logo"
                                      fill
                                      className="object-contain p-4"
                                    />
                                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                                      <div className="bg-white rounded-full p-3 shadow-lg">
                                        <Upload className="w-5 h-5 text-pink-600" />
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="flex flex-col items-center text-gray-500 group-hover:text-pink-600 transition-colors">
                                    <div className="w-14 h-14 rounded-full bg-gray-100 group-hover:bg-pink-100 flex items-center justify-center mb-3 transition-colors">
                                      <Upload className="w-6 h-6" />
                                    </div>
                                    <span className="text-sm font-medium">
                                      {uploading ? 'Uploading...' : 'Click to upload'}
                                    </span>
                                    <span className="text-xs text-gray-400 mt-1">PNG, JPG up to 10MB</span>
                                  </div>
                                )}
                              </label>
                            </ModernCard>
                          );
                        }
                        
                        if (field === 'description') {
                          return (
                            <ModernTextArea
                              key={field}
                              label="Company Description"
                              value={formData[field] || ''}
                              onChange={(value) => handleInputChange(field as keyof FormData, value)}
                              placeholder="Describe what your company does, your mission, and values..."
                              rows={6}
                              maxLength={500}
                              autoGenerate={!!formData.companyWebsite}
                              onAutoGenerate={handleFetchSummary}
                              isGenerating={isFetchingSummary}
                              hint="A compelling description helps attract the right talent"
                            />
                          );
                        }
                        
                        if (field === 'size') {
                          return (
                            <ModernSelector
                              key={field}
                              label="Company Size"
                              options={companySizes}
                              value={formData[field] || ''}
                              onChange={(value) => handleInputChange(field as keyof FormData, value as string)}
                              placeholder="Select company size"
                              required
                            />
                          );
                        }
                        
                        if (field === 'cultureFitDescription') {
                          return (
                            <ModernTextArea
                              key={field}
                              label="Company Culture"
                              value={formData[field] || ''}
                              onChange={(value) => handleInputChange(field as keyof FormData, value)}
                              placeholder="Describe your company culture, work environment, and team dynamics..."
                              rows={5}
                              maxLength={400}
                              hint="Help candidates understand if they'd be a good fit"
                            />
                          );
                        }
                        
                        if (field === 'companyValues') {
                          return (
                            <ModernSelector
                              key={field}
                              label="Company Values"
                              options={companyValuesOptions}
                              selectedValues={Array.isArray(formData[field]) ? formData[field] : []}
                              onChange={(values) => handleInputChange(field as keyof FormData, values)}
                              placeholder="Select your core values"
                              multiple
                              searchable
                              maxSelections={5}
                              hint="Choose up to 5 values that best represent your company"
                            />
                          );
                        }
                        
                        if (field === 'phoneNumber') {
                          return (
                            <div>
                              <label className="block text-xs font-medium text-white mb-1">
                                Phone Number
                              </label>
                              <PhoneInput
                                value={(formData[field] as string) || ''}
                                onChange={(value: string) => handleInputChange(field, value)}
                                onValidationChange={handlePhoneValidation}
                                placeholder="Enter phone number"
                                error={phoneError || undefined}
                                required
                              />
                            </div>
                          );
                        }
                        
                        // Default input fields
                        if (field === 'contactEmail') {
                          return (
                            <ModernFormInput
                              key={field}
                              label="Contact Email"
                              type="email"
                              value={(formData[field] as string) || ''}
                              onChange={(value) => handleInputChange(field, value)}
                              placeholder="<EMAIL>"
                              required
                              error={emailError || undefined}
                              hint="We'll use this email for all communications"
                              prefix={<Mail className="w-4 h-4" />}
                            />
                          );
                        }
                        
                        if (field === 'industry') {
                          return (
                            <ModernSelector
                              key={field}
                              label="Industry"
                              options={industries}
                              value={formData[field] || ''}
                              onChange={(value) => handleInputChange(field as keyof FormData, value as string)}
                              placeholder="Select your industry"
                              required
                              searchable
                              hint="Your primary industry or sector"
                            />
                          );
                        }
                        
                        // All other text fields
                        return (
                          <ModernFormInput
                            key={field}
                            label={
                              field === 'companyName' ? 'Company Name' :
                              field === 'companyWebsite' ? 'Company Website' :
                              field === 'contactName' ? 'Contact Name' :
                              field === 'location' ? 'Location' :
                              String(field).charAt(0).toUpperCase() +
                              String(field).slice(1).replace(/([A-Z])/g, ' $1')
                            }
                            type={
                              field === 'companyWebsite' ? 'url' :
                              'text'
                            }
                            value={(formData[field as keyof FormData] as string) || ''}
                            onChange={(value) => handleInputChange(field as keyof FormData, value)}
                            placeholder={
                              field === 'companyName' ? 'Your company name' :
                              field === 'companyWebsite' ? 'https://www.example.com' :
                              field === 'contactName' ? 'Full name' :
                              field === 'location' ? 'City, Country' :
                              `Enter ${String(field).toLowerCase().replace(/([A-Z])/g, ' $1')}`
                            }
                            required={field !== 'location'}
                            prefix={
                              field === 'companyName' ? <Building2 className="w-4 h-4" /> :
                              field === 'companyWebsite' ? <Globe className="w-4 h-4" /> :
                              field === 'contactName' ? <User className="w-4 h-4" /> :
                              field === 'location' ? <MapPin className="w-4 h-4" /> :
                              null
                            }
                          />
                        );
                      })
                    )}
                  </div>
                </div>
              </div>

              {/* Navigation Buttons - Modern design */}
              <div className="sticky bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-4">
                <div className="flex items-center justify-between gap-3">
                  <Button
                    onClick={handleBack}
                    variant="outline"
                    className="flex items-center justify-center gap-2 px-5 py-2.5 bg-white hover:bg-gray-50 transition-all text-gray-700 border-gray-300 rounded-lg"
                    disabled={currentSlide === 0}
                  >
                    <ArrowLeft className="w-4 h-4" />
                    <span className="text-sm font-medium">Previous</span>
                  </Button>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">
                      Step {currentSlide + 1} of {slides.length}
                    </span>
                  </div>
                  
                  <Button
                    onClick={handleNext}
                    disabled={!isCurrentSlideValid() || isValidatingEmail}
                    className="bg-pink-600 text-white hover:bg-pink-700 flex items-center justify-center gap-2 px-5 py-2.5 rounded-lg transition-all disabled:opacity-50 disabled:hover:bg-pink-600 disabled:cursor-not-allowed shadow-sm"
                  >
                    <span className="text-sm font-medium">
                      {isValidatingEmail
                        ? 'Validating...'
                        : currentSlide === slides.length - 1
                          ? 'Complete Setup'
                          : 'Continue'}
                    </span>
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Right side - Image display with overlay */}
            <div className="hidden md:block absolute right-0 top-0 bottom-0 left-[520px]">
              <div className="relative w-full h-full">
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{
                    backgroundImage: `url(${slides[currentSlide].image})`,
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-black/40" />
                  
                  {/* Floating info cards */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 max-w-md">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl"
                    >
                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        Welcome to Kaleido Talent
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Setting up your company profile helps us match you with the best candidates for your roles.
                      </p>
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-gray-700">Access to qualified candidates</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-gray-700">AI-powered matching</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-gray-700">Streamlined hiring process</span>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
