'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle2, AlertCircle, Eye, EyeOff } from 'lucide-react';

interface ModernFormInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  type?: 'text' | 'email' | 'tel' | 'url' | 'password';
  placeholder?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  success?: boolean;
  className?: string;
  disabled?: boolean;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  validation?: (value: string) => string | null;
}

export const ModernFormInput: React.FC<ModernFormInputProps> = ({
  label,
  value,
  onChange,
  type = 'text',
  placeholder,
  required = false,
  error,
  hint,
  success,
  className = '',
  disabled = false,
  prefix,
  suffix,
  validation,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    if (validation) {
      const validationError = validation(newValue);
      setLocalError(validationError);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    if (validation && value) {
      const validationError = validation(value);
      setLocalError(validationError);
    }
  };

  const displayError = error || localError;
  const isValid = success || (value && !displayError && !localError);
  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-pink-500 ml-1">*</span>}
      </label>

      <div className="relative">
        <div
          className={`relative flex items-center border rounded-lg transition-all duration-200 ${
            isFocused
              ? 'border-pink-500 ring-2 ring-pink-100'
              : displayError
              ? 'border-red-500'
              : isValid
              ? 'border-green-500'
              : 'border-gray-300 hover:border-gray-400'
          } ${
            disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'
          }`}
        >
          {prefix && (
            <div className="pl-3 pr-2 text-gray-500">
              {prefix}
            </div>
          )}

          <input
            type={inputType}
            value={value}
            onChange={handleChange}
            onFocus={() => setIsFocused(true)}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            className={`flex-1 px-3 py-3 bg-transparent focus:outline-none text-gray-900 placeholder-gray-400 ${
              prefix ? 'pl-0' : ''
            } ${
              suffix || type === 'password' || isValid ? 'pr-10' : ''
            }`}
          />

          {type === 'password' && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          )}

          {!type.includes('password') && (suffix || displayError || isValid) && (
            <div className="absolute right-3 flex items-center">
              {suffix || (
                <>
                  {displayError ? (
                    <AlertCircle className="w-5 h-5 text-red-500" />
                  ) : isValid ? (
                    <CheckCircle2 className="w-5 h-5 text-green-500" />
                  ) : null}
                </>
              )}
            </div>
          )}
        </div>
      </div>

      <AnimatePresence>
        {(displayError || hint) && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {displayError ? (
              <p className="text-sm text-red-600 flex items-center gap-2">
                <AlertCircle className="w-4 h-4" />
                {displayError}
              </p>
            ) : hint ? (
              <p className="text-sm text-gray-500">{hint}</p>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export const ModernTextArea: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  rows?: number;
  maxLength?: number;
  className?: string;
  autoGenerate?: boolean;
  onAutoGenerate?: () => void;
  isGenerating?: boolean;
}> = ({
  label,
  value,
  onChange,
  placeholder,
  required = false,
  error,
  hint,
  rows = 4,
  maxLength,
  className = '',
  autoGenerate = false,
  onAutoGenerate,
  isGenerating = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const characterCount = value.length;
  const isNearLimit = maxLength && characterCount > maxLength * 0.8;

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-pink-500 ml-1">*</span>}
        </label>
        {autoGenerate && onAutoGenerate && (
          <button
            type="button"
            onClick={onAutoGenerate}
            disabled={isGenerating}
            className="text-xs px-3 py-1.5 bg-pink-50 hover:bg-pink-100 text-pink-600 rounded-lg transition-colors flex items-center gap-1.5 disabled:opacity-50"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            {isGenerating ? 'Generating...' : 'Auto-generate'}
          </button>
        )}
      </div>

      <div
        className={`relative border rounded-lg transition-all duration-200 ${
          isFocused
            ? 'border-pink-500 ring-2 ring-pink-100'
            : error
            ? 'border-red-500'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          rows={rows}
          maxLength={maxLength}
          className="w-full px-3 py-3 bg-transparent focus:outline-none text-gray-900 placeholder-gray-400 resize-none"
        />
      </div>

      <div className="flex items-center justify-between">
        <div>
          <AnimatePresence>
            {(error || hint) && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                {error ? (
                  <p className="text-sm text-red-600 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    {error}
                  </p>
                ) : hint ? (
                  <p className="text-sm text-gray-500">{hint}</p>
                ) : null}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        {maxLength && (
          <span
            className={`text-xs ${
              isNearLimit
                ? characterCount >= maxLength
                  ? 'text-red-500'
                  : 'text-amber-500'
                : 'text-gray-400'
            }`}
          >
            {characterCount}/{maxLength}
          </span>
        )}
      </div>
    </div>
  );
};

export default ModernFormInput;