import React, { useEffect, useState } from 'react';

import {
  ArrowRight,
  Briefcase,
  Building,
  ChevronDown,
  ChevronUp,
  Circle,
  Clock,
  RefreshCw,
} from 'lucide-react';

import StatusTimeline, { getStatusInfo } from '@/components/shared/StatusTimeline';
import StatusUpdateModalWithDate, {
  StatusUpdateData,
} from '@/components/shared/StatusUpdateModalWithDate';
import { convertApiStatus, getAvailableActions } from '@/components/shared/StatusUtils';
import { showToast } from '@/components/Toaster';
import apiHelper from '@/lib/apiHelper';
import { CandidateStatus } from '@/types/candidate.types';

import { CandidateWithHistory } from '../types';

// Define interfaces matching the backend response but using our local CandidateStatus enum
interface JobApplication {
  id: string;
  title: string;
  company: string;
  location: string;
  status: CandidateStatus;
  statusTimelineData: Array<{
    newStatus: string;
    previousStatus: string;
    timestamp: string;
    description: string;
    metadata?: {
      jobId?: string;
      notes?: string;
    };
  }>;
  evaluationId?: string;
  tier?: string;
  matchScore?: number;
  contacted?: boolean;
  hasCompletedVideoInterview?: boolean;
  notes?: string;
}

interface CandidateJobApplicationsResponse {
  jobApplications: JobApplication[];
  candidateName: string;
  candidateId: string;
}

interface StatusTimelineTabProps {
  candidate: CandidateWithHistory;
  onRefresh?: () => void;
}

const StatusTimelineTab: React.FC<StatusTimelineTabProps> = ({ candidate, onRefresh }) => {
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [expandedJobId, setExpandedJobId] = useState<string | null>(null);
  const [selectedJobForStatus, setSelectedJobForStatus] = useState<string | null>(null);
  const [statusToChange, setStatusToChange] = useState<CandidateStatus | null>(null);
  const [jobApplicationsData, setJobApplicationsData] =
    useState<CandidateJobApplicationsResponse | null>(null);
  const [pendingStatusUpdate, setPendingStatusUpdate] = useState<StatusUpdateData | null>(null);

  // Get job information from the candidate's __job__ property
  const jobTitle = candidate.__job__?.title || 'the position';
  const companyName = candidate.__job__?.company?.name || 'Our Company';

  // Fetch job applications data from the backend
  const fetchJobApplicationsData = async () => {
    try {
      setIsLoading(true);
      const response = await apiHelper.get(`/candidates/${candidate.id}/job-applications`);

      // Convert API response to use our local CandidateStatus
      const convertedJobApplications = {
        ...response,
        jobApplications: (response.jobApplications || []).map((job: any) => ({
          ...job,
          status: convertApiStatus(job.status),
          statusTimelineData: (job.statusTimelineData || []).map((item: any) => ({
            ...item,
            newStatus: convertApiStatus(item.newStatus),
            previousStatus: convertApiStatus(item.previousStatus),
          })),
        })),
      };

      setJobApplicationsData(convertedJobApplications);
    } catch (error) {
      console.error('Error fetching job applications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch job applications when component mounts
  useEffect(() => {
    fetchJobApplicationsData();
  }, [candidate.id]);

  // We don't need these functions anymore as we're using getStatusInfo from StatusUtils

  const handleUpdateStatus = async (statusData: StatusUpdateData) => {
    try {
      setIsLoading(true);

      // Format the API request
      const requestData = {
        status: statusToChange,
        notes: statusData.message,
        jobId: selectedJobForStatus, // Include the job ID
        dateSensitiveInfo: {
          ...(statusData.date && { date: statusData.date }),
          ...(statusData.meetingLink && { meetingLink: statusData.meetingLink }),
          ...(statusData.startDate && { startDate: statusData.startDate }),
          ...(statusData.onboardingLink && { onboardingLink: statusData.onboardingLink }),
        },
      };

      // Make API call to update candidate status
      const response = await apiHelper.patch(`/candidates/${candidate.id}/status`, requestData);
      // Update the local state with the new status
      if (response && response.candidate) {
        // Update the job applications data with the new status
        setJobApplicationsData(prevData => {
          if (!prevData) return null;

          return {
            ...prevData,
            jobApplications: prevData.jobApplications.map(job => {
              if (job.id === selectedJobForStatus) {
                // Create a new status timeline entry
                const newTimelineEntry = {
                  newStatus: statusToChange,
                  previousStatus: job.status,
                  timestamp: new Date().toISOString(),
                  description: `Status changed from ${getStatusInfo(job.status).label} to ${getStatusInfo(statusToChange).label}`,
                  metadata: {
                    jobId: selectedJobForStatus,
                    notes: statusData.message || '',
                  },
                };

                // Update the job with new status and add to timeline
                return {
                  ...job,
                  status: statusToChange,
                  statusTimelineData: [...(job.statusTimelineData || []), newTimelineEntry],
                };
              }
              return job;
            }),
          };
        });

        // Show success toast
        showToast({
          message: `Status updated to ${getStatusInfo(statusToChange).label}`,
          isSuccess: true,
        });
      }

      // Refresh the candidate data if refresh function is provided
      if (onRefresh) {
        onRefresh();
      }

      setIsStatusModalOpen(false);
      setStatusToChange(null);
      setSelectedJobForStatus(null);
    } catch (error) {
      console.error('Error updating candidate status:', error);
      showToast({
        message: 'Failed to update candidate status',
        isSuccess: false,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusClick = (jobId: string, status: CandidateStatus) => {
    setSelectedJobForStatus(jobId);
    setStatusToChange(status);
    setIsStatusModalOpen(true);
  };

  const toggleJobExpansion = (jobId: string) => {
    setExpandedJobId(expandedJobId === jobId ? null : jobId);
  };

  const handleSkipCulturalFitSetup = async () => {
    if (!pendingStatusUpdate || !statusToChange || !selectedJobForStatus) {
      return;
    }

    try {
      setIsLoading(true);

      // Format the API request with skipCulturalFit flag
      const requestData = {
        status: statusToChange,
        notes: pendingStatusUpdate.message,
        jobId: selectedJobForStatus,
        skipCulturalFit: true, // Flag to bypass cultural fit check
        dateSensitiveInfo: {
          ...(pendingStatusUpdate.date && { date: pendingStatusUpdate.date }),
          ...(pendingStatusUpdate.meetingLink && { meetingLink: pendingStatusUpdate.meetingLink }),
          ...(pendingStatusUpdate.startDate && { startDate: pendingStatusUpdate.startDate }),
          ...(pendingStatusUpdate.onboardingLink && {
            onboardingLink: pendingStatusUpdate.onboardingLink,
          }),
        },
      };

      // Make API call to update candidate status
      const response = await apiHelper.patch(`/candidates/${candidate.id}/status`, requestData);

      // Update the local state with the new status
      if (response && response.candidate) {
        setJobApplicationsData(prevData => {
          if (!prevData) return prevData;

          return {
            ...prevData,
            jobApplications: prevData.jobApplications.map(job => {
              if (job.id === selectedJobForStatus) {
                const newTimelineEntry = {
                  newStatus: statusToChange,
                  previousStatus: job.status,
                  timestamp: new Date().toISOString(),
                  description: `Status changed from ${getStatusInfo(job.status).label} to ${getStatusInfo(statusToChange).label}`,
                  metadata: {
                    jobId: selectedJobForStatus,
                    notes: pendingStatusUpdate.message || '',
                  },
                };

                return {
                  ...job,
                  status: statusToChange,
                  statusTimelineData: [...(job.statusTimelineData || []), newTimelineEntry],
                };
              }
              return job;
            }),
          };
        });

        showToast({
          message: `Status updated to ${getStatusInfo(statusToChange).label}`,
          isSuccess: true,
        });
      }

      // Refresh the candidate data if refresh function is provided
      if (onRefresh) {
        onRefresh();
      }

      // Clear pending status update
      setPendingStatusUpdate(null);
      setStatusToChange(null);
      setSelectedJobForStatus(null);
    } catch (error) {
      console.error('Error updating candidate status:', error);
      showToast({
        message: 'Failed to update candidate status',
        isSuccess: false,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold flex items-center">
          <Clock className="mr-2 text-purple-400" size={20} />
          Status Timeline
        </h2>
        <button
          type="button"
          onClick={fetchJobApplicationsData}
          className="flex items-center text-sm text-purple-400 hover:text-purple-300 transition-colors"
        >
          <RefreshCw size={14} className="mr-1" />
          Refresh
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : jobApplicationsData && jobApplicationsData.jobApplications.length > 0 ? (
        <div className="space-y-4">
          {/* Job Applications */}
          {jobApplicationsData.jobApplications.map(job => (
            <div
              key={job.id}
              className="bg-gray-800/80 backdrop-blur-xl border border-white/5 rounded-lg overflow-hidden"
            >
              {/* Job Header */}
              <div
                className="p-4 flex justify-between items-center cursor-pointer hover:bg-white/5 transition-colors"
                onClick={() => toggleJobExpansion(job.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-full bg-white/5 backdrop-blur-md">
                    <Briefcase className="w-4 h-4 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-white">{job.title}</h3>
                    <div className="flex items-center text-xs text-gray-400 mt-0.5">
                      <Building className="w-3 h-3 mr-1" />
                      {job.company}
                      {job.location && <span className="ml-2">{job.location}</span>}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="px-2 py-1 text-xs rounded-full bg-white/10 text-white">
                    {getStatusInfo(job.status).label}
                  </span>
                  {expandedJobId === job.id ? (
                    <ChevronUp className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  )}
                </div>
              </div>

              {/* Expanded Job Content */}
              {expandedJobId === job.id && (
                <div className="p-4 border-t border-white/10 space-y-6">
                  {/* Status Timeline */}
                  <div>
                    <h5 className="text-xs font-medium text-white mb-2 flex items-center gap-2">
                      <Circle className="w-2 h-2 text-purple-400" />
                      Application Progress
                      <span className="ml-2 text-xs py-0.5 px-2 rounded-full bg-white/10 text-gray-300">
                        {(() => {
                          const allStatuses = [
                            CandidateStatus.NEW,
                            CandidateStatus.MATCHED,
                            CandidateStatus.SHORTLISTED,
                            CandidateStatus.INTERVIEWING,
                            CandidateStatus.OFFER_PENDING_APPROVAL,
                            CandidateStatus.OFFER_APPROVED,
                            CandidateStatus.OFFER_EXTENDED,
                            CandidateStatus.OFFER_ACCEPTED,
                            CandidateStatus.HIRED,
                          ];
                          const index = allStatuses.findIndex(s => s === job.status);
                          return index === -1
                            ? 0
                            : Math.round((index / (allStatuses.length - 1)) * 100);
                        })()}
                        %
                      </span>
                    </h5>

                    <StatusTimeline
                      currentStatus={job.status}
                      onStatusClick={newStatus => handleStatusClick(job.id, newStatus)}
                      statusTimelineData={job.statusTimelineData}
                    />
                  </div>

                  {/* Available Actions */}
                  <div>
                    <h5 className="text-xs font-medium text-white mb-3 flex items-center gap-2">
                      <Circle className="w-2 h-2 text-purple-400" />
                      Available Actions
                    </h5>

                    {getAvailableActions(job.status).length > 0 ? (
                      <div className="flex flex-row gap-2 flex-wrap pt-1">
                        {getAvailableActions(job.status).map((action, actionIdx) => (
                          <button
                            type="button"
                            key={actionIdx}
                            onClick={() => handleStatusClick(job.id, action.status)}
                            disabled={isLoading}
                            className={`flex items-center justify-between px-4 py-2.5 rounded-lg text-xs font-medium transition-all duration-200 flex-1
                              ${
                                action.status === CandidateStatus.REJECTED
                                  ? 'bg-red-950/20 text-red-300 hover:bg-red-900/30 border border-red-900/20 backdrop-blur-md'
                                  : 'bg-green-400/10 text-white hover:bg-white/10 border border-white/10 hover:border-green-500/30 backdrop-blur-md'
                              } disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500/50`}
                          >
                            <span>{action.label}</span>
                            <ArrowRight size={12} className="ml-2" />
                          </button>
                        ))}
                      </div>
                    ) : (
                      <div className="text-sm text-gray-400 italic">
                        No further actions available for this status
                      </div>
                    )}
                  </div>

                  {/* Status History */}
                  {job.statusTimelineData && job.statusTimelineData.length > 0 && (
                    <div>
                      <h5 className="text-xs font-medium text-white mb-3 flex items-center gap-2">
                        <Circle className="w-2 h-2 text-purple-400" />
                        Status History
                      </h5>
                      <div className="space-y-3">
                        {job.statusTimelineData.map((status, idx) => (
                          <div
                            key={idx}
                            className="bg-white/5 backdrop-blur-md rounded-lg p-3 border border-white/10"
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="text-sm font-medium text-white">
                                  {getStatusInfo(status.newStatus as CandidateStatus).label}
                                </div>
                                {status.previousStatus && (
                                  <div className="text-xs text-gray-400 mt-0.5">
                                    From:{' '}
                                    {getStatusInfo(status.previousStatus as CandidateStatus).label}
                                  </div>
                                )}
                              </div>
                              <div className="text-xs text-gray-400">
                                {(() => {
                                  const date = new Date(status.timestamp);
                                  return isNaN(date.getTime())
                                    ? 'Invalid date'
                                    : date.toLocaleString();
                                })()}
                              </div>
                            </div>
                            {status.metadata?.notes && (
                              <div className="mt-2 text-xs text-gray-300 border-t border-white/10 pt-2">
                                {status.metadata.notes}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-500 py-8 bg-gray-800/80 backdrop-blur-xl border border-white/10 rounded-lg">
          <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p>No job applications found for this candidate</p>
        </div>
      )}

      {/* Status update modal */}
      {isStatusModalOpen && statusToChange && selectedJobForStatus && jobApplicationsData && (
        <StatusUpdateModalWithDate
          isOpen={isStatusModalOpen}
          onClose={() => {
            setIsStatusModalOpen(false);
            setStatusToChange(null);
            setSelectedJobForStatus(null);
          }}
          currentStatus={
            jobApplicationsData.jobApplications.find(job => job.id === selectedJobForStatus)
              ?.status || (candidate.status as CandidateStatus)
          }
          newStatus={statusToChange}
          onConfirm={handleUpdateStatus}
          isLoading={isLoading}
          candidateName={`${candidate.firstName} ${candidate.lastName}`}
          jobTitle={
            jobApplicationsData.jobApplications.find(job => job.id === selectedJobForStatus)
              ?.title || jobTitle
          }
          candidateEmail={candidate.email}
          companyName={
            jobApplicationsData.jobApplications.find(job => job.id === selectedJobForStatus)
              ?.company || companyName
          }
        />
      )}
    </div>
  );
};

export default StatusTimelineTab;
