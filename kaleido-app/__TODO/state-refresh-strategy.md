# State Management & Refresh Strategy for Headstart App

## Current Issues Identified

### 1. Data Not Refreshing Until Manual Page Refresh
The app has systemic issues where state changes (especially after background job completions) don't trigger UI updates. Users must manually refresh the page to see updated data.

### 2. Root Causes

#### A. Incomplete Worker Completion Handling
In `src/stores/unifiedJobStore/slices/upload-jobs-operations.ts`:
- The `onWorkerComplete` function for 'matchrank' type does nothing:
```typescript
case 'matchrank':
  // Handle other worker types as needed
  break;
```
- It should trigger `fetchCandidates()` or `fetchJobDetails()` to refresh the data

#### B. StatusManagerStore Integration Issues
In `src/stores/statusManagerStore.ts`:
- When showing completion modal, it only calls `onWorkerComplete` which doesn't refresh data
- The dynamic import is correct but the worker completion handler is incomplete

#### C. Missing Event Listeners
- Components aren't listening for worker completion events
- No automatic refresh mechanism when jobs complete

### 3. Current Data Flow

```mermaid
graph TD
    A[Background Job Completes] --> B[GenericStatusManager]
    B --> C[showCompletionModal]
    C --> D[StatusManagerStore]
    D --> E[onWorkerComplete]
    E --> F[Nothing happens for matchrank]
    G[User sees stale data] --> H[Manual refresh required]
```

## Proposed Solution

### Phase 1: Fix Immediate matchRank Refresh Issue

#### 1.1 Update onWorkerComplete in unifiedJobStore
```typescript
// src/stores/unifiedJobStore/slices/upload-jobs-operations.ts
case 'matchrank':
  // Refresh candidates for the current job
  const currentJobId = get().selectedJobId;
  if (currentJobId) {
    // Clear cache and fetch fresh data
    set((state: any) => ({
      lastFetchTime: {
        ...state.lastFetchTime,
        [`candidates-${currentJobId}`]: 0,
        [`job-details-${currentJobId}`]: 0,
      }
    }));
    
    // Fetch fresh candidates and job details
    await get().fetchCandidates(currentJobId, 1);
    await get().fetchJobDetails(currentJobId, true);
    
    // Dispatch refresh event for components
    window.dispatchEvent(
      new CustomEvent('candidatesRefreshed', {
        detail: { jobId: currentJobId, timestamp: Date.now() }
      })
    );
  }
  break;
```

#### 1.2 Update StatusCompletionModalManager for Match/Rank Screen
```typescript
// src/components/shared/StatusCompletionModalManager.tsx
// Add special handling for match/rank screen
useEffect(() => {
  const handleJobCompleted = (event: CustomEvent) => {
    const { action, jobId } = event.detail;
    const currentPath = window.location.pathname;
    
    // Check if we're on the match/rank screen
    const isMatchRankScreen = currentPath.includes('/jobs/') && 
                             currentPath.includes('/candidates');
    
    if (isMatchRankScreen && action === 'matchRank') {
      // Don't show modal, just refresh the data
      const { useUnifiedJobStore } = await import('@/stores/unifiedJobStore');
      const store = useUnifiedJobStore.getState();
      
      // Force refresh without showing modal
      if (store.selectedJobId === jobId) {
        await store.fetchCandidates(jobId, store.currentPage);
        await store.fetchJobDetails(jobId, true);
        
        // Show subtle notification instead of modal
        showToast('Candidates updated', 'success');
        
        // Close the status manager
        if (event.detail.closeManager) {
          event.detail.closeManager();
        }
        
        return; // Don't show modal
      }
    }
    
    // Continue with normal modal flow for other cases
    setModalProps({...});
    setIsModalOpen(true);
  };
}, []);
```

### Phase 2: Implement Global State Refresh Strategy

#### 2.1 Create Refresh Manager
```typescript
// src/stores/refreshManager.ts
export const RefreshManager = {
  // Track what needs refreshing
  pendingRefreshes: new Set<string>(),
  
  // Register refresh handlers
  handlers: new Map<string, () => Promise<void>>(),
  
  // Queue a refresh
  queueRefresh(key: string) {
    this.pendingRefreshes.add(key);
    this.processQueue();
  },
  
  // Process pending refreshes
  async processQueue() {
    for (const key of this.pendingRefreshes) {
      const handler = this.handlers.get(key);
      if (handler) {
        await handler();
        this.pendingRefreshes.delete(key);
      }
    }
  },
  
  // Register a refresh handler
  registerHandler(key: string, handler: () => Promise<void>) {
    this.handlers.set(key, handler);
  }
};
```

#### 2.2 Implement Store Subscriptions
```typescript
// In each store, subscribe to relevant events
useUnifiedJobStore.subscribe(
  (state) => state.workerJobs,
  (workerJobs) => {
    // Check for completed jobs and trigger refreshes
    Object.values(workerJobs).forEach(job => {
      if (job.status === 'completed' && !job.refreshed) {
        RefreshManager.queueRefresh(`job-${job.relatedId}`);
        job.refreshed = true;
      }
    });
  }
);
```

### Phase 3: Component-Level Improvements

#### 3.1 Add Real-time Listeners to Components
```typescript
// In components that display job data
useEffect(() => {
  const handleCandidatesRefreshed = (event: CustomEvent) => {
    if (event.detail.jobId === jobId) {
      // Component-specific refresh logic
      refetchData();
    }
  };
  
  window.addEventListener('candidatesRefreshed', handleCandidatesRefreshed);
  return () => {
    window.removeEventListener('candidatesRefreshed', handleCandidatesRefreshed);
  };
}, [jobId]);
```

#### 3.2 Implement Optimistic Updates
```typescript
// When status changes, update UI immediately
const handleStatusChange = async (candidateId: string, newStatus: string) => {
  // Optimistic update
  updateCandidateStatusOptimistic(candidateId, newStatus);
  
  try {
    await updateCandidateStatus(candidateId, newStatus);
  } catch (error) {
    // Revert on error
    revertOptimisticUpdate(candidateId);
  }
};
```

## Implementation Checklist

### Immediate Fixes (Priority 1)
- [ ] Fix `onWorkerComplete` for matchrank type
- [ ] Update StatusCompletionModalManager for match/rank screen
- [ ] Add fetchCandidates call after matchrank completion
- [ ] Test with actual matchrank job completion

### Short-term Improvements (Priority 2)
- [ ] Implement RefreshManager
- [ ] Add event listeners to key components
- [ ] Add loading states during refresh
- [ ] Implement debouncing for multiple rapid refreshes

### Long-term Improvements (Priority 3)
- [ ] Migrate to real-time WebSocket updates
- [ ] Implement proper cache invalidation strategy
- [ ] Add background sync for offline support
- [ ] Implement differential updates (only fetch changed data)

## Testing Strategy

### Manual Testing
1. Upload candidates via match/rank
2. Wait for processing to complete
3. Verify data refreshes without manual page reload
4. Check all tabs update correctly

### Automated Testing
```typescript
describe('Worker Completion Refresh', () => {
  it('should refresh candidates when matchrank completes', async () => {
    const store = useUnifiedJobStore.getState();
    
    // Simulate worker completion
    await store.onWorkerComplete('job-123', 'matchrank');
    
    // Verify refresh was triggered
    expect(store.fetchCandidates).toHaveBeenCalledWith('job-123', 1);
    expect(store.fetchJobDetails).toHaveBeenCalledWith('job-123', true);
  });
});
```

## Migration Path

### Step 1: Quick Fix (Today)
- Implement the matchrank refresh in onWorkerComplete
- Update StatusCompletionModalManager

### Step 2: Rollout (This Week)
- Test in development environment
- Monitor for performance issues
- Deploy to staging for QA

### Step 3: Full Implementation (Next Sprint)
- Implement RefreshManager
- Add comprehensive event system
- Update all affected components

## Performance Considerations

### Debouncing
- Implement 1-second debounce for refresh requests
- Batch multiple refresh requests together

### Caching
- Keep cache for 5 minutes by default
- Force refresh only when necessary
- Use stale-while-revalidate pattern

### Optimization
- Only fetch visible data (pagination)
- Use minimal candidate data for lists
- Fetch full details on demand

## Known Limitations

### Current Architecture
- No real-time updates (requires polling)
- All refreshes are full data fetches
- No differential updates

### Future Improvements Needed
- WebSocket integration for real-time updates
- GraphQL for efficient data fetching
- Proper state synchronization across tabs

## Related Files

### Core Files to Modify
1. `/src/stores/unifiedJobStore/slices/upload-jobs-operations.ts` - Fix onWorkerComplete
2. `/src/components/shared/StatusCompletionModalManager.tsx` - Update modal behavior
3. `/src/stores/statusManagerStore.ts` - Improve refresh logic

### Components Affected
1. `/src/components/MatchRank/UnifiedCandidateView.tsx`
2. `/src/components/MatchRank/MatchedCandidateList.tsx`
3. All tabs in `/src/components/MatchRank/CandidateInfo/tabs/`

### Stores Involved
1. `unifiedJobStore` - Main job and candidate data
2. `statusManagerStore` - Worker job tracking
3. `matchrankDetailsStore` - Match/rank specific state

## Success Metrics

### User Experience
- Zero manual refreshes required
- Data updates within 2 seconds of job completion
- Smooth UI transitions during updates

### Technical Metrics
- Refresh latency < 1 second
- No duplicate API calls
- Cache hit rate > 80%

## Rollback Plan

If issues arise:
1. Revert onWorkerComplete changes
2. Restore original StatusCompletionModalManager
3. Document specific failure scenarios
4. Implement fixes in staging first

---

**Document Version**: 1.0  
**Last Updated**: 2025-08-05  
**Author**: AI Assistant  
**Status**: Ready for Implementation