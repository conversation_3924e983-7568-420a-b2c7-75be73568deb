import '@testing-library/jest-dom';

// Set default timeout for all tests
jest.setTimeout(15000);

// Prevent hanging promises by adding unhandled promise rejection handler
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Add afterEach to clean up any timers or async operations
afterEach(() => {
  jest.clearAllTimers();
  jest.clearAllMocks();
  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }
  // Clear any pending promises
  return Promise.resolve();
});

// Add beforeEach to ensure clean state
beforeEach(() => {
  jest.clearAllTimers();
  jest.clearAllMocks();
});

// Mock axios globally
jest.mock('axios', () => {
  const createMockAxiosInstance = () => {
    const instance = jest.fn().mockResolvedValue({ data: {} });
    instance.interceptors = {
      request: {
        use: jest.fn(),
      },
      response: {
        use: jest.fn(),
      },
    };
    instance.get = jest.fn().mockResolvedValue({ data: {} });
    instance.post = jest.fn().mockResolvedValue({ data: {} });
    instance.put = jest.fn().mockResolvedValue({ data: {} });
    instance.delete = jest.fn().mockResolvedValue({ data: {} });
    instance.patch = jest.fn().mockResolvedValue({ data: {} });
    return instance;
  };

  return {
    default: {
      create: jest.fn(() => createMockAxiosInstance()),
      isCancel: jest.fn(),
      CancelToken: {
        source: jest.fn(() => ({
          token: {},
          cancel: jest.fn(),
        })),
      },
    },
    create: jest.fn(() => createMockAxiosInstance()),
    isCancel: jest.fn(),
    CancelToken: {
      source: jest.fn(() => ({
        token: {},
        cancel: jest.fn(),
      })),
    },
  };
});

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
      isLocaleDomain: false,
      isReady: true,
      defaultLocale: 'en',
      domainLocales: [],
      isPreview: false,
    }
  },
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    // Filter out Next.js-specific props that don't belong on regular img elements
    const { priority, quality, placeholder, blurDataURL, loader, unoptimized, fill, sizes, ...imgProps } = props
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...imgProps} />
  },
}))

// Mock Next.js Link component
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }) => {
    return <a {...props}>{children}</a>
  },
}))

// Mock environment variables
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_API_URL_BASE = 'http://localhost:8080/api'

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
}

// Mock fetch
global.fetch = jest.fn()

// Add TextEncoder and TextDecoder polyfills for Node.js environment
const { TextEncoder, TextDecoder } = require('util')
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Add Request and Response polyfills for Next.js/Auth0
global.Request = class Request {
  constructor(input, init = {}) {
    this.url = input
    this.method = init.method || 'GET'
    this.headers = new Map(Object.entries(init.headers || {}))
    this.body = init.body
  }
}

global.Response = class Response {
  constructor(body, init = {}) {
    this.body = body
    this.status = init.status || 200
    this.statusText = init.statusText || 'OK'
    this.headers = new Map(Object.entries(init.headers || {}))
  }

  json() {
    return Promise.resolve(JSON.parse(this.body))
  }

  text() {
    return Promise.resolve(this.body)
  }
}

// Suppress console errors and warnings during tests
const originalError = console.error
const originalWarn = console.warn

beforeAll(() => {
  console.error = (...args) => {
    // Check for JSDOM navigation errors first (can be Error objects or strings)
    const firstArg = args[0]
    if (firstArg) {
      // Handle Error objects
      if (firstArg instanceof Error && firstArg.message && firstArg.message.includes('Not implemented: navigation')) {
        return
      }

      // Handle string messages
      if (typeof firstArg === 'string' &&
          (firstArg.includes('Not implemented: navigation') || firstArg.includes('Not implemented: window.scrollTo'))) {
        return
      }

      // Handle Error objects with type property (JSDOM not implemented features)
      if (firstArg.type === 'not implemented' && firstArg.message &&
          (firstArg.message.includes('navigation') || firstArg.message.includes('window.scrollTo'))) {
        return
      }
    }

    if (typeof args[0] === 'string') {
      // Suppress React warnings that are expected in test environment
      if (
        args[0].includes('Warning: ReactDOM.render is no longer supported') ||
        args[0].includes('Warning: Received `true` for a non-boolean attribute') ||
        args[0].includes('Warning: Received `false` for a non-boolean attribute') ||
        args[0].includes('Warning: React does not recognize the') ||
        args[0].includes('Warning: React.createFactory() is deprecated') ||
        args[0].includes('Warning: componentWillReceiveProps has been renamed') ||
        args[0].includes('Warning: componentWillMount has been renamed') ||
        args[0].includes('Warning: componentWillUpdate has been renamed') ||
        args[0].includes('Warning: An update to') ||
        args[0].includes('not wrapped in act(...)') ||
        args[0].includes('Failed to parse cached role data:') ||
        args[0].includes('priority') ||
        args[0].includes('fill="true"') ||
        args[0].includes('for a non-boolean attribute `fill`') ||
        args[0].includes('non-boolean attribute `fill`') ||
        args[0].includes('Received `true` for a non-boolean attribute `fill`') ||
        args[0].includes('Warning: Received `true` for a non-boolean attribute `fill`') ||
        args[0].includes('for a non-boolean attribute `multiline`') ||
        args[0].includes('non-boolean attribute `multiline`') ||
        args[0].includes('Received `true` for a non-boolean attribute `multiline`') ||
        args[0].includes('Warning: Received `true` for a non-boolean attribute `multiline`') ||
        (args[0].includes('Warning: Received') && args[0].includes('non-boolean attribute `fill`')) ||
        (args[0].includes('Warning: Received') && args[0].includes('non-boolean attribute `multiline`'))
      ) {
        return
      }

      // Suppress expected test error messages
      if (
        args[0].includes('Enhanced user data fetch error:') ||
        args[0].includes('Network error') ||
        args[0].includes('Backend error') ||
        args[0].includes('Unauthorized') ||
        args[0].includes('User not found') ||
        args[0].includes('Error fetching subscription data:') ||
        args[0].includes('Error refreshing subscription data:') ||
        args[0].includes('Error consuming credits for') ||
        args[0].includes('Error saving job:') ||
        args[0].includes('Error fetching job status for job') ||
        args[0].includes('Error polling video status:') ||
        args[0].includes('_apiHelper.default.get is not a function') ||
        args[0].includes('Failed to set backend feature flag override:') ||
        args[0].includes('Failed to clear backend feature flag override:') ||
        args[0].includes('Failed to clear all backend feature flag overrides:') ||
        args[0].includes('Error checking candidates:') ||
        args[0].includes('API error') ||
        args[0].includes('Authentication error:') ||
        args[0].includes('Authentication failed') ||
        args[0].includes('useEnhancedUserData: Error in queryFn:') ||
        args[0].includes('PostLoginRoleHandler: Error:') ||
        args[0].includes('❌ [Polling] Error details:') ||
        args[0].includes('markAsPollingEndpoint is not a function') ||
        args[0].includes('Error cancelling job:') ||
        args[0].includes('Failed to fetch script from API:') ||
        args[0].includes('Failed to fetch insights:') ||
        args[0].includes('Failed to fetch insight:') ||
        args[0].includes('Error updating candidate status:') ||
        args[0].includes('Error updating shortlist status:') ||
        args[0].includes('Error completing company setup:') ||
        args[0].includes('Error fetching company profile:') ||
        args[0].includes('AppLayout: No valid role found for user') ||
        args[0].includes('[onWorkerComplete] Error refreshing after matchrank:') ||
        args[0].includes('Error fetching user data:') ||
        args[0].includes('Session expired in getSession') ||
        args[0].includes('Failed to get session:') ||
        args[0].includes('Network error')
      ) {
        return
      }
    }

    originalError.call(console, ...args)
  }

  console.warn = (...args) => {
    if (typeof args[0] === 'string') {
      // Suppress expected test warning messages
      if (
        args[0].includes('Failed to parse cached role data:') ||
        args[0].includes('SyntaxError: Unexpected token') ||
        args[0].includes('is not valid JSON') ||
        args[0].includes('Failed to fetch feature flags from backend:') ||
        args[0].includes('User not authenticated on protected route:')
      ) {
        return
      }
    }

    originalWarn.call(console, ...args)
  }
})

// Suppress specific console.log messages during tests
const originalLog = console.log

console.log = (...args) => {
  if (typeof args[0] === 'string') {
    // Suppress component debug logs
    if (
      args[0].includes('PostLoginRoleHandler:') ||
      args[0].includes('useEnhancedUserData:') ||
      args[0].includes('🎯 [') ||
      args[0].includes('🔍 [') ||
      args[0].includes('✨ [') ||
      args[0].includes('📋 [') ||
      args[0].includes('🔄 [') ||
      args[0].includes('📊 [') ||
      args[0].includes('🧹 [') ||
      args[0].includes('[test]') ||
      args[0].includes('[Polling]') ||
      args[0].includes('[Completion Detection]') ||
      args[0].includes('Manager Status:') ||
      args[0].includes('No valid role found after grace period') ||
      args[0].includes('Clearing auth cache and signing out')
    ) {
      return
    }
  }
  
  originalLog.call(console, ...args)
}

afterAll(() => {
  console.error = originalError
  console.warn = originalWarn
  console.log = originalLog
})
