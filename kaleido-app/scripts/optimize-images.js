const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const inputDir = path.join(__dirname, '../public/images/job-seeker-setup/new');
const outputDir = inputDir;

// Shorter, more descriptive filenames
const fileNameMap = {
  'african-man-in-white-shirt-posing-2025-04-04-09-49-32-utc.jpg': 'profile-1',
  'businessman-looking-at-the-camera-and-smile-while-2025-01-09-17-50-11-utc.jpg': 'profile-2',
  'close-up-of-a-smiling-young-woman-standing-at-the-2025-02-14-20-08-47-utc.jpg': 'profile-3',
  'face-of-happy-indian-man-with-mustache-wearing-blu-2025-01-29-03-03-07-utc.jpg': 'profile-4',
  'handsome-african-man-in-the-street-2024-10-18-04-53-50-utc.jpg': 'profile-5',
  'outdoor-portrait-of-a-black-woman-with-a-scarf-2025-01-15-16-18-57-utc.jpg': 'profile-6',
  'portrait-of-happy-young-businesswoman-in-a-cafe-2024-09-21-17-53-27-utc.jpg': 'profile-7',
  'portrait-of-young-couple-enjoying-city-life-headin-2024-10-21-20-07-51-utc.jpg': 'profile-8',
  'two-men-one-laughing-with-his-head-back-2025-04-04-06-40-50-utc.jpg': 'profile-9',
  'vertical-shot-of-attractive-happy-stylish-woman-we-2025-03-25-08-05-40-utc.jpg': 'profile-10',
  'young-breakdancer-2025-03-13-10-50-11-utc.jpg': 'profile-11',
  'young-employee-2025-03-16-10-58-49-utc.jpg': 'profile-12',
};

async function optimizeImages() {
  const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.jpg'));
  
  console.log(`Found ${files.length} images to optimize...`);
  
  for (const file of files) {
    const inputPath = path.join(inputDir, file);
    const newName = fileNameMap[file] || file.replace('.jpg', '');
    const outputPath = path.join(outputDir, `${newName}.webp`);
    
    try {
      await sharp(inputPath)
        .webp({ 
          quality: 85,
          effort: 6,
          smartSubsample: true,
          reductionEffort: 6
        })
        .resize(1200, null, { 
          withoutEnlargement: true,
          fit: 'inside'
        })
        .toFile(outputPath);
      
      console.log(`✓ Optimized: ${file} -> ${newName}.webp`);
      
      // Delete original file
      fs.unlinkSync(inputPath);
      console.log(`  Deleted original: ${file}`);
    } catch (error) {
      console.error(`✗ Failed to optimize ${file}:`, error.message);
    }
  }
  
  console.log('\n✅ Image optimization complete!');
}

optimizeImages().catch(console.error);