{"extends": "next/core-web-vitals", "ignorePatterns": ["src/lib/__tests__/apiHelper.test.ts"], "rules": {"react/no-unescaped-entities": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/restrict-template-expressions": "off", "@typescript-eslint/no-floating-promises": "off", "@typescript-eslint/no-misused-promises": "off", "@typescript-eslint/require-await": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/ban-types": "off", "import/order": "off"}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.*", "**/*.spec.*", "**/jest.setup.js", "jest.setup.js", "src/__tests__/**/*", "src/**/__tests__/**/*"], "rules": {"@next/next/no-img-element": "off"}}]}