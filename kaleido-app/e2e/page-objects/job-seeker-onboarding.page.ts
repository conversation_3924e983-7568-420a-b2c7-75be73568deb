import { Page, Locator } from '@playwright/test';

export class JobSeekerOnboardingPage {
  readonly page: Page;
  
  // Step navigation
  readonly nextButton: Locator;
  readonly backButton: Locator;
  readonly skipButton: Locator;
  readonly progressIndicator: Locator;
  readonly closeButton: Locator;
  
  // Floating Profile Assistant Widget
  readonly profileAssistant: Locator;
  readonly assistantAvatar: Locator;
  readonly assistantMinimizeButton: Locator;
  readonly assistantExpandButton: Locator;
  readonly assistantMessage: Locator;
  readonly mandatoryProgressRing: Locator;
  readonly optionalProgressRing: Locator;
  readonly overallProgressRing: Locator;
  readonly mandatoryFieldsList: Locator;
  readonly assistantSkipOptionalButton: Locator;
  readonly assistantWhyNeededButton: Locator;
  
  // Visual indicators for mandatory/optional sections
  readonly mandatorySectionIndicators: Locator;
  readonly optionalSectionIndicators: Locator;
  readonly completedFieldCheckmarks: Locator;
  readonly incompleteFieldWarnings: Locator;
  readonly collapsibleSections: Locator;
  readonly expandCollapsibleButton: Locator;
  
  // Step 1: Smart Resume Import (Optional Helper)
  readonly resumeUploadArea: Locator;
  readonly resumeFileInput: Locator;
  readonly uploadedFileName: Locator;
  readonly parseResumeButton: Locator;
  readonly linkedInImportButton: Locator;
  readonly skipImportButton: Locator;
  readonly importPreviewSection: Locator;
  
  // Step 2: Essential Information (MANDATORY FIELDS VISIBLE)
  readonly essentialInfoSection: Locator;
  readonly mandatoryInfoSection: Locator;
  readonly firstNameInput: Locator;
  readonly lastNameInput: Locator;
  readonly emailInput: Locator;
  readonly firstNameValidationError: Locator;
  readonly lastNameValidationError: Locator;
  readonly emailValidationError: Locator;
  // Optional fields in collapsible section
  readonly additionalInfoSection: Locator;
  readonly phoneInput: Locator;
  readonly locationInput: Locator;
  readonly linkedInInput: Locator;
  readonly websiteInput: Locator;
  
  // Step 3: Professional Profile (MANDATORY SKILLS VISIBLE)
  readonly professionalProfileSection: Locator;
  readonly mandatorySkillsSection: Locator;
  readonly skillsSelector: Locator;
  readonly skillsInput: Locator;
  readonly skillTags: Locator;
  readonly addSkillButton: Locator;
  readonly removeSkillButton: Locator;
  readonly skillsRequiredAlert: Locator;
  readonly minSkillsWarning: Locator;
  // Optional collapsible sections
  readonly professionalSummarySection: Locator;
  readonly summaryTextarea: Locator;
  readonly headlineInput: Locator;
  readonly yearsOfExperienceInput: Locator;
  readonly workExperienceSection: Locator;
  readonly addExperienceButton: Locator;
  readonly jobTitleInput: Locator;
  readonly companyNameInput: Locator;
  readonly experienceStartDateInput: Locator;
  readonly experienceEndDateInput: Locator;
  readonly currentlyWorkingCheckbox: Locator;
  readonly jobDescriptionTextarea: Locator;
  readonly educationSection: Locator;
  readonly addEducationButton: Locator;
  readonly schoolNameInput: Locator;
  readonly degreeInput: Locator;
  readonly fieldOfStudyInput: Locator;
  readonly educationStartDateInput: Locator;
  readonly educationEndDateInput: Locator;
  readonly currentlyStudyingCheckbox: Locator;
  readonly languagesInput: Locator;
  readonly addLanguageButton: Locator;
  
  // Step 4: Job Preferences (ALL MANDATORY FIELDS VISIBLE)
  readonly jobPreferencesSection: Locator;
  readonly preferencesRequiredAlert: Locator;
  // Job Types - Required
  readonly jobTypesSection: Locator;
  readonly jobTypesCheckboxes: Locator;
  readonly jobTypesError: Locator;
  // Locations - Required
  readonly locationsSection: Locator;
  readonly locationPreferencesInput: Locator;
  readonly locationTags: Locator;
  readonly locationsError: Locator;
  // Remote Preference - Required
  readonly remotePreferenceSection: Locator;
  readonly remotePreferenceRadios: Locator;
  readonly remotePreferenceError: Locator;
  // Salary Range - All fields required
  readonly salaryRangeSection: Locator;
  readonly salaryCurrencySelect: Locator;
  readonly salaryPeriodSelect: Locator;
  readonly minSalaryInput: Locator;
  readonly maxSalaryInput: Locator;
  readonly salaryRangeError: Locator;
  // Optional
  readonly industriesSection: Locator;
  readonly industriesMultiSelect: Locator;
  
  // Step 5: Additional Details (All Optional)
  readonly additionalDetailsSection: Locator;
  readonly optionalStepIndicator: Locator;
  // Values & Culture
  readonly valuesSection: Locator;
  readonly valuesCheckboxes: Locator;
  readonly valuesSearchInput: Locator;
  // Availability
  readonly availabilitySection: Locator;
  readonly immediatelyAvailableCheckbox: Locator;
  readonly noticePeriodInput: Locator;
  readonly startDateInput: Locator;
  // Portfolio Links
  readonly portfolioSection: Locator;
  readonly portfolioUrlInput: Locator;
  readonly githubUrlInput: Locator;
  readonly addPortfolioItemButton: Locator;
  
  // Step 6: Verification & Media (Optional but Highlighted)
  readonly verificationSection: Locator;
  readonly trustBadgeIndicator: Locator;
  readonly verificationBenefits: Locator;
  // Video Introduction
  readonly videoIntroSection: Locator;
  readonly recordVideoButton: Locator;
  readonly uploadVideoButton: Locator;
  readonly videoPreview: Locator;
  readonly deleteVideoButton: Locator;
  // ID Verification
  readonly idVerificationSection: Locator;
  readonly idUploadArea: Locator;
  readonly idFileInput: Locator;
  readonly idVerificationStatus: Locator;
  
  // Step 7: Privacy & Launch
  readonly privacyLaunchSection: Locator;
  readonly privacySettingsSection: Locator;
  readonly profileVisibilityRadios: Locator;
  readonly allowMessagesCheckbox: Locator;
  readonly showSalaryCheckbox: Locator;
  readonly showContactCheckbox: Locator;
  readonly profilePreviewSection: Locator;
  readonly profilePreviewButton: Locator;
  readonly completionChecklist: Locator;
  readonly mandatoryFieldsStatus: Locator;
  readonly optionalFieldsStatus: Locator;
  
  // Completion
  readonly completeSetupButton: Locator;
  readonly completionMessage: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Step navigation
    this.nextButton = page.getByRole('button', { name: /^(next|continue)$/i });
    this.backButton = page.getByRole('button', { name: /^(back|previous)$/i });
    this.skipButton = page.getByRole('button', { name: /^skip$/i });
    this.progressIndicator = page.getByTestId('step-progress');
    this.closeButton = page.getByRole('button', { name: /close/i }).or(page.locator('button[aria-label="Close"]'));
    
    // Floating Profile Assistant Widget
    this.profileAssistant = page.locator('[data-testid="profile-assistant"], .profile-assistant-widget');
    this.assistantAvatar = page.locator('[data-testid="assistant-avatar"], .assistant-avatar');
    this.assistantMinimizeButton = page.locator('[data-testid="assistant-minimize"]');
    this.assistantExpandButton = page.locator('[data-testid="assistant-expand"]');
    this.assistantMessage = page.locator('[data-testid="assistant-message"], .assistant-message');
    this.mandatoryProgressRing = page.locator('[data-testid="mandatory-progress"], .progress-ring-mandatory');
    this.optionalProgressRing = page.locator('[data-testid="optional-progress"], .progress-ring-optional');
    this.overallProgressRing = page.locator('[data-testid="overall-progress"], .progress-ring-total');
    this.mandatoryFieldsList = page.locator('[data-testid="mandatory-fields-list"], .mandatory-fields-display');
    this.assistantSkipOptionalButton = page.locator('[data-testid="assistant-skip-optional"]');
    this.assistantWhyNeededButton = page.locator('[data-testid="assistant-why-needed"]');
    
    // Visual indicators for mandatory/optional sections
    this.mandatorySectionIndicators = page.locator('.mandatory-section, [data-mandatory="true"]');
    this.optionalSectionIndicators = page.locator('.optional-section, [data-optional="true"]');
    this.completedFieldCheckmarks = page.locator('.field-status-complete, [data-field-complete="true"]');
    this.incompleteFieldWarnings = page.locator('.field-status-incomplete, [data-field-complete="false"]');
    this.collapsibleSections = page.locator('details, .collapsible-section');
    this.expandCollapsibleButton = page.locator('summary, .expand-section-button');
    
    // Step 1: Smart Resume Import (Optional Helper)
    this.resumeUploadArea = page.locator('[data-testid="resume-upload-area"], .dropzone');
    this.resumeFileInput = page.locator('input[type="file"][accept*="pdf"]');
    this.uploadedFileName = page.locator('[data-testid="uploaded-file-name"]');
    this.parseResumeButton = page.getByRole('button', { name: /parse|extract/i });
    this.linkedInImportButton = page.getByRole('button', { name: /linkedin|import.*linkedin/i });
    this.skipImportButton = page.getByRole('button', { name: /skip.*import|skip.*step/i });
    this.importPreviewSection = page.locator('[data-testid="import-preview"], .import-preview');
    
    // Step 2: Essential Information (MANDATORY FIELDS VISIBLE)
    this.essentialInfoSection = page.locator('[data-testid="essential-info-step"], .essential-info-section');
    this.mandatoryInfoSection = page.locator('[data-testid="mandatory-info"], .mandatory-section');
    this.firstNameInput = page.locator('input[name="firstName"], input[placeholder*="First"]');
    this.lastNameInput = page.locator('input[name="lastName"], input[placeholder*="Last"]');
    this.emailInput = page.locator('input[name="email"], input[type="email"]');
    this.firstNameValidationError = page.locator('[data-testid="firstName-error"], .firstName-error');
    this.lastNameValidationError = page.locator('[data-testid="lastName-error"], .lastName-error');
    this.emailValidationError = page.locator('[data-testid="email-error"], .email-error');
    // Optional fields in collapsible section
    this.additionalInfoSection = page.locator('[data-testid="additional-info"], details:has-text("Additional Information")');
    this.phoneInput = page.locator('input[name="phone"], input[type="tel"]');
    this.locationInput = page.locator('input[name="location"], input[placeholder*="Location"]');
    this.linkedInInput = page.locator('input[name="linkedIn"], input[placeholder*="LinkedIn"]');
    this.websiteInput = page.locator('input[name="website"], input[placeholder*="Website"]');
    
    // Step 3: Professional Profile (MANDATORY SKILLS VISIBLE)
    this.professionalProfileSection = page.locator('[data-testid="professional-profile-step"], .professional-profile-section');
    this.mandatorySkillsSection = page.locator('[data-testid="mandatory-skills"], .mandatory-skills-section');
    this.skillsSelector = page.locator('[data-testid="skills-selector"], .skills-selector');
    this.skillsInput = page.locator('input[placeholder*="skill"], input[name="skills"]');
    this.skillTags = page.locator('[data-testid="skill-tag"], .skill-tag');
    this.addSkillButton = page.getByRole('button', { name: /add.*skill/i });
    this.removeSkillButton = page.locator('[data-testid="remove-skill"], .remove-skill-btn');
    this.skillsRequiredAlert = page.locator('[data-testid="skills-required-alert"], .skills-required-alert');
    this.minSkillsWarning = page.locator('[data-testid="min-skills-warning"], .min-skills-warning');
    // Optional collapsible sections
    this.professionalSummarySection = page.locator('[data-testid="professional-summary"], details:has-text("Professional Summary")');
    this.summaryTextarea = page.locator('textarea[name="summary"], textarea[placeholder*="summary"]');
    this.headlineInput = page.locator('input[name="headline"], input[placeholder*="headline"]');
    this.yearsOfExperienceInput = page.locator('input[name="yearsOfExperience"], input[type="number"]');
    this.workExperienceSection = page.locator('[data-testid="work-experience"], details:has-text("Work Experience")');
    this.addExperienceButton = page.getByRole('button', { name: /add.*experience/i });
    this.jobTitleInput = page.locator('input[name="jobTitle"], input[placeholder*="Job Title"]');
    this.companyNameInput = page.locator('input[name="companyName"], input[placeholder*="Company"]');
    this.experienceStartDateInput = page.locator('input[name="startDate"]').first();
    this.experienceEndDateInput = page.locator('input[name="endDate"]').first();
    this.currentlyWorkingCheckbox = page.locator('input[type="checkbox"][name*="current"]');
    this.jobDescriptionTextarea = page.locator('textarea[name="description"], textarea[placeholder*="Description"]');
    this.educationSection = page.locator('[data-testid="education"], details:has-text("Education")');
    this.addEducationButton = page.getByRole('button', { name: /add.*education/i });
    this.schoolNameInput = page.locator('input[name="schoolName"], input[placeholder*="School"]');
    this.degreeInput = page.locator('input[name="degree"], input[placeholder*="Degree"]');
    this.fieldOfStudyInput = page.locator('input[name="fieldOfStudy"], input[placeholder*="Field"]');
    this.educationStartDateInput = page.locator('input[name="startDate"]').last();
    this.educationEndDateInput = page.locator('input[name="endDate"]').last();
    this.currentlyStudyingCheckbox = page.locator('input[type="checkbox"][name*="current"]').last();
    this.languagesInput = page.locator('input[placeholder*="language"]');
    this.addLanguageButton = page.getByRole('button', { name: /add.*language/i });
    
    // Step 4: Job Preferences (ALL MANDATORY FIELDS VISIBLE)
    this.jobPreferencesSection = page.locator('[data-testid="job-preferences-step"], .job-preferences-section');
    this.preferencesRequiredAlert = page.locator('[data-testid="preferences-required-alert"], .alert-info');
    // Job Types - Required
    this.jobTypesSection = page.locator('[data-testid="job-types-section"], .job-types-section');
    this.jobTypesCheckboxes = page.locator('input[type="checkbox"][name*="jobType"]');
    this.jobTypesError = page.locator('[data-testid="job-types-error"], .job-types-error');
    // Locations - Required
    this.locationsSection = page.locator('[data-testid="locations-section"], .locations-section');
    this.locationPreferencesInput = page.locator('input[placeholder*="location preference"]');
    this.locationTags = page.locator('[data-testid="location-tag"], .location-tag');
    this.locationsError = page.locator('[data-testid="locations-error"], .locations-error');
    // Remote Preference - Required
    this.remotePreferenceSection = page.locator('[data-testid="remote-preference-section"], .remote-preference-section');
    this.remotePreferenceRadios = page.locator('input[type="radio"][name*="remote"]');
    this.remotePreferenceError = page.locator('[data-testid="remote-preference-error"], .remote-preference-error');
    // Salary Range - All fields required
    this.salaryRangeSection = page.locator('[data-testid="salary-range-section"], .salary-range-section');
    this.salaryCurrencySelect = page.locator('select[name*="currency"], [data-testid="salary-currency"]');
    this.salaryPeriodSelect = page.locator('select[name*="salaryPeriod"]');
    this.minSalaryInput = page.locator('input[name*="minSalary"], input[placeholder*="Min"]');
    this.maxSalaryInput = page.locator('input[name*="maxSalary"], input[placeholder*="Max"]');
    this.salaryRangeError = page.locator('[data-testid="salary-range-error"], .salary-range-error');
    // Optional
    this.industriesSection = page.locator('[data-testid="industries-section"], details:has-text("Industries")');
    this.industriesMultiSelect = page.locator('[data-testid="industries-select"], .industries-multi-select');
    
    // Step 5: Additional Details (All Optional)
    this.additionalDetailsSection = page.locator('[data-testid="additional-details-step"], .additional-details-section');
    this.optionalStepIndicator = page.locator('[data-testid="optional-step-indicator"], .optional-indicator');
    // Values & Culture
    this.valuesSection = page.locator('[data-testid="values-section"], details:has-text("Values")');
    this.valuesCheckboxes = page.locator('input[type="checkbox"][name*="value"]');
    this.valuesSearchInput = page.locator('input[placeholder*="Search values"]');
    // Availability
    this.availabilitySection = page.locator('[data-testid="availability-section"], details:has-text("Availability")');
    this.immediatelyAvailableCheckbox = page.locator('input[type="checkbox"][name*="immediately"]');
    this.noticePeriodInput = page.locator('input[name*="noticePeriod"]');
    this.startDateInput = page.locator('input[name*="startDate"], input[type="date"][placeholder*="start"]');
    // Portfolio Links
    this.portfolioSection = page.locator('[data-testid="portfolio-section"], details:has-text("Portfolio")');
    this.portfolioUrlInput = page.locator('input[placeholder*="portfolio"]');
    this.githubUrlInput = page.locator('input[placeholder*="github"], input[name*="github"]');
    this.addPortfolioItemButton = page.getByRole('button', { name: /add.*portfolio/i });
    
    // Step 6: Verification & Media (Optional but Highlighted)
    this.verificationSection = page.locator('[data-testid="verification-step"], .verification-section');
    this.trustBadgeIndicator = page.locator('[data-testid="trust-badge"], .trust-badge-indicator');
    this.verificationBenefits = page.locator('[data-testid="verification-benefits"], .verification-benefits');
    // Video Introduction
    this.videoIntroSection = page.locator('[data-testid="video-intro-section"], .video-intro-section');
    this.recordVideoButton = page.getByRole('button', { name: /record.*video/i });
    this.uploadVideoButton = page.getByRole('button', { name: /upload.*video/i });
    this.videoPreview = page.locator('video, [data-testid="video-preview"]');
    this.deleteVideoButton = page.locator('[data-testid="delete-video"], button[aria-label="Delete video"]');
    // ID Verification
    this.idVerificationSection = page.locator('[data-testid="id-verification-section"], .id-verification-section');
    this.idUploadArea = page.locator('[data-testid="id-upload-area"]');
    this.idFileInput = page.locator('input[type="file"][accept*="image"]');
    this.idVerificationStatus = page.locator('[data-testid="id-verification-status"], .verification-status');
    
    // Step 7: Privacy & Launch
    this.privacyLaunchSection = page.locator('[data-testid="privacy-launch-step"], .privacy-launch-section');
    this.privacySettingsSection = page.locator('[data-testid="privacy-settings"], .privacy-settings');
    this.profileVisibilityRadios = page.locator('input[type="radio"][name*="visibility"]');
    this.allowMessagesCheckbox = page.locator('input[type="checkbox"][name*="messages"]');
    this.showSalaryCheckbox = page.locator('input[type="checkbox"][name*="salary"]');
    this.showContactCheckbox = page.locator('input[type="checkbox"][name*="contact"]');
    this.profilePreviewSection = page.locator('[data-testid="profile-preview"], .profile-preview-section');
    this.profilePreviewButton = page.getByRole('button', { name: /preview.*profile/i });
    this.completionChecklist = page.locator('[data-testid="completion-checklist"], .completion-checklist');
    this.mandatoryFieldsStatus = page.locator('[data-testid="mandatory-fields-status"], .mandatory-status');
    this.optionalFieldsStatus = page.locator('[data-testid="optional-fields-status"], .optional-status');
    
    // Completion
    this.completeSetupButton = page.getByRole('button', { name: /complete|finish/i });
    this.completionMessage = page.locator('[data-testid="completion-message"], .completion-message');
  }

  async goto() {
    await this.page.goto('/jobseeker-onboarding');
  }

  async waitForOnboardingToLoad() {
    // Wait for the loading state to complete
    await this.page.waitForLoadState('networkidle');
    
    // Wait for either the skip button or the first input to be visible
    // This ensures the onboarding slider has fully loaded
    await this.page.waitForSelector(
      'button:has-text("Skip"), button:has-text("Next"), input[type="file"], input[name="firstName"]',
      { timeout: 30000 }
    );
  }

  async uploadResume(filePath: string) {
    await this.resumeFileInput.setInputFiles(filePath);
    await this.page.waitForTimeout(1000); // Wait for upload processing
  }

  async fillPersonalInfo(data: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    location?: string;
    linkedIn?: string;
    website?: string;
  }) {
    await this.firstNameInput.fill(data.firstName);
    await this.lastNameInput.fill(data.lastName);
    await this.emailInput.fill(data.email);
    
    if (data.phone) await this.phoneInput.fill(data.phone);
    if (data.location) await this.locationInput.fill(data.location);
    if (data.linkedIn) await this.linkedInInput.fill(data.linkedIn);
    if (data.website) await this.websiteInput.fill(data.website);
  }

  async fillProfessionalSummary(data: {
    summary: string;
    headline?: string;
    yearsOfExperience?: number;
  }) {
    await this.summaryTextarea.fill(data.summary);
    
    if (data.headline) await this.headlineInput.fill(data.headline);
    if (data.yearsOfExperience !== undefined) {
      await this.yearsOfExperienceInput.fill(data.yearsOfExperience.toString());
    }
  }

  async addExperience(data: {
    jobTitle: string;
    companyName: string;
    startDate: string;
    endDate?: string;
    currentlyWorking?: boolean;
    description?: string;
  }) {
    await this.addExperienceButton.click();
    await this.jobTitleInput.fill(data.jobTitle);
    await this.companyNameInput.fill(data.companyName);
    await this.experienceStartDateInput.fill(data.startDate);
    
    if (data.currentlyWorking) {
      await this.currentlyWorkingCheckbox.check();
    } else if (data.endDate) {
      await this.experienceEndDateInput.fill(data.endDate);
    }
    
    if (data.description) {
      await this.jobDescriptionTextarea.fill(data.description);
    }
  }

  async selectJobTypes(types: string[]) {
    for (const type of types) {
      await this.page.locator(`input[type="checkbox"][value="${type}"]`).check();
    }
  }

  async setRemotePreference(preference: 'onsite' | 'remote' | 'hybrid') {
    await this.page.locator(`input[type="radio"][value="${preference}"]`).check();
  }

  async setSalaryExpectations(min: number, max: number, period: 'yearly' | 'monthly' | 'hourly' = 'yearly') {
    await this.minSalaryInput.fill(min.toString());
    await this.maxSalaryInput.fill(max.toString());
    await this.salaryPeriodSelect.selectOption(period);
  }

  async selectValues(values: string[]) {
    for (const value of values) {
      const checkbox = this.page.locator(`input[type="checkbox"][value="${value}"]`);
      if (await checkbox.isVisible()) {
        await checkbox.check();
      } else {
        // Search for value if not visible
        await this.valuesSearchInput.fill(value);
        await this.page.waitForTimeout(500);
        await checkbox.check();
        await this.valuesSearchInput.clear();
      }
    }
  }

  async setPrivacySettings(settings: {
    profileVisibility?: 'public' | 'private' | 'employers-only';
    allowMessages?: boolean;
    showSalary?: boolean;
    showContact?: boolean;
  }) {
    if (settings.profileVisibility) {
      await this.page.locator(`input[type="radio"][value="${settings.profileVisibility}"]`).check();
    }
    
    if (settings.allowMessages !== undefined) {
      settings.allowMessages ? 
        await this.allowMessagesCheckbox.check() : 
        await this.allowMessagesCheckbox.uncheck();
    }
    
    if (settings.showSalary !== undefined) {
      settings.showSalary ? 
        await this.showSalaryCheckbox.check() : 
        await this.showSalaryCheckbox.uncheck();
    }
    
    if (settings.showContact !== undefined) {
      settings.showContact ? 
        await this.showContactCheckbox.check() : 
        await this.showContactCheckbox.uncheck();
    }
  }

  async goToNextStep() {
    await this.nextButton.click();
    await this.page.waitForTimeout(500); // Wait for animation
  }

  async goToPreviousStep() {
    await this.backButton.click();
    await this.page.waitForTimeout(500); // Wait for animation
  }

  async skipStep() {
    if (await this.skipButton.isVisible()) {
      await this.skipButton.click();
      await this.page.waitForTimeout(500); // Wait for animation
    }
  }

  async completeSetup() {
    await this.completeSetupButton.click();
  }

  async waitForCompletion() {
    await this.completionMessage.waitFor({ state: 'visible', timeout: 10000 });
  }

  async getCurrentStepTitle(): Promise<string> {
    const titleElement = this.page.locator('h2, h3').first();
    return await titleElement.textContent() || '';
  }

  async isOnStep(stepTitle: string): Promise<boolean> {
    const currentTitle = await this.getCurrentStepTitle();
    return currentTitle.toLowerCase().includes(stepTitle.toLowerCase());
  }

  // =============== New Methods for Optimized 7-Step Flow ===============

  // Profile Assistant Widget Methods
  async isAssistantVisible(): Promise<boolean> {
    return await this.profileAssistant.isVisible();
  }

  async minimizeAssistant() {
    if (await this.assistantMinimizeButton.isVisible()) {
      await this.assistantMinimizeButton.click();
    }
  }

  async expandAssistant() {
    if (await this.assistantExpandButton.isVisible()) {
      await this.assistantExpandButton.click();
    }
  }

  async getAssistantMessage(): Promise<string> {
    return await this.assistantMessage.textContent() || '';
  }

  async getMandatoryProgress(): Promise<string> {
    return await this.mandatoryProgressRing.getAttribute('data-progress') || '0';
  }

  async getOptionalProgress(): Promise<string> {
    return await this.optionalProgressRing.getAttribute('data-progress') || '0';
  }

  async getOverallProgress(): Promise<string> {
    return await this.overallProgressRing.getAttribute('data-progress') || '0';
  }

  async getMandatoryFieldsList(): Promise<string[]> {
    const fields = await this.mandatoryFieldsList.locator('[data-field-name]').all();
    return Promise.all(fields.map(f => f.textContent() || ''));
  }

  // Collapsible Section Methods
  async expandSection(sectionName: string) {
    const section = this.page.locator(`details:has-text("${sectionName}"), [data-testid="${sectionName.toLowerCase().replace(/\s+/g, '-')}"]`);
    if (await section.isVisible() && !(await section.getAttribute('open'))) {
      await section.locator('summary').click();
    }
  }

  async collapseSection(sectionName: string) {
    const section = this.page.locator(`details:has-text("${sectionName}"), [data-testid="${sectionName.toLowerCase().replace(/\s+/g, '-')}"]`);
    if (await section.isVisible() && await section.getAttribute('open')) {
      await section.locator('summary').click();
    }
  }

  async isSectionExpanded(sectionName: string): Promise<boolean> {
    const section = this.page.locator(`details:has-text("${sectionName}"), [data-testid="${sectionName.toLowerCase().replace(/\s+/g, '-')}"]`);
    return (await section.getAttribute('open')) === '';
  }

  // Mandatory Field Validation Methods
  async isMandatoryFieldValid(fieldName: string): Promise<boolean> {
    const fieldStatus = this.page.locator(`[data-field="${fieldName}"] .field-status, [data-testid="${fieldName}-status"]`);
    const isValid = await fieldStatus.getAttribute('data-valid');
    return isValid === 'true';
  }

  async getMandatoryFieldsStatus(): Promise<{ total: number; completed: number; percentage: number }> {
    const mandatoryFields = await this.mandatorySectionIndicators.all();
    const completedFields = await this.completedFieldCheckmarks.all();
    const total = mandatoryFields.length;
    const completed = completedFields.length;
    const percentage = total > 0 ? (completed / total) * 100 : 0;
    
    return { total, completed, percentage };
  }

  async canProceedToNextStep(): Promise<boolean> {
    // Check if Next button is enabled (not disabled)
    const isDisabled = await this.nextButton.isDisabled();
    return !isDisabled;
  }

  // Step 1: Smart Import Methods
  async performSmartImport(options: {
    resumePath?: string;
    useLinkedIn?: boolean;
    skip?: boolean;
  }) {
    if (options.skip) {
      await this.skipImportButton.click();
    } else if (options.resumePath) {
      await this.uploadResume(options.resumePath);
      await this.parseResumeButton.click();
      await this.page.waitForTimeout(2000); // Wait for parsing
    } else if (options.useLinkedIn) {
      await this.linkedInImportButton.click();
      // Handle LinkedIn OAuth flow if needed
    }
  }

  // Step 2: Essential Info Methods
  async fillEssentialInfo(data: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    location?: string;
  }) {
    // Fill mandatory fields
    await this.firstNameInput.fill(data.firstName);
    await this.lastNameInput.fill(data.lastName);
    await this.emailInput.fill(data.email);
    
    // Fill optional fields if provided
    if (data.phone || data.location) {
      await this.expandSection('Additional Information');
      if (data.phone) await this.phoneInput.fill(data.phone);
      if (data.location) await this.locationInput.fill(data.location);
    }
  }

  async validateEssentialInfo(): Promise<boolean> {
    const firstName = await this.firstNameInput.inputValue();
    const lastName = await this.lastNameInput.inputValue();
    const email = await this.emailInput.inputValue();
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    return !!(firstName && lastName && email && emailRegex.test(email));
  }

  // Step 3: Professional Profile Methods
  async addSkills(skills: string[]) {
    for (const skill of skills) {
      await this.skillsInput.fill(skill);
      await this.skillsInput.press('Enter');
      await this.page.waitForTimeout(500);
    }
  }

  async removeSkill(skillName: string) {
    const skillTag = this.page.locator(`[data-testid="skill-tag"]:has-text("${skillName}")`);
    const removeButton = skillTag.locator('[data-testid="remove-skill"]');
    await removeButton.click();
  }

  async getSkillCount(): Promise<number> {
    const skills = await this.skillTags.all();
    return skills.length;
  }

  async fillOptionalProfessionalInfo(options: {
    summary?: string;
    experience?: Array<{
      jobTitle: string;
      companyName: string;
      startDate: string;
      endDate?: string;
      currentlyWorking?: boolean;
    }>;
    education?: Array<{
      schoolName: string;
      degree: string;
      fieldOfStudy: string;
      startDate: string;
      endDate?: string;
    }>;
  }) {
    if (options.summary) {
      await this.expandSection('Professional Summary');
      await this.summaryTextarea.fill(options.summary);
    }
    
    if (options.experience) {
      await this.expandSection('Work Experience');
      for (const exp of options.experience) {
        await this.addExperience(exp);
      }
    }
    
    if (options.education) {
      await this.expandSection('Education');
      for (const edu of options.education) {
        await this.addEducationButton.click();
        await this.schoolNameInput.fill(edu.schoolName);
        await this.degreeInput.fill(edu.degree);
        await this.fieldOfStudyInput.fill(edu.fieldOfStudy);
        await this.educationStartDateInput.fill(edu.startDate);
        if (edu.endDate) await this.educationEndDateInput.fill(edu.endDate);
      }
    }
  }

  // Step 4: Job Preferences Methods
  async fillAllMandatoryPreferences(data: {
    jobTypes: string[];
    locations: string[];
    remotePreference: 'Remote' | 'Hybrid' | 'On-site' | 'Flexible';
    salary: {
      currency: string;
      period: 'yearly' | 'monthly' | 'hourly';
      min: number;
      max: number;
    };
    industries?: string[];
  }) {
    // Job Types - Required
    for (const type of data.jobTypes) {
      await this.page.locator(`input[type="checkbox"][value="${type}"]`).check();
    }
    
    // Locations - Required
    for (const location of data.locations) {
      await this.locationPreferencesInput.fill(location);
      await this.locationPreferencesInput.press('Enter');
      await this.page.waitForTimeout(500);
    }
    
    // Remote Preference - Required
    await this.page.locator(`input[type="radio"][value="${data.remotePreference}"]`).check();
    
    // Salary Range - All fields required
    await this.salaryCurrencySelect.selectOption(data.salary.currency);
    await this.salaryPeriodSelect.selectOption(data.salary.period);
    await this.minSalaryInput.fill(data.salary.min.toString());
    await this.maxSalaryInput.fill(data.salary.max.toString());
    
    // Industries - Optional
    if (data.industries) {
      await this.expandSection('Industries');
      // Implement multi-select logic for industries
    }
  }

  async validateAllPreferences(): Promise<boolean> {
    const jobTypes = await this.jobTypesCheckboxes.filter({ hasText: ':checked' }).count();
    const locations = await this.locationTags.count();
    const remoteSelected = await this.remotePreferenceRadios.filter({ hasText: ':checked' }).count();
    const minSalary = await this.minSalaryInput.inputValue();
    const maxSalary = await this.maxSalaryInput.inputValue();
    const currency = await this.salaryCurrencySelect.inputValue();
    const period = await this.salaryPeriodSelect.inputValue();
    
    return !!(jobTypes > 0 && locations > 0 && remoteSelected > 0 && 
             minSalary && maxSalary && currency && period);
  }

  // Step 5: Additional Details Methods (All Optional)
  async fillAdditionalDetails(options: {
    values?: string[];
    immediatelyAvailable?: boolean;
    noticePeriod?: string;
    portfolioUrl?: string;
    githubUrl?: string;
  }) {
    if (options.values) {
      await this.expandSection('Values & Culture');
      await this.selectValues(options.values);
    }
    
    if (options.immediatelyAvailable !== undefined) {
      await this.expandSection('Availability');
      if (options.immediatelyAvailable) {
        await this.immediatelyAvailableCheckbox.check();
      } else if (options.noticePeriod) {
        await this.noticePeriodInput.fill(options.noticePeriod);
      }
    }
    
    if (options.portfolioUrl || options.githubUrl) {
      await this.expandSection('Portfolio Links');
      if (options.portfolioUrl) await this.portfolioUrlInput.fill(options.portfolioUrl);
      if (options.githubUrl) await this.githubUrlInput.fill(options.githubUrl);
    }
  }

  // Step 6: Verification & Media Methods
  async addVerification(options: {
    video?: { record?: boolean; uploadPath?: string };
    idPath?: string;
  }) {
    if (options.video) {
      if (options.video.record) {
        await this.recordVideoButton.click();
        // Handle video recording flow
      } else if (options.video.uploadPath) {
        await this.uploadVideoButton.click();
        // Handle video upload
      }
    }
    
    if (options.idPath) {
      await this.idFileInput.setInputFiles(options.idPath);
      await this.page.waitForTimeout(2000); // Wait for upload
    }
  }

  async getVerificationStatus(): Promise<{
    hasVideo: boolean;
    hasId: boolean;
    trustScore: number;
  }> {
    const hasVideo = await this.videoPreview.isVisible();
    const hasId = await this.idVerificationStatus.isVisible();
    const trustBadge = await this.trustBadgeIndicator.getAttribute('data-trust-score');
    
    return {
      hasVideo,
      hasId,
      trustScore: parseInt(trustBadge || '0')
    };
  }

  // Step 7: Privacy & Launch Methods
  async configurePrivacyAndLaunch(settings: {
    visibility?: 'public' | 'private' | 'employers-only';
    allowMessages?: boolean;
    showSalary?: boolean;
    showContact?: boolean;
    preview?: boolean;
  }) {
    await this.setPrivacySettings(settings);
    
    if (settings.preview) {
      await this.profilePreviewButton.click();
      await this.page.waitForTimeout(2000); // Wait for preview to load
    }
  }

  async getCompletionStatus(): Promise<{
    mandatoryComplete: boolean;
    optionalComplete: boolean;
    overallPercentage: number;
    canLaunch: boolean;
  }> {
    const mandatoryStatus = await this.mandatoryFieldsStatus.textContent();
    const optionalStatus = await this.optionalFieldsStatus.textContent();
    const overallProgress = await this.getOverallProgress();
    const canLaunch = await this.completeSetupButton.isEnabled();
    
    return {
      mandatoryComplete: mandatoryStatus?.includes('Complete') || false,
      optionalComplete: optionalStatus?.includes('Complete') || false,
      overallPercentage: parseInt(overallProgress),
      canLaunch
    };
  }

  // Complete optimized flow helper
  async completeOptimizedOnboarding(data: {
    // Step 1 - Optional
    skipImport?: boolean;
    resumePath?: string;
    // Step 2 - Mandatory
    firstName: string;
    lastName: string;
    email: string;
    // Step 3 - Mandatory skill
    skills: string[];
    // Step 4 - All mandatory
    jobTypes: string[];
    locations: string[];
    remotePreference: 'Remote' | 'Hybrid' | 'On-site' | 'Flexible';
    salary: {
      currency: string;
      period: 'yearly' | 'monthly' | 'hourly';
      min: number;
      max: number;
    };
    // Optional steps
    skipOptional?: boolean;
  }) {
    // Step 1: Smart Import
    await this.performSmartImport({
      skip: data.skipImport,
      resumePath: data.resumePath
    });
    await this.goToNextStep();
    
    // Step 2: Essential Info
    await this.fillEssentialInfo({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email
    });
    await this.goToNextStep();
    
    // Step 3: Professional Profile
    await this.addSkills(data.skills);
    await this.goToNextStep();
    
    // Step 4: Job Preferences
    await this.fillAllMandatoryPreferences({
      jobTypes: data.jobTypes,
      locations: data.locations,
      remotePreference: data.remotePreference,
      salary: data.salary
    });
    await this.goToNextStep();
    
    // Steps 5-6: Skip optional if requested
    if (data.skipOptional) {
      await this.skipStep();
      await this.skipStep();
    } else {
      await this.goToNextStep();
      await this.goToNextStep();
    }
    
    // Step 7: Privacy & Launch
    await this.configurePrivacyAndLaunch({
      visibility: 'public'
    });
    await this.completeSetup();
  }
}