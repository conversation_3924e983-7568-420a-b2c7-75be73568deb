# Database Migration Strategy & Workflow

This document outlines the comprehensive migration strategy for the Headstart backend application using TypeORM with PostgreSQL.

## 🎯 Migration Philosophy

**From now on, ALL database schema changes MUST go through migrations. No more synchronize: true!**

- ✅ **Migrations are mandatory** for any new fields, tables, or schema changes
- ✅ **Migrations run automatically** during deployment
- ✅ **Robust migrations** that can be run multiple times safely
- ✅ **Backup and rollback** capabilities for production safety

## 🏗️ Architecture Overview

### Migration Configuration
- **Development**: Uses TypeScript files in `src/migrations/`
- **Production**: Uses compiled JavaScript files in `dist/migrations/`
- **Configuration**: `src/config/migration.config.ts` (environment-aware)
- **Helpers**: `src/shared/utils/migration-helpers.ts` (robust utilities)

### Deployment Integration
- **Docker**: Migrations run automatically in `docker-entrypoint.sh`
- **Environment Variable**: Set `SKIP_MIGRATIONS=true` to skip (emergency only)
- **Backup**: Automatic database backup before migrations (unless `SKIP_MIGRATION_BACKUP=true`)

## 🚀 Quick Start

### Creating a New Migration

```bash
# 1. Create a new robust migration with built-in helper functions
pnpm migration:create:robust YourMigrationName

# 2. Edit the migration file in src/migrations/timestamp-YourMigrationName.ts
#    The template already includes imports for helper functions

# 3. Test the migration locally
pnpm migration:run

# 4. Verify migration status
pnpm migration:status
```

### Alternative: Standard TypeORM Commands

```bash
# Generate migration from entity changes (auto-detect)
pnpm migration:generate src/migrations/YourMigrationName

# Create empty migration file
pnpm migration:create src/migrations/YourMigrationName

# Run pending migrations
pnpm migration:run

# Revert last migration
pnpm migration:revert

# Show migration status
pnpm migration:show
```

## 📋 Available Scripts

### Development Scripts
```bash
pnpm migration:create:robust <name>    # Create robust migration with helpers
pnpm migration:generate <name>         # Auto-generate from entity changes
pnpm migration:create <name>           # Create empty migration
pnpm migration:run                     # Run pending migrations
pnpm migration:revert                  # Revert last migration
pnpm migration:show                    # Show migration status
pnpm migration:test                    # Test migrations safely
pnpm migrate:safe                      # Run migrations with backup
```

### Production Scripts
```bash
pnpm migration:run:prod               # Run migrations in production
pnpm migration:deploy                 # Deploy migrations (used by Docker)
pnpm migration:status:prod            # Check production migration status
pnpm migrate:safe:prod                # Safe production migration with backup
```

## 🔧 Migration Helpers

The project includes robust helper functions in `src/shared/utils/migration-helpers.ts`:

### Checking Functions
```typescript
await tableExists(queryRunner, 'table_name')
await columnExists(queryRunner, 'table_name', 'column_name')
await enumTypeExists(queryRunner, 'enum_type_name')
await indexExists(queryRunner, 'table_name', 'index_name')
await constraintExists(queryRunner, 'table_name', 'constraint_name')
```

### Creation Functions
```typescript
await createTableIfNotExists(queryRunner, 'table_name', createTableSql)
await addColumnIfNotExists(queryRunner, 'table_name', 'column_name', 'column_definition')
await createEnumTypeIfNotExists(queryRunner, 'enum_name', ['value1', 'value2'])
await createIndexIfNotExists(queryRunner, 'table_name', 'index_name', ['column1', 'column2'])
await addForeignKeyIfNotExists(queryRunner, 'table_name', 'constraint_name', ...)
```

## 🚀 Deployment Process

### Automatic Deployment
Migrations run automatically during Docker deployment:

1. **Container starts** → `docker-entrypoint.sh`
2. **Redis starts** → Database connection ready
3. **Migrations run** → `deploy-migrations.js`
4. **Application starts** → Ready to serve

### Environment Variables
```bash
# Skip migrations (emergency only)
SKIP_MIGRATIONS=true

# Skip backup creation
SKIP_MIGRATION_BACKUP=true

# Migration timeout (default: 5 minutes)
MIGRATION_TIMEOUT=300000

# Max retry attempts
MIGRATION_MAX_RETRIES=3
```

## 📝 Migration Best Practices

### 1. Always Use Robust Migrations
```typescript
// ✅ Good - Uses helper functions
await addColumnIfNotExists(queryRunner, 'users', 'email', 'varchar(255) UNIQUE');

// ❌ Bad - Will fail if column exists
await queryRunner.query('ALTER TABLE users ADD COLUMN email varchar(255) UNIQUE');
```

### 2. Handle Rollbacks Properly
```typescript
export class AddUserEmail1234567890 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await addColumnIfNotExists(queryRunner, 'users', 'email', 'varchar(255)');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    if (await columnExists(queryRunner, 'users', 'email')) {
      await queryRunner.query('ALTER TABLE users DROP COLUMN email');
    }
  }
}
```

### 3. Test Migrations Locally
```bash
# Test migration
pnpm migration:run

# If something goes wrong, revert
pnpm migration:revert

# Check status
pnpm migration:show
```

### 4. Use Descriptive Names
```bash
# ✅ Good
pnpm migration:create:robust AddEmailToUsers
pnpm migration:create:robust CreateJobApplicationsTable
pnpm migration:create:robust AddIndexToUserEmail

# ❌ Bad
pnpm migration:create:robust Update1
pnpm migration:create:robust Fix
```

## 🆘 Emergency Procedures

### Skip Migrations During Deployment
```bash
# Set environment variable to skip migrations
SKIP_MIGRATIONS=true
```

### Manual Migration Rollback
```bash
# Connect to production database
# Run rollback migration manually
pnpm migration:revert

# Or restore from backup
# Use backup file created before migration
```

### Check Migration Status
```bash
# Development
pnpm migration:show

# Production
pnpm migration:status:prod
```

## 🔍 Troubleshooting

### Common Issues

1. **Migration fails during deployment**
   - Check logs for specific error
   - Backup is automatically created
   - Set `SKIP_MIGRATIONS=true` to bypass temporarily

2. **Entity changes not reflected**
   - Ensure you created a migration for the changes
   - Run `pnpm migration:generate` to auto-detect changes

3. **Migration runs multiple times**
   - Use helper functions to make migrations idempotent
   - Check migration table for duplicates

### Debug Commands
```bash
# Check what migrations are pending
pnpm migration:show

# Test migration without applying
pnpm migration:test

# Run with detailed logging
NODE_ENV=development pnpm migration:run
```

## 📊 Migration Monitoring

### Logs to Monitor
- Migration start/completion times
- Number of migrations applied
- Any warnings or errors
- Backup creation status

### Health Checks
- Verify application starts after migrations
- Check database schema matches entities
- Monitor application performance post-migration

---

## 🎯 Summary

This migration strategy ensures:
- **Zero-downtime deployments** with automatic migrations
- **Safe rollback capabilities** with automatic backups
- **Robust migrations** that can run multiple times
- **Clear workflow** for development and production
- **Emergency procedures** for critical situations

**Remember**: All schema changes must go through migrations from now on!

```bash
# Create a basic migration
pnpm migration:create

# Generate a migration based on entity changes
pnpm migration:generate src/migrations/YourMigrationName
```

## Robust Migration Approach

Our migration strategy follows a "check before change" approach:

1. **Check if objects exist** before creating them
2. **Skip operations** for objects that already exist
3. **Use helper functions** to simplify common migration tasks
4. **Include proper down migrations** that check before dropping

This approach ensures that:

- Migrations can be run multiple times without errors
- Deployments to different environments work consistently
- Changes are applied only when needed

## Migration Commands

### Generate a Migration

To generate a migration based on entity changes:

```bash
pnpm migration:generate src/migrations/MigrationName
```

This will create a new migration file in the `src/migrations` directory with the changes detected between your entity models and the current database schema.

### Create an Empty Migration

To create an empty migration file:

```bash
pnpm migration:create src/migrations/YourMigrationName
```

This will create a new empty migration file in the `src/migrations` directory that you can fill with custom SQL or TypeORM query builder commands.

### Run Migrations

To run all pending migrations:

```bash
pnpm migration:run
```

This will apply all pending migrations to the database.

### Show Migration Status

To see the status of all migrations:

```bash
pnpm migration:show
```

This will display a list of all migrations and whether they have been applied.

### Revert the Latest Migration

To revert the most recently applied migration:

```bash
pnpm migration:revert
```

This will undo the changes made by the most recent migration.

## Safe Migration Process

For production environments, we use a safe migration process that:

1. Creates a backup of the database before running migrations
2. Runs all pending migrations
3. Provides detailed logs of the migration process

To run migrations safely:

```bash
# For development
pnpm migrate:safe

# For production
pnpm migrate:safe:prod
```

## Migration Best Practices

1. **Always test migrations locally** before applying them to production.
2. **Keep migrations small and focused** on specific changes to make them easier to understand and revert if necessary.
3. **Include both `up` and `down` methods** in your migrations to allow for reverting changes.
4. **Use explicit SQL statements** when possible to ensure database compatibility.
5. **Add comments** to explain complex migrations.
6. **Avoid using `synchronize: true`** in production as it can lead to data loss.
7. **Define enums directly in the migration file** rather than importing them to avoid dependency issues.
8. **Use IF EXISTS/IF NOT EXISTS clauses** in your SQL to make migrations more robust.

## Writing Migrations

We've provided several resources to help you write robust migrations:

1. **Template Files**:
   - `src/migrations/1720000003000-MigrationTemplate.ts` - Basic migration template
   - `src/migrations/1720000004000-RobustMigrationTemplate.ts` - Comprehensive template with existence checks
   - `src/migrations/1720000005000-ExampleMigrationWithHelpers.ts` - Example using helper functions

2. **Helper Functions**:
   - Located in `src/shared/utils/migration-helpers.ts`
   - Simplify common migration tasks with built-in existence checks
   - Make migrations more readable and maintainable

### Using Helper Functions

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';
import {
  createTableIfNotExists,
  addColumnIfNotExists,
  createEnumTypeIfNotExists,
  // ... other helpers
} from '../shared/utils/migration-helpers';

export class YourMigration1234567890123 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create a table if it doesn't exist
    await createTableIfNotExists(
      queryRunner,
      'your_table',
      `CREATE TABLE "your_table" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "name" varchar(255) NOT NULL,
        CONSTRAINT "PK_your_table" PRIMARY KEY ("id")
      )`
    );

    // Add a column if it doesn't exist
    await addColumnIfNotExists(
      queryRunner,
      'your_table',
      'description',
      'text'
    );

    // Create an enum type if it doesn't exist
    await createEnumTypeIfNotExists(
      queryRunner,
      'status_enum',
      ['active', 'inactive', 'pending']
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Down migrations are also simplified with helper functions
    // ...
  }
}
```

### Example: Adding a Column

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserEmailColumn1234567890123 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the column already exists
    const columnExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'email'
      );
    `);

    if (!columnExists[0].exists) {
      await queryRunner.query(`
        ALTER TABLE "users"
        ADD COLUMN "email" varchar(255);
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users"
      DROP COLUMN IF EXISTS "email";
    `);
  }
}
```

### Example: Creating a Table

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductsTable1234567890123 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the table already exists
    const tableExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'products'
      );
    `);

    if (!tableExists[0].exists) {
      await queryRunner.query(`
        CREATE TABLE "products" (
          "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
          "name" character varying NOT NULL,
          "price" numeric(10,2) NOT NULL,
          "created_at" TIMESTAMP NOT NULL DEFAULT now(),
          "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
          CONSTRAINT "PK_products" PRIMARY KEY ("id")
        )
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS "products"`);
  }
}
```

### Example: Adding an Enum Type

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserRoleEnum1234567890123 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the enum type already exists
    const typeExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT 1 FROM pg_type
        WHERE typname = 'user_role_enum'
      );
    `);

    if (!typeExists[0].exists) {
      await queryRunner.query(`
        CREATE TYPE "user_role_enum" AS ENUM ('admin', 'user', 'guest')
      `);
    }

    // Check if the column already exists
    const columnExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'role'
      );
    `);

    if (!columnExists[0].exists) {
      await queryRunner.query(`
        ALTER TABLE "users"
        ADD COLUMN "role" "user_role_enum" NOT NULL DEFAULT 'user'
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "users"
      DROP COLUMN IF EXISTS "role"
    `);

    await queryRunner.query(`DROP TYPE IF EXISTS "user_role_enum"`);
  }
}
```

## Troubleshooting

### Migration Fails to Apply

If a migration fails to apply, you can:

1. Check the error message for details about what went wrong.
2. Fix the issue in your migration file.
3. If you've already applied a partial migration, you may need to manually fix the database state.
4. Use `pnpm migration:show` to see the current migration status.

### Path Resolution Issues

If you encounter path resolution issues with imports in your migration files:

- Define any enums or interfaces directly in the migration file
- Use relative imports instead of path aliases
- Avoid importing from entity files if possible

### Restoring from Backup

If you need to restore from a backup:

```bash
pg_restore -d your_database_name /path/to/backup/db-backup-TIMESTAMP.sql
```

Backups are automatically created before running migrations with the `migrate:safe` command and are stored in the `/backup` directory.

## Deployment Process

During deployment, migrations are automatically run as part of the deployment process:

```bash
pnpm deploy
```

This command builds the application, runs migrations safely, and starts the application.
