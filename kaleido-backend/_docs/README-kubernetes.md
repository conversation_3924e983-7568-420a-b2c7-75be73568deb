# Kubernetes Deployment Guide

This guide explains how to deploy the Headstart Backend application to Kubernetes.

## Prerequisites

- Docker installed on your local machine
- `kubectl` installed on your local machine
- `doctl` (DigitalOcean CLI) installed on your local machine
- Access to the DigitalOcean Kubernetes cluster
- Access to the DigitalOcean Container Registry

## Deployment Options

You have three options to deploy the application:

1. **GitHub Actions (Recommended)**: Automatically build and deploy when you push to the main branch
2. **Local Script**: Use the provided script to build and deploy from your local machine
3. **Manual Deployment**: Step-by-step manual deployment

## Option 1: GitHub Actions Deployment

The repository is configured with GitHub Actions to automatically build and deploy the application when you push to the main branch.

1. Make your changes and push to the main branch:

```bash
git add .
git commit -m "Your commit message"
git push origin main
```

2. The GitHub Actions workflow will automatically:
   - Build the Docker image
   - Push it to the DigitalOcean Container Registry
   - Deploy it to the Kubernetes cluster

3. You can monitor the deployment in the "Actions" tab of the GitHub repository.

## Option 2: Local Script Deployment

We've provided scripts to build and deploy the application from your local machine.

1. Make sure you're logged in to DigitalOcean:

```bash
doctl auth init
```

2. Build and push the Docker image:

```bash
./scripts/build-and-push-image.sh
```

3. Deploy to Kubernetes:

```bash
./scripts/deploy-to-kubernetes.sh
```

4. The scripts will:
   - Build the Docker image
   - Push it to the DigitalOcean Container Registry
   - Deploy it to the Kubernetes cluster

## Option 3: Manual Deployment

If you prefer to deploy manually, follow these steps:

1. Connect to the Kubernetes cluster:

```bash
doctl kubernetes cluster kubeconfig save 0baeed35-fd17-4b15-92cd-73675a74569c
```

2. Build the Docker image:

```bash
docker build -t registry.digitalocean.com/kaleido/kaleido-backend:latest .
```

3. Push the Docker image to the registry:

```bash
# Log in to the registry
doctl registry login

# Push the image
docker push registry.digitalocean.com/kaleido/kaleido-backend:latest
```

4. Apply the Kubernetes configuration:

```bash
# Apply the deployment
kubectl apply -f k8s/deployment.yaml

# Apply the service
kubectl apply -f k8s/service.yaml

# Apply the ingress
kubectl apply -f k8s/ingress.yaml
```

5. Wait for the deployment to be ready:

```bash
kubectl rollout status deployment/kaleido-backend
```

## Monitoring and Troubleshooting

### Check Pod Status

```bash
kubectl get pods -l app=kaleido-backend
```

### View Pod Logs

```bash
# Get the pod name
POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")

# View logs
kubectl logs $POD_NAME
```

### Check Service Status

```bash
kubectl get service kaleido-backend
```

### Check Ingress Status

```bash
kubectl get ingress kaleido-backend-ingress
```

### Restart the Deployment

```bash
kubectl rollout restart deployment/kaleido-backend
```

## Accessing the Application

The application is accessible at:

- API: https://api.kaleidotalent.com
- Swagger Documentation: https://api.kaleidotalent.com/api/docs

## Configuration

The application is configured using environment variables stored in Kubernetes secrets. To update the configuration:

1. Update the secrets:

```bash
kubectl edit secret kaleido-backend-secrets
```

2. Restart the deployment:

```bash
kubectl rollout restart deployment/kaleido-backend
```

## Redis Configuration

The application uses an internal Redis instance running in the same container. Redis is configured with:

- Host: localhost
- Port: 6379
- Password: From the REDIS_PASSWORD environment variable

## Database Migrations

Database migrations are automatically run when the container starts. If you need to run migrations manually:

```bash
# Get the pod name
POD_NAME=$(kubectl get pods -l app=kaleido-backend -o jsonpath="{.items[0].metadata.name}")

# Run migrations
kubectl exec $POD_NAME -- pnpm migration:run:prod
```
