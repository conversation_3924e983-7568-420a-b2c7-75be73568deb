# Headstart Backend Deployment Guide

This guide provides comprehensive instructions for deploying the Headstart Backend application using Docker and Kubernetes on DigitalOcean.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Docker Setup](#docker-setup)
- [Kubernetes Setup](#kubernetes-setup)
- [Redis Setup Options](#redis-setup-options)
- [Deployment Process](#deployment-process)
- [Troubleshooting](#troubleshooting)
- [Maintenance](#maintenance)

## Prerequisites

- Docker installed on your local machine
- `doctl` (DigitalOcean CLI) installed
- `kubectl` installed
- Access to a DigitalOcean account with permissions to create Kubernetes clusters
- DigitalOcean Container Registry access

## Docker Setup

### Building the Docker Image

1. Build the Docker image using the production Dockerfile:

```bash
docker build -t registry.digitalocean.com/kaleido/kaleido-backend:latest -f Dockerfile.prod .
```

2. Log in to DigitalOcean Container Registry:

```bash
doctl auth init  # If you haven't authenticated yet
doctl registry login
```

3. Push the image to the registry:

```bash
docker push registry.digitalocean.com/kaleido/kaleido-backend:latest
```

## Kubernetes Setup

### Connecting to Your Kubernetes Cluster

1. Connect to your DigitalOcean Kubernetes cluster:

```bash
doctl kubernetes cluster kubeconfig save <cluster-id>
```

Replace `<cluster-id>` with your actual cluster ID (e.g., `0baeed35-fd17-4b15-92cd-73675a74569c`).

2. Verify the connection:

```bash
kubectl get nodes
```

### Setting Up Registry Access

Create a Kubernetes secret for pulling images from your private registry:

```bash
kubectl create secret docker-registry do-registry-secret \
  --docker-server=registry.digitalocean.com \
  --docker-username=$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.username') \
  --docker-password=$(doctl registry docker-config --read-write | jq -r '.auths | to_entries | .[0].value.password') \
  --docker-email=<EMAIL>
```

## Redis Setup Options

### Option 1: Redis as a Kubernetes StatefulSet

Deploy Redis as a StatefulSet with persistent storage:

```bash
kubectl apply -f k8s/redis-deployment.yaml
kubectl apply -f k8s/redis-service.yaml
```

### Option 2: Simple Redis Deployment

For a simpler setup without persistent storage:

```bash
kubectl apply -f k8s/redis-deployment-simple.yaml
kubectl apply -f k8s/redis-service.yaml
```

### Option 3: External Redis Service

If you prefer to use an external Redis service, update the `REDIS_HOST`, `REDIS_PORT`, and `REDIS_PASSWORD` in your secrets.

## Deployment Process

### 1. Create Configuration and Secrets

Apply the ConfigMap and Secrets:

```bash
kubectl apply -f k8s/configmap.yaml
```

### 2. Deploy Redis

```bash
kubectl apply -f k8s/redis-deployment.yaml
kubectl apply -f k8s/redis-service.yaml
```

### 3. Deploy the Application

```bash
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
```

### 4. Set Up Ingress

```bash
kubectl apply -f k8s/ingress.yaml
```

### 5. Verify Deployment

Check if all components are running:

```bash
kubectl get pods
kubectl get services
kubectl get ingress
```

## Troubleshooting

### Image Pull Issues

If you encounter image pull errors:

1. Verify your registry secret:

```bash
kubectl get secret do-registry-secret
```

2. Update the deployment to use the correct image and pull secret:

```bash
kubectl patch deployment kaleido-backend -p '{"spec":{"template":{"spec":{"containers":[{"name":"kaleido-backend","image":"registry.digitalocean.com/kaleido/kaleido-backend:latest"}]}}}}'

kubectl patch deployment kaleido-backend -p '{"spec":{"template":{"spec":{"imagePullSecrets":[{"name":"do-registry-secret"}]}}}}'
```

### Redis Connection Issues

If the application can't connect to Redis:

1. Check if Redis is running:

```bash
kubectl get pods | grep redis
```

2. Test Redis connectivity:

```bash
# Get the Redis pod name
REDIS_POD=$(kubectl get pods -l app=redis -o jsonpath='{.items[0].metadata.name}')

# Check Redis is working
kubectl exec $REDIS_POD -- redis-cli -a password123 ping
```

3. Verify the Redis service:

```bash
kubectl describe service redis
```

### Application Startup Issues

If the application fails to start:

1. Check the application logs:

```bash
kubectl logs deployment/kaleido-backend
```

2. Check the pod status:

```bash
kubectl describe pod $(kubectl get pods -l app=kaleido-backend -o jsonpath='{.items[0].metadata.name}')
```

## Maintenance

### Updating the Application

To update the application with a new version:

1. Build and push a new Docker image:

```bash
docker build -t registry.digitalocean.com/kaleido/kaleido-backend:v1.0.1 -f Dockerfile.prod .
docker push registry.digitalocean.com/kaleido/kaleido-backend:v1.0.1
```

2. Update the deployment:

```bash
kubectl set image deployment/kaleido-backend kaleido-backend=registry.digitalocean.com/kaleido/kaleido-backend:v1.0.1
```

### Scaling the Application

To scale the application horizontally:

```bash
kubectl scale deployment kaleido-backend --replicas=3
```

### Backup and Restore

For Redis data backup:

```bash
# Get the Redis pod name
REDIS_POD=$(kubectl get pods -l app=redis -o jsonpath='{.items[0].metadata.name}')

# Trigger Redis backup
kubectl exec $REDIS_POD -- redis-cli -a password123 SAVE

# Copy the dump.rdb file locally
kubectl cp $REDIS_POD:/data/dump.rdb ./redis-backup-$(date +%Y%m%d).rdb
```

### Monitoring

Set up monitoring using DigitalOcean's built-in monitoring or deploy Prometheus and Grafana:

```bash
# Add the Prometheus Helm repository
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts

# Install Prometheus and Grafana
helm install prometheus prometheus-community/kube-prometheus-stack
```

## Security Considerations

- Regularly update your Docker images and Kubernetes components
- Use network policies to restrict pod-to-pod communication
- Enable audit logging for your Kubernetes cluster
- Rotate secrets regularly
- Use RBAC to limit access to your Kubernetes resources

## Additional Resources

- [DigitalOcean Kubernetes Documentation](https://docs.digitalocean.com/products/kubernetes/)
- [Kubernetes Official Documentation](https://kubernetes.io/docs/)
- [Docker Documentation](https://docs.docker.com/)
