# Pricing Model Comparison: Current vs. Proposed

## Executive Summary

This document compares our current **credit-based pricing model** with a proposed **granular feature-based pricing model** discovered in recent pricing research. Both models have distinct advantages and complexity trade-offs.

## Current Implementation: Credit-Based Model

### Structure
- **Simple Credit Currency**: 1 credit = 1 action (with some exceptions)
- **Four Tiers**: Free (250 credits), Starter ($150/5K credits), Professional ($250/15K credits), Enterprise (Unlimited)
- **Flexible Usage**: Credits can be used for any feature
- **Variable Pricing**: Video JD costs 20-50 credits based on length

### Credit Costs
| Action | Cost | Notes |
|--------|------|-------|
| Job Description | FREE | Unlimited |
| Resume Upload | 1 credit | Per resume |
| LinkedIn Scout | 1 credit | Per candidate |
| Match & Rank | 1 credit | Per operation |
| Video JD | 20-50 credits | Variable by length |
| Video Intro Questions | 1 credit | Per question set |
| Unlock Candidate Details | 5 credits | Per unlock |

### Benefits
- ✅ **Simple & Intuitive**: Easy for users to understand
- ✅ **Flexible**: Use credits however needed
- ✅ **Implemented**: Fully working system
- ✅ **Gamified**: Credits feel like currency
- ✅ **Marketing Friendly**: Clear value proposition

### Drawbacks
- ❌ **Less Precise Costing**: Not tied to actual feature costs
- ❌ **Potential Revenue Loss**: Heavy users of expensive features
- ❌ **Limited Segmentation**: Fewer ways to differentiate plans

## Proposed Model: Granular Feature-Based Pricing

### Structure
- **Cost-Plus Pricing**: Each feature priced based on overhead allocation + 70% markup
- **Hybrid Billing**: One-time, monthly, and usage-based components
- **Feature Limits**: Specific caps per feature type
- **Four Tiers**: Freemium ($0), Startup ($45), Small/Advanced ($195), Medium/Growth ($414)

### Feature Pricing Examples
| Feature | Cost Basis | User Price | Limit Structure |
|---------|------------|------------|-----------------|
| Job Description Tool | $0.11 | One-time | Unlimited |
| Sourcing Tool | $0.14 | Monthly | 0→20→100→250 |
| Video JD (5min) | $3.39 | Per use | 0→5min→15min→30min |
| Data Package (100 CVs) | $16.10 | Monthly | Tiered packages |
| Match & Rank | $0.16 | Per CV | 0→100→500→1000 |

### Benefits
- ✅ **Precise Cost Attribution**: Based on actual overhead calculations
- ✅ **Higher Revenue Potential**: More sophisticated pricing tiers
- ✅ **Better Segmentation**: Feature gates align with customer needs
- ✅ **Prevents Abuse**: Limits on expensive features
- ✅ **Scalable**: Usage-based components grow with customers

### Drawbacks
- ❌ **Complex Implementation**: Multiple billing mechanisms
- ❌ **User Confusion**: Harder to understand pricing
- ❌ **Technical Debt**: Requires significant re-architecture
- ❌ **Support Overhead**: More complex billing questions
- ❌ **Slower Iteration**: Harder to adjust pricing

## Complexity Analysis

### Current Model Complexity: 3/10
- Single credit currency system
- Simple deduction logic
- Straightforward billing integration
- Easy customer communication

### Proposed Model Complexity: 8/10
- Multiple pricing mechanisms (one-time, monthly, usage-based)
- Feature-specific limit tracking and enforcement
- Complex overhead allocation calculations
- Multiple subscription matrices
- Advanced billing reconciliation requirements

## Flow Diagrams

### Current Credit-Based Flow
```mermaid
flowchart TD
    A[User Wants to Perform Action] --> B{Check Credit Balance}
    B -->|Sufficient Credits| C[Perform Action]
    B -->|Insufficient Credits| D[Show Upgrade/Purchase Modal]
    C --> E[Deduct Credits]
    E --> F[Update Balance]
    D --> G{User Choice}
    G -->|Upgrade Plan| H[Process Subscription Change]
    G -->|Buy Credits| I[Process One-time Purchase]
    G -->|Cancel| J[Action Blocked]
    H --> K[Add Monthly Credits]
    I --> L[Add Purchased Credits]
    K --> C
    L --> C
```

### Proposed Feature-Based Flow
```mermaid
flowchart TD
    A[User Wants to Perform Action] --> B{Identify Feature Type}
    B -->|One-time Feature| C{Already Purchased?}
    B -->|Monthly Feature| D{Within Monthly Limit?}
    B -->|Usage-based Feature| E{Calculate Cost}

    C -->|Yes| F[Allow Action]
    C -->|No| G[Charge One-time Fee]

    D -->|Yes| F
    D -->|No| H[Block or Charge Overage]

    E --> I{Check Payment Method}
    I -->|Subscription Credits| J{Sufficient Credits?}
    I -->|Pay-per-use| K[Charge Per Use]

    J -->|Yes| F
    J -->|No| L[Show Upgrade Options]

    G --> M{Payment Success?}
    K --> M
    M -->|Yes| F
    M -->|No| N[Action Blocked]

    F --> O[Perform Action]
    O --> P[Update Usage Counters]
    P --> Q[Update Billing Records]
```

## Recommendation: Stick with Credit-Based Model

### Primary Reasons

1. **✅ Proven Implementation**: Current system is fully functional and battle-tested
2. **📈 Revenue Optimization Opportunity**: Can adjust credit costs and plan prices without architectural changes
3. **🎯 User Experience**: Credits are intuitive and provide flexibility
4. **⚡ Agility**: Easy to experiment with pricing changes
5. **🔧 Technical Simplicity**: Lower maintenance overhead and technical debt

### Potential Optimizations for Current Model

#### Short-term Improvements (Low Effort, High Impact)
- **Adjust Plan Pricing**: Consider increasing to $195-$414 range based on market research
- **Optimize Credit Costs**: Fine-tune expensive features (Video JD, Data operations)
- **Add Usage Analytics**: Better tracking of credit consumption patterns
- **Introduce Credit Bundles**: Larger one-time purchase options

#### Medium-term Enhancements (Moderate Effort)
- **Feature-Specific Caps**: Add monthly limits within credit plans (e.g., "max 10 Video JDs/month")
- **Data Add-on Packages**: Separate products for bulk data access
- **Smart Final Summary**: Suggest plan upgrades based on usage patterns
- **Credit Rollover**: Premium feature for unused credits

#### Long-term Considerations (High Effort)
- **Hybrid Model**: Combine credits with some feature-specific limits
- **Enterprise Custom Pricing**: Tailored packages for large customers
- **API Rate Limiting**: Credit-based API access controls

## Implementation Effort Comparison

### Current Model Optimizations
| Change | Effort | Impact | Timeline |
|--------|--------|--------|----------|
| Price Adjustments | Low | High | 1-2 weeks |
| Credit Cost Tuning | Low | Medium | 1 week |
| Usage Analytics | Medium | High | 4-6 weeks |
| Feature Caps | Medium | Medium | 6-8 weeks |

### Proposed Model Implementation
| Component | Effort | Risk | Timeline |
|-----------|--------|------|----------|
| Feature Limit Engine | High | High | 12-16 weeks |
| Multi-billing System | High | High | 8-12 weeks |
| Usage Tracking Rebuild | High | Medium | 6-8 weeks |
| Frontend Redesign | High | Medium | 8-10 weeks |
| **Total Estimated** | **Very High** | **High** | **6-8 months** |

## Financial Impact Analysis

### Current Model Revenue Potential
- **Immediate**: Increase plan prices by 30-65% based on market research
- **Short-term**: Optimize credit costs for 10-20% revenue increase
- **Medium-term**: Add-on packages for additional 15-25% revenue

### Proposed Model Revenue Potential
- **Higher ceiling**: More sophisticated pricing could increase revenue 50-100%
- **Risk factor**: Complex pricing might reduce conversion rates
- **Implementation cost**: 6-8 months of development resources

## Conclusion

The **credit-based model should be maintained and optimized** rather than replaced. The proposed granular model, while sophisticated, introduces significant complexity that outweighs its benefits given our current stage and resources.

### Recommended Action Plan

1. **Phase 1 (Month 1-2)**: Adjust current plan pricing based on market research
2. **Phase 2 (Month 2-4)**: Implement usage analytics and credit optimization
3. **Phase 3 (Month 4-8)**: Add selective feature caps and data packages
4. **Phase 4 (Month 8+)**: Evaluate hybrid approach based on data and customer feedback

This approach allows us to capture most of the revenue benefits while maintaining our competitive advantage in simplicity and user experience.
