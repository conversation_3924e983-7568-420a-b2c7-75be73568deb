# Redis Docker Setup Guide

This guide explains how to use Redis with Dock<PERSON> in the Headstart backend application.

## Overview

The application now supports two Redis deployment options:
1. **External Redis** - Using a separate Redis container (default in development)
2. **Internal Redis** - Running Redis inside the application container (recommended for production)

## Internal Redis Setup (Recommended for Production)

The internal Redis setup runs Redis directly inside the application container, eliminating the need for an external Redis service. This is the recommended approach for production environments like DigitalOcean App Platform.

### How It Works

1. Redis is installed in the Docker container during the build process
2. Redis is started automatically when the container starts
3. The application connects to Redis on localhost (127.0.0.1)
4. Redis data is persisted in a Docker volume

### Using Internal Redis

To use the internal Redis setup:

```bash
# Build and run with internal Redis
pnpm docker:build:prod
pnpm docker:run:prod

# Or using docker-compose
pnpm docker:compose:internal-redis
```

### Environment Variables

When using internal Redis, set these environment variables:

```
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password_here
```

## External Redis Setup (Default for Development)

The external Redis setup uses a separate Redis container, which is the default setup for development environments.

### Using External Redis

```bash
# Start the application with external Redis
pnpm docker:compose
```

### Environment Variables

When using external Redis, set these environment variables:

```
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_password_here
```

## DigitalOcean App Platform Deployment

For DigitalOcean App Platform, the internal Redis setup is recommended:

1. Use the `Dockerfile.prod` file for your app
2. Set the environment variables as described above
3. No need to provision a separate Redis service

## Troubleshooting

### Checking Redis Status

To check Redis status inside the Docker container:

```bash
# For containers using internal Redis
docker exec -it <container_id> /app/scripts/check-redis-docker.sh
```

### Redis Logs

To view Redis logs inside the Docker container:

```bash
docker exec -it <container_id> cat /var/log/redis.log
```

### Common Issues

1. **Redis connection failures**: Ensure the REDIS_PASSWORD environment variable is set correctly
2. **Redis not starting**: Check the Redis logs for errors
3. **Permission issues**: The container runs Redis without requiring root privileges
