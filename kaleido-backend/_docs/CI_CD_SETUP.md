# Backend CI/CD Pipeline and Testing Setup

This document outlines the complete CI/CD pipeline and testing setup that has been implemented for the Headstart backend application.

## 🚀 What's Been Set Up

### 1. Testing Framework
- **Unit Tests**: Jest with NestJS Testing utilities
- **Integration Tests**: Database integration testing with TypeORM
- **E2E Tests**: Full application testing with Supertest
- **Coverage**: Code coverage reporting with configurable thresholds

### 2. CI/CD Pipeline
- **GitHub Actions**: Automated workflows for testing and deployment
- **Multi-stage Pipeline**: Lint → Test → Build → Deploy
- **Database Testing**: PostgreSQL and Redis services in CI
- **Docker Support**: Container build testing

### 3. Package Scripts Added
```json
{
  "test": "jest",
  "test:watch": "jest --watch",
  "test:coverage": "jest --coverage",
  "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand",
  "test:e2e": "jest --config ./test/jest-e2e.json",
  "test:e2e:watch": "jest --config ./test/jest-e2e.json --watch",
  "test:integration": "jest --config ./test/jest-integration.json",
  "test:unit": "jest --testPathIgnorePatterns=.*\\.integration\\.spec\\.ts --testPathIgnorePatterns=.*\\.e2e-spec\\.ts",
  "test:ci": "jest --ci --coverage --watchAll=false",
  "test:ci:e2e": "jest --config ./test/jest-e2e.json --ci --watchAll=false"
}
```

## 📁 File Structure

```
headstart_backend/
├── .github/
│   └── workflows/
│       ├── backend-ci.yml       # Main CI pipeline
│       ├── backend-e2e.yml      # E2E testing
│       └── deploy-k8s.yml       # Updated deployment with tests
├── test/
│   ├── e2e/
│   │   └── health.e2e-spec.ts
│   ├── integration/
│   │   └── auth.integration.spec.ts
│   ├── factories/
│   │   └── user.factory.ts
│   ├── mocks/
│   │   └── database.mock.ts
│   ├── jest-e2e.json
│   ├── jest-integration.json
│   ├── setup.ts
│   ├── setup-e2e.ts
│   └── setup-integration.ts
├── src/
│   └── modules/
│       ├── auth/
│       │   ├── auth.service.ts
│       │   └── auth.service.spec.ts
│       └── job/
│           ├── job.service.ts
│           └── job.service.spec.ts
├── jest.config.js
└── TESTING.md
```

## 🔧 Configuration Files

### Jest Configuration (`jest.config.js`)
- Configured for TypeScript and TSX (React email templates)
- Module path mapping for imports
- Coverage thresholds set to 70%
- Excludes E2E and integration tests from unit test runs
- React and email template mocking

### Test Configurations
- `test/jest-e2e.json`: E2E test configuration with database setup
- `test/jest-integration.json`: Integration test configuration
- `test/setup.ts`: Global test setup and mocks
- `test/setup-e2e.ts`: E2E test setup with database
- `test/setup-integration.ts`: Integration test setup

## 🚦 GitHub Actions Workflows

### 1. Backend CI Pipeline (`.github/workflows/backend-ci.yml`)
**Triggers**: Push/PR to main/develop branches
**Jobs**:
- **Lint and Type Check**: ESLint, TypeScript, Prettier
- **Unit Tests**: Jest with coverage reporting
- **Integration Tests**: With PostgreSQL and Redis services
- **Build**: Production build verification
- **Docker Build**: Container build testing (PRs only)

### 2. Backend E2E Testing (`.github/workflows/backend-e2e.yml`)
**Triggers**: Push/PR to main/develop branches
**Jobs**:
- **E2E Tests**: Full application testing with database
- **API Documentation Tests**: Swagger/OpenAPI validation
- **Performance Tests**: Basic load testing with Artillery (main branch only)

### 3. Updated Deployment (`.github/workflows/deploy-k8s.yml`)
**Enhanced with**:
- **Pre-deployment Tests**: Full test suite before deployment
- **Database Migration Testing**: Verify migrations work
- **Multi-stage Validation**: Tests → Build → Deploy

## 🧪 Sample Tests Included

### Unit Tests
- **AuthService**: Token verification, role checking
- **JobService**: CRUD operations, candidate management

### Integration Tests
- **Auth Integration**: JWT token operations, authentication flow

### E2E Tests
- **Health Endpoints**: Application health checks
- **API Documentation**: Swagger UI and OpenAPI validation

### Test Utilities
- **Test Factories**: User, Company, Job data generation
- **Database Mocks**: Repository and QueryBuilder mocking
- **Service Mocks**: External service mocking (OpenAI, AWS, Stripe, etc.)

## 🚀 Getting Started

### 1. Install Dependencies
```bash
pnpm install
```

### 2. Run Tests
```bash
# Unit tests
pnpm test

# Integration tests (requires PostgreSQL)
pnpm test:integration

# E2E tests (requires PostgreSQL + Redis)
pnpm test:e2e

# Coverage report
pnpm test:coverage
```

### 3. Database Setup for Testing
```bash
# Create test databases
createdb headstart_test
createdb headstart_integration_test

# Set environment variables
export TEST_DB_HOST=localhost
export TEST_DB_PORT=5432
export TEST_DB_USERNAME=test
export TEST_DB_PASSWORD=test
export TEST_DB_NAME=headstart_test
```

## 📊 Coverage Reports

Coverage reports are generated in the `coverage/` directory:
- HTML report: `coverage/lcov-report/index.html`
- LCOV format: `coverage/lcov.info`

**Current Coverage Thresholds**: Disabled (can be re-enabled by uncommenting in jest.config.js)

## 🔧 Environment Variables

### Required for CI/CD
- `TEST_DB_HOST`: Test database host
- `TEST_DB_PORT`: Test database port
- `TEST_DB_USERNAME`: Test database username
- `TEST_DB_PASSWORD`: Test database password
- `TEST_DB_NAME`: Test database name
- `REDIS_URL`: Redis connection URL

### Mocked in Tests
- `OPENAI_API_KEY`: OpenAI API key (mocked)
- `AWS_ACCESS_KEY_ID`: AWS access key (mocked)
- `AWS_SECRET_ACCESS_KEY`: AWS secret key (mocked)
- `STRIPE_SECRET_KEY`: Stripe secret key (mocked)

## 📝 Next Steps

1. **Add More Tests**: Expand test coverage for critical business logic
2. **Database Seeding**: Create test data fixtures for E2E tests
3. **Performance Testing**: Expand load testing scenarios
4. **Security Testing**: Add security-focused tests
5. **API Contract Testing**: Implement contract testing with Pact

## 🛠 Maintenance

### Adding New Tests
- Unit tests: Create files alongside source files with `.spec.ts` extension
- Integration tests: Add files in `test/integration/` with `.integration.spec.ts`
- E2E tests: Add files in `test/e2e/` with `.e2e-spec.ts`

### Updating Dependencies
```bash
# Update testing dependencies
pnpm update jest @nestjs/testing supertest

# Update test database
# Update PostgreSQL/Redis versions in CI workflows
```

### Debugging
- **Unit Tests**: `pnpm test:debug`
- **Integration Tests**: `pnpm test:integration --verbose`
- **E2E Tests**: `pnpm test:e2e --verbose`

## 📚 Documentation

- [NestJS Testing Documentation](https://docs.nestjs.com/fundamentals/testing)
- [Jest Documentation](https://jestjs.io/)
- [Supertest Documentation](https://github.com/visionmedia/supertest)
- [TypeORM Testing](https://typeorm.io/testing)

---

**Status**: ✅ Setup Complete
**Last Updated**: December 2024
**Test Coverage**: 4.29% (baseline - will improve as more tests are added)
