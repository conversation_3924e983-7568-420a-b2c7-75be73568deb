# Credit-Based Pricing Model

## Overview

The Headstart platform uses a **credit-based pricing model** that provides flexibility and control over how users consume platform resources. This document reflects the current implementation as of 2025.

## Current Pricing Tiers

### Free Plan - $0
- **5 credits per month** - *Updated based on new pricing structure from spreadsheet*
- Video JD Max Duration: 60 seconds
- ATS Integration: Basic
- Database Retention: 1 month
- No Priority Support
- No Custom Branding
- No API Access
- No Dedicated Account Manager

### Starter Plan - $150/month
- **359 credits per month** - *Updated based on new pricing structure from spreadsheet*
- Video JD Max Duration: 90 seconds
- ATS Integration: Basic
- Database Retention: 3 months
- Priority Support: Yes
- Custom Branding: Yes
- API Access: Yes
- Bulk Credit Purchase: Available
- Advanced Analytics: Basic

### Professional Plan - $360/month
- **885 credits per month** - *Updated based on new pricing structure from spreadsheet*
- Video JD Max Duration: 90 seconds
- ATS Integration: Advanced
- Database Retention: 12 months
- Priority Support: Yes
- Custom Branding: Yes
- API Access: Yes
- Dedicated Account Manager: Yes
- Bulk Credit Purchase: Discounted
- Advanced Analytics: Full
- Custom Integrations: Limited
- Tailored Workflows: Basic

### Enterprise Plan - Contact Us
- **Unlimited credits**
- Video JD Max Duration: 5 minutes (300 seconds)
- ATS Integration: Custom
- Database Retention: 24 months
- All Professional features plus:
  - Enterprise Support
  - Custom Training
  - Custom Integrations: Full
  - Tailored Workflows: Custom
  - Maximum Bulk Credit Discounts

## Yearly Billing (16% Discount)

### Starter Plan - $1,512/year
- **60,000 credits + 10,000 bonus credits per year**
- All monthly features included

### Professional Plan - $2,520/year
- **180,000 credits + 30,000 bonus credits per year**
- All monthly features included

## Credit Costs

| Action | Credit Cost | Description |
|--------|-------------|-------------|
| Job Description Generation | **FREE** | Create unlimited job descriptions |
| Resume Upload | 1 credit | Per resume uploaded |
| LinkedIn Scout | 10 credit | Per candidate scouted |
| Match & Rank | 1 credit | Per candidate matched/ranked |
| Video JD | 80-180 credits | Variable based on video length |
| Video Intro Questions | 1 credit | Per culture fit question set |
| Unlock Candidate Details | 5 credits | Per candidate detail unlock |

### Video JD Variable Pricing
- **Short videos** (≤10 seconds): 80 credits
- **Medium videos** (30-45 seconds): 140 credits
- **Long videos** (45-110 seconds): 180 credits

## One-Time Credit Packages

| Package | Price | Base Credits | Bonus Credits | Total Credits |
|---------|-------|--------------|---------------|---------------|
| Starter Pack | $25 | 250 | 0 | 250 |
| Value Pack | $50 | 500 | 25 | 525 |
| Pro Pack | $100 | 1,000 | 100 | 1,100 |
| Business Pack | $150 | 1,500 | 200 | 1,700 |
| Enterprise Pack | $200 | 2,000 | 300 | 2,300 |

## Key Benefits

1. **Flexibility**: Use credits however you need without rigid feature limits
2. **Transparency**: Clear credit costs for each action
3. **Scalability**: Higher tiers provide more credits for growing teams
4. **Free Forever**: Job description generation remains free
5. **No Expiration**: Credits don't expire on active plans
6. **Instant Top-ups**: Buy additional credits anytime without changing subscription
7. **Bonus Credits**: Get bonus credits with larger one-time purchases and yearly billing

## Current Implementation Status

### Frontend Implementation ✅
- Credit-based pricing display in `locales/en.json`
- `SubscriptionStatusIndicator` shows credit usage
- `subscriptionStore` uses credit-based methods
- `useSubscription` hook focuses on credits
- Credit purchase options and packages
- Yearly billing with 16% discount

### Backend Implementation ✅
- Credit-based endpoints (`/subscription/credits/*`)
- `CreditGuard` and `@CheckCreditAction` decorator
- Credit validation engine
- Subscription plans configuration in `PLAN_CREDITS`
- Stripe pricing service with current rates
- One-time credit purchase system

### Database Implementation ✅
- `subscriptionCredits` JSONB column in companies table
- Migration script for credit-based system
- Default credit allocation based on subscription plan
- Credit purchase tracking

## Technical Implementation

### Credit Consumption
Credits are consumed when actions are performed:
```typescript
// Resume uploads are now free - no credit consumption needed
// Example for other actions:
const success = await creditService.checkAndConsumeCredits(
  companyId,
  CreditActionType.SCOUT,
  1
);
```

### Credit Validation
Validate before performing actions:
```typescript
// Resume uploads are now free - no validation needed
// Example for other actions:
const validation = await creditValidationEngine.canScout(companyId, count);
if (!validation.isValid) {
  throw new ForbiddenException(validation.message);
}
```

### Guard Usage
Protect endpoints with credit requirements:
```typescript
// Resume uploads are now free - no guards needed
// Example for other actions:
@UseGuards(Auth0Guard, CreditGuard)
@CheckCreditAction(CreditActionType.SCOUT)
@Post('scout-candidates')
async scoutCandidates(@GetUser() user: User) {
  // Action is automatically validated by guard
}
```

### Frontend Credit Checking
```typescript
// Check if user has enough credits before action
const hasCredits = subscriptionStore.hasEnoughCredits(CreditActionType.VIDEO_JD, 1);
if (!hasCredits) {
  // Show upgrade prompt or credit purchase modal
}
```

## Current Configuration Files

### Backend
- `headstart_backend/src/shared/constants/subscription-plans.ts` - Plan configurations
- `headstart_backend/src/modules/payment/stripe.service.ts` - Pricing definitions
- `headstart_backend/src/shared/enums/subscription-limit-type.enum.ts` - Credit costs and packages

### Frontend
- `kaleido-app/src/locales/en.json` - Pricing display and copy
- `kaleido-app/src/types/subscription.ts` - Credit costs and types
- `kaleido-app/src/lib/pricingUtils.ts` - Pricing utilities
- `kaleido-app/src/stores/subscriptionStore.ts` - Credit management

## Analytics and Monitoring

The credit system provides insights into:
- Platform usage patterns by credit consumption
- Feature adoption rates through credit usage
- Revenue optimization through credit package sales
- Customer upgrade triggers based on credit depletion

## Implemented Features

✅ **Credit-based subscription plans**
✅ **One-time credit purchases**
✅ **Yearly billing with 16% discount**
✅ **Variable video JD pricing**
✅ **Credit validation and guards**
✅ **Frontend credit checking**
✅ **Stripe payment integration**
✅ **Credit usage tracking**

## Future Enhancements

1. **Credit Rollover**: Unused credits carry over to next month (premium feature)
2. **Usage Analytics Dashboard**: Detailed credit consumption reports
3. **Smart Final Summary**: Suggest plan upgrades based on usage patterns
4. **Credit Alerts**: Notify users when credits are running low
5. **Bulk Enterprise Discounts**: Custom pricing for large credit purchases
