# Google Wallet Integration Setup

This guide explains how to set up Google Wallet integration for job seeker profiles.

## Prerequisites

1. Google Cloud Platform account
2. Google Pay & Wallet Console access

## Setup Steps

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Wallet API

### 2. Create Service Account

1. Go to IAM & Admin > Service Accounts
2. Click "Create Service Account"
3. Name: `wallet-service-account`
4. Grant role: `Wallet Objects API Admin`
5. Create and download the JSON key file

### 3. Get Issuer ID

1. Go to [Google Pay & Wallet Console](https://pay.google.com/business/console)
2. Create a new issuer account if needed
3. Note your Issuer ID (format: `3388000000012345678`)

### 4. Configure Environment Variables

Add these to your `.env` file:

```bash
# Google Wallet Configuration
GOOGLE_WALLET_ISSUER_ID=3388000000012345678
GOOGLE_WALLET_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_WALLET_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_FROM_JSON_FILE\n-----END PRIVATE KEY-----"
FRONTEND_URL=http://localhost:3000
```

### 5. Setup Generic Class (One-time)

Run this endpoint once to create the wallet pass class:

```bash
POST /api/wallet/google-wallet/setup-class
Authorization: Bearer <admin-token>
```

## API Endpoints

### Generate Wallet Pass
```bash
POST /api/wallet/google-wallet/generate
Authorization: Bearer <job-seeker-token>
```

Response:
```json
{
  "saveUrl": "https://pay.google.com/gp/v/save/...",
  "message": "Google Wallet pass generated successfully"
}
```

### Check Wallet Status
```bash
GET /api/wallet/google-wallet/status
Authorization: Bearer <job-seeker-token>
```

Response:
```json
{
  "available": true,
  "profileComplete": true,
  "message": "Google Wallet is available"
}
```

## Pass Content

The wallet pass includes:
- Job seeker name as header
- Email, phone, location as text modules
- Top 3 skills
- QR code linking to mobile profile
- Company logo
- Link to full profile

## Testing

1. Generate a pass using the API
2. Open the `saveUrl` on a mobile device
3. Add to Google Wallet
4. Verify the pass appears correctly
5. Test QR code scanning

## Troubleshooting

### Common Issues

1. **Invalid Issuer ID**: Ensure the issuer ID is correct and active
2. **Authentication Error**: Check service account permissions
3. **Class Not Found**: Run the setup-class endpoint first
4. **Invalid Private Key**: Ensure proper formatting with `\n` for line breaks

### Debug Mode

Set `NODE_ENV=development` to see detailed error logs.

## Security Notes

- Keep private keys secure
- Use environment variables for sensitive data
- Regularly rotate service account keys
- Monitor API usage in Google Cloud Console

## Production Deployment

1. Use production Google Cloud project
2. Set up proper IAM roles
3. Configure HTTPS endpoints
4. Set correct `FRONTEND_URL`
5. Test thoroughly before launch
