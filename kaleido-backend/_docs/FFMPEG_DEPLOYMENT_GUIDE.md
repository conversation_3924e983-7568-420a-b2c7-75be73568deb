# FFmpeg Deployment Guide for DigitalOcean

This guide explains how to deploy your application with FFmpeg support on DigitalOcean App Platform.

## 🚀 Quick Start

### Option 1: Using Docker (Recommended)

1. **Build Docker image with FFmpeg:**
   ```bash
   cd headstart_backend
   chmod +x scripts/rebuild-with-ffmpeg.sh
   ./scripts/rebuild-with-ffmpeg.sh
   ```

2. **Push to DigitalOcean Container Registry:**
   ```bash
   # Login to DOCR
   doctl registry login

   # Tag and push
   docker tag kaleido-backend:latest registry.digitalocean.com/your-registry/kaleido-backend:latest
   docker push registry.digitalocean.com/your-registry/kaleido-backend:latest
   ```

3. **Deploy using the app spec:**
   ```bash
   doctl apps create --spec do-app-spec.yaml
   ```

### Option 2: Direct Server Installation

If you have SSH access to your server:

1. **Run the installation script:**
   ```bash
   chmod +x scripts/install-ffmpeg-do.sh
   ./scripts/install-ffmpeg-do.sh
   ```

2. **Restart your application:**
   ```bash
   pm2 restart all  # or your process manager
   ```

## 📋 Detailed Steps

### Step 1: Prepare Your Docker Image

The updated Dockerfiles now include FFmpeg installation:

```dockerfile
# In Dockerfile.prod (line 25)
RUN corepack enable && corepack prepare pnpm@10.10.0 --activate && \
    apk add --no-cache redis bash curl ffmpeg
```

### Step 2: Build and Test Locally

```bash
# Build the image
docker build -f Dockerfile.prod -t kaleido-backend:ffmpeg .

# Test FFmpeg is working
docker run --rm kaleido-backend:ffmpeg ffmpeg -version

# Test your application
docker run -p 8080:8080 kaleido-backend:ffmpeg
```

### Step 3: Deploy to DigitalOcean

#### Using DigitalOcean Container Registry (DOCR):

1. **Create a registry** (if you don't have one):
   ```bash
   doctl registry create your-registry-name
   ```

2. **Build and push:**
   ```bash
   # Build
   docker build -f Dockerfile.prod -t kaleido-backend:latest .

   # Tag for DOCR
   docker tag kaleido-backend:latest registry.digitalocean.com/your-registry/kaleido-backend:latest

   # Push
   docker push registry.digitalocean.com/your-registry/kaleido-backend:latest
   ```

3. **Update your app:**
   - Go to DigitalOcean Control Panel
   - Navigate to your App Platform app
   - Update the image source to use your DOCR image
   - Deploy

#### Using App Platform with Source Code:

1. **Update your repository** with the new Dockerfiles
2. **Push to your Git repository**
3. **Trigger a new deployment** in DigitalOcean App Platform

### Step 4: Verify FFmpeg Installation

After deployment, check your application logs:

```bash
# Using doctl
doctl apps logs your-app-id --type=run

# Look for FFmpeg-related messages like:
# "FFmpeg path set to: /usr/bin/ffmpeg"
# "Using system FFmpeg (fluent-ffmpeg will auto-detect)"
```

## 🔧 Configuration

### Environment Variables

Make sure these are set in your DigitalOcean app:

```env
NODE_ENV=production
PORT=8080
# ... your other environment variables
```

### App Specification

Use the provided `do-app-spec.yaml` file:

```bash
# Create new app
doctl apps create --spec do-app-spec.yaml

# Update existing app
doctl apps update your-app-id --spec do-app-spec.yaml
```

## 🐛 Troubleshooting

### Common Issues:

1. **FFmpeg not found:**
   - Check if FFmpeg is installed: `docker run --rm your-image ffmpeg -version`
   - Verify the Dockerfile includes FFmpeg installation

2. **Video conversion fails:**
   - Check application logs for specific FFmpeg errors
   - Ensure input video format is supported
   - Verify sufficient memory/CPU resources

3. **Build fails:**
   - Check Docker build logs
   - Ensure all dependencies are available
   - Try building locally first

### Debug Commands:

```bash
# Check FFmpeg in running container
docker exec -it container-id ffmpeg -version

# Check available codecs
docker exec -it container-id ffmpeg -codecs

# Test video processing
docker exec -it container-id ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 -f null -
```

## 📊 Performance Considerations

### Resource Requirements:

- **CPU**: Video processing is CPU-intensive
- **Memory**: Recommend at least 1GB RAM for video conversion
- **Storage**: Temporary files during conversion

### Recommended DigitalOcean Sizes:

- **Development**: `basic-xxs` (512MB RAM, 1 vCPU)
- **Production**: `basic-s` (1GB RAM, 1 vCPU) or higher
- **High volume**: `professional-xs` (1GB RAM, 1 dedicated vCPU)

## 🔄 Monitoring

### Health Checks:

The app spec includes health checks at `/health`. Make sure your application responds to this endpoint.

### Logging:

Monitor these log messages:
- FFmpeg initialization
- Video conversion start/completion
- Error messages from fluent-ffmpeg

## 📝 Next Steps

1. **Test video conversion** after deployment
2. **Monitor performance** and adjust resources if needed
3. **Set up alerts** for failed video conversions
4. **Consider CDN** for serving converted videos

## 🆘 Support

If you encounter issues:

1. Check the application logs
2. Verify FFmpeg installation
3. Test video conversion manually
4. Review DigitalOcean App Platform documentation
