# Auth0 Management API Setup

This document explains how to set up the Auth0 Management API for updating user profiles when name information is extracted from resumes or company contact details.

## Overview

The Auth0 Management API integration allows the backend to update user profiles in Auth0 when we have better information than what was initially provided during registration. This is particularly useful when:

1. **Job Seekers upload resumes** - We extract first and last names from the resume and update the Auth0 profile
2. **Companies update contact information** - We sync contact name changes to the Auth0 profile

## Auth0 Setup

### 1. Create a Machine-to-Machine Application

1. Go to your Auth0 Dashboard
2. Navigate to **Applications** → **Create Application**
3. Choose **Machine to Machine Applications**
4. Name it something like "Headstart Backend Management API"
5. Select your API (the one used for your main application)
6. Grant the following scopes:
   - `read:users`
   - `update:users`

### 2. Get Credentials

After creating the application, you'll get:
- **Client ID** - Use this for `AUTH0_M2M_CLIENT_ID`
- **Client Secret** - Use this for `AUTH0_M2M_CLIENT_SECRET`
- **Domain** - This should be the same as your existing `AUTH0_ISSUER`

## Environment Variables

Add these environment variables to your backend configuration:

```bash
# Auth0 Management API Credentials
AUTH0_M2M_CLIENT_ID=your_machine_to_machine_client_id
AUTH0_M2M_CLIENT_SECRET=your_machine_to_machine_client_secret

# Your existing AUTH0_ISSUER will be used for the domain
AUTH0_ISSUER=your-tenant.auth0.com
```

## How It Works

### For Job Seekers

When a job seeker uploads a resume:

1. The resume is parsed to extract personal information including first and last names
2. If the job seeker's current profile has missing or placeholder names (like "New User"), the extracted names are used to update both:
   - The local database record
   - The Auth0 user profile via the Management API

### For Companies

When a company updates their contact information:

1. If the `contactName` field is updated, the system will:
   - Update the local database record
   - Update the Auth0 user profile with the new contact name
   - Attempt to split the contact name into first and last name components

## Code Implementation

The implementation includes:

- **Auth0ManagementService** (`src/auth/auth0-management.service.ts`) - Handles all Auth0 Management API calls
- **JobSeekerService** - Updated to call Auth0 when name data is extracted from resumes
- **CompanyService** - Updated to call Auth0 when contact names are updated

## Error Handling

The Auth0 Management API calls are designed to be non-blocking:

- If Auth0 credentials are not configured, warnings are logged but the main functionality continues
- If Auth0 API calls fail, errors are logged but don't break the main user flow
- This ensures that resume uploads and profile updates work even if Auth0 sync fails

## Testing

To test the integration:

1. Set up the environment variables
2. Upload a resume for a job seeker with placeholder names
3. Check the Auth0 user profile to see if the names were updated
4. Update a company's contact name and verify the Auth0 profile is updated

## Security Notes

- The Machine-to-Machine application credentials should be kept secure
- Only grant the minimum required scopes (`read:users` and `update:users`)
- The Management API has rate limits, but the implementation includes proper error handling
- User metadata is used to track when names were updated from resumes vs. other sources
