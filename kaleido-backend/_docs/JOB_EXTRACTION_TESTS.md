# Job Description Extraction Tests

This document outlines the comprehensive test suite for the job description extraction functionality using the provided sample job description.

## Sample Job Description

```
Software Engineer
Ka<PERSON>ido <PERSON>s

Location: Abu Dhabi, Dubai
Salary Range: AED 60,000 - 84,000
Experience: Senior level (5-8 years)
Type of Hiring: Project
Type of Job: Contract
Industry: Information Technology

About the Company:
Kaleido Talent is a company specializing in talent management and human resources consulting services...

Responsibilities:
- Design, develop, and maintain software applications
- Write clean, scalable, and efficient code
- Collaborate with cross-functional teams to define project requirements
...

Required Skills:
- Proficiency in programming languages (e.g., Java, Python, C++)
- Understanding of software development methodologies (e.g., Agile, Scrum)
- Experience with version control systems (e.g., Git)
...

Benefits:
- Accommodation
- Annual Leave
- Housing Allowance
- Professional Development Opportunities
- Meal Allowance
```

## Expected Extraction Results

### ✅ Correctly Mapped Fields

| Field | Expected Value | Validation |
|-------|---------------|------------|
| **title** | "Software Engineer" | ✅ Direct extraction |
| **companyName** | "Kaleido Mafias" | ✅ Direct extraction |
| **department** | "Engineering" | ✅ Maps to valid enum |
| **jobType** | "Software Engineer" | ✅ Maps to valid enum |
| **typeOfHiring** | "PROJECT" | ✅ Correct enum value |
| **typeOfJob** | "CONTRACT" | ✅ Correct enum value |
| **experienceLevel** | "senior" | ✅ Correct enum (not "SENIOR_LEVEL") |
| **currency** | "AED" | ✅ Valid currency enum |
| **salaryRange** | "AED 60,000 - 84,000" | ✅ Direct extraction |
| **location** | "Abu Dhabi, Dubai" | ✅ Direct extraction |

### 📋 Array Fields

| Field | Expected Content |
|-------|-----------------|
| **skills** | `["Java", "Python", "C++", "Git", "SQL", "NoSQL", "HTML", "CSS", "JavaScript", "AWS", "Azure"]` |
| **jobResponsibilities** | `["Design, develop, and maintain software applications", "Write clean, scalable, and efficient code", ...]` |
| **requirements** | `["Proficiency in programming languages", "Understanding of software development methodologies", ...]` |
| **benefits** | `["Accommodation", "Annual Leave", "Housing Allowance", "Professional Development Opportunities", "Meal Allowance"]` |

## Test Files

### 1. Controller Tests (`src/modules/job/tests/job-extraction.spec.ts`)

Tests the `JobController.extractJobDescription()` method:

- ✅ **Successful extraction** with valid job description
- ✅ **Enum validation** - converts invalid enum values to null
- ✅ **Error handling** for empty/missing job description
- ✅ **OpenAI service failures** handling
- ✅ **Partial extraction** with some null values
- ✅ **Empty/whitespace strings** - converts to null
- ✅ **Case-insensitive enum matching** - handles different cases
- ✅ **Malformed array data** - cleans invalid array values
- ✅ **Invalid salary formats** - validates salary range patterns
- ✅ **Completely invalid responses** - handles wrong data types
- ✅ **Near-match enum values** - rejects close but incorrect matches

### 2. OpenAI Service Tests (`src/shared/services/tests/openai-extraction.spec.ts`)

Tests the `OpenaiService.extractJobInformation()` method:

- ✅ **Correct enum values** in extraction
- ✅ **API error handling** gracefully
- ✅ **Invalid JSON response** handling
- ✅ **Empty response** handling
- ✅ **Correct OpenAI parameters** validation
- ✅ **Prompt enum validation** - ensures correct values in prompt

### 3. Edge Cases & Data Integrity (`src/modules/job/tests/job-extraction-edge-cases.spec.ts`)

Tests data integrity and database safety:

- ✅ **SQL injection prevention** - handles malicious input safely
- ✅ **Extremely long strings** - handles large text without breaking
- ✅ **Unicode & special characters** - preserves international text
- ✅ **Circular references** - handles complex objects safely
- ✅ **Large arrays** - processes big datasets efficiently
- ✅ **Null/undefined consistency** - handles missing values uniformly
- ✅ **Varied casing/spacing** - normalizes enum values correctly
- ✅ **Mixed data types** - filters invalid array elements

## Running the Tests

### Option 1: Run Individual Test Files
```bash
# Run controller tests
npm run test -- src/modules/job/tests/job-extraction.spec.ts

# Run OpenAI service tests
npm run test -- src/shared/services/tests/openai-extraction.spec.ts
```

### Option 2: Run All Tests with Script
```bash
# Make script executable
chmod +x test-job-extraction.js

# Run all job extraction tests
node test-job-extraction.js
```

### Option 3: Run with Coverage
```bash
npm run test:cov -- src/modules/job/tests/job-extraction.spec.ts src/shared/services/tests/openai-extraction.spec.ts
```

## Key Test Scenarios

### 1. Enum Validation Test
```typescript
it('should validate and clean extracted data with invalid enum values', async () => {
  const mockInvalidResponse = {
    experienceLevel: 'SENIOR_LEVEL', // Invalid (old format)
    typeOfHiring: 'INVALID_HIRING',  // Invalid
    department: 'InvalidDepartment', // Invalid
    // ...
  };
  
  // Should convert invalid values to null
  expect(result.experienceLevel).toBeNull();
  expect(result.typeOfHiring).toBeNull();
  expect(result.department).toBeNull();
});
```

### 2. Successful Extraction Test
```typescript
it('should successfully extract job information from valid job description', async () => {
  const result = await controller.extractJobDescription(
    { jobDescriptionText: sampleJobDescription },
    mockUser
  );
  
  expect(result.title).toBe('Software Engineer');
  expect(result.experienceLevel).toBe('senior'); // Not 'SENIOR_LEVEL'
  expect(result.typeOfHiring).toBe('PROJECT');
  expect(result.currency).toBe('AED');
});
```

## Validation Rules

### Enum Validation
The tests verify that the following enum values are correctly mapped:

- **ExperienceLevel**: `graduate`, `junior`, `mid`, `senior`, `lead`
- **TypeOfHiring**: `EMPLOYMENT`, `PROJECT`
- **TypeOfJob**: `PERMANENT`, `CONTRACT`, `PART_TIME`
- **Currency**: `AED`, `USD`, `EUR`, `GBP`, `SAR`, `QAR`, `KWD`, `BHD`, `OMR`
- **Department**: Must match frontend constants exactly
- **JobType**: Must match frontend constants exactly

### Error Scenarios
- Empty job description → `BadRequestException`
- OpenAI API failure → `InternalServerErrorException`
- Invalid JSON response → Graceful error handling
- Invalid enum values → Convert to null (no database errors)

## Success Criteria

✅ All tests pass without errors
✅ Enum values match database schema exactly
✅ No `QueryFailedError` for invalid enum values
✅ Proper error handling for edge cases
✅ Correct extraction of all field types (strings, arrays, enums)
✅ Validation logic prevents invalid data from reaching database

## Debugging

If tests fail, check:

1. **OpenAI API Key**: Ensure `OPENAI_API_KEY` is set in environment
2. **Database Connection**: Ensure test database is accessible
3. **Enum Values**: Verify enum values match between prompt and database schema
4. **Mock Responses**: Check that mock data matches expected format

## Integration Testing

To test with real OpenAI API:
1. Set `OPENAI_API_KEY` environment variable
2. Remove OpenAI service mocks
3. Run tests with actual API calls (note: this will consume API credits)

```bash
# Run integration tests with real API
INTEGRATION_TEST=true npm run test -- src/modules/job/tests/job-extraction.spec.ts
```
