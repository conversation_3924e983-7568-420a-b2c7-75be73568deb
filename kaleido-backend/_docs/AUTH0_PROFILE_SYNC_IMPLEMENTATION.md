# Auth0 Profile Sync Implementation

## Overview

This implementation provides automatic synchronization of user profile data between the application database and Auth0 when better information becomes available. This addresses the issue where Auth0 registration only captures email/password, but we later extract more complete name information from resume uploads or company contact details.

## Key Features

### 1. Auth0 Management API Integration
- **Service**: `Auth0ManagementService` (`src/auth/auth0-management.service.ts`)
- **Purpose**: Handles all Auth0 Management API interactions
- **Capabilities**:
  - Update user profiles with name data extracted from resumes
  - Update company contact names in Auth0 profiles
  - Graceful error handling (non-blocking)
  - Automatic token management for Management API

### 2. Job Seeker Profile Updates
- **Location**: `JobSeekerService.mergeProfileData()` method
- **Trigger**: When resumes are uploaded and parsed
- **Behavior**:
  - Extracts first and last names from resume content
  - Updates local database with extracted information
  - Automatically syncs name data to Auth0 profile
  - Tracks update source in Auth0 user metadata

### 3. Company Profile Updates
- **Location**: `CompanyService.update()` and `CompanyService.updateByClientId()` methods
- **Trigger**: When company contact information is updated
- **Behavior**:
  - Detects changes to `contactName` field
  - Updates local database with new contact information
  - Automatically syncs contact name to Auth0 profile
  - Attempts to split full names into first/last name components

## Implementation Details

### Auth0ManagementService

```typescript
// Key methods:
- updateUserNameFromResume(userId, firstName, lastName)
- updateCompanyContactName(userId, contactName)
- updateUserProfile(userId, updates)
- getUserProfile(userId)
```

### Environment Variables Required

```bash
# Auth0 Management API Credentials (Machine-to-Machine Application)
AUTH0_M2M_CLIENT_ID=your_m2m_client_id
AUTH0_M2M_CLIENT_SECRET=your_m2m_client_secret

# Existing Auth0 configuration
AUTH0_ISSUER=your-tenant.auth0.com
```

### Error Handling Strategy

1. **Non-blocking**: Auth0 sync failures don't break main application flow
2. **Graceful degradation**: Missing credentials result in warnings, not errors
3. **Comprehensive logging**: All Auth0 operations are logged for debugging
4. **Retry logic**: Built into the Auth0 SDK for transient failures

## Setup Instructions

### 1. Auth0 Configuration

1. Create a Machine-to-Machine Application in Auth0 Dashboard
2. Grant scopes: `read:users` and `update:users`
3. Note the Client ID and Client Secret

### 2. Environment Configuration

Add the required environment variables to your `.env` file:

```bash
AUTH0_M2M_CLIENT_ID=your_machine_to_machine_client_id
AUTH0_M2M_CLIENT_SECRET=your_machine_to_machine_client_secret
```

### 3. Verification

1. Upload a resume for a user with placeholder names
2. Check Auth0 user profile to verify name updates
3. Update a company's contact name and verify Auth0 sync
4. Monitor logs for Auth0 Management API operations

## Benefits

### 1. Consistent User Experience
- Users see their correct names across all Auth0-integrated applications
- Eliminates confusion from placeholder names like "New User"

### 2. Improved Data Quality
- Auth0 becomes the single source of truth for user identity
- Automatic data enrichment from resume parsing
- Consistent naming across all integrated systems

### 3. Seamless Integration
- No changes required to existing Auth0 login flows
- Backward compatible with existing user profiles
- Non-disruptive to current application functionality

### 4. Audit Trail
- All updates tracked in Auth0 user metadata
- Source attribution (resume vs. manual update)
- Timestamp tracking for all profile changes

## Testing

### Unit Tests
- Comprehensive test suite for `Auth0ManagementService`
- Mocked Auth0 API interactions
- Error handling scenarios covered
- Configuration validation tests

### Integration Testing
1. Test resume upload with name extraction
2. Test company contact name updates
3. Verify Auth0 profile synchronization
4. Test error scenarios (API failures, missing credentials)

## Monitoring and Maintenance

### Logs to Monitor
- Auth0 Management API initialization
- Profile update operations
- API failures and retries
- Configuration warnings

### Metrics to Track
- Success rate of Auth0 profile updates
- Resume parsing accuracy for name extraction
- API response times and rate limits

## Future Enhancements

1. **Batch Updates**: Process multiple profile updates in batches
2. **Conflict Resolution**: Handle cases where Auth0 and local data diverge
3. **Additional Fields**: Sync more profile fields (phone, location, etc.)
4. **Real-time Sync**: WebSocket-based real-time profile synchronization
