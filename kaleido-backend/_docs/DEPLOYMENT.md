# Deployment Guide for Digital Ocean App Platform

This document provides information about the deployment process to Digital Ocean App Platform.

## Deployment Process

The deployment is automatically handled by Digital Ocean's App Platform when you push to either the `master` or `production` branch. The key features of this deployment are:

1. Automatic builds triggered by <PERSON>it pushes
2. Automatic database migrations via the `migrate:safe:prod` script
3. Zero-downtime deployments
4. Branch-specific environments

## Database Migrations

Migrations are automatically run as part of the pre-deployment process defined in `app.json`. The `migrate:safe:prod` script:

1. Creates a backup of the database before running migrations
2. Runs all pending migrations
3. Provides detailed logs of the migration process

## Monitoring Deployments

You can monitor deployment status in the Digital Ocean dashboard:

1. Log into your Digital Ocean account
2. Navigate to the App Platform section
3. Select your Headstart application
4. View the deployment history and logs

## Troubleshooting

### Checking Migration Logs

If migrations fail, you can check the logs in the Digital Ocean dashboard:

1. Go to your app in the Digital Ocean dashboard
2. Click on the "Components" tab
3. Select your backend service
4. Click on "Logs" to view deployment and migration logs

### Manual Migration

If you need to run migrations manually:

```bash
# SSH into your app (via Digital Ocean console)
pnpm migrate:safe:prod
```

### Database Backups

Backups are automatically created before running migrations. These are stored in the `/backup` directory of your app.

To list backups:
```bash
ls -la /backup
```

To restore a backup:
```bash
pg_restore -d your_database_name /backup/db-backup-TIMESTAMP.sql
```

## Environment Variables

The following environment variables should be configured in your Digital Ocean App Platform settings:

1. `DB_HOST`: Database hostname
2. `DB_PORT`: Database port (usually 5432)
3. `DB_USERNAME`: Database username
4. `DB_PASSWORD`: Database password
5. `DB_NAME`: Database name
6. `DB_SSL`: SSL settings for database connection (if needed)

To add or update environment variables:
1. Go to your app in the Digital Ocean dashboard
2. Click on the "Settings" tab
3. Select "Environment Variables"
4. Add or edit variables as needed 