# Production Database Sync Instructions

## Issue
The TypeORM synchronize feature tries to drop and recreate enum types which fails in production when those enums are already in use by existing tables.

## Solution
We've created two approaches:

### 1. Safe Schema Check Script (Recommended)
This script only checks for missing tables and doesn't make any automatic changes:

```bash
pnpm db:sync:prod
```

This will:
- Check if critical tables exist
- Report any missing tables
- Suggest using migrations for any schema changes
- NOT attempt to drop or modify existing types/columns

### 2. Migration-Based Approach
If tables are missing, use migrations:

```bash
# Run pending migrations
pnpm migration:run

# Or generate new migrations if needed
pnpm migration:generate src/migrations/AddMissingTables
```

## What We Fixed

1. **Entity Relationships**: Fixed the `@ManyToOne` decorators in `CompanyMember` and `CompanyInvitation` entities to include inverse relationship parameters.

2. **Entity Imports**: Added all missing entities to the sync script's entity list.

3. **Safe Sync Script**: Created `sync-schema-production.ts` that only checks for missing tables without attempting to modify existing schema.

4. **Manual Migration**: Created `FixCompanyMembersRelations1754261100000` migration that safely creates tables and enums only if they don't exist.

## Production Deployment Steps

1. **On your production server, run:**
   ```bash
   pnpm db:sync:prod
   ```

2. **If it reports missing tables, run migrations:**
   ```bash
   pnpm migration:run
   ```

3. **If you need to make schema changes in the future:**
   - Generate migrations locally
   - Test them thoroughly
   - Deploy and run migrations in production
   - Never use TypeORM's synchronize in production

## Important Notes

- TypeORM's `synchronize: true` should NEVER be used in production
- Always use migrations for schema changes
- The sync script now only checks for missing tables, it doesn't modify anything
- Enum types in PostgreSQL cannot be dropped if they're in use