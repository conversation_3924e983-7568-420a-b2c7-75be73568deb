# Offline Development Mode

This feature allows you to run the backend in development mode without requiring Auth0 connectivity, making it easier to develop and test locally when you don't have internet access or want to avoid Auth0 API calls.

## Configuration

### Environment Variable

Set the following environment variable to enable offline development mode:

```bash
AUTH_OFFLINE_DEV=true
```

**Important**: This feature only works when `NODE_ENV=development`. It will be ignored in production environments for security.

### Using the npm script

We've added a convenient npm script that automatically sets the required environment variables:

```bash
pnpm run dev:offline
```

This is equivalent to:
```bash
NODE_ENV=development AUTH_OFFLINE_DEV=true pnpm run dev
```

## How it works

When offline development mode is enabled:

1. **JWT Strategy**: Skips Auth0 JWT validation and creates a mock user payload
2. **Auth Guard**: Bypasses authentication and creates a mock user with admin privileges
3. **Mock User**: A development user is created with the following properties:
   - `userId`: 'offline-dev-user'
   - `email`: '<EMAIL>'
   - `name`: 'Development User'
   - `roles`: [UserRole.ADMIN] (full access)

## Security

- ⚠️ **WARNING**: This feature is only available in development mode (`NODE_ENV=development`)
- 🔒 The feature is automatically disabled in production environments
- 🚨 Never set `AUTH_OFFLINE_DEV=true` in production

## Usage Examples

### Regular development (with Auth0)
```bash
pnpm run dev
```

### Offline development (without Auth0)
```bash
pnpm run dev:offline
```

### Using environment file
Create a `.env` file with:
```
NODE_ENV=development
AUTH_OFFLINE_DEV=true
# ... other environment variables
```

Then run:
```bash
pnpm run dev
```

## Logging

When offline mode is enabled, you'll see warning messages in the console:

```
[JwtStrategy] 🔓 AUTH_OFFLINE_DEV is enabled - JWT validation is DISABLED for development
[Auth0Guard] 🔓 AUTH_OFFLINE_DEV is enabled - Authentication is DISABLED for development
```

These warnings remind you that authentication is bypassed.

## Troubleshooting

### Feature not working?
1. Ensure `NODE_ENV=development`
2. Ensure `AUTH_OFFLINE_DEV=true`
3. Check the console for warning messages
4. Restart the development server after changing environment variables

### Still getting Auth0 errors?
- Make sure you're using the `dev:offline` script or have set the environment variables correctly
- Check that no other environment variables are overriding the settings
