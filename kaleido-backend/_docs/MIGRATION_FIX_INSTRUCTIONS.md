# Migration Fix Instructions for Production

## Problem Summary
The production deployment is failing because:
1. The migration runner couldn't resolve TypeScript path aliases (`@/common/enums/role.enum`)
2. The application is trying to access columns that don't exist in the production database (e.g., `job.referralSettings`)
3. The referral system migrations haven't been applied to production

## Solutions Implemented

### 1. Fixed Path Alias Issue
- Updated `src/shared/types/enums.ts` to use relative imports instead of `@/` alias
- Updated `src/config/migration.config.ts` to properly register tsconfig-paths
- Updated `scripts/run-migrations-prod.js` to use ts-node with tsconfig-paths

### 2. Created Clean Migration Strategy
We've created multiple approaches to fix the database schema:

#### Option A: Emergency SQL Script (Recommended for immediate fix)
```bash
# Run this in your production container
./scripts/run-emergency-migration.sh
```

This script will:
- Add the missing `referralSettings` column to the jobs table
- Add the missing `referralProgram` column to the companies table
- Create all missing referral-related tables if they don't exist
- Add all necessary indexes and foreign keys

#### Option B: Run TypeORM Migrations
```bash
# Run this in your production container
./scripts/run-migrations-manual.sh
```

This will run the standard TypeORM migrations with proper path resolution.

### 3. Files Created/Modified

#### Modified Files:
- `src/shared/types/enums.ts` - Changed to relative imports
- `src/config/migration.config.ts` - Fixed path registration
- `scripts/run-migrations-prod.js` - Updated to use ts-node with paths
- `package.json` - Updated migration:run:prod and migration:deploy scripts

#### New Files:
- `scripts/emergency-add-missing-columns.sql` - SQL to add missing columns
- `scripts/run-emergency-migration.sh` - Script to run emergency SQL
- `scripts/run-migrations-manual.sh` - Manual migration runner
- `src/migrations/1754256498249-InitialSchemaClean.ts` - Clean initial migration

## Deployment Steps

### For Immediate Fix (Recommended):

1. Deploy the code changes (already in your staging)
2. SSH into your production container or run a one-off command
3. Run the emergency migration:
   ```bash
   ./scripts/run-emergency-migration.sh
   ```
4. Restart your application

### For Full Migration Reset (if needed):

1. Backup your production database
2. Clear the migrations table (only if starting fresh):
   ```sql
   DELETE FROM migrations WHERE name LIKE '%Referral%';
   ```
3. Run the migrations:
   ```bash
   npm run migration:run:prod
   ```

## Verification

After applying the fix, verify by:
1. Checking if the application starts without errors
2. Running a query to confirm columns exist:
   ```sql
   SELECT column_name FROM information_schema.columns 
   WHERE table_name = 'jobs' AND column_name = 'referralSettings';
   ```

## Prevention

To prevent this in the future:
1. Always test migrations on a staging environment that mirrors production
2. Use relative imports in migration-related files
3. Consider using a migration validation step in CI/CD
4. Keep development and production schemas in sync

## Rollback

If needed, you can rollback by:
1. Removing the added columns (though this is usually safe to keep)
2. Restoring from backup
3. The columns are nullable, so they won't break existing functionality