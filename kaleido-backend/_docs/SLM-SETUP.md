# Small Language Model (SLM) Setup Guide

This guide explains how to set up and use a small language model (SLM) on your Hostinger VPS for tasks that don't require a full-scale LLM like OpenAI's GPT models.

## Why Use a Self-Hosted SLM?

- **Cost Efficiency**: No per-token charges for simple tasks
- **Privacy**: Data stays on your servers
- **Latency**: Lower latency for simple operations
- **Control**: Full control over the model and its parameters

## Requirements

- Hostinger VPS with at least:
  - 4GB RAM
  - 2 vCPUs
  - 80GB SSD storage
- Ubuntu 22.04 LTS (recommended)
- <PERSON>er and <PERSON>er Compose

## Setup Instructions

### 1. Prepare Your VPS

1. Log in to your Hostinger VPS via SSH:
   ```bash
   ssh username@your-vps-ip
   ```

2. Copy the `setup-slm-vps.sh` script to your VPS:
   ```bash
   scp setup-slm-vps.sh username@your-vps-ip:~/
   ```

3. Make the script executable and run it:
   ```bash
   chmod +x setup-slm-vps.sh
   ./setup-slm-vps.sh
   ```

The script will:
- Update your system
- Install Docker and Docker Compose
- Set up Ollama with Llama 3 8B model
- Configure the service to start automatically

### 2. Configure Your Backend

1. Update your `.env` file with the SLM configuration:
   ```
   SLM_API_URL=http://your-vps-ip:11434/api/generate
   SLM_MODEL=llama3:8b
   SLM_MAX_TOKENS=1024
   SLM_TEMPERATURE=0.7
   ```

2. Restart your backend service to apply the changes.

### 3. Automatic SLM Connection

The application now includes an automatic SLM connection feature that:

1. Tries to connect directly to the SLM server at the VPS IP address
2. If direct connection fails, tries to connect to a local SLM server
3. Updates the `.env` file with the correct configuration

This means you can simply use:
```bash
pnpm dev
```
or
```bash
pnpm start:prod
```

And the application will automatically configure the SLM connection.

### 4. SSH Tunnel Option

If direct connection to the SLM server is not possible (e.g., due to firewall restrictions), you can still use the SSH tunnel approach:

```bash
# For development mode
pnpm dev:tunnel

# For production mode
pnpm start:prod:tunnel
```

These commands will:
- Set up an SSH tunnel to the Ollama server
- Check if the required model is available
- Update your `.env` file with the correct configuration
- Start the application in the requested mode
- Clean up the tunnel when the application stops

## Usage

The SLM is now integrated into your application through the `SLMService` and `SLMApplicationProcessorService`. These services provide:

1. **Basic Text Generation**: Use `SLMService.generateCompletion()` for simple text generation tasks.
2. **Structured Output**: Use `SLMService.generateStructuredOutput<T>()` to get JSON responses.
3. **Candidate Screening**: Use `SLMApplicationProcessorService.performBasicEvaluation()` for initial candidate screening.

### Example: Initial Candidate Screening

```typescript
// Inject the service
constructor(
  private slmApplicationProcessor: SLMApplicationProcessorService
) {}

// Use it for initial screening
async screenCandidates(jobId: string) {
  const job = await this.jobRepository.findOne({
    where: { id: jobId },
    relations: ['candidates']
  });
  
  const results = await this.slmApplicationProcessor.batchProcessInitialScreening(job);
  return results;
}
```

## Monitoring and Maintenance

- **Check Logs**: `docker logs ollama-slm`
- **Restart Service**: `docker restart ollama-slm`
- **Update Model**: `docker exec ollama-slm ollama pull llama3:8b`
- **Add New Models**: `docker exec ollama-slm ollama pull mistral:7b`

## Troubleshooting

- **Service Not Responding**: Check if Docker is running with `docker ps`
- **High Memory Usage**: Adjust memory limits in `docker-compose.yml`
- **Slow Response Times**: Consider using a smaller model or upgrading your VPS

## Security Considerations

- The Ollama API doesn't include authentication by default
- Secure your VPS with a firewall to restrict access to port 11434
- Consider setting up a reverse proxy with authentication for production use 