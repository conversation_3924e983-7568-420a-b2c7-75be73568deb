alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
- rule: CPU_UTILIZATION
  value: 85
  window: FIVE_MINUTES
- rule: MEM_UTILIZATION  
  value: 85
  window: FIVE_MINUTES
- rule: RESTART_COUNT
  value: 5
  window: FIVE_MINUTES

domains:
- domain: staging.kaleidotalent.com
  type: PRIMARY
  zone: kaleidotalent.com

features:
- buildpack-stack=ubuntu-22

ingress:
  rules:
  - component:
      name: preview-backend
    match:
      path:
        prefix: /

name: preview-backend
region: lon

services:
- name: preview-backend
  environment_slug: node-js
  
  # GitHub configuration
  github:
    branch: master
    deploy_on_push: true
    repo: headStart-Solutions/headstart_backend
  
  # Build configuration with safety
  build_command: |
    echo "🚀 Starting DigitalOcean build with database safety..." &&
    curl -f https://get.pnpm.io/v6.16.js | node - add --global pnpm@10.13.1 &&
    pnpm install --frozen-lockfile &&
    echo "🔧 Building application..." &&
    SKIP_SENTRY_RELEASE=true pnpm build &&
    echo "📋 Build completed successfully"
  
  # Run command using our safety script
  run_command: node scripts/do-app-start.js
  
  # Source directory
  source_dir: /
  
  # Resource configuration
  instance_count: 1
  instance_size_slug: professional-xs
  http_port: 8080
  
  # Health check
  health_check:
    http_path: /health
    initial_delay_seconds: 60
    period_seconds: 30
    timeout_seconds: 10
    success_threshold: 1
    failure_threshold: 3
  
  # Environment variables
  envs:
  # Core configuration
  - key: NODE_ENV
    value: production
    type: GENERAL
  - key: PORT
    value: "8080"
    type: GENERAL
  - key: NODE_OPTIONS
    value: "--max-old-space-size=2048"
    type: GENERAL
  
  # Database configuration
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DB_SSL
    value: "require"
    type: GENERAL
  
  # Migration control
  - key: DO_APP_PLATFORM
    value: "true"
    type: GENERAL
  - key: MIGRATION_TIMEOUT
    value: "300000"
    type: GENERAL
  - key: ALLOW_SAFE_SYNC
    value: "true"
    type: GENERAL
  - key: FORCE_START_ON_SYNC_FAILURE
    value: "false"
    type: GENERAL
  
  # Redis configuration
  - key: REDIS_HOST
    scope: RUN_TIME
    type: SECRET
  - key: REDIS_PORT
    scope: RUN_TIME
    type: SECRET
  - key: REDIS_PASSWORD
    scope: RUN_TIME
    type: SECRET
  
  # Auth0 configuration
  - key: AUTH0_DOMAIN
    scope: RUN_TIME
    type: SECRET
  - key: AUTH0_ISSUER
    scope: RUN_TIME
    type: SECRET
  - key: AUTH0_AUDIENCE
    scope: RUN_TIME
    type: SECRET
  
  # API Keys
  - key: OPENAI_API_KEY
    scope: RUN_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_TIME
    type: SECRET
  - key: SYNTHESIA_API_KEY
    scope: RUN_TIME
    type: SECRET
  - key: RESEND_API_KEY
    scope: RUN_TIME
    type: SECRET
  - key: SENTRY_AUTH_TOKEN
    scope: BUILD_TIME
    type: SECRET
  
  # DigitalOcean Spaces
  - key: DO_SPACES_BUCKET
    scope: RUN_TIME
    type: GENERAL
  - key: DO_SPACES_ENDPOINT
    scope: RUN_TIME
    type: GENERAL
  - key: DO_SPACES_REGION
    scope: RUN_TIME
    type: GENERAL
  - key: DO_SPACES_ACCESS_KEY_ID
    scope: RUN_TIME
    type: SECRET
  - key: DO_SPACES_SECRET_ACCESS_KEY
    scope: RUN_TIME
    type: SECRET
  
  # LinkedIn configuration
  - key: LINKEDIN_CLIENT_ID
    scope: RUN_TIME
    type: SECRET
  - key: LINKEDIN_CLIENT_SECRET
    scope: RUN_TIME
    type: SECRET
  - key: LINKEDIN_ACCESS_TOKEN
    scope: RUN_TIME
    type: SECRET
  - key: LINKEDIN_ORGANIZATION_ID
    scope: RUN_TIME
    type: GENERAL
  
  # Application configuration
  - key: APP_URL
    scope: RUN_TIME
    type: GENERAL
  - key: EMAIL_FROM
    scope: RUN_TIME
    type: GENERAL
  - key: MAX_CANDIDATE_UPLOADS
    value: "50"
    scope: RUN_TIME
    type: GENERAL
  
  # SLM configuration
  - key: SLM_API_URL
    scope: RUN_TIME
    type: GENERAL
  - key: SLM_MODEL
    scope: RUN_TIME
    type: GENERAL
  - key: SLM_MAX_TOKENS
    value: "4096"
    scope: RUN_TIME
    type: GENERAL
  - key: SLM_TEMPERATURE
    value: "0.7"
    scope: RUN_TIME
    type: GENERAL
  - key: SLM_PASSWORD
    scope: RUN_TIME
    type: SECRET

# Jobs for database safety
jobs:
- name: db-pre-deploy-check
  kind: PRE_DEPLOY
  environment_slug: node-js
  source_dir: /
  
  # Build command for the job
  build_command: |
    curl -f https://get.pnpm.io/v6.16.js | node - add --global pnpm@10.13.1 &&
    pnpm install --frozen-lockfile
  
  # Run the pre-deploy check
  run_command: node scripts/do-migration-check.js
  
  # Environment variables for the job
  envs:
  - key: NODE_ENV
    value: production
    type: GENERAL
  - key: DATABASE_URL
    scope: RUN_TIME
    type: SECRET
  - key: ALLOW_DEPLOY_ON_CHECK_FAILURE
    value: "true"
    type: GENERAL