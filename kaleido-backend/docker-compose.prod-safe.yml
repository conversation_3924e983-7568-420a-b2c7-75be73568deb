version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      # Database configuration
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DB_SSL=${DB_SSL}
      # Redis configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      # AWS configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
      # API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - SYNTHESIA_API_KEY=${SYNTHESIA_API_KEY}
      # Auth0 configuration
      - AUTH0_DOMAIN=${AUTH0_DOMAIN}
      - AUTH0_ISSUER=${AUTH0_ISSUER}
      - AUTH0_AUDIENCE=${AUTH0_AUDIENCE}
      # LinkedIn configuration
      - LINKEDIN_CLIENT_ID=${LINKEDIN_CLIENT_ID}
      - LINKEDIN_CLIENT_SECRET=${LINKEDIN_CLIENT_SECRET}
      - LINKEDIN_ACCESS_TOKEN=${LINKEDIN_ACCESS_TOKEN}
      - LINKEDIN_ORGANIZATION_ID=${LINKEDIN_ORGANIZATION_ID}
      # Application configuration
      - APP_URL=${APP_URL}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - EMAIL_FROM=${EMAIL_FROM}
      # DigitalOcean Spaces
      - DO_SPACES_BUCKET=${DO_SPACES_BUCKET}
      - DO_SPACES_ENDPOINT=${DO_SPACES_ENDPOINT}
      - DO_SPACES_REGION=${DO_SPACES_REGION}
      - DO_SPACES_ACCESS_KEY_ID=${DO_SPACES_ACCESS_KEY_ID}
      - DO_SPACES_SECRET_ACCESS_KEY=${DO_SPACES_SECRET_ACCESS_KEY}
      # Monitoring
      - SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}
      # SLM configuration
      - SLM_API_URL=${SLM_API_URL}
      - SLM_MODEL=${SLM_MODEL}
      - SLM_MAX_TOKENS=${SLM_MAX_TOKENS}
      - SLM_TEMPERATURE=${SLM_TEMPERATURE}
      - SLM_PASSWORD=${SLM_PASSWORD}
      # Application limits
      - MAX_CANDIDATE_UPLOADS=${MAX_CANDIDATE_UPLOADS}
      # Migration safety flags
      - SKIP_MIGRATIONS=${SKIP_MIGRATIONS:-false}
      - FORCE_SAFE_SYNC=${FORCE_SAFE_SYNC:-false}
      - MIGRATION_TIMEOUT=${MIGRATION_TIMEOUT:-300000}
    depends_on:
      redis:
        condition: service_healthy
      db-check:
        condition: service_completed_successfully
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    volumes:
      # Mount logs directory for debugging
      - ./logs:/app/logs
      - ./migration-reports:/app/scripts/migration-reports
    networks:
      - kaleido-network

  # Database connectivity check service
  db-check:
    image: postgres:15-alpine
    environment:
      - PGPASSWORD=${DB_PASSWORD}
    command: >
      sh -c "
        echo 'Checking database connectivity...' &&
        until pg_isready -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USERNAME} -d ${DB_NAME}; do
          echo 'Waiting for database...'
          sleep 2
        done &&
        echo 'Database is ready!'
      "
    networks:
      - kaleido-network

  redis:
    image: redis:7-alpine
    command: >
      sh -c "
        if [ -n \"${REDIS_PASSWORD}\" ]; then
          redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
        else
          redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
        fi
      "
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: always
    healthcheck:
      test: >
        sh -c "
          if [ -n \"${REDIS_PASSWORD}\" ]; then
            redis-cli -a \"${REDIS_PASSWORD}\" ping
          else
            redis-cli ping
          fi
        "
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    networks:
      - kaleido-network

  # Optional: Database backup service
  db-backup:
    image: postgres:15-alpine
    environment:
      - PGPASSWORD=${DB_PASSWORD}
    volumes:
      - ./backups:/backups
    command: >
      sh -c "
        while true; do
          echo 'Running database backup...' &&
          pg_dump -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USERNAME} -d ${DB_NAME} > /backups/backup-$$(date +%Y%m%d-%H%M%S).sql &&
          echo 'Backup completed' &&
          # Keep only last 7 days of backups
          find /backups -name 'backup-*.sql' -mtime +7 -delete &&
          # Sleep for 24 hours
          sleep 86400
        done
      "
    restart: always
    networks:
      - kaleido-network
    profiles:
      - with-backup

volumes:
  redis_data:
  
networks:
  kaleido-network:
    driver: bridge