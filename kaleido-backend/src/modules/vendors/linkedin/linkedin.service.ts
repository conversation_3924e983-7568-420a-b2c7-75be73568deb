import axios from 'axios';

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import {
  PostContent,
  PostResult,
  SocialMediaPlatform,
} from '../../company/types/social-media-connector.types';
import { Job } from '../../job/entities/job.entity';

@Injectable()
export class LinkedInService {
  private readonly logger = new Logger(LinkedInService.name);
  private readonly apiUrl = 'https://api.linkedin.com/v2';
  private accessToken: string | null = null;

  constructor(private readonly configService: ConfigService) {}

  private async getUserInfo(accessToken: string): Promise<any> {
    try {
      const response = await axios.get('https://api.linkedin.com/v2/userinfo', {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get LinkedIn user info:', error);
      throw new Error('Failed to get LinkedIn user information');
    }
  }

  private async getAccessToken(): Promise<string> {
    if (this.accessToken) return this.accessToken;

    try {
      const envToken = this.configService.get('LINKEDIN_ACCESS_TOKEN');
      if (envToken) {
        this.accessToken = envToken;
        return envToken;
      }

      throw new Error('No access token found. User needs to authorize via LinkedIn OAuth flow.');
    } catch (error) {
      this.logger.error('Failed to get LinkedIn access token', error);
      throw new Error('Failed to authenticate with LinkedIn');
    }
  }

  async createPost(job: Job) {
    try {
      const accessToken = await this.getAccessToken();

      // Simple post structure for w_member_social scope
      const post = {
        author: `urn:li:person:hq9cN3O37q`, // Your LinkedIn member ID
        lifecycleState: 'PUBLISHED',
        specificContent: {
          'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
              text: this.createPostContent(job),
            },
            shareMediaCategory: 'NONE',
          },
        },
        visibility: {
          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC',
        },
      };

      // this.logger.debug('LinkedIn Post Request:', {
      //   url: `${this.apiUrl}/ugcPosts`,
      //   body: JSON.stringify(post, null, 2),
      // });

      const response = await axios.post(`${this.apiUrl}/ugcPosts`, post, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0',
          'Content-Type': 'application/json',
        },
      });

      return {
        status: 'posted',
        platformJobId: response.headers['x-restli-id'],
        url: `https://www.linkedin.com/feed/update/${response.headers['x-restli-id']}`,
      };
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        this.logger.error(
          `Failed to post to LinkedIn: ${error.response?.data?.message || error.message}`,
          error.response?.data,
        );
        throw new Error(
          `LinkedIn posting failed: ${error.response?.data?.message || error.message}`,
        );
      }
      throw new Error('LinkedIn posting failed: Unknown error');
    }
  }

  /**
   * Enhanced posting method using stored connector
   */
  async createPostWithConnector(
    accessToken: string,
    userProfileId: string,
    content: PostContent,
  ): Promise<PostResult> {
    try {
      // First, get the user's profile ID from the access token
      const userInfo = await this.getUserInfo(accessToken);

      let post: any;

      // For now, we'll create clean text posts without image URLs
      // To properly support native images, we would need to upload to LinkedIn's Assets API first
      const postText = content.text;

      // Handle different post types based on content
      if (content.linkUrl) {
        // Article/link post
        post = {
          author: `urn:li:person:${userInfo.sub}`,
          lifecycleState: 'PUBLISHED',
          specificContent: {
            'com.linkedin.ugc.ShareContent': {
              shareCommentary: {
                text: postText,
              },
              shareMediaCategory: 'ARTICLE',
              media: [
                {
                  status: 'READY',
                  description: {
                    text: content.linkDescription || '',
                  },
                  originalUrl: content.linkUrl,
                  title: {
                    text: content.linkTitle || '',
                  },
                },
              ],
            },
          },
          visibility: {
            'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC',
          },
        };
      } else {
        // Text-only post
        post = {
          author: `urn:li:person:${userInfo.sub}`,
          lifecycleState: 'PUBLISHED',
          specificContent: {
            'com.linkedin.ugc.ShareContent': {
              shareCommentary: {
                text: postText,
              },
              shareMediaCategory: 'NONE',
            },
          },
          visibility: {
            'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC',
          },
        };
      }

      const response = await axios.post(`${this.apiUrl}/ugcPosts`, post, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'X-Restli-Protocol-Version': '2.0.0',
          'Content-Type': 'application/json',
        },
      });

      const postId = response.headers['x-restli-id'];
      return {
        platform: SocialMediaPlatform.LINKEDIN,
        status: 'success',
        postId,
        postUrl: `https://www.linkedin.com/feed/update/${postId}`,
        timestamp: new Date(),
      };
    } catch (error: unknown) {
      this.logger.error('LinkedIn posting failed:', error);
      return {
        platform: SocialMediaPlatform.LINKEDIN,
        status: 'failed',
        error: axios.isAxiosError(error)
          ? error.response?.data?.message || error.message
          : 'Unknown error',
        timestamp: new Date(),
      };
    }
  }

  private createPostContent(job: Job): string {
    return `
We're hiring! 🚀

${job.companyName} is looking for a ${job.jobType}

💼 What we offer:
${
  job.benefits
    ?.slice(0, 3)
    .map((benefit) => `• ${benefit}`)
    .join('\n') || 'Competitive package'
}

Interested? Learn more and apply here:
${this.configService.get('APP_URL')}/open-jobs/${job.id}

#hiring #jobs #career ${
      job.skills
        ?.slice(0, 3)
        .map((skill) => `#${skill.replace(/\s+/g, '')}`)
        .join(' ') || ''
    }
    `.trim();
  }
}
