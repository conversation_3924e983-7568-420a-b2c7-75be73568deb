import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { EmailService } from '../email/email.service';
import { AssessmentQuestionDto, AssessmentQuestionsDto } from './dto/assessment-questions.dto';
import {
  AssessmentInsightDto,
  AssessmentRecommendationDto,
  AssessmentReportDto,
} from './dto/assessment-report.dto';
import { AssessmentResponseDto } from './dto/assessment-response.dto';
import { CreateAssessmentDto } from './dto/create-assessment.dto';
import { UpdateAssessmentDto } from './dto/update-assessment.dto';
import {
  AssessmentAnswers,
  AssessmentScoring,
  RecruitmentAssessment,
} from './entities/recruitment-assessment.entity';

@Injectable()
export class AssessmentService {
  private readonly logger = new Logger(AssessmentService.name);

  constructor(
    @InjectRepository(RecruitmentAssessment)
    private readonly assessmentRepository: Repository<RecruitmentAssessment>,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Assessment questions data
   */
  private readonly assessmentQuestions: AssessmentQuestionDto[] = [
    {
      id: 'presentationMethod',
      label: 'How do you typically present job opportunities to attract top talent?',
      required: true,
      type: 'radio',
      icon: 'Target',
      options: [
        { value: 'text-only', label: 'A. Text-only job boards', points: 1 },
        { value: 'basic-page', label: 'B. Basic company career page', points: 2 },
        { value: 'engaging-text', label: 'C. Engaging text with some visuals', points: 3 },
        {
          value: 'rich-media',
          label: 'D. Rich media (e.g., video job descriptions) and interactive content',
          points: 4,
        },
      ],
    },
    {
      id: 'timeToFill',
      label:
        'What is your average time-to-fill for critical roles from job posting to offer acceptance?',
      required: true,
      type: 'radio',
      icon: 'Clock',
      options: [
        { value: '60-plus', label: 'A. 60+ days', points: 1 },
        { value: '45-59', label: 'B. 45-59 days', points: 2 },
        { value: '30-44', label: 'C. 30-44 days', points: 3 },
        { value: 'under-30', label: 'D. Under 30 days', points: 4 },
      ],
    },
    {
      id: 'culturalFit',
      label: "How do you primarily assess a candidate's cultural fit and personality?",
      required: true,
      type: 'radio',
      icon: 'Users',
      options: [
        { value: 'informal', label: 'A. Informal interviews only', points: 1 },
        { value: 'reference', label: 'B. Reference checks/out feeling', points: 2 },
        { value: 'behavioral', label: 'C. Structured behavioral interviews', points: 3 },
        {
          value: 'data-driven',
          label:
            'D. Data-driven tools, e.g., video analysis, personality insights, or structured assessments',
          points: 4,
        },
      ],
    },
    {
      id: 'turnoverReason',
      label: 'What is the most common reason for new hires leaving within their first six months?',
      required: true,
      type: 'radio',
      icon: 'TrendingUp',
      options: [
        { value: 'generic', label: 'A. Often generic, low engagement', points: 1 },
        { value: 'standard', label: 'B. Standard templates, some customization', points: 2 },
        { value: 'tailored', label: 'C. Tailored, but time-consuming to create', points: 3 },
        {
          value: 'ai-driven',
          label: 'D. AI-driven, engaging, and optimized for reach/future skills',
          points: 4,
        },
      ],
    },
    {
      id: 'applicationExperience',
      label: 'How would you describe your typical candidate application experience?',
      required: true,
      type: 'radio',
      icon: 'MessageSquare',
      options: [
        { value: 'lengthy', label: 'A. Lengthy forms, opaque process', points: 1 },
        { value: 'standard-online', label: 'B. Standard online forms, some updates', points: 2 },
        { value: 'streamlined', label: 'C. Streamlined, but manual tracking', points: 3 },
        {
          value: 'intuitive',
          label: 'D. Intuitive, transparent, with personalized dashboards and digital CV wallets',
          points: 4,
        },
      ],
    },
    {
      id: 'candidateIdentification',
      label: 'How do you primarily identify and match candidates to open roles?',
      required: true,
      type: 'radio',
      options: [
        { value: 'manual-cv', label: 'A. Manual CV screening, keyword search', points: 1 },
        { value: 'basic-ats', label: 'B. Basic ATS filtering', points: 2 },
        { value: 'automated', label: 'C. Some automated matching, limited insights', points: 3 },
        {
          value: 'ai-powered',
          label: 'D. AI-powered contextual matching and ranking with insights beyond CVs',
          points: 4,
        },
      ],
    },
    {
      id: 'candidateEngagement',
      label:
        'Do you gain insights into candidate engagement, communication style, or potential fit before a formal interview?',
      required: true,
      type: 'radio',
      options: [
        { value: 'no-interviews', label: 'A. No, only during interviews', points: 1 },
        { value: 'limited-cover', label: 'B. Limited, through cover letters', points: 2 },
        {
          value: 'some-prescreening',
          label: 'C. Some, through pre-screening questions',
          points: 3,
        },
        {
          value: 'yes-video',
          label: 'D. Yes, through video introductions and AI-driven analysis',
          points: 4,
        },
      ],
    },
    {
      id: 'atsDescription',
      label:
        'How would you describe your current Applicant Tracking System (ATS) or recruitment tools?',
      required: true,
      type: 'radio',
      options: [
        { value: 'basic-manual', label: 'A. Basic, manual, outdated', points: 1 },
        { value: 'standard-ats', label: 'B. Standard ATS, lacks modern features', points: 2 },
        { value: 'modern-ats', label: 'C. Modern ATS, but complex/expensive', points: 3 },
        {
          value: 'future-ready',
          label: 'D. Future-ready, adaptable, affordable, and AI-powered',
          points: 4,
        },
      ],
    },
    {
      id: 'externalPartnerships',
      label:
        'Do you leverage external partnerships or an ecosystem to broaden your talent reach and insights?',
      required: true,
      type: 'radio',
      options: [
        { value: 'rarely', label: 'A. Rarely or not at all', points: 1 },
        { value: 'ad-hoc', label: 'B. Ad-hoc partnerships', points: 2 },
        { value: 'strategic', label: 'C. Some strategic partnerships', points: 3 },
        {
          value: 'actively-connected',
          label: 'D. Actively connected to a broad talent ecosystem for sourcing and insights',
          points: 4,
        },
      ],
    },
  ];

  /**
   * Score mapping for assessment answers
   */
  private readonly scoreMapping = {
    presentationMethod: {
      'text-only': 1,
      'basic-page': 2,
      'engaging-text': 3,
      'rich-media': 4,
    },
    timeToFill: {
      '60-plus': 1,
      '45-59': 2,
      '30-44': 3,
      'under-30': 4,
    },
    culturalFit: {
      informal: 1,
      reference: 2,
      behavioral: 3,
      'data-driven': 4,
    },
    turnoverReason: {
      generic: 1,
      standard: 2,
      tailored: 3,
      'ai-driven': 4,
    },
    applicationExperience: {
      lengthy: 1,
      'standard-online': 2,
      streamlined: 3,
      intuitive: 4,
    },
    candidateIdentification: {
      'manual-cv': 1,
      'basic-ats': 2,
      automated: 3,
      'ai-powered': 4,
    },
    candidateEngagement: {
      'no-interviews': 1,
      'limited-cover': 2,
      'some-prescreening': 3,
      'yes-video': 4,
    },
    atsDescription: {
      'basic-manual': 1,
      'standard-ats': 2,
      'modern-ats': 3,
      'future-ready': 4,
    },
    externalPartnerships: {
      rarely: 1,
      'ad-hoc': 2,
      strategic: 3,
      'actively-connected': 4,
    },
  };

  /**
   * Calculate scoring based on answers
   */
  private calculateScoring(answers: AssessmentAnswers): AssessmentScoring {
    const scoring: AssessmentScoring = {
      presentationMethod:
        (this.scoreMapping.presentationMethod as any)[answers.presentationMethod] || 0,
      timeToFill: (this.scoreMapping.timeToFill as any)[answers.timeToFill] || 0,
      culturalFit: (this.scoreMapping.culturalFit as any)[answers.culturalFit] || 0,
      turnoverReason: (this.scoreMapping.turnoverReason as any)[answers.turnoverReason] || 0,
      applicationExperience:
        (this.scoreMapping.applicationExperience as any)[answers.applicationExperience] || 0,
      candidateIdentification:
        (this.scoreMapping.candidateIdentification as any)[answers.candidateIdentification] || 0,
      candidateEngagement:
        (this.scoreMapping.candidateEngagement as any)[answers.candidateEngagement] || 0,
      atsDescription: (this.scoreMapping.atsDescription as any)[answers.atsDescription] || 0,
      externalPartnerships:
        (this.scoreMapping.externalPartnerships as any)[answers.externalPartnerships] || 0,
      totalScore: 0,
    };

    // Calculate total score
    scoring.totalScore = Object.values(scoring).reduce((sum, score) => {
      return typeof score === 'number' ? sum + score : sum;
    }, 0);

    return scoring;
  }

  /**
   * Get assessment questions
   */
  async getQuestions(): Promise<AssessmentQuestionsDto> {
    return {
      questions: this.assessmentQuestions,
    };
  }

  /**
   * Create a new assessment
   */
  async create(createAssessmentDto: CreateAssessmentDto): Promise<AssessmentResponseDto> {
    try {
      const { answers, ...personalInfo } = createAssessmentDto;

      // Calculate scoring
      const scoring = this.calculateScoring(answers);

      // Create assessment entity
      const assessment = this.assessmentRepository.create({
        ...personalInfo,
        answers,
        scoring,
        totalScore: scoring.totalScore,
        maturityLevel: this.getMaturityLevel(scoring.totalScore),
        isCompleted: true,
      });

      const savedAssessment = await this.assessmentRepository.save(assessment);

      this.logger.log(
        `Created assessment ${savedAssessment.id} for ${savedAssessment.firstName} ${savedAssessment.lastName} with score ${savedAssessment.totalScore}`,
      );

      // Send email with assessment results
      try {
        const fullName = `${savedAssessment.firstName} ${savedAssessment.lastName}`;
        const maturityLevel = savedAssessment.getMaturityLevel();

        await this.emailService.sendRecruitmentAssessmentResultEmail(
          savedAssessment.email,
          fullName,
          savedAssessment.company,
          savedAssessment.position,
          savedAssessment.totalScore,
          maturityLevel,
          savedAssessment.getWhatYoureDoing(),
          savedAssessment.getWhereYouMightBeLosingTalent(),
          savedAssessment.getStrategicRecommendations(),
          savedAssessment.getSuggestedNextStep(),
        );

        this.logger.log(`Sent assessment result email to ${savedAssessment.email}`);
      } catch (error) {
        this.logger.error('Failed to send assessment result email', error);
        // Don't fail the assessment creation if email fails
      }

      return this.mapToResponseDto(savedAssessment);
    } catch (error) {
      this.logger.error('Failed to create assessment', error);
      throw error;
    }
  }

  /**
   * Get all assessments for an email
   */
  async findByEmail(email: string): Promise<AssessmentResponseDto[]> {
    const assessments = await this.assessmentRepository.find({
      where: { email },
      order: { createdAt: 'DESC' },
    });

    return assessments.map((assessment) => this.mapToResponseDto(assessment));
  }

  /**
   * Get assessment by ID
   */
  async findOne(id: string): Promise<AssessmentResponseDto> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    return this.mapToResponseDto(assessment);
  }

  /**
   * Update an assessment
   */
  async update(
    id: string,
    updateAssessmentDto: UpdateAssessmentDto,
  ): Promise<AssessmentResponseDto> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    // If answers are being updated, recalculate scoring
    if (updateAssessmentDto.answers) {
      const scoring = this.calculateScoring(updateAssessmentDto.answers);
      updateAssessmentDto = {
        ...updateAssessmentDto,
        scoring,
        totalScore: scoring.totalScore,
        maturityLevel: this.getMaturityLevel(scoring.totalScore),
      } as any;
    }

    Object.assign(assessment, updateAssessmentDto);
    const updatedAssessment = await this.assessmentRepository.save(assessment);

    this.logger.log(`Updated assessment ${updatedAssessment.id}`);

    return this.mapToResponseDto(updatedAssessment);
  }

  /**
   * Delete an assessment
   */
  async remove(id: string): Promise<void> {
    const result = await this.assessmentRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    this.logger.log(`Deleted assessment ${id}`);
  }

  /**
   * Get maturity level based on total score
   */
  private getMaturityLevel(totalScore: number): string {
    if (totalScore >= 34) return 'Transformative';
    if (totalScore >= 26) return 'Optimized';
    if (totalScore >= 18) return 'Developing';
    return 'Foundational';
  }

  /**
   * Get maturity description based on level
   */
  private getMaturityDescription(level: string): string {
    switch (level) {
      case 'Transformative':
        return "You're in the top 10% of companies when it comes to recruitment efficiency.";
      case 'Optimized':
        return "You're performing well but missing opportunities for optimization.";
      case 'Developing':
        return "You're facing common challenges that are costing you time and talent.";
      case 'Foundational':
        return 'Your recruitment process has significant issues that are seriously impacting your business.';
      default:
        return 'Assessment incomplete.';
    }
  }

  /**
   * Map entity to response DTO
   */
  private mapToResponseDto(assessment: RecruitmentAssessment): AssessmentResponseDto {
    return {
      id: assessment.id,
      firstName: assessment.firstName,
      lastName: assessment.lastName,
      email: assessment.email,
      phoneNumber: assessment.phoneNumber,
      position: assessment.position,
      company: assessment.company,
      answers: assessment.answers,
      scoring: assessment.scoring,
      totalScore: assessment.totalScore,
      maturityLevel: assessment.maturityLevel,
      maturityDescription: this.getMaturityDescription(assessment.maturityLevel),
      isCompleted: assessment.isCompleted,
      createdAt: assessment.createdAt,
      updatedAt: assessment.updatedAt,
    };
  }

  /**
   * Generate full assessment report with insights and recommendations
   */
  async generateFullReport(id: string): Promise<AssessmentReportDto> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    // Generate insights for each assessment area
    const insights = this.generateInsights(assessment.answers, assessment.scoring);

    // Generate personalized recommendations
    const recommendations = this.generateRecommendations(assessment.answers, assessment.scoring);

    // Calculate percentage score
    const maxPossibleScore = 40; // 9 questions × 4 points max + 4 bonus points
    const percentageScore = Math.round((assessment.totalScore / maxPossibleScore) * 100);

    const report: AssessmentReportDto = {
      id: assessment.id,
      firstName: assessment.firstName,
      lastName: assessment.lastName,
      email: assessment.email,
      company: assessment.company,
      position: assessment.position,
      totalScore: assessment.totalScore,
      maxPossibleScore,
      percentageScore,
      maturityLevel: assessment.maturityLevel,
      maturityDescription: this.getMaturityDescription(assessment.maturityLevel),
      insights,
      recommendations,
      completedAt: assessment.createdAt.toISOString(),
      emailSent: true, // Assume email was sent when assessment was created
      whatYoureDoing: assessment.getWhatYoureDoing(),
      whereYouMightBeLosingTalent: assessment.getWhereYouMightBeLosingTalent(),
      strategicRecommendations: assessment.getStrategicRecommendations(),
      suggestedNextStep: assessment.getSuggestedNextStep(),
    };

    this.logger.log(`Generated full report for assessment ${id}`);
    return report;
  }

  /**
   * Generate insights for each assessment area
   */
  private generateInsights(
    answers: AssessmentAnswers,
    scoring: AssessmentScoring,
  ): AssessmentInsightDto[] {
    const insights: AssessmentInsightDto[] = [];

    // Job Presentation insight
    insights.push({
      area: 'Job Presentation',
      score: scoring.presentationMethod,
      maxScore: 4,
      performance: this.getPerformanceLevel(scoring.presentationMethod, 4),
      feedback: this.getJobPresentationFeedback(
        answers.presentationMethod,
        scoring.presentationMethod,
      ),
    });

    // Time to Fill insight
    insights.push({
      area: 'Time to Fill',
      score: scoring.timeToFill,
      maxScore: 4,
      performance: this.getPerformanceLevel(scoring.timeToFill, 4),
      feedback: this.getTimeToFillFeedback(answers.timeToFill, scoring.timeToFill),
    });

    // Cultural Fit insight
    insights.push({
      area: 'Cultural Fit Assessment',
      score: scoring.culturalFit,
      maxScore: 4,
      performance: this.getPerformanceLevel(scoring.culturalFit, 4),
      feedback: this.getCulturalFitFeedback(answers.culturalFit, scoring.culturalFit),
    });

    // Add more insights for other areas...
    insights.push({
      area: 'Candidate Experience',
      score: scoring.applicationExperience,
      maxScore: 4,
      performance: this.getPerformanceLevel(scoring.applicationExperience, 4),
      feedback: this.getCandidateExperienceFeedback(
        answers.applicationExperience,
        scoring.applicationExperience,
      ),
    });

    return insights;
  }

  /**
   * Generate personalized recommendations
   */
  private generateRecommendations(
    answers: AssessmentAnswers,
    scoring: AssessmentScoring,
  ): AssessmentRecommendationDto[] {
    const recommendations: AssessmentRecommendationDto[] = [];

    // Job Presentation recommendations
    if (scoring.presentationMethod < 3) {
      recommendations.push({
        title: 'Enhance Job Presentation',
        description:
          'Consider using rich media content like video job descriptions to attract top talent and stand out from competitors.',
        priority: 'high',
        category: 'presentation',
      });
    }

    // Time to Fill recommendations
    if (scoring.timeToFill < 3) {
      recommendations.push({
        title: 'Optimize Recruitment Speed',
        description:
          'Streamline your hiring process to reduce time-to-fill. Consider implementing automated screening and faster decision-making protocols.',
        priority: 'high',
        category: 'efficiency',
      });
    }

    // Cultural Fit recommendations
    if (scoring.culturalFit < 3) {
      recommendations.push({
        title: 'Improve Cultural Fit Assessment',
        description:
          'Develop structured cultural fit evaluation methods to ensure better long-term employee retention and satisfaction.',
        priority: 'medium',
        category: 'assessment',
      });
    }

    // Candidate Experience recommendations
    if (scoring.applicationExperience < 3) {
      recommendations.push({
        title: 'Enhance Candidate Experience',
        description:
          'Simplify your application process and provide clear communication throughout the recruitment journey.',
        priority: 'medium',
        category: 'experience',
      });
    }

    return recommendations;
  }

  /**
   * Get performance level based on score
   */
  private getPerformanceLevel(
    score: number,
    maxScore: number,
  ): 'Excellent' | 'Good' | 'Needs Improvement' | 'Poor' {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 90) return 'Excellent';
    if (percentage >= 70) return 'Good';
    if (percentage >= 50) return 'Needs Improvement';
    return 'Poor';
  }

  /**
   * Get specific feedback for job presentation
   */
  private getJobPresentationFeedback(answer: string, score: number): string {
    switch (answer) {
      case 'text-only':
        return 'You are using basic text-only job postings. Consider adding visual elements and rich media to make your opportunities more engaging.';
      case 'basic-page':
        return 'You have a basic career page setup. Adding more interactive content and company culture showcases could improve candidate attraction.';
      case 'engaging-text':
        return 'You are using engaging content with visuals. Consider incorporating video content and interactive elements for even better results.';
      case 'rich-media':
        return 'Excellent! You are using rich media content effectively to attract top talent and differentiate your opportunities.';
      default:
        return 'Your job presentation approach could benefit from more engaging and visual content.';
    }
  }

  /**
   * Get specific feedback for time to fill
   */
  private getTimeToFillFeedback(answer: string, score: number): string {
    switch (answer) {
      case '60-plus':
        return 'Your time-to-fill is quite long. Consider streamlining your process and implementing faster decision-making protocols.';
      case '45-60':
        return 'Your time-to-fill is above average. There are opportunities to optimize your recruitment process for faster results.';
      case '30-45':
        return 'Good time-to-fill performance. Fine-tuning your process could help you compete for top talent more effectively.';
      case 'under-30':
        return 'Excellent time-to-fill! You are moving quickly to secure top talent before competitors.';
      default:
        return 'Focus on optimizing your recruitment timeline to improve candidate experience and secure top talent.';
    }
  }

  /**
   * Get specific feedback for cultural fit
   */
  private getCulturalFitFeedback(answer: string, score: number): string {
    switch (answer) {
      case 'informal':
        return 'You rely on informal Video Intro Assessment. Consider developing more structured evaluation methods for better consistency.';
      case 'basic-questions':
        return 'You use basic cultural questions. Expanding your assessment toolkit could provide deeper insights into candidate fit.';
      case 'structured-interview':
        return 'Good use of structured interviews. Consider adding behavioral assessments for more comprehensive evaluation.';
      case 'comprehensive':
        return 'Excellent! Your comprehensive Video Intro Assessment approach helps ensure long-term employee success and retention.';
      default:
        return 'Developing a more structured approach to Video Intro Assessment could improve your hiring outcomes.';
    }
  }

  /**
   * Get specific feedback for candidate experience
   */
  private getCandidateExperienceFeedback(answer: string, score: number): string {
    switch (answer) {
      case 'basic-form':
        return 'Your application process is quite basic. Consider simplifying and adding more engaging elements to improve candidate experience.';
      case 'standard-process':
        return 'You have a standard application process. Adding more personalized touches could help you stand out to candidates.';
      case 'streamlined':
        return 'Good streamlined process. Consider adding more interactive elements and clearer communication touchpoints.';
      case 'exceptional':
        return 'Excellent candidate experience! Your thoughtful approach helps attract and retain top talent throughout the process.';
      default:
        return 'Focus on creating a more engaging and streamlined application experience for candidates.';
    }
  }
}
