import { ApiProperty } from '@nestjs/swagger';

export class AssessmentRecommendationDto {
  @ApiProperty({ example: 'Improve Job Presentation' })
  title!: string;

  @ApiProperty({ example: 'Consider using rich media content to attract top talent' })
  description!: string;

  @ApiProperty({ example: 'high' })
  priority!: 'high' | 'medium' | 'low';

  @ApiProperty({ example: 'presentation' })
  category!: string;
}

export class AssessmentInsightDto {
  @ApiProperty({ example: 'Job Presentation' })
  area!: string;

  @ApiProperty({ example: 3 })
  score!: number;

  @ApiProperty({ example: 4 })
  maxScore!: number;

  @ApiProperty({ example: 'Good' })
  performance!: 'Excellent' | 'Good' | 'Needs Improvement' | 'Poor';

  @ApiProperty({ example: 'You are using engaging content but could benefit from rich media' })
  feedback!: string;
}

export class AssessmentReportDto {
  @ApiProperty({ example: 'uuid' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;

  @ApiProperty({ example: '<EMAIL>' })
  email!: string;

  @ApiProperty({ example: 'Acme Corp' })
  company!: string;

  @ApiProperty({ example: 'HR Manager' })
  position!: string;

  @ApiProperty({ example: 27 })
  totalScore!: number;

  @ApiProperty({ example: 40 })
  maxPossibleScore!: number;

  @ApiProperty({ example: 67.5 })
  percentageScore!: number;

  @ApiProperty({ example: 'Developing' })
  maturityLevel!: string;

  @ApiProperty({
    example: 'Your recruitment process shows good foundation with room for optimization',
  })
  maturityDescription!: string;

  @ApiProperty({ type: [AssessmentInsightDto] })
  insights!: AssessmentInsightDto[];

  @ApiProperty({ type: [AssessmentRecommendationDto] })
  recommendations!: AssessmentRecommendationDto[];

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  completedAt!: string;

  @ApiProperty({ example: true })
  emailSent!: boolean;

  @ApiProperty({
    example: ['Strong Video Intro Assessment', 'Streamlined processes'],
    description: 'What the company is doing well in recruitment',
  })
  whatYoureDoing!: string[];

  @ApiProperty({
    example: ['Qualified candidates lost to faster processes', 'Manual screening inefficiencies'],
    description: 'Areas where the company might be losing talent',
  })
  whereYouMightBeLosingTalent!: string[];

  @ApiProperty({
    example: ['Implement AI-powered screening', 'Enhanced job descriptions'],
    description: 'Strategic recommendations for improvement',
  })
  strategicRecommendations!: string[];

  @ApiProperty({
    example: 'Join our Early Access program to see how AI can amplify your existing strengths',
    description: 'Suggested next step based on maturity level',
  })
  suggestedNextStep!: string;
}
