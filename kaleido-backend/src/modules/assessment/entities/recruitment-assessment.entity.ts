import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { ApiProperty } from '@nestjs/swagger';

export interface AssessmentAnswers {
  presentationMethod: string;
  timeToFill: string;
  culturalFit: string;
  turnoverReason: string;
  applicationExperience: string;
  candidateIdentification: string;
  candidateEngagement: string;
  atsDescription: string;
  externalPartnerships: string;
}

export interface AssessmentScoring {
  presentationMethod: number;
  timeToFill: number;
  culturalFit: number;
  turnoverReason: number;
  applicationExperience: number;
  candidateIdentification: number;
  candidateEngagement: number;
  atsDescription: number;
  externalPartnerships: number;
  totalScore: number;
}

@Entity('recruitment_assessments')
@Index('idx_recruitment_assessments_email', ['email'])
@Index('idx_recruitment_assessments_created_at', ['createdAt'])
@Index('idx_recruitment_assessments_total_score', ['totalScore'])
export class RecruitmentAssessment {
  @ApiProperty({ example: 'uuid' })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  // Personal Information
  @ApiProperty({ example: 'John' })
  @Column({ name: 'first_name' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  @Column({ name: 'last_name' })
  lastName!: string;

  @ApiProperty({ example: '<EMAIL>' })
  @Column({ name: 'email' })
  email!: string;

  @ApiProperty({ example: '+1234567890', required: false })
  @Column({ name: 'phone_number', nullable: true })
  phoneNumber?: string;

  @ApiProperty({ example: 'HR Manager' })
  @Column()
  position!: string;

  @ApiProperty({ example: 'Acme Corp' })
  @Column()
  company!: string;

  // Assessment Answers (stored as JSON)
  @ApiProperty({
    example: {
      presentationMethod: 'text-only',
      timeToFill: '60-plus',
      culturalFit: 'informal',
    },
  })
  @Column('jsonb')
  answers!: AssessmentAnswers;

  // Assessment Scoring (stored as JSON)
  @ApiProperty({
    example: {
      presentationMethod: 1,
      timeToFill: 1,
      culturalFit: 1,
      totalScore: 9,
    },
  })
  @Column('jsonb')
  scoring!: AssessmentScoring;

  // Total Score for easy querying
  @ApiProperty({ example: 27 })
  @Column({ name: 'total_score', type: 'integer' })
  totalScore!: number;

  // Maturity Level based on score
  @ApiProperty({ example: 'Emerging' })
  @Column({ name: 'maturity_level', nullable: true })
  maturityLevel!: string;

  // Assessment completion status
  @ApiProperty({ example: true })
  @Column({ name: 'is_completed', default: true })
  isCompleted!: boolean;

  @ApiProperty()
  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @ApiProperty()
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;

  // Helper method to determine maturity level
  getMaturityLevel(): 'Transformative' | 'Optimized' | 'Developing' | 'Foundational' {
    if (this.totalScore >= 34) return 'Transformative';
    if (this.totalScore >= 26) return 'Optimized';
    if (this.totalScore >= 18) return 'Developing';
    return 'Foundational';
  }

  // Helper method to get maturity description
  getMaturityDescription(): string {
    const level = this.getMaturityLevel();

    switch (level) {
      case 'Transformative':
        return "You're in the top 10% of companies when it comes to recruitment efficiency.";
      case 'Optimized':
        return "You're performing well but missing opportunities for optimization.";
      case 'Developing':
        return "You're facing common challenges that are costing you time and talent.";
      case 'Foundational':
        return 'Your recruitment process has significant issues that are seriously impacting your business.';
      default:
        return 'Assessment incomplete.';
    }
  }

  // Helper method to get what they're doing well
  getWhatYoureDoing(): string[] {
    const level = this.getMaturityLevel();

    switch (level) {
      case 'Transformative':
        return [
          'Exceptional candidate quality and application volume management',
          'Streamlined processes that save time and money',
          'Strong Video Intro Assessment and low turnover rates',
          'Integrated technology stack that enhances efficiency',
          'Future-focused approach to skills and talent planning',
        ];
      case 'Optimized':
        return [
          'Good foundation with reasonable time-to-fill',
          'Decent candidate quality and manageable processes',
          'Some technology integration in place',
          'Awareness of cultural fit importance',
        ];
      case 'Developing':
        return [
          'Basic processes in place',
          'Some awareness of recruitment metrics',
          'Effort toward candidate experience',
          'Recognition that improvement is needed',
        ];
      case 'Foundational':
        return [
          "You're aware enough to take this assessment",
          'Basic hiring processes exist',
          'Some candidates are still being hired',
          'Recognition that change is needed',
        ];
    }
  }

  // Helper method to get where they might be losing talent
  getWhereYouMightBeLosingTalent(): string[] {
    const level = this.getMaturityLevel();

    switch (level) {
      case 'Transformative':
        return [
          'Even rockstars have room for innovation',
          'Potential blind spots in emerging recruitment trends',
          'Opportunity to leverage AI for even better matching',
          'Video-first approach could enhance candidate engagement',
        ];
      case 'Optimized':
        return [
          'Qualified candidates lost to faster, more engaging processes',
          'Time wasted on manual screening could be automated',
          'Job descriptions may not attract ideal candidates',
          'Missing data-driven insights for optimization',
        ];
      case 'Developing':
        return [
          'Long time-to-fill means top candidates go elsewhere',
          'Unqualified applications waste significant time',
          'Poor Video Intro Assessment leads to early turnover',
          'Fragmented tools create inefficiencies',
        ];
      case 'Foundational':
        return [
          'Extremely long hiring cycles lose 90% of top candidates',
          'Poor job descriptions attract wrong candidates',
          'Manual processes waste 60%+ of your time',
          'High turnover suggests fundamental matching problems',
          'Lack of Video Intro Assessment creates retention issues',
        ];
    }
  }

  // Helper method to get strategic recommendations
  getStrategicRecommendations(): string[] {
    const level = this.getMaturityLevel();

    switch (level) {
      case 'Transformative':
        return [
          'Enhance with AI: Leverage AI-powered matching to identify hidden gems',
          'Video JD: Add video job descriptions for higher engagement',
          'Video Introduction: Get to know your talents faster and cut through the noise',
        ];
      case 'Optimized':
        return [
          'Smart Automation: Implement AI-powered screening to focus on qualified candidates',
          'Enhanced Job Descriptions: Use our tool to create compelling, targeted descriptions',
          'Video Integration: Add video elements to stand out from competitors',
          'Analytics Dashboard: Track and optimize your recruitment ROI',
        ];
      case 'Developing':
        return [
          'Process Overhaul: Implement integrated recruitment platform',
          'AI-Powered Matching: Reduce screening time',
          'Cultural Fit Tools: Use our assessment features to improve retention',
          'Quick Wins: Start with job description optimization for immediate impact',
        ];
      case 'Foundational':
        return [
          'Urgent Process Redesign: Complete recruitment transformation needed',
          'AI Implementation: Automate screening and matching immediately',
          'Professional Development: Invest in modern recruitment training',
          'Technology Upgrade: Move from manual to integrated AI platform',
        ];
    }
  }

  // Helper method to get suggested next step
  getSuggestedNextStep(): string {
    const level = this.getMaturityLevel();

    switch (level) {
      case 'Transformative':
        return "You're already succeeding - imagine what you could achieve with cutting-edge AI enhancement. Join our Founder's Circle to influence the future of recruitment.";
      case 'Optimized':
        return "You're doing good work - let's make it great. Join our Early Access program to see how AI can amplify your existing strengths.";
      case 'Developing':
        return 'The gap between you and top performers is closeable. Take our free assessment to see exactly where to focus first.';
      case 'Foundational':
        return 'Your recruitment challenges are costing you significant money and talent. Book a free strategy call to create your transformation roadmap.';
    }
  }
}
