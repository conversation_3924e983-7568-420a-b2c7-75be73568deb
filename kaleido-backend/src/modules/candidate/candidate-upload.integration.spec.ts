import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { FileUploadProcessor } from '@/shared/processors/file-upload.processor';
import { CandidateService } from './candidate.service';
import { CandidateServiceUtils } from './candidate.service.utils';
import { CandidateUploadService } from './services/candidate-upload.service';
import { Candidate } from './entities/candidate.entity';
import { Job } from '../job/entities/job.entity';
import { QueueService } from '@/shared/services/queue.service';
import { CreditService } from '../subscription/credit.service';
import { ResumePerformanceMonitorService } from '@/shared/services/resume-performance-monitor.service';
import { CandidateStatus } from '@/shared/types';

// Mock the static methods of CandidateServiceUtils
jest.mock('./candidate.service.utils', () => ({
  CandidateServiceUtils: {
    isDuplicateFile: jest.fn(),
    filterCandidatesByClient: jest.fn(),
  },
}));

describe('Candidate Upload Integration - Multiple Jobs', () => {
  let fileUploadProcessor: FileUploadProcessor;
  let candidateService: any;
  let candidateRepository: any;
  let jobRepository: any;
  let queueService: any;
  let creditService: any;
  let performanceMonitor: any;

  const mockFile = {
    originalname: 'john-doe-resume.pdf',
    mimetype: 'application/pdf',
    size: 1024,
    buffer: Buffer.from('mock file content'),
  } as Express.Multer.File;

  const mockJob1 = {
    id: 'job-123',
    jobType: 'Software Engineer',
    department: 'Engineering',
    companyName: 'Test Company',
  };

  const mockJob2 = {
    id: 'job-456',
    jobType: 'Senior Developer',
    department: 'Engineering',
    companyName: 'Test Company',
  };

  const mockParsedData = {
    fullName: 'John Doe',
    email: '<EMAIL>',
    location: 'New York, NY',
    skills: ['JavaScript', 'TypeScript', 'Node.js'],
    experience: [],
    summary: 'Experienced developer',
  };

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mocks
    const mockCandidateService = {
      findByJob: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      processResumeWithDuplicateCheck: jest.fn(),
    };

    const mockCandidateRepository = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      query: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const mockJobRepository = {
      findOne: jest.fn(),
    };

    const mockQueueService = {
      addToUploadQueue: jest.fn(),
    };

    const mockCreditService = {
      chargeCreditsForCandidateUpload: jest.fn().mockResolvedValue({
        creditsCharged: 0,
        creditsRemaining: 100,
        creditWarning: null,
      }),
    };

    const mockPerformanceMonitor = {
      startSession: jest.fn().mockReturnValue({ sessionId: 'test-session' }),
      startStage: jest.fn(),
      endStage: jest.fn(),
      updateResults: jest.fn(),
      endSession: jest.fn().mockReturnValue({}),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileUploadProcessor,
        {
          provide: CandidateService,
          useValue: mockCandidateService,
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockCandidateRepository,
        },
        {
          provide: getRepositoryToken(Job),
          useValue: mockJobRepository,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
        {
          provide: CreditService,
          useValue: mockCreditService,
        },
        {
          provide: ResumePerformanceMonitorService,
          useValue: mockPerformanceMonitor,
        },
      ],
    }).compile();

    fileUploadProcessor = module.get<FileUploadProcessor>(FileUploadProcessor);
    candidateService = module.get(CandidateService);
    candidateRepository = module.get(getRepositoryToken(Candidate));
    jobRepository = module.get(getRepositoryToken(Job));
    queueService = module.get(QueueService);
    creditService = module.get(CreditService);
    performanceMonitor = module.get(ResumePerformanceMonitorService);
  });

  describe('Uploading same candidate to multiple jobs', () => {
    it('should create single candidate record and update appliedJobs array', async () => {
      // Mock job repository responses
      jobRepository.findOne
        .mockResolvedValueOnce(mockJob1 as any) // First upload
        .mockResolvedValueOnce(mockJob2 as any); // Second upload

      // Define the candidate that will be created from first upload
      const candidateWithAppliedJobs = {
        id: 'candidate-1',
        jobId: 'job-123',
        appliedJobs: ['job-123'],
        email: '<EMAIL>',
        fullName: 'John Doe',
        originalFilename: 'john-doe-resume.pdf',
        clientId: 'client-1',
        status: CandidateStatus.APPLIED,
      };

      // Mock finding existing candidates (empty for first job)
      candidateService.findByJob
        .mockResolvedValueOnce([]) // First call - no existing candidates
        .mockResolvedValueOnce([candidateWithAppliedJobs]); // Second call - after candidate added

      // Mock processing resume with no duplicate found
      candidateService.processResumeWithDuplicateCheck.mockResolvedValueOnce(
        candidateWithAppliedJobs as any,
      );

      // First upload - creates new candidate
      const job1Data = {
        files: [mockFile],
        jobId: 'job-123',
        userId: 'user-1',
        clientId: 'client-1',
      };

      const result1 = await fileUploadProcessor.handleFileUpload({
        data: job1Data,
        progress: jest.fn(),
      } as any);

      expect(result1.successCount).toBe(1);
      expect(result1.duplicateCount).toBe(0);
      expect(candidateService.processResumeWithDuplicateCheck).toHaveBeenCalledWith(
        mockFile,
        'job-123',
        'client-1',
      );

      // Mock for second upload - find existing candidate
      candidateService.findByJob.mockResolvedValueOnce([
        {
          id: 'candidate-1',
          jobId: 'job-123',
          appliedJobs: ['job-123'],
          email: '<EMAIL>',
          originalFilename: 'john-doe-resume.pdf',
        } as any,
      ]);

      // Mock duplicate detection
      (CandidateServiceUtils.isDuplicateFile as jest.Mock).mockReturnValueOnce(true);
      (CandidateServiceUtils.filterCandidatesByClient as jest.Mock).mockReturnValueOnce([
        {
          id: 'candidate-1',
          jobId: 'job-123',
          appliedJobs: ['job-123'],
          email: '<EMAIL>',
          originalFilename: 'john-doe-resume.pdf',
        } as any,
      ]);

      // Mock update for adding to appliedJobs
      candidateService.update.mockResolvedValueOnce({} as any);

      // Second upload - updates existing candidate
      const job2Data = {
        files: [mockFile],
        jobId: 'job-456',
        userId: 'user-1',
        clientId: 'client-1',
      };

      const result2 = await fileUploadProcessor.handleFileUpload({
        data: job2Data,
        progress: jest.fn(),
      } as any);

      expect(result2.successCount).toBe(1);
      expect(result2.duplicateCount).toBe(1);

      // Verify update was called to add new job to appliedJobs
      expect(candidateService.update).toHaveBeenCalledWith('candidate-1', {
        appliedJobs: ['job-123', 'job-456'],
      });
    });

    it('should find candidates across multiple jobs using appliedJobs array', async () => {
      const candidateInBothJobs = {
        id: 'candidate-1',
        jobId: 'job-123', // Original job
        appliedJobs: ['job-123', 'job-456'], // Applied to both jobs
        email: '<EMAIL>',
        fullName: 'John Doe',
      };

      // Mock the service to return the candidate for both jobs
      candidateService.findByJob
        .mockResolvedValueOnce([candidateInBothJobs])
        .mockResolvedValueOnce([candidateInBothJobs]);

      const job1Candidates = await candidateService.findByJob('job-123', 'client-1');

      expect(candidateService.findByJob).toHaveBeenCalledWith('job-123', 'client-1');
      expect(job1Candidates).toHaveLength(1);
      expect(job1Candidates[0].id).toBe('candidate-1');

      const job2Candidates = await candidateService.findByJob('job-456', 'client-1');

      expect(candidateService.findByJob).toHaveBeenCalledWith('job-456', 'client-1');
      expect(job2Candidates).toHaveLength(1);
      expect(job2Candidates[0].id).toBe('candidate-1');
    });

    it('should handle duplicate file uploads for same job correctly', async () => {
      // Setup existing candidate already applied to the job
      const existingCandidate = {
        id: 'candidate-1',
        jobId: 'job-123',
        appliedJobs: ['job-123'],
        email: '<EMAIL>',
        originalFilename: 'john-doe-resume.pdf',
      };

      candidateService.findByJob.mockResolvedValue([existingCandidate] as any);

      (CandidateServiceUtils.isDuplicateFile as jest.Mock).mockReturnValue(true);
      (CandidateServiceUtils.filterCandidatesByClient as jest.Mock).mockReturnValue([
        existingCandidate,
      ] as any);

      const jobData = {
        files: [mockFile],
        jobId: 'job-123',
        userId: 'user-1',
        clientId: 'client-1',
      };

      const result = await fileUploadProcessor.handleFileUpload({
        data: jobData,
        progress: jest.fn(),
      } as any);

      expect(result.successCount).toBe(1);
      expect(result.duplicateCount).toBe(1);

      // Should NOT update appliedJobs since candidate already has this job
      expect(candidateService.update).not.toHaveBeenCalled();
    });

    it('should handle batch uploads with mixed new and duplicate files', async () => {
      const file1 = { ...mockFile, originalname: 'john-doe.pdf' };
      const file2 = { ...mockFile, originalname: 'jane-smith.pdf' };

      // Mock finding existing candidates
      candidateService.findByJob.mockResolvedValue([
        {
          id: 'candidate-1',
          jobId: 'job-789',
          appliedJobs: ['job-789'],
          originalFilename: 'john-doe.pdf',
          clientId: 'client-1',
        } as any,
      ]);

      // john-doe.pdf is duplicate, jane-smith.pdf is new
      (CandidateServiceUtils.isDuplicateFile as jest.Mock)
        .mockReturnValueOnce(true) // john-doe.pdf
        .mockReturnValueOnce(false); // jane-smith.pdf

      (CandidateServiceUtils.filterCandidatesByClient as jest.Mock).mockReturnValue([
        {
          id: 'candidate-1',
          jobId: 'job-789',
          appliedJobs: ['job-789'],
          originalFilename: 'john-doe.pdf',
        } as any,
      ]);

      // Mock processing new candidate
      candidateService.processResumeWithDuplicateCheck.mockResolvedValue({
        id: 'candidate-2',
        jobId: 'job-123',
        appliedJobs: ['job-123'],
        email: '<EMAIL>',
        fullName: 'Jane Smith',
      } as any);

      // Mock update for duplicate
      candidateService.update.mockResolvedValue({} as any);

      const jobData = {
        files: [file1, file2],
        jobId: 'job-123',
        userId: 'user-1',
        clientId: 'client-1',
      };

      const result = await fileUploadProcessor.handleFileUpload({
        data: jobData,
        progress: jest.fn(),
      } as any);

      expect(result.totalFiles).toBe(2);
      expect(result.successCount).toBe(2);
      expect(result.duplicateCount).toBe(1);

      // Should update appliedJobs for the duplicate
      expect(candidateService.update).toHaveBeenCalledWith('candidate-1', {
        appliedJobs: ['job-789', 'job-123'],
      });

      // Should process the new candidate
      expect(candidateService.processResumeWithDuplicateCheck).toHaveBeenCalledWith(
        file2,
        'job-123',
        'client-1',
      );
    });

    it('should handle empty appliedJobs arrays gracefully', async () => {
      // Mock the service to return empty array
      candidateService.findByJob.mockResolvedValue([]);

      const result = await candidateService.findByJob('job-123', 'client-1');

      // Should return empty array without crashing
      expect(result).toEqual([]);
    });
  });
});
