import { Repository } from 'typeorm';

import { EmailService } from '@/modules/email/email.service';
import { Job } from '@/modules/entities';
import { JobService } from '@/modules/job/job.service';
import { CandidateStatus } from '@/shared/types';
import { ActivityType } from '@/shared/types/activity.types';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CandidateEvaluation } from '../entities/candidate-evaluation.entity';
import { Candidate } from '../entities/candidate.entity';
import { CandidateActivityService } from './candidate-activity.service';
import { CandidateEnhancementService } from './candidate-enhancement.service';

@Injectable()
export class CandidateStatusService {
  private readonly logger = new Logger('CandidateStatusService');

  constructor(
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @InjectRepository(CandidateEvaluation)
    private readonly candidateEvaluationRepository: Repository<CandidateEvaluation>,
    @Inject(forwardRef(() => JobService))
    private readonly jobService: JobService,
    private readonly emailService: EmailService,
    private readonly candidateActivityService: CandidateActivityService,
    private readonly candidateEnhancementService: CandidateEnhancementService,
  ) {}

  async updateStatus(id: string, status: CandidateStatus, notes?: string, additionalData?: any) {
    // First ensure the candidate has status history
    await this.candidateActivityService.ensureStatusHistory(id).catch((error) => {
      this.logger.warn(`Failed to ensure status history for candidate ${id}: ${error.message}`);
    });

    const candidate = await this.candidateEnhancementService.findOne(id);
    const previousStatus = candidate.status;
    candidate.status = status;
    if (notes) {
      candidate.notes = notes;
    }

    // Determine which jobId to use (from additionalData or candidate)
    const jobId = additionalData?.jobId || candidate.jobId;

    // Log the status change activity
    await this.candidateActivityService.logActivity(
      candidate,
      ActivityType.STATUS_CHANGED,
      `Status changed from ${previousStatus} to ${status}`,
      {
        previousStatus,
        newStatus: status,
        notes,
        additionalData,
      },
      jobId,
    );

    // Update the candidate
    const updatedCandidate = await this.candidateRepository.save(candidate);

    // Sync history with job seeker if available
    if (candidate.jobSeekerId) {
      await this.candidateActivityService
        .syncCandidateJobSeekerHistory(candidate.id, candidate.jobSeekerId)
        .catch((error) => {
          this.logger.error(
            `Failed to sync history with job seeker: ${
              error instanceof Error ? error.message : 'Unknown error'
            }`,
          );
        });
    }

    // If the candidate has a jobId, also update the corresponding evaluation
    if (candidate.jobId) {
      const evaluation = await this.candidateEvaluationRepository.findOne({
        where: { candidateId: id, jobId: candidate.jobId },
      });

      if (evaluation) {
        evaluation.status = status;
        if (notes) {
          evaluation.notes = notes;
        }
        // Update contacted status if it's being set on the candidate
        if (typeof additionalData?.contacted === 'boolean') {
          evaluation.contacted = additionalData.contacted;
        }
        await this.candidateEvaluationRepository.save(evaluation);
      }
    }

    // If additionalData contains a jobId, update or create an evaluation for that job
    if (additionalData?.jobId && additionalData.jobId !== candidate.jobId) {
      let evaluation = await this.candidateEvaluationRepository.findOne({
        where: { candidateId: id, jobId: additionalData.jobId },
      });

      if (evaluation) {
        evaluation.status = status;
        if (notes) {
          evaluation.notes = notes;
        }
        // Update contacted status if it's being set on the candidate
        if (typeof additionalData?.contacted === 'boolean') {
          evaluation.contacted = additionalData.contacted;
        }
      } else {
        // Create a new evaluation for this job
        evaluation = this.candidateEvaluationRepository.create({
          candidateId: id,
          jobId: additionalData.jobId,
          status: status,
          notes: notes,
          contacted: additionalData?.contacted ?? false,
        });
      }

      await this.candidateEvaluationRepository.save(evaluation);
    }

    // Get the job for email sending - use additionalData.jobId as fallback
    let job: Job | null = null;
    if (candidate.jobId) {
      job = await this.jobService.findOne(candidate.jobId, ['company']);
    } else if (additionalData?.jobId) {
      // Load job with company relation to ensure we have the proper company name
      job = await this.jobService.findOne(additionalData.jobId, ['company']);
    }

    // Send appropriate email based on status change
    if (job && candidate.email) {
      try {
        // Check for skipCulturalFit flag in various places
        let skipCulturalFit = additionalData?.skipCulturalFit;

        // Also check in notes if it's a JSON string
        if (!skipCulturalFit && notes) {
          try {
            const parsedNotes = JSON.parse(notes);
            if (parsedNotes.skipCulturalFit) {
              skipCulturalFit = true;
            }
          } catch (error) {
            // Notes is not JSON, ignore
          }
        }

        // Check if the job has cultureFitQuestions set up, but only for SHORTLISTED status
        // and unless skipCulturalFit is true
        if (
          status === CandidateStatus.SHORTLISTED &&
          !skipCulturalFit &&
          (!job.cultureFitQuestions || job.cultureFitQuestions.length === 0)
        ) {
          // If cultureFitQuestions are not set up, return a response indicating that they need to be set up
          return {
            ...updatedCandidate,
            needsCulturalFitSetup: true,
            jobId: job.id,
            message:
              'Video Introduction questionnaires can be set up to enhance candidate evaluation.',
          };
        }

        // Prepare job title
        const jobTitle =
          job.department && job.jobType
            ? `${job.department} ${job.jobType}`
            : job.jobType || 'Job Position';

        // Handle special cases first
        if (status === CandidateStatus.CULTURAL_FIT_ANSWERED) {
          // We need to handle this case differently because we need the result for needsCulturalFitSetup
          // So we'll await this one, but make it clear in a comment that this is an exception

          // EXCEPTION: We need to await this email to check for needsCulturalFitSetup
          const emailResult = await this.emailService.sendCulturalFitCompletedEmail(
            candidate.email,
            candidate.fullName,
            jobTitle,
            job.company?.companyName || job.companyName,
            undefined,
            job,
            undefined,
            skipCulturalFit,
          );

          // Log activity (still awaited since we're already awaiting the email)
          await this.candidateActivityService.logActivity(
            candidate,
            ActivityType.EMAIL_SENT,
            'Sent cultural fit completion email',
            { emailType: 'CULTURAL_FIT_COMPLETED' },
          );

          if (
            emailResult &&
            typeof emailResult === 'object' &&
            'needsCulturalFitSetup' in emailResult
          ) {
            return { ...updatedCandidate, ...emailResult };
          }
        } else {
          // For all other status changes, use the generic status update email
          // Determine the appropriate status type for the email
          let statusType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status' = 'status';
          const additionalEmailData: Record<string, any> = {};

          // Add the candidate status and job object to additionalEmailData
          additionalEmailData.candidateStatus = status;
          additionalEmailData.job = job;

          // Map status to email type and prepare additional data
          switch (status) {
            case CandidateStatus.INTERVIEWING:
              statusType = 'interview';
              if (additionalData?.dateSensitiveInfo) {
                additionalEmailData.interviewDate = additionalData.dateSensitiveInfo.date;
                additionalEmailData.meetingLink = additionalData.dateSensitiveInfo.meetingLink;
              }
              break;
            case CandidateStatus.HIRED:
              statusType = 'hired';
              if (additionalData?.dateSensitiveInfo) {
                additionalEmailData.startDate = additionalData.dateSensitiveInfo.startDate;
                additionalEmailData.onboardingLink =
                  additionalData.dateSensitiveInfo.onboardingLink;
              }
              break;
            case CandidateStatus.OFFER_EXTENDED:
              statusType = 'offer';
              break;
            case CandidateStatus.OFFER_ACCEPTED:
              statusType = 'offerAccepted';
              break;
          }

          // Generate a default message based on the status
          const defaultMessage = this.getDefaultStatusMessage(status, jobTitle, job.companyName);

          // For email notification with dateSensitiveInfo, we need to await the response
          // to check for needsCulturalFitSetup
          if (
            additionalData?.dateSensitiveInfo &&
            typeof additionalData.dateSensitiveInfo === 'object'
          ) {
            try {
              // Add skipCulturalFit flag to additionalEmailData if present in dateSensitiveInfo
              if (additionalData.dateSensitiveInfo.skipCulturalFit) {
                additionalEmailData.skipCulturalFit = true;
              }

              const emailResult = await this.emailService.sendStatusUpdateEmail(
                candidate.email,
                candidate.fullName,
                jobTitle,
                statusType,
                defaultMessage,
                job.company?.companyName || job.companyName,
                additionalEmailData,
              );

              // Log success activity after email is sent
              await this.candidateActivityService.logActivity(
                candidate,
                ActivityType.EMAIL_SENT,
                `Sent ${statusType} status update email`,
                {
                  emailType: 'STATUS_UPDATE',
                  statusType,
                  candidateStatus: status,
                  additionalData: additionalEmailData,
                },
              );

              // Check if the email service returned needsCulturalFitSetup
              if (
                emailResult &&
                typeof emailResult === 'object' &&
                'needsCulturalFitSetup' in emailResult
              ) {
                return { ...updatedCandidate, ...emailResult };
              }
            } catch (emailError) {
              // Log error if email fails
              this.logger.error(
                `Failed to send status update email to ${candidate.email}:`,
                emailError,
              );
              await this.candidateActivityService.logActivity(
                candidate,
                ActivityType.ERROR,
                'Failed to send status update email',
                { error: emailError instanceof Error ? emailError.message : 'Unknown error' },
              );
            }
          }
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.logger.error(`Failed to send status update email to ${candidate.email}:`, error);
        await this.candidateActivityService.logActivity(
          candidate,
          ActivityType.ERROR,
          'Failed to send status update email',
          { error: errorMessage },
        );
      }
    }

    // AUTOMATIC JOB APPLICATION STATUS SYNCHRONIZATION
    // After successfully updating the candidate status, also update the job application status
    // to ensure that job seekers see the updated status in their applications
    if (candidate.jobSeekerId && jobId) {
      try {
        await this.jobService.updateJobApplicationStatus(jobId, candidate.jobSeekerId, status);
        this.logger.log(
          `Successfully synchronized job application status for candidate ${id}, jobSeeker ${candidate.jobSeekerId}, job ${jobId}: ${status}`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to synchronize job application status for candidate ${id}: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`,
        );
        // Don't throw the error - the candidate status update was successful,
        // we just failed to sync the application status
      }
    }

    return updatedCandidate;
  }
  /**
   * Generates a default message for status update emails based on the candidate status
   */
  getDefaultStatusMessage(status: CandidateStatus, jobTitle: string, companyName: string): string {
    switch (status) {
      case CandidateStatus.MATCHED:
        return `We're pleased to inform you that your profile has been matched with the ${jobTitle} position at ${companyName}. Our team has reviewed your qualifications and found them to be a good fit for this role.`;

      case CandidateStatus.SHORTLISTED:
        return `Congratulations! You've been shortlisted for the ${jobTitle} position at ${companyName}. Your qualifications and experience have impressed our team, and we'd like to move forward with your application.`;

      case CandidateStatus.CONTACTED:
        return `We've attempted to reach out to you regarding your application for the ${jobTitle} position at ${companyName}. Please check your phone and email for our message.`;

      case CandidateStatus.INTERESTED:
        return `Thank you for expressing interest in the ${jobTitle} position at ${companyName}. We appreciate your enthusiasm and will be in touch with next steps soon.`;

      case CandidateStatus.INTERVIEWING:
        return `We're excited to invite you to interview for the ${jobTitle} position at ${companyName}. Please review the interview details and confirm your availability.`;

      case CandidateStatus.OFFER_PENDING_APPROVAL:
        return `Your application for the ${jobTitle} position at ${companyName} is progressing well. We're currently finalizing some details and will be in touch soon with more information.`;

      case CandidateStatus.OFFER_APPROVED:
        return `Good news regarding your application for the ${jobTitle} position at ${companyName}. We're preparing an offer for you and will be in touch very soon.`;

      case CandidateStatus.OFFER_EXTENDED:
        return `We're pleased to offer you the ${jobTitle} position at ${companyName}. Please review the offer details and let us know if you have any questions.`;

      case CandidateStatus.OFFER_ACCEPTED:
        return `Thank you for accepting our offer for the ${jobTitle} position at ${companyName}. We're excited to have you join our team!`;

      case CandidateStatus.HIRE_PENDING_APPROVAL:
        return `We're in the final stages of processing your employment for the ${jobTitle} position at ${companyName}. We'll be in touch soon with more details.`;

      case CandidateStatus.HIRE_APPROVED:
        return `Your employment for the ${jobTitle} position at ${companyName} has been approved. We'll be contacting you shortly with next steps.`;

      case CandidateStatus.HIRED:
        return `Welcome to ${companyName}! We're thrilled to have you join our team as ${jobTitle}. Please review the onboarding information and start date details.`;

      case CandidateStatus.REJECTED:
        return `Thank you for your interest in the ${jobTitle} position at ${companyName}. After careful consideration, we've decided to move forward with other candidates whose qualifications better match our current needs.`;

      case CandidateStatus.WITHDRAWN:
        return `We've received your request to withdraw your application for the ${jobTitle} position at ${companyName}. We appreciate your interest and wish you the best in your job search.`;

      case CandidateStatus.CULTURAL_FIT_ANSWERED:
        return `Thank you for completing the Video Intro Assessment for the ${jobTitle} position at ${companyName}. We'll review your responses and be in touch with next steps.`;

      default:
        return `This is an update regarding your application for the ${jobTitle} position at ${companyName}.`;
    }
  }
}
