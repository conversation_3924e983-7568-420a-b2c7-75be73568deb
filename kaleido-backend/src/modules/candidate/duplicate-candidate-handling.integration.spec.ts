import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';

import { FileUploadProcessor } from '@/shared/processors/file-upload.processor';
import { CandidateService } from './candidate.service';
import { CandidateServiceUtils } from './candidate.service.utils';
import { Candidate } from './entities/candidate.entity';
import { Job } from '../job/entities/job.entity';
import { JobCandidatesHelpers } from '../job/job.candidates.helpers';
import { JobApplication } from '../job-seeker/entities/job-application.entity';
import { JobSeeker } from '../job-seeker/entities/job-seeker.entity';
import { CreditService } from '../subscription/credit.service';
import { ResumePerformanceMonitorService } from '@/shared/services/resume-performance-monitor.service';

// Mock the static methods of CandidateServiceUtils
jest.mock('./candidate.service.utils', () => ({
  CandidateServiceUtils: {
    isDuplicateFile: jest.fn(),
    filterCandidatesByClient: jest.fn(),
  },
}));

describe('Duplicate Candidate Handling - Integration Test', () => {
  let fileUploadProcessor: FileUploadProcessor;
  let candidateService: CandidateService;
  let jobCandidatesHelpers: JobCandidatesHelpers;
  let candidateRepository: Repository<Candidate>;
  let jobRepository: Repository<Job>;

  const mockFile = {
    originalname: 'john-doe-resume.pdf',
    mimetype: 'application/pdf',
    size: 1024,
    buffer: Buffer.from('mock file content'),
  } as Express.Multer.File;

  const mockJob1 = {
    id: 'job-123',
    jobType: 'Software Engineer',
    department: 'Engineering',
    companyName: 'Test Company',
    clientId: 'client-1',
    topCandidateThreshold: 70,
    secondTierCandidateThreshold: 50,
  } as Job;

  const mockJob2 = {
    id: 'job-456',
    jobType: 'Senior Developer',
    department: 'Engineering',
    companyName: 'Test Company',
    clientId: 'client-1',
    topCandidateThreshold: 70,
    secondTierCandidateThreshold: 50,
  } as Job;

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    const mockCandidateService = {
      findByJob: jest.fn(),
      processResumeWithDuplicateCheck: jest.fn(),
      update: jest.fn(),
    };

    const mockCandidateRepository = {
      createQueryBuilder: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
    };

    const mockJobRepository = {
      findOne: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const mockCreditService = {
      chargeCreditsForCandidateUpload: jest.fn().mockResolvedValue({
        creditsCharged: 0,
        creditsRemaining: 100,
        creditWarning: null,
      }),
    };

    const mockPerformanceMonitor = {
      startSession: jest.fn().mockReturnValue({ sessionId: 'test-session' }),
      startStage: jest.fn(),
      endStage: jest.fn(),
      updateResults: jest.fn(),
      endSession: jest.fn().mockReturnValue({}),
    };

    const mockJobCandidatesHelpers = {
      findCandidates: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileUploadProcessor,
        {
          provide: JobCandidatesHelpers,
          useValue: mockJobCandidatesHelpers,
        },
        {
          provide: CandidateService,
          useValue: mockCandidateService,
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockCandidateRepository,
        },
        {
          provide: getRepositoryToken(Job),
          useValue: mockJobRepository,
        },
        {
          provide: CreditService,
          useValue: mockCreditService,
        },
        {
          provide: ResumePerformanceMonitorService,
          useValue: mockPerformanceMonitor,
        },
      ],
    }).compile();

    fileUploadProcessor = module.get<FileUploadProcessor>(FileUploadProcessor);
    candidateService = module.get(CandidateService);
    jobCandidatesHelpers = module.get(JobCandidatesHelpers);
    candidateRepository = module.get(getRepositoryToken(Candidate));
    jobRepository = module.get(getRepositoryToken(Job));
  });

  describe('Upload same candidate to multiple jobs', () => {
    it('should add candidate to appliedJobs and return correct counts', async () => {
      // Mock existing candidate from first upload
      const existingCandidate = {
        id: 'candidate-1',
        jobId: 'job-123',
        appliedJobs: ['job-123'],
        email: '<EMAIL>',
        fullName: 'John Doe',
        originalFilename: 'john-doe-resume.pdf',
        clientId: 'client-1',
      } as Candidate;

      // First upload - no existing candidates
      (candidateService.findByJob as jest.Mock)
        .mockResolvedValueOnce([]) // First call - no existing candidates
        .mockResolvedValueOnce([existingCandidate]); // Second call - after candidate added

      // Mock processing new candidate
      (candidateService.processResumeWithDuplicateCheck as jest.Mock).mockResolvedValueOnce({
        ...existingCandidate,
        isDuplicate: false,
      });

      // First upload
      const job1Data = {
        files: [mockFile],
        jobId: 'job-123',
        userId: 'user-1',
        clientId: 'client-1',
      };

      const result1 = await fileUploadProcessor.handleFileUpload({
        data: job1Data,
        progress: jest.fn(),
      } as any);

      // Verify first upload created new candidate
      expect(result1.success).toBe(true);
      expect(result1.newCandidatesCount).toBe(1);
      expect(result1.duplicateCount).toBe(0);

      // Second upload - existing candidate found
      (candidateService.findByJob as jest.Mock).mockResolvedValueOnce([existingCandidate]);

      // Mock the static method properly
      (CandidateServiceUtils.isDuplicateFile as jest.Mock).mockReturnValueOnce(true);
      (CandidateServiceUtils.filterCandidatesByClient as jest.Mock).mockReturnValueOnce([
        existingCandidate,
      ]);

      // Mock update to add job to appliedJobs
      (candidateService.update as jest.Mock).mockResolvedValueOnce({
        ...existingCandidate,
        appliedJobs: ['job-123', 'job-456'],
      });

      // Second upload to different job
      const job2Data = {
        files: [mockFile],
        jobId: 'job-456',
        userId: 'user-1',
        clientId: 'client-1',
      };

      const result2 = await fileUploadProcessor.handleFileUpload({
        data: job2Data,
        progress: jest.fn(),
      } as any);

      // Verify second upload handled as duplicate
      expect(result2.success).toBe(true);
      expect(result2.newCandidatesCount).toBe(0); // No new candidates
      expect(result2.duplicateCount).toBe(1); // One duplicate

      // Verify update was called to add job to appliedJobs
      expect(candidateService.update).toHaveBeenCalledWith('candidate-1', {
        appliedJobs: ['job-123', 'job-456'],
      });
    });

    it('should find candidates across multiple jobs using appliedJobs', async () => {
      // Mock the findCandidates method to return a candidate that belongs to multiple jobs
      (jobCandidatesHelpers.findCandidates as jest.Mock).mockResolvedValue({
        stats: {
          totalCandidates: 1,
          topTier: 0,
          secondTier: 0,
          other: 1,
        },
        candidates: {
          unranked: [
            {
              id: 'candidate-1',
              fullName: 'John Doe',
              email: '<EMAIL>',
              jobId: 'job-123', // Original job
              appliedJobs: ['job-123', 'job-456'], // Applied to both jobs
              evaluations: [],
            },
          ],
          topTier: [],
          secondTier: [],
          other: [],
        },
      });

      // Find candidates for job-456
      const result = await jobCandidatesHelpers.findCandidates('job-456');

      // Verify findCandidates was called with the correct job ID
      expect(jobCandidatesHelpers.findCandidates).toHaveBeenCalledWith('job-456');

      // Verify candidate was found
      expect(result.stats.totalCandidates).toBe(1);
      expect(result.candidates.unranked).toHaveLength(1);
      expect(result.candidates.unranked?.[0]?.id).toBe('candidate-1');
    });
  });
});
