import { Public } from '@/auth/public.decorator';
import { UserRole } from '@/common/enums/role.enum';
import { multerConfig } from '@/config/multer.config';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { Roles } from '@/shared/decorators/roles.decorator';
import { FILE_UPLOAD } from '@/shared/types/constants';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Headers,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  ParseFilePipeBuilder,
  Patch,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
// import { FileInterceptor } from '@nestjs/platform-express';
import { FastifyFileInterceptor } from '@/shared/interceptors/fastify-file.interceptor';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ProfileFormatter } from '@shared/utils/profile-formatter.util';

import { Auth0Guard } from '../../auth/auth.guard';
import { DigitalOceanSpacesService } from '../../shared/services/digital-ocean-spaces.service';
import { EmailService } from '../email/email.service';
import { CreateJobSeekerDraftDto } from './dto/create-job-seeker-draft.dto';
import { CreateJobSeekerDto } from './dto/create-job-seeker.dto';
import { JobSeeker } from './entities/job-seeker.entity';
import { JobSeekerService } from './job-seeker.service';

@ApiTags('job-seekers')
@ApiBearerAuth()
@UseGuards(Auth0Guard)
@Controller('job-seekers')
export class JobSeekerController {
  private readonly logger = new Logger(JobSeekerController.name);

  constructor(
    private readonly jobSeekerService: JobSeekerService,
    private readonly digitalOceanSpacesService: DigitalOceanSpacesService,
    private readonly emailService: EmailService,
  ) {}

  // Helper method to get RolesService instance
  private async getRolesService() {
    try {
      const rolesService = await import('../../modules/roles/roles.service');
      const { getDataSource } = await import('../../shared/database/config');
      const { UserRoleEntity } = await import('../../modules/roles/entities/user-role.entity');
      const { Auth0ManagementService } = await import('../../auth/auth0-management.service');
      const { ConfigService } = await import('@nestjs/config');
      const dataSource = await getDataSource();
      const userRoleRepository = dataSource.getRepository(UserRoleEntity);

      // Create ConfigService and Auth0ManagementService instances
      const configService = new ConfigService();
      const auth0ManagementService = new Auth0ManagementService(configService);

      // Note: This is a workaround since we can't inject RolesService directly due to circular dependency
      // In a real production environment, you would refactor to avoid this pattern
      return new rolesService.RolesService(userRoleRepository, auth0ManagementService);
    } catch (error) {
      console.error('Error importing RolesService:', error);
      return null;
    }
  }

  @Get('public/profile/:id')
  @Public()
  @ApiOperation({ summary: 'Get job seeker profile by ID (public access)' })
  async getPublicProfile(@Param('id') id: string) {
    return this.jobSeekerService.findOne(id);
  }

  @Post('public/create')
  @Public()
  @ApiOperation({ summary: 'Create a job seeker profile without authentication' })
  @ApiResponse({ status: 201, description: 'Job seeker profile created successfully' })
  async createPublic(@Body() createJobSeekerDto: CreateJobSeekerDto) {
    // Validate required fields
    if (!createJobSeekerDto.clientId && !createJobSeekerDto.userId) {
      throw new BadRequestException('User ID or Client ID is required');
    }

    // Ensure both userId and clientId are set
    const userId = createJobSeekerDto.userId || createJobSeekerDto.clientId || '';
    const clientId = createJobSeekerDto.clientId || createJobSeekerDto.userId || '';

    try {
      // First check if user exists by userId
      let existingUser = await this.jobSeekerService.getByUserId(userId);

      // If not found by userId but we have an email, try to find by email
      if (!existingUser && createJobSeekerDto.email) {
        const usersByEmail = await this.jobSeekerService.findByEmail(createJobSeekerDto.email);
        if (usersByEmail && usersByEmail.length > 0) {
          existingUser = usersByEmail[0];
          // Update the userId to match the current auth user
          existingUser.userId = userId;
          await this.jobSeekerService.update(existingUser.id, existingUser as CreateJobSeekerDto);
        }
      }

      if (existingUser) {
        // For existing users, we don't update their role
        return ProfileFormatter.toStandardProfile(existingUser); // Return existing user instead of throwing error
      }

      // Create a new profile with the provided data
      const enhancedDto = {
        ...createJobSeekerDto,
        userId,
        clientId,
        role: UserRole.JOB_SEEKER,
      };

      const newJobSeeker = await this.jobSeekerService.create(enhancedDto);

      // Check if the user already has a role before updating
      try {
        const rolesServiceInstance = await this.getRolesService();
        if (rolesServiceInstance) {
          const existingRole = await rolesServiceInstance.findByClientId(userId);

          if (!existingRole) {
            // Only create a new role if one doesn't exist
            await rolesServiceInstance.create({
              clientId: userId,
              role: UserRole.JOB_SEEKER,
            });
          } else {
          }
        }
      } catch (roleError) {
        console.error('Error handling user role:', roleError);
        // Continue even if role handling fails
      }

      return ProfileFormatter.toStandardProfile(newJobSeeker);
    } catch (error: any) {
      if (error.code === '23505') {
        // PostgreSQL unique constraint violation
        // Double-check for race condition
        if (userId) {
          const existingUser = await this.jobSeekerService.getByUserId(userId);
          if (existingUser) {
            return ProfileFormatter.toStandardProfile(existingUser);
          }
        }

        // If we still have a constraint error but couldn't find the user by userId,
        // it might be an email constraint - try to find by email
        if (createJobSeekerDto.email) {
          const usersByEmail = await this.jobSeekerService.findByEmail(createJobSeekerDto.email);
          if (usersByEmail && usersByEmail.length > 0) {
            const existingUser = usersByEmail[0];
            return ProfileFormatter.toStandardProfile(existingUser);
          }
        }
      }
      console.error('Error creating job seeker with public endpoint:', error);
      throw error;
    }
  }

  // Helper methods for name extraction
  private extractFirstName(user: User): string {
    // Try to get first name from various sources
    if (user.given_name) {
      return user.given_name;
    } else if (user.firstName) {
      return user.firstName;
    } else if (user.name) {
      return user.name.split(' ')[0];
    } else if (user.fullName) {
      return user.fullName.split(' ')[0];
    } else if (user.nickname) {
      return user.nickname.split(' ')[0];
    }
    return '';
  }

  private extractLastName(user: User): string {
    // Try to get last name from various sources
    if (user.family_name) {
      return user.family_name;
    } else if (user.lastName) {
      return user.lastName;
    } else if (user.name && user.name.split(' ').length > 1) {
      return user.name.split(' ').slice(1).join(' ');
    } else if (user.fullName && user.fullName.split(' ').length > 1) {
      return user.fullName.split(' ').slice(1).join(' ');
    }
    return '';
  }

  @Post()
  @ApiOperation({ summary: 'Create a new job seeker profile or return existing one' })
  @ApiResponse({ status: 200, description: 'Existing job seeker profile found' })
  @ApiResponse({ status: 201, description: 'Job seeker profile created successfully' })
  async create(@Body() createJobSeekerDto: CreateJobSeekerDto, @GetUser() authUser: User) {
    // Get userId from multiple possible sources, prioritizing Auth0 data
    const userId = authUser.sub || authUser.userId || createJobSeekerDto.clientId;

    if (!userId) {
      throw new BadRequestException('User ID is required');
    }

    try {
      // First check if user exists by userId
      let existingUser = await this.jobSeekerService.getByUserId(userId);

      // If not found by userId but we have an email, try to find by email
      if (!existingUser && createJobSeekerDto.email) {
        const usersByEmail = await this.jobSeekerService.findByEmail(createJobSeekerDto.email);
        if (usersByEmail && usersByEmail.length > 0) {
          existingUser = usersByEmail[0];
          // Update the userId to match the current auth user
          existingUser.userId = userId;
          await this.jobSeekerService.update(existingUser.id, existingUser as CreateJobSeekerDto);
        }
      }

      if (existingUser) {
        // For existing users, we don't update their role
        return ProfileFormatter.toStandardProfile(existingUser); // Return existing user instead of throwing error
      }

      // If no existing user, create new one with enhanced data
      const enhancedDto = {
        ...createJobSeekerDto,
        userId,
        clientId: userId,
        // Use provided values or fall back to Auth0 user data
        firstName: createJobSeekerDto.firstName || this.extractFirstName(authUser),
        lastName: createJobSeekerDto.lastName || this.extractLastName(authUser),
        email: createJobSeekerDto.email || authUser.email,
        role: UserRole.JOB_SEEKER,
      };

      // Add profile image if present in Auth0 user
      if (authUser.picture && !enhancedDto.myProfileImage) {
        enhancedDto.myProfileImage = authUser.picture;
      }

      const newJobSeeker = await this.jobSeekerService.create(enhancedDto);

      // Check if the user already has a role before updating
      try {
        const rolesServiceInstance = await this.getRolesService();
        if (rolesServiceInstance) {
          const existingRole = await rolesServiceInstance.findByClientId(userId);

          if (!existingRole) {
            // Only create a new role if one doesn't exist
            await rolesServiceInstance.create({
              clientId: userId,
              role: UserRole.JOB_SEEKER,
            });
          } else {
          }
        }
      } catch (roleError) {
        console.error('Error handling user role:', roleError);
        // Continue even if role handling fails
      }

      return ProfileFormatter.toStandardProfile(newJobSeeker);
    } catch (error: any) {
      if (error.code === '23505') {
        // PostgreSQL unique constraint violation
        // Double-check for race condition
        if (userId) {
          const existingUser = await this.jobSeekerService.getByUserId(userId);
          if (existingUser) {
            return ProfileFormatter.toStandardProfile(existingUser);
          }
        }

        // If we still have a constraint error but couldn't find the user by userId,
        // it might be an email constraint - try to find by email
        if (createJobSeekerDto.email) {
          const usersByEmail = await this.jobSeekerService.findByEmail(createJobSeekerDto.email);
          if (usersByEmail && usersByEmail.length > 0) {
            const existingUser = usersByEmail[0];
            return ProfileFormatter.toStandardProfile(existingUser);
          }
        }
      }
      console.error('Error creating job seeker:', error);
      throw error;
    }
  }

  @Put(':id')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({ summary: 'Update an existing job seeker profile' })
  update(@Param('id') id: string, @Body() updateJobSeekerDto: CreateJobSeekerDto) {
    return this.jobSeekerService.update(id, updateJobSeekerDto);
  }

  @Patch('profile')
  @Roles(UserRole.JOB_SEEKER, UserRole.ADMIN)
  async updateProfile(@GetUser() user: User, @Body() dto: Partial<CreateJobSeekerDto>) {
    const jobSeeker = await this.jobSeekerService.getByUserId(user.userId);
    if (!jobSeeker) {
      throw new NotFoundException('Job seeker does not exist.');
    }
    return this.jobSeekerService.update(jobSeeker.id, {
      ...jobSeeker,
      ...dto,
    } as CreateJobSeekerDto);
  }

  @Patch(':id')
  @Roles(UserRole.JOB_SEEKER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Partially update a job seeker profile' })
  async partialUpdate(
    @Param('id') id: string,
    @Body() updateData: Partial<CreateJobSeekerDto>,
    @GetUser() user: User,
  ) {
    // Check if this is the user's own profile or if they're an admin
    const jobSeeker = await this.jobSeekerService.findOne(id);
    if (!jobSeeker) {
      throw new NotFoundException('Job seeker profile not found');
    }

    // Only allow users to update their own profiles unless they're an admin
    if (jobSeeker.userId !== user.userId && !user.roles?.includes(UserRole.ADMIN)) {
      throw new ForbiddenException('You can only update your own profile');
    }

    // Log the update attempt

    // If this is a LinkedIn update, we need special handling
    if (updateData.isLinkedInUpdate) {
      // Remove the flag as it's not a real field in the entity
      delete updateData.isLinkedInUpdate;

      // Only update fields that are present in the update data
      // Don't merge with the entire jobSeeker object to avoid validation issues
      return this.jobSeekerService.partialUpdate(id, updateData);
    }

    // For regular updates, merge with existing data
    return this.jobSeekerService.update(id, {
      ...jobSeeker,
      ...updateData,
    } as CreateJobSeekerDto);
  }

  @Post('upload/resume')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload resume file' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(new FastifyFileInterceptor('file'))
  async uploadResume(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(pdf|doc|docx)$/,
        })
        .addMaxSizeValidator({
          maxSize: FILE_UPLOAD.MAX_SIZE * 2,
        })
        .build({
          errorHttpStatusCode: 400,
          fileIsRequired: true,
        }),
    )
    file: Express.Multer.File,
    @GetUser() authUser: User,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      // Use Auth0 sub as the primary identifier
      const userId = authUser.sub || authUser.userId || `temp_${Date.now()}`;
      const clientId = authUser.sub || authUser.userId || `temp_${Date.now()}`;
      const path = `resumes/${clientId}`;
      const resumeUrl = await this.digitalOceanSpacesService.uploadFile(file, path);

      // Process the resume and update job seeker profile
      const jobSeeker = await this.jobSeekerService.uploadResume(clientId, file, userId);

      return { resumeUrl, ...jobSeeker };
    } catch (error) {
      console.error('Error uploading resume:', error);
      throw new BadRequestException('Failed to upload resume. Please try again.');
    }
  }

  @Post(':id/approve')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Approve a job seeker registration' })
  @ApiResponse({ status: 200, description: 'The job seeker has been successfully approved.' })
  async approve(@Param('id') id: string, @GetUser() user: User) {
    // Approve the job seeker
    const approvedJobSeeker = await this.jobSeekerService.approve(id);

    // Send approval email
    if (approvedJobSeeker && approvedJobSeeker.email) {
      try {
        // Extract skills from job seeker profile
        const topSkills = approvedJobSeeker.skills || [];
        const yearsExperience = undefined; // yearsOfExperience not available on JobSeeker entity
        const industry = approvedJobSeeker.preferences?.industries?.[0]; // Get first industry from preferences

        await this.emailService.sendProfileApproved(
          approvedJobSeeker.email,
          approvedJobSeeker.firstName || 'Job Seeker',
          topSkills,
          yearsExperience,
          industry,
          undefined, // dashboardUrl - will use default
          user.userId,
        );
      } catch (emailError) {
        console.error('Failed to send approval email:', emailError);
        // Don't fail the approval if email fails
      }
    }

    return approvedJobSeeker;
  }

  @Post(':id/decline')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Decline a job seeker registration' })
  @ApiResponse({ status: 200, description: 'The job seeker has been successfully declined.' })
  async decline(@Param('id') id: string, @Body() body: { reason: string }, @GetUser() user: User) {
    // Decline the job seeker
    const declinedJobSeeker = await this.jobSeekerService.decline(id, body.reason);

    // Send decline email
    if (declinedJobSeeker && declinedJobSeeker.email) {
      try {
        await this.emailService.sendProfileRejected(
          declinedJobSeeker.email,
          declinedJobSeeker.firstName || 'Job Seeker',
          user.userId,
        );
      } catch (emailError) {
        console.error('Failed to send decline email:', emailError);
        // Don't fail the decline if email fails
      }
    }

    return declinedJobSeeker;
  }

  @Post('upload/video')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload video introduction' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(new FastifyFileInterceptor('file'))
  async uploadVideo(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(mp4|webm|mov)$/,
        })
        .addMaxSizeValidator({
          maxSize: FILE_UPLOAD.MAX_SIZE * 20,
        })
        .build({
          errorHttpStatusCode: 400,
          fileIsRequired: true,
        }),
    )
    file: Express.Multer.File,
    @GetUser() authUser: User,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      // Generate a unique path for the file
      const userId = authUser.userId || `temp_${Date.now()}`;
      const path = `videos/${userId}`;
      const videoUrl = await this.digitalOceanSpacesService.uploadFile(file, path);

      // Update the job seeker's onboarding progress to mark video introduction as completed
      try {
        const jobSeeker = await this.jobSeekerService.getByUserId(userId);
        if (jobSeeker) {
          await this.jobSeekerService.markVideoIntroCompleted(jobSeeker.id);
        }
      } catch (progressError) {
        console.error('Error updating onboarding progress:', progressError);
        // Continue even if progress update fails
      }

      return { videoUrl };
    } catch (error) {
      console.error('Error uploading video:', error);
      throw new BadRequestException('Failed to upload video. Please try again.');
    }
  }

  @Post(':id/mark-video-completed')
  @ApiOperation({ summary: 'Mark video introduction as completed in profile' })
  async markVideoCompleted(@Param('id') id: string, @GetUser() authUser: User) {
    try {
      // Verify the job seeker exists and belongs to the authenticated user
      const jobSeeker = await this.jobSeekerService.findOne(id);
      if (!jobSeeker) {
        throw new NotFoundException('Job seeker not found');
      }

      if (jobSeeker.userId !== authUser.userId && !authUser.roles?.includes(UserRole.ADMIN)) {
        throw new BadRequestException('You can only update your own profile');
      }

      // Mark the video introduction as completed
      await this.jobSeekerService.markVideoIntroCompleted(id);

      return { success: true, message: 'Video introduction marked as completed' };
    } catch (error) {
      console.error('Error marking video as completed:', error);
      throw new BadRequestException('Failed to mark video as completed. Please try again.');
    }
  }

  @Delete('upload/video')
  @ApiOperation({ summary: 'Delete video introduction' })
  async deleteVideo(@Query('url') url: string) {
    if (!url) {
      throw new BadRequestException('No video URL provided');
    }

    try {
      await this.digitalOceanSpacesService.deleteFile(url);
      return { success: true };
    } catch (error) {
      console.error('Error deleting video:', error);
      throw new BadRequestException('Failed to delete video. Please try again.');
    }
  }

  @Post('upload/identity')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload identity document' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        documentType: {
          type: 'string',
          enum: ['passport', 'driverLicense', 'nationalId', 'residencePermit'],
        },
      },
    },
  })
  @UseInterceptors(new FastifyFileInterceptor('file'))
  async uploadIdentity(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: /(pdf|jpg|jpeg|png)$/,
        })
        .addMaxSizeValidator({
          maxSize: FILE_UPLOAD.MAX_SIZE,
        })
        .build({
          errorHttpStatusCode: 400,
          fileIsRequired: true,
        }),
    )
    file: Express.Multer.File,
    @Body('documentType') documentType: string,
    @GetUser() authUser: User,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (!documentType) {
      throw new BadRequestException('Document type is required');
    }

    try {
      // Generate a unique path for the file
      const userId = authUser.userId || `temp_${Date.now()}`;
      const path = `identity-documents/${userId}/${documentType}`;
      const documentUrl = await this.digitalOceanSpacesService.uploadFile(file, path);

      return { documentUrl };
    } catch (error) {
      console.error('Error uploading identity document:', error);
      throw new BadRequestException('Failed to upload identity document. Please try again.');
    }
  }

  @Post('apply/:jobId')
  @ApiOperation({ summary: 'Apply for a job' })
  async applyForJob(
    @Param('jobId') jobId: string,
    @GetUser() authUser: User,
    @Body('coverLetter') coverLetter?: string,
    @Body('applicationReason') applicationReason?: string,
  ) {
    // Validate profile completion first
    const validationResult = await this.jobSeekerService.validateMandatoryFields(authUser);

    // If validation fails, return the validation result with an error status
    if (!validationResult.isValid) {
      throw new BadRequestException({
        message: 'Profile incomplete. Please complete your profile before applying.',
        validationResult,
      });
    }

    // If validation passes, proceed with the application
    const applicationResult = await this.jobSeekerService.applyForJob(
      authUser.userId,
      jobId,
      coverLetter,
      applicationReason,
    );

    // Send email notifications after successful application
    try {
      // Get job seeker and job information for emails
      const jobSeeker = await this.jobSeekerService.getByUserId(authUser.userId);
      const job = await this.jobSeekerService.findJobById(jobId);

      if (jobSeeker && job) {
        const candidateName =
          `${jobSeeker.firstName || ''} ${jobSeeker.lastName || ''}`.trim() || 'Job Seeker';
        const jobTitle = job.jobType || 'the position';
        const companyName = job.company?.companyName || job.companyName;
        if (!companyName) {
          this.logger.warn(`Job ${job.id} has no company name - skipping email notification`);
          // For job seekers, we might want to skip the email rather than throw
          // but log it for investigation
          return {
            success: true,
            applied: true,
            message: 'Application submitted successfully',
            warning: 'Email notification could not be sent due to missing company information',
          };
        }

        // Send automated confirmation email to candidate
        if (jobSeeker.email) {
          await this.emailService.sendJobApplicationSubmittedAutomated(
            jobSeeker.email,
            candidateName,
            jobTitle,
            companyName,
            undefined, // dashboardUrl - will use default
            undefined, // companyProfileUrl - TODO: add company profile URL if available
            authUser.userId,
          );
        }

        // Send notification email to company
        if (job.company?.contactEmail) {
          const applicationDate = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          });

          const viewApplicationUrl = `${process.env.APP_URL || 'https://app.kaleidotalent.com'}/applications?id=${job.id}`;
          const jobPostingUrl = `${process.env.APP_URL || 'https://app.kaleidotalent.com'}/open-jobs/${job.id}`;
          const matchRankUrl = `${process.env.APP_URL || 'https://app.kaleidotalent.com'}/jobs/${job.id}/candidates`;

          await this.emailService.sendJobApplicationNotificationToCompany(
            companyName,
            undefined, // recruiterName - we don't have this info yet
            jobTitle,
            candidateName,
            applicationDate,
            jobPostingUrl,
            viewApplicationUrl,
            matchRankUrl,
            job.company.contactEmail,
            authUser.userId,
          );
        }
      }
    } catch (emailError) {
      console.error('Failed to send application emails:', emailError);
      // Don't fail the application if email fails
    }

    return applicationResult;
  }

  @Get('client')
  @ApiOperation({ summary: 'Get job seeker by client ID' })
  async getJobSeekerByClientId(@GetUser() authUser: User, @Query('clientId') clientId?: string) {
    try {
      // Use either the provided clientId or the user's ID
      const userId = clientId || authUser.userId || authUser.sub;

      if (!userId) {
        throw new BadRequestException('User ID is required');
      }

      const jobSeeker = await this.jobSeekerService.getByUserId(userId);

      // Check if the user already has a role
      let existingRole = null;
      try {
        const rolesServiceInstance = await this.getRolesService();
        if (rolesServiceInstance) {
          existingRole = await rolesServiceInstance.findByClientId(userId);
          if (existingRole) {
          }
        }
      } catch (roleError) {
        console.error('Error checking existing role:', roleError);
      }

      if (!jobSeeker) {
        // If no job seeker found, create one with enhanced data
        const createDto: CreateJobSeekerDto = {
          clientId: userId,
          firstName: this.extractFirstName(authUser),
          lastName: this.extractLastName(authUser),
          email: authUser.email || '',
          role: UserRole.JOB_SEEKER,
        } as CreateJobSeekerDto; // Cast to allow additional properties

        // Add profile image if present in Auth0 user
        if (authUser.picture) {
          createDto.myProfileImage = authUser.picture;
        }

        const newJobSeeker = await this.jobSeekerService.create(createDto);

        // Only update the user's role to JOB_SEEKER if they don't already have a role
        if (!existingRole) {
          try {
            const rolesServiceInstance = await this.getRolesService();
            if (rolesServiceInstance) {
              await rolesServiceInstance.create({
                clientId: userId,
                role: UserRole.JOB_SEEKER,
              });
            }
          } catch (roleError) {
            console.error('Error creating user role:', roleError);
            // Continue even if role creation fails
          }
        } else {
        }

        return ProfileFormatter.toStandardProfile(newJobSeeker, true);
      }

      return ProfileFormatter.toStandardProfile(jobSeeker, true);
    } catch (error) {
      console.error('Error getting/creating job seeker:', error);
      throw error;
    }
  }

  @Get('applications')
  @ApiOperation({ summary: 'Get job seeker applications' })
  getApplications(@GetUser() authUser: User) {
    return this.jobSeekerService.getApplications(authUser.userId);
  }

  @Get('admin/all')
  // @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all job seekers with pagination (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of all job seekers',
  })
  async getAllJobSeekers(@Query('page') page = 1, @Query('limit') limit = 10) {
    return this.jobSeekerService.findAllPaginated(Number(page), Number(limit));
  }

  @Get('admin/grouped-by-status')
  // @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all job seekers grouped by approval status (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns job seekers grouped by approval status',
  })
  async getAllJobSeekersGroupedByStatus(): Promise<{
    approved: JobSeeker[];
    pending: JobSeeker[];
  }> {
    return this.jobSeekerService.findAllGroupedByStatus();
  }

  @Get()
  @ApiOperation({ summary: 'Get current user job seeker profile' })
  @ApiResponse({
    status: 200,
    description: 'Returns the job seeker profile for the current user',
  })
  @ApiResponse({
    status: 404,
    description: 'Job seeker profile not found - frontend should create one',
  })
  async getCurrentJobSeeker(@GetUser() user: User) {
    try {
      // Check user role first - only job seekers and graduates should have job seeker profiles
      const userRole = user.roles?.[0];
      if (userRole && userRole !== UserRole.JOB_SEEKER && userRole !== UserRole.GRADUATE) {
        // User is not a job seeker or graduate, return 404
        throw new NotFoundException({
          message: `User has role ${userRole}, not a job seeker`,
          userRole: userRole,
        });
      }

      // Try to find the job seeker (will not create one if it doesn't exist)
      let jobSeeker = await this.jobSeekerService.findByUser(user);

      // If no job seeker found, create a new one with Auth0 data
      if (!jobSeeker) {
        // Extract name parts from Auth0 user
        let firstName = '';
        let lastName = '';

        // Check if we have a full name in any field that we can split
        const fullNameSources = [user.name, user.fullName, user.nickname].filter(Boolean); // Remove undefined/null values

        let nameParts: string[] = [];
        for (const source of fullNameSources) {
          if (typeof source === 'string' && source.includes(' ')) {
            nameParts = source.split(' ');
            break;
          }
        }

        // Try to get first name from various sources
        if (user.given_name) {
          firstName = user.given_name;
        } else if (user.firstName) {
          firstName = user.firstName;
        } else if (nameParts.length > 0) {
          firstName = nameParts[0];
        } else if (user.name) {
          firstName = user.name; // Use full name as first name if no space
        }

        // Try to get last name from various sources
        if (user.family_name) {
          lastName = user.family_name;
        } else if (user.lastName) {
          lastName = user.lastName;
        } else if (nameParts.length > 1) {
          lastName = nameParts.slice(1).join(' ');
        }

        // Create a new job seeker with the extracted data
        try {
          const userId = user.sub || user.userId;
          if (!userId) {
            throw new BadRequestException('User ID is required');
          }

          // Prepare data for creating a new job seeker
          // Use extracted data if available, otherwise use placeholder values
          // but avoid using 'New User' as a placeholder when we have better data
          // Check if there's already a job seeker with an empty email before creating one
          if (!user.email) {
            // Check for existing job seekers with empty email
            const existingEmptyEmailUsers = await this.jobSeekerService.findByEmail('');
            if (existingEmptyEmailUsers && existingEmptyEmailUsers.length > 0) {
              // Check if any of these users don't have a userId set
              const userWithoutId = existingEmptyEmailUsers.find(
                (u) => !u.userId || u.userId === '',
              );
              if (userWithoutId) {
                userWithoutId.userId = userId;
                userWithoutId.clientId = userId;
                await this.jobSeekerService.update(
                  userWithoutId.id,
                  userWithoutId as CreateJobSeekerDto,
                );
                return ProfileFormatter.toStandardProfile(userWithoutId, true);
              }
            }
          }

          // Generate a unique placeholder email if none is provided
          const email = user.email || `placeholder-${userId}@placeholder.com`;

          const createDto: CreateJobSeekerDto = {
            userId,
            clientId: userId,
            firstName: firstName || '', // Empty string instead of 'New' to allow easier replacement later
            lastName: lastName || '', // Empty string instead of 'User' to allow easier replacement later
            email: email,
            role: UserRole.JOB_SEEKER,
            skills: [],
          } as CreateJobSeekerDto;

          // Add LinkedIn data if available
          if (user.linkedInProfile?.profile) {
            const linkedInProfile = user.linkedInProfile.profile;

            // Override name fields if available from LinkedIn
            if (linkedInProfile.firstName && linkedInProfile.firstName.trim() !== '') {
              createDto.firstName = linkedInProfile.firstName;
            }

            if (linkedInProfile.lastName && linkedInProfile.lastName.trim() !== '') {
              createDto.lastName = linkedInProfile.lastName;
            }

            // Override email if available from LinkedIn
            if (linkedInProfile.email && linkedInProfile.email.trim() !== '') {
              createDto.email = linkedInProfile.email;
            }

            // Only set LinkedIn URL if available
            if (linkedInProfile.profileUrl) {
              createDto.linkedinUrl = linkedInProfile.profileUrl;
            }

            // Set summary from headline or summary if available
            if (linkedInProfile.headline || linkedInProfile.summary) {
              createDto.summary = linkedInProfile.headline || linkedInProfile.summary;
            }

            // Set location if available
            if (linkedInProfile.location) {
              // Handle location if it comes as an object
              if (typeof linkedInProfile.location === 'object') {
                // Extract meaningful location string from object
                const locObj = linkedInProfile.location;
                if (locObj.country && locObj.city) {
                  createDto.location = `${locObj.city}, ${locObj.country}`;
                } else if (locObj.country) {
                  createDto.location = locObj.country;
                } else if (locObj.name) {
                  createDto.location = locObj.name;
                } else {
                  // Fallback to string representation if structure is unknown
                  createDto.location = Object.values(locObj).filter(Boolean).join(', ');
                }
              } else if (typeof linkedInProfile.location === 'string') {
                // If it's already a string, just use it
                createDto.location = linkedInProfile.location;
              }
            }

            // Process skills to ensure they're all strings (if available)
            if (linkedInProfile.skills && Array.isArray(linkedInProfile.skills)) {
              createDto.skills = linkedInProfile.skills
                .map((skill: any) => {
                  if (typeof skill === 'string') return skill;
                  if (typeof skill === 'object' && skill.name) return skill.name;
                  return null;
                })
                .filter((skill: string | null) => skill !== null);
            }

            // Process experience/positions if available
            if (linkedInProfile.positions && Array.isArray(linkedInProfile.positions)) {
              createDto.experience = linkedInProfile.positions.map((position: any) => {
                const startDate = position.startDate
                  ? new Date(position.startDate).toISOString()
                  : null;
                const endDate = position.endDate ? new Date(position.endDate).toISOString() : null;
                const duration = startDate
                  ? endDate
                    ? `${new Date(startDate).getFullYear()} - ${new Date(endDate).getFullYear()}`
                    : `${new Date(startDate).getFullYear()} - Present`
                  : '';
                return {
                  company: position.companyName || position.company || '',
                  title: position.title || '',
                  duration,
                  startDate,
                  endDate,
                };
              });
            }

            // Process education if available
            if (linkedInProfile.educations && Array.isArray(linkedInProfile.educations)) {
              createDto.education = linkedInProfile.educations.map((edu: any) => ({
                institution: edu.schoolName || edu.school || '',
                degree: edu.degree || '',
                field: edu.fieldOfStudy || '',
                startDate: edu.startDate ? new Date(edu.startDate) : undefined,
                endDate: edu.endDate ? new Date(edu.endDate) : undefined,
              }));
            }

            // Add these as custom properties using type assertion
            (createDto as any).isImportedFromLinkedIn = true;
            (createDto as any).linkedInImportDate = new Date();

            // Store the full LinkedIn profile for future reference
            (createDto as any).linkedInProfile = user.linkedInProfile;
          } else {
          }

          // Add profile image if present in Auth0 user
          if (user.picture) {
            createDto.myProfileImage = user.picture;
          }

          try {
            const newJobSeeker = await this.jobSeekerService.create(createDto);
            jobSeeker = newJobSeeker;
          } catch (createError: any) {
            // Handle unique constraint violations
            if (createError.code === '23505') {
              console.error(
                'Unique constraint violation when creating job seeker:',
                createError.detail,
              );

              // Try to find the job seeker again by userId
              const existingUser = await this.jobSeekerService.getByUserId(userId);
              if (existingUser) {
                jobSeeker = existingUser;
              }
              // If the error is about email constraint, try to find by email
              else if (createError.detail && createError.detail.includes('email')) {
                const emailUsers = await this.jobSeekerService.findByEmail(createDto.email);
                if (emailUsers && emailUsers.length > 0) {
                  const existingEmailUser = emailUsers[0];

                  // Update the userId if it's not set
                  if (!existingEmailUser.userId) {
                    existingEmailUser.userId = userId;
                    await this.jobSeekerService.update(
                      existingEmailUser.id,
                      existingEmailUser as CreateJobSeekerDto,
                    );
                  }

                  jobSeeker = existingEmailUser;
                } else {
                  throw createError;
                }
              } else {
                throw createError;
              }
            } else {
              throw createError;
            }
          }

          // Also update the user's role to JOB_SEEKER
          try {
            const rolesServiceInstance = await this.getRolesService();
            if (rolesServiceInstance) {
              await rolesServiceInstance.updateRole(userId, UserRole.JOB_SEEKER);
            }
          } catch (roleError) {
            console.error('Error updating user role:', roleError);
            // Continue even if role update fails
          }

          // jobSeeker variable is set in the try/catch block above
        } catch (error) {
          console.error('Error creating job seeker:', error);
          throw new InternalServerErrorException({
            message: 'Failed to create job seeker profile',
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // Also run validation to populate the onboardingProgress
      const validationResult = await this.jobSeekerService.validateMandatoryFields(user);

      // Make sure jobSeeker is not null at this point
      if (!jobSeeker) {
        throw new NotFoundException('Failed to find or create job seeker profile');
      }

      const standardProfile = ProfileFormatter.toStandardProfile(jobSeeker, true);
      standardProfile.validation = {
        isValid: validationResult.isValid,
        missingFields: validationResult.missingFields,
        mandatoryMissingFields: validationResult.mandatoryMissingFields,
        hasCompletedOnboarding: validationResult.hasCompletedOnboarding,
        completion: validationResult.completion,
        onboardingProgress: jobSeeker.onboardingProgress,
      };

      return standardProfile;
    } catch (error) {
      console.error('Error getting job seeker:', error);
      throw error;
    }
  }

  @Get(':id/candidates')
  @ApiOperation({ summary: 'Get job seeker candidate profiles' })
  getCandidates(@Param('id') id: string) {
    return this.jobSeekerService.getJobSeekerCandidates(id);
  }

  @Patch('applications/:applicationId/withdraw')
  @ApiOperation({ summary: 'Withdraw job application by application ID' })
  async withdrawApplication(
    @GetUser() authUser: User,
    @Param('applicationId') applicationId: string,
    @Body() body: { reason?: string },
  ) {
    try {
      await this.jobSeekerService.withdrawApplication(authUser.userId, applicationId, body.reason);

      return {
        success: true,
        message: 'Application withdrawn successfully',
      };
    } catch (error) {
      console.error('Error withdrawing application:', error);
      throw error;
    }
  }

  @Patch('jobs/:jobId/withdraw-application')
  @ApiOperation({ summary: 'Withdraw job application by job ID' })
  async withdrawApplicationByJobId(
    @GetUser() authUser: User,
    @Param('jobId') jobId: string,
    @Body() body: { reason?: string },
  ) {
    try {
      await this.jobSeekerService.withdrawApplicationByJobId(authUser.userId, jobId, body.reason);

      return {
        success: true,
        message: 'Application withdrawn successfully',
      };
    } catch (error) {
      console.error('Error withdrawing application:', error);
      throw error;
    }
  }

  @Post(':id/video-intro')
  @UseInterceptors(new FastifyFileInterceptor('file'))
  @ApiOperation({ summary: 'Upload video introduction' })
  @ApiConsumes('multipart/form-data')
  async uploadVideoIntro(@Param('id') id: string, @UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }
    return this.jobSeekerService.uploadVideoIntro(id, file);
  }

  @Get(':id/export-json')
  @ApiOperation({ summary: 'Export resume data as JSON' })
  async exportResumeJson(@Param('id') id: string) {
    return this.jobSeekerService.exportResumeJson(id);
  }

  @Post(':id/import/linkedin')
  @ApiOperation({ summary: 'Import profile from LinkedIn' })
  async importFromLinkedIn(@Param('id') id: string, @Body() linkedInData: any) {
    return this.jobSeekerService.importFromLinkedIn(id, linkedInData);
  }

  @Post(':id/verify/:type')
  @ApiOperation({ summary: 'Request verification for specific credential' })
  async requestVerification(
    @Param('id') id: string,
    @Param('type') type: 'email' | 'phone' | 'education' | 'employment',
  ) {
    return this.jobSeekerService.generateVerificationToken(id, type);
  }

  @Get(':id/share')
  @ApiOperation({ summary: 'Generate shareable profile link' })
  async shareProfile(@Param('id') id: string, @Query('platform') platform: string) {
    return this.jobSeekerService.shareProfile(id, platform);
  }

  @Post('validate-profile')
  @ApiOperation({ summary: 'Check if all mandatory fields are filled in' })
  @ApiResponse({
    status: 200,
    description: 'Returns validation status, completion percentages, and full record',
  })
  async validateMandatoryFields(@Body() body: User) {
    return await this.jobSeekerService.validateMandatoryFields(body);
  }

  @Post('draft')
  @ApiOperation({ summary: 'Create a draft job seeker profile' })
  @ApiResponse({ status: 201, description: 'Draft profile created successfully' })
  async createDraft(@Body() createDraftDto: CreateJobSeekerDraftDto) {
    return this.jobSeekerService.createDraft(createDraftDto);
  }

  @Post(':userId/profile-image')
  @UseInterceptors(new FastifyFileInterceptor('image'))
  async uploadProfileImage(
    @Param('userId') userId: string,
    @GetUser() user: User,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!userId || userId.trim() === '') {
      throw new BadRequestException('Invalid or missing user ID');
    }

    if (!file) {
      throw new BadRequestException('No image file uploaded');
    }

    return this.jobSeekerService.uploadProfileImage(user, file);
  }

  @Post('profile-image')
  @Roles(UserRole.JOB_SEEKER)
  @UseInterceptors(new FastifyFileInterceptor('image'))
  async uploadProfileImageForCurrentUser(
    @GetUser() user: User,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (!user || !user.userId) {
      throw new BadRequestException('User authentication required');
    }

    if (!file) {
      throw new BadRequestException('No image file uploaded');
    }

    return this.jobSeekerService.uploadProfileImage(user, file);
  }

  @Put(':id/resume-update')
  @Roles(UserRole.JOB_SEEKER)
  @ApiOperation({
    summary: 'Update job seeker profile after resume upload with relaxed validation',
  })
  async updateAfterResumeUpload(
    @Param('id') id: string,
    @Body() updateJobSeekerDto: any,
    @GetUser() user: User,
  ) {
    // Make sure the user only updates their own profile
    const jobSeeker = await this.jobSeekerService.getByUserId(user.userId);
    if (!jobSeeker || jobSeeker.id !== id) {
      throw new NotFoundException(
        'Job seeker not found or you do not have permission to update this profile',
      );
    }

    try {
      // Log the attempt to update after resume upload

      // More aggressive sanitization of education data
      if (updateJobSeekerDto.education) {
        // Filter out any invalid education entries first
        updateJobSeekerDto.education = updateJobSeekerDto.education
          .filter((edu: any) => edu && typeof edu === 'object')
          .map((edu: any, index: number) => {
            // Log problematic entries
            if (typeof edu.degree !== 'string') {
            }

            return {
              // Ensure these are always strings with fallbacks
              institution: typeof edu.institution === 'string' ? edu.institution : 'Not specified',
              degree: typeof edu.degree === 'string' ? edu.degree : 'Not specified',
              field: typeof edu.field === 'string' ? edu.field : 'Not specified',
              // Handle dates
              startDate: edu.startDate ? new Date(edu.startDate) : new Date(),
              endDate: edu.endDate ? new Date(edu.endDate) : null,
              description: typeof edu.description === 'string' ? edu.description : '',
            };
          });
      }

      // Sanitize certification data
      if (updateJobSeekerDto.certifications) {
        updateJobSeekerDto.certifications = updateJobSeekerDto.certifications
          .filter((cert: any) => cert && typeof cert === 'object')
          .map((cert: any) => ({
            name: typeof cert.name === 'string' ? cert.name : 'Not specified',
            issuer: typeof cert.issuer === 'string' ? cert.issuer : 'Not specified',
            issueDate: cert.issueDate ? new Date(cert.issueDate) : new Date(),
            expiryDate: cert.expiryDate ? new Date(cert.expiryDate) : null,
            credentialId: typeof cert.credentialId === 'string' ? cert.credentialId : '',
            credentialUrl:
              cert.credentialUrl && typeof cert.credentialUrl === 'string'
                ? cert.credentialUrl.startsWith('http')
                  ? cert.credentialUrl
                  : `https://${cert.credentialUrl}`
                : 'https://example.com',
          }));
      }

      // Update with sanitized data
      return this.jobSeekerService.update(id, {
        ...jobSeeker,
        ...updateJobSeekerDto,
      } as CreateJobSeekerDto);
    } catch (error: any) {
      console.error('Error updating job seeker after resume upload:', error);
      throw new BadRequestException(
        `Failed to update profile after resume upload: ${error.message || 'Please try again.'}`,
      );
    }
  }

  // Temporary user endpoints for onboarding without authentication
  @Post('temp')
  @Public()
  @ApiOperation({ summary: 'Create temporary job seeker profile for onboarding' })
  @ApiResponse({ status: 201, description: 'Temporary profile created successfully' })
  async createTempJobSeeker(
    @Body() createJobSeekerDto: CreateJobSeekerDto,
    @Headers('x-temp-user-id') tempUserId: string,
  ) {
    if (!tempUserId) {
      throw new BadRequestException('Temporary user ID is required');
    }

    try {
      // Create job seeker with temporary clientId
      const tempJobSeeker = await this.jobSeekerService.create({
        ...createJobSeekerDto,
        clientId: tempUserId,
        userId: tempUserId,
      });

      return {
        success: true,
        message: 'Temporary job seeker profile created successfully',
        data: tempJobSeeker,
        tempUserId,
      };
    } catch (error: any) {
      console.error('Error creating temporary job seeker:', error);
      throw new BadRequestException(
        `Failed to create temporary profile: ${error.message || 'Please try again.'}`,
      );
    }
  }

  @Put('temp')
  @Public()
  @ApiOperation({ summary: 'Update temporary job seeker profile during onboarding' })
  @ApiResponse({ status: 200, description: 'Temporary profile updated successfully' })
  async updateTempJobSeeker(
    @Body() updateJobSeekerDto: Partial<CreateJobSeekerDto>,
    @Headers('x-temp-user-id') tempUserId: string,
  ) {
    if (!tempUserId) {
      throw new BadRequestException('Temporary user ID is required');
    }

    try {
      // Find and update the temporary job seeker
      const existingJobSeeker = await this.jobSeekerService.getByClientId(tempUserId);

      if (!existingJobSeeker) {
        // Create if doesn't exist
        return this.createTempJobSeeker(updateJobSeekerDto as CreateJobSeekerDto, tempUserId);
      }

      // Update existing temporary profile
      const updatedJobSeeker = await this.jobSeekerService.update(existingJobSeeker.id, {
        ...existingJobSeeker,
        ...updateJobSeekerDto,
        clientId: tempUserId,
        userId: tempUserId,
      } as CreateJobSeekerDto);

      return {
        success: true,
        message: 'Temporary job seeker profile updated successfully',
        data: updatedJobSeeker,
        tempUserId,
      };
    } catch (error: any) {
      console.error('Error updating temporary job seeker:', error);
      throw new BadRequestException(
        `Failed to update temporary profile: ${error.message || 'Please try again.'}`,
      );
    }
  }

  @Get('temp')
  @Public()
  @ApiOperation({ summary: 'Get temporary job seeker profile during onboarding' })
  @ApiResponse({ status: 200, description: 'Temporary profile retrieved successfully' })
  async getTempJobSeeker(@Headers('x-temp-user-id') tempUserId: string) {
    if (!tempUserId) {
      throw new BadRequestException('Temporary user ID is required');
    }

    try {
      const tempJobSeeker = await this.jobSeekerService.getByClientId(tempUserId);

      if (!tempJobSeeker) {
        return {
          success: false,
          message: 'Temporary profile not found',
          data: null,
          tempUserId,
        };
      }

      return {
        success: true,
        message: 'Temporary job seeker profile retrieved successfully',
        data: tempJobSeeker,
        tempUserId,
      };
    } catch (error: any) {
      console.error('Error retrieving temporary job seeker:', error);
      throw new BadRequestException(
        `Failed to retrieve temporary profile: ${error.message || 'Please try again.'}`,
      );
    }
  }
}
