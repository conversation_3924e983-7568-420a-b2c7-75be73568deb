import * as React from 'react';

import { But<PERSON>, Heading, Hr, Text } from '@react-email/components';

import { EmailLayout } from './components/EmailLayout';

interface CulturalFitEmployerNotificationEmailProps {
  employerName: string;
  candidateName: string;
  jobTitle: string;
  companyName?: string;
  dashboardUrl?: string;
}

export const CulturalFitEmployerNotificationEmail: React.FC<
  CulturalFitEmployerNotificationEmailProps
> = ({
  employerName,
  candidateName,
  jobTitle,
  companyName = 'Kaleido Talent',
  dashboardUrl = `${process.env.APP_URL}/dashboard`,
}) => {
  const preview = `${candidateName} has completed their Video Introduction Assessment`;

  return (
    <EmailLayout preview={preview}>
      <Heading style={h1}>New Video Introduction Assessment Completed 🎯</Heading>
      <Text style={text}>Dear {employerName},</Text>
      <Text style={text}>
        {candidateName} has completed their Video Intro Assessment for the {jobTitle} position at{' '}
        {companyName}.
      </Text>
      <Text style={text}>
        You can now review their responses and evaluate how well they align with your company
        culture. This assessment will help you make a more informed decision about the candidate's
        potential fit within your team.
      </Text>
      <Text style={text}>Click the button below to review their responses:</Text>
      <Button style={button} href={dashboardUrl}>
        Review Responses
      </Button>
      <Hr style={hr} />
      <Text style={footer}>
        This is an automated notification from the Kaleido Talent recruitment platform. If you have
        any questions, please contact our support team.
      </Text>
    </EmailLayout>
  );
};

const h1 = {
  color: '#1d1c1d',
  fontSize: '36px',
  fontWeight: '700',
  margin: '30px 0',
  padding: '0',
  lineHeight: '42px',
};

const text = {
  color: '#1d1c1d',
  fontSize: '18px',
  lineHeight: '28px',
  marginBottom: '20px',
};

const button = {
  backgroundColor: '#2563eb',
  borderRadius: '4px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px',
  margin: '20px 0',
};

const hr = {
  borderColor: '#e5e5e5',
  margin: '20px 0',
};

const footer = {
  color: '#9ca3af',
  fontSize: '14px',
  marginTop: '20px',
};

export default CulturalFitEmployerNotificationEmail;
