import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailLayout } from './components/EmailLayout';

export interface MidTrialCheckInEmailProps {
  contactName: string;
  contactEmail: string;
  contactPhone?: string;
  companyName: string;
  bookingUrl: string;
  roadmapUrl: string;
}

export function MidTrialCheckInEmail({
  contactName,
  contactEmail,
  contactPhone,
  companyName,
  bookingUrl,
  roadmapUrl,
}: MidTrialCheckInEmailProps) {
  const previewText = `How's your first week going? (Quick check-in) 👋`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <EmailLayout>
          <Container style={container}>
            <Heading style={h1}>How's your first week going? (Quick check-in) 👋</Heading>
            
            <Text style={text}>Hi {contactName},</Text>
            
            <Text style={text}>
              Just wanted to check in; how's your first week with Kale<PERSON> going? Anything you require help with? 
              Don't be shy to reach out via the help chat or book a call.
            </Text>

            <Section style={highlightSection}>
              <Text style={highlightTitle}>Quick Question:</Text>
              <Text style={highlightText}>What's been your biggest "aha moment" so far?</Text>
            </Section>

            <Section style={section}>
              <Text style={sectionTitle}>Need a Quick Boost?</Text>
              <Text style={text}>
                If you want to maximize your trial, here are two things that make the biggest difference:
              </Text>
              <Text style={listItem}>
                1. Try the <strong>AI Video job description on your socials and share it with us to post on ours</strong>
              </Text>
              <Text style={listItem}>
                2. <strong>Use your job board link whenever you post the job on other platforms</strong>
              </Text>
            </Section>

            <Section style={section}>
              <Text style={sectionTitle}>Stuck on anything?</Text>
              <Text style={text}>
                I'm here to help. Book a 15-minute call or just reply to this email.
              </Text>
              <Button href={bookingUrl} style={button}>
                Book a Quick Call
              </Button>
            </Section>

            <Text style={text}>Your success is our success - let's make this trial amazing!</Text>

            <Hr style={hr} />

            <Text style={footerText}>
              P.S.: Check out our <Link href={roadmapUrl} style={link}>comment section for our Product Roadmap</Link> and 
              let us know what matters most to you. We are building for YOU after all.
            </Text>

            {contactPhone && (
              <Section style={contactSection}>
                <Text style={contactInfo}>
                  Contact: {contactName}<br />
                  Email: {contactEmail}<br />
                  Phone: {contactPhone}
                </Text>
              </Section>
            )}

            <Text style={signature}>
              Cheers,<br />
              Team Kaleido
            </Text>
          </Container>
        </EmailLayout>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 20px 48px',
  marginBottom: '64px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '32px',
  margin: '16px 0',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '16px 0',
};

const section = {
  margin: '24px 0',
  padding: '16px',
  backgroundColor: '#f8f9fa',
  borderRadius: '8px',
};

const highlightSection = {
  margin: '24px 0',
  padding: '20px',
  backgroundColor: '#e0f2fe',
  borderRadius: '8px',
  borderLeft: '4px solid #0ea5e9',
};

const highlightTitle = {
  color: '#0c4a6e',
  fontSize: '16px',
  fontWeight: '600',
  marginBottom: '8px',
};

const highlightText = {
  color: '#075985',
  fontSize: '16px',
  lineHeight: '24px',
};

const sectionTitle = {
  color: '#333',
  fontSize: '18px',
  fontWeight: '600',
  marginBottom: '12px',
};

const listItem = {
  color: '#555',
  fontSize: '15px',
  lineHeight: '24px',
  margin: '8px 0',
};

const button = {
  backgroundColor: '#6366f1',
  borderRadius: '8px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
  margin: '16px 0',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '30px 0',
};

const footerText = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  fontStyle: 'italic',
};

const link = {
  color: '#6366f1',
  textDecoration: 'underline',
};

const contactSection = {
  marginTop: '20px',
  padding: '12px',
  backgroundColor: '#f0f4f8',
  borderRadius: '6px',
};

const contactInfo = {
  color: '#555',
  fontSize: '14px',
  lineHeight: '20px',
};

const signature = {
  color: '#333',
  fontSize: '15px',
  lineHeight: '22px',
  marginTop: '20px',
};

export default MidTrialCheckInEmail;