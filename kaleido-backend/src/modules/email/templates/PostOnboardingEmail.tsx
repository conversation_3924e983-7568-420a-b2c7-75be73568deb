import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailLayout } from './components/EmailLayout';

export interface PostOnboardingEmailProps {
  companyName: string;
  contactName: string;
  contactEmail: string;
  contactPhone?: string;
  dashboardUrl: string;
  demoVideoUrl: string;
  supportUrl: string;
  roadmapUrl: string;
}

export function PostOnboardingEmail({
  companyName,
  contactName,
  contactEmail,
  contactPhone,
  dashboardUrl,
  demoVideoUrl,
  supportUrl,
  roadmapUrl,
}: PostOnboardingEmailProps) {
  const previewText = `Your Kaleido journey starts now (plus your demo video) 🚀`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <EmailLayout>
          <Container style={container}>
            <Heading style={h1}>Your Kaleido journey starts now (plus your demo video) 🚀</Heading>
            
            <Text style={text}>Hi {companyName} team,</Text>
            
            <Text style={text}>
              Thanks for a great onboarding call! We loved hearing about your goals — and we're excited to help you move 
              beyond the usual routines and build a future-ready way to connect with the right talent.
            </Text>

            <Text style={text}>Here's everything we covered, so you don't have to take notes:</Text>

            <Section style={section}>
              <Text style={sectionTitle}>Your Personalized Setup:</Text>
              <Text style={listItem}>• Dashboard walkthrough completed</Text>
              <Text style={listItem}>• Your Branding setup walkthrough completed</Text>
              <Text style={listItem}>• Any integration required tested or scheduled</Text>
            </Section>

            <Section style={section}>
              <Text style={sectionTitle}>Your Demo Video (bookmark this!)</Text>
              <Button href={demoVideoUrl} style={button}>
                Watch Full Demo
              </Button>
              <Text style={smallText}>
                We recorded this for your team. Share it with anyone who needs to get up to speed quickly.
              </Text>
            </Section>

            <Section style={section}>
              <Text style={sectionTitle}>What's Next:</Text>
              <Text style={listItem}>1. Post your first job within 24 hours (while everything's fresh)</Text>
              <Text style={listItem}>2. Check out the quick-start guide we've tailored for you (Hit the Question Mark in the platform)</Text>
              <Text style={listItem}>3. Remember: Your first 30 days include unlimited support</Text>
            </Section>

            <Section style={section}>
              <Text style={sectionTitle}>Your Trial Perks:</Text>
              <Text style={perkItem}>🎁 <strong>50 bonus credits</strong> (we've already added them to your account)</Text>
              <Text style={perkItem}>🎁 <strong>Free job description review</strong> by our team</Text>
              <Text style={perkItem}>🎁 <strong>Priority support</strong> - We respond within 6 hours</Text>
              <Text style={perkItem}>🎁 <strong>Extended trial</strong> - You get 28 days instead of 14</Text>
            </Section>

            <Text style={text}>
              Questions? Just hit reply. We're together on this journey and here to make this transition smooth and successful.
            </Text>

            <Text style={text}>Looking forward to seeing your first successful hires with Kaleido!</Text>

            <Hr style={hr} />

            <Text style={footerText}>
              P.S.: Check out our <Link href={roadmapUrl} style={link}>comment section for our Product Roadmap</Link> and 
              let us know what matters most to you. We are building for YOU after all.
            </Text>

            {contactPhone && (
              <Section style={contactSection}>
                <Text style={contactInfo}>
                  Contact: {contactName}<br />
                  Email: {contactEmail}<br />
                  Phone: {contactPhone}
                </Text>
              </Section>
            )}

            <Text style={signature}>
              Best,<br />
              Team Kaleido
            </Text>
          </Container>
        </EmailLayout>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 20px 48px',
  marginBottom: '64px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '32px',
  margin: '16px 0',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '16px 0',
};

const section = {
  margin: '24px 0',
  padding: '16px',
  backgroundColor: '#f8f9fa',
  borderRadius: '8px',
};

const sectionTitle = {
  color: '#333',
  fontSize: '18px',
  fontWeight: '600',
  marginBottom: '12px',
};

const listItem = {
  color: '#555',
  fontSize: '15px',
  lineHeight: '24px',
  margin: '4px 0',
};

const perkItem = {
  color: '#555',
  fontSize: '15px',
  lineHeight: '26px',
  margin: '8px 0',
};

const button = {
  backgroundColor: '#6366f1',
  borderRadius: '8px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
  margin: '16px 0',
};

const smallText = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '8px 0',
};

const hr = {
  borderColor: '#e6ebf1',
  margin: '30px 0',
};

const footerText = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  fontStyle: 'italic',
};

const link = {
  color: '#6366f1',
  textDecoration: 'underline',
};

const contactSection = {
  marginTop: '20px',
  padding: '12px',
  backgroundColor: '#f0f4f8',
  borderRadius: '6px',
};

const contactInfo = {
  color: '#555',
  fontSize: '14px',
  lineHeight: '20px',
};

const signature = {
  color: '#333',
  fontSize: '15px',
  lineHeight: '22px',
  marginTop: '20px',
};

export default PostOnboardingEmail;