import { Link } from '@react-email/components';
import * as React from 'react';
import { baseStyles } from './components';
import {
  Card,
  CTASection,
  Divider,
  EmailHeading,
  EmailText,
  IconText,
  List,
} from './components/EmailComponents';
import { EnhancedEmailLayout } from './components/EnhancedEmailLayout';

interface JobApplicationSubmittedAutomatedProps {
  talentName: string;
  jobTitle: string;
  companyName: string;
  dashboardUrl?: string;
  companyProfileUrl?: string;
}

export const JobApplicationSubmittedAutomated: React.FC<JobApplicationSubmittedAutomatedProps> = ({
  talentName,
  jobTitle,
  companyName,
  dashboardUrl = `${process.env.APP_URL}/applications`,
  companyProfileUrl,
}) => {
  const preview = `Your application is on its way! Here's what happens next 📨`;

  return (
    <EnhancedEmailLayout preview={preview}>
      <EmailHeading level={1} emoji="📨">
        Your application is on its way! Here's what happens next
      </EmailHeading>

      <EmailText>Hi {talentName},</EmailText>

      <EmailText>
        Your application for <strong>{jobTitle}</strong> at <strong>{companyName}</strong> has been
        successfully submitted!
      </EmailText>

      <EmailHeading level={2}>What's next:</EmailHeading>

      <Card variant="info">
        <EmailText>
          If there's a match, the next step might include a short video introduction — just a simple
          way for the team to get to know the person behind the profile.
        </EmailText>
      </Card>

      <EmailHeading level={2}>What You Can Do Now:</EmailHeading>

      <List
        icon="✓"
        items={[
          <>
            <strong>Keep applying</strong> - Don't put all your eggs in one basket
          </>,
          <>
            <strong>Update your profile</strong> – Make sure it reflects your latest skills,
            projects, and aspirations. Maybe record a video and show them who you are?
          </>,
          <>
            <strong>Check your dashboard</strong> - New opportunities are added daily
          </>,
          <>
            <strong>Prepare for success</strong> - Research the company and role
          </>,
          <>
            <strong>Stay available</strong> - Great opportunities move fast, so keep an eye on your
            inbox (always check your spam)!
          </>,
        ]}
      />

      <CTASection
        buttonText="Track Your Application"
        buttonHref={dashboardUrl}
        description="Monitor your application status in your dashboard"
      />

      {companyProfileUrl && (
        <Card variant="highlight">
          <IconText
            icon="💡"
            text={
              <>
                <strong>Pro Tip:</strong> Companies love candidates who show genuine interest. If
                you haven't already,{' '}
                <Link href={companyProfileUrl} style={baseStyles.link}>
                  check out {companyName}'s profile
                </Link>{' '}
                on our platform to learn more about their culture and values.
              </>
            }
          />
        </Card>
      )}

      <EmailText>Questions about the role or process? Just reply to this email.</EmailText>

      <EmailText>Fingers crossed - this could be the one!</EmailText>

      <Divider spacing="large" />

      <EmailText variant="small" color="muted">
        Best,
        <br />
        The Kaleido Team
      </EmailText>
    </EnhancedEmailLayout>
  );
};

export default JobApplicationSubmittedAutomated;
