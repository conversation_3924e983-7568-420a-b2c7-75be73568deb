import * as React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { EmailLayout } from './components/EmailLayout';

export interface JobPostedFirstEmailProps {
  contactName: string;
  contactEmail: string;
  contactPhone?: string;
  companyName: string;
  jobTitle: string;
  dashboardUrl: string;
  bookingUrl: string;
  supportUrl: string;
}

export function JobPostedFirstEmail({
  contactName,
  contactEmail,
  contactPhone,
  companyName,
  jobTitle,
  dashboardUrl,
  bookingUrl,
  supportUrl,
}: JobPostedFirstEmailProps) {
  const previewText = `Your first job is live! Here's what happens next 🎉`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <EmailLayout>
          <Container style={container}>
            <Heading style={h1}>Your first job is live! Here's what happens next 🎉</Heading>
            
            <Text style={text}>Hi {contactName},</Text>
            
            <Text style={text}>
              Congratulations! Your first job post "{jobTitle}" is now live on Kaleido.
            </Text>

            <Section style={section}>
              <Text style={sectionTitle}>Pro Tips for Maximum Results:</Text>
              <Text style={listItem}>
                1. <strong>Check your dashboard daily</strong> - great candidates move fast
              </Text>
              <Text style={listItem}>
                2. <strong>Respond quickly</strong> - our data shows 24-hour response increases hire rates by 30%
              </Text>
              <Text style={listItem}>
                3. <strong>Use the video intro feature</strong> - it helps with decision-making later
              </Text>
            </Section>

            <Section style={section}>
              <Text style={sectionTitle}>Watch for These Signals:</Text>
              <Text style={listItem}>
                • <strong>High match scores</strong> (80%+) - These can be game-changers
              </Text>
              <Text style={listItem}>
                • <strong>Video introductions</strong> - Candidates who record these are 2x more likely to accept offers
              </Text>
              <Text style={listItem}>
                • <strong>Quick applications</strong> - Applied within 24 hours usually means genuine interest
              </Text>
            </Section>

            <Button href={dashboardUrl} style={button}>
              Check Your Dashboard
            </Button>

            <Section style={section}>
              <Text style={sectionTitle}>Need Help?</Text>
              <Text style={listItem}>
                • <strong>Candidate questions?</strong> Use our built-in messaging (Intercom)
              </Text>
              <Text style={listItem}>
                • <strong>Technical issues?</strong> We're here within 2 hours
              </Text>
              <Text style={listItem}>
                • <strong>Strategy advice?</strong> <Link href={bookingUrl} style={link}>Book a quick call</Link>
              </Text>
            </Section>

            <Text style={text}>
              Your perfect hire might be applying right now. Let's make it happen!
            </Text>

            {contactPhone && (
              <Section style={contactSection}>
                <Text style={contactInfo}>
                  Contact: {contactName}<br />
                  Email: {contactEmail}<br />
                  Phone: {contactPhone}
                </Text>
              </Section>
            )}

            <Text style={signature}>
              Cheers,<br />
              Kaleido Talent Team
            </Text>
          </Container>
        </EmailLayout>
      </Body>
    </Html>
  );
}

const main = {
  backgroundColor: '#f6f9fc',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 20px 48px',
  marginBottom: '64px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '32px',
  margin: '16px 0',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '16px 0',
};

const section = {
  margin: '24px 0',
  padding: '16px',
  backgroundColor: '#f8f9fa',
  borderRadius: '8px',
};

const sectionTitle = {
  color: '#333',
  fontSize: '18px',
  fontWeight: '600',
  marginBottom: '12px',
};

const listItem = {
  color: '#555',
  fontSize: '15px',
  lineHeight: '24px',
  margin: '8px 0',
};

const button = {
  backgroundColor: '#6366f1',
  borderRadius: '8px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: '600',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
  margin: '20px 0',
};

const link = {
  color: '#6366f1',
  textDecoration: 'underline',
};

const contactSection = {
  marginTop: '20px',
  padding: '12px',
  backgroundColor: '#f0f4f8',
  borderRadius: '6px',
};

const contactInfo = {
  color: '#555',
  fontSize: '14px',
  lineHeight: '20px',
};

const signature = {
  color: '#333',
  fontSize: '15px',
  lineHeight: '22px',
  marginTop: '20px',
};

export default JobPostedFirstEmail;