import * as React from 'react';
import { EnhancedEmailLayout } from './components/EnhancedEmailLayout';
import {
  EmailHeading,
  EmailText,
  Card,
  Divider,
  List,
  CTASection,
  Signature,
  IconText,
} from './components/EmailComponents';
import { Link } from '@react-email/components';
import { baseStyles } from './components';

interface InterestEmailProps {
  candidateName: string;
  jobTitle: string;
  roleName: string;
  companyName: string;
  roleDetailsUrl?: string;
  companyProfileUrl?: string;
  yourName: string;
  yourTitle: string;
  replyEmail?: string;
}

export const InterestEmail: React.FC<InterestEmailProps> = ({
  candidateName,
  jobTitle,
  roleName,
  companyName,
  roleDetailsUrl,
  companyProfileUrl,
  yourName,
  yourTitle,
  replyEmail = '<EMAIL>',
}) => {
  const preview = `Your profile caught our attention for a ${jobTitle} role at ${companyName}`;

  return (
    <EnhancedEmailLayout preview={preview}>
      <EmailHeading level={1} emoji="✨">
        Your profile caught our attention
      </EmailHeading>
      
      <EmailText>Hi {candidateName},</EmailText>
      
      <EmailText>
        I hope this message finds you well!
      </EmailText>
      
      <EmailText>
        I'm reaching out from {companyName} because your background and experience really stood out to us. 
        We're currently looking for a <strong>{roleName}</strong>, and based on what we've seen, 
        we think you could be a great fit for our team.
      </EmailText>
      
      <Card variant="highlight">
        <IconText 
          icon="🎯" 
          text={<><strong>Why we're interested:</strong></>}
        />
        <EmailText>
          Your skills and experience align well with what we're looking for, and we believe you could 
          bring valuable expertise to our team. We'd love to explore whether this opportunity might be 
          of interest to you.
        </EmailText>
      </Card>
      
      <EmailHeading level={2}>About the opportunity</EmailHeading>
      
      <EmailText>
        We're building something exciting at {companyName}, and this role is a key part of our growth. 
        Here's what makes this opportunity special:
      </EmailText>
      
      <List 
        icon="✓"
        items={[
          'Opportunity to make a real impact in a growing company',
          'Work with a talented and collaborative team',
          'Competitive compensation and benefits',
          'Room for professional growth and development'
        ]}
      />
      
      {(roleDetailsUrl || companyProfileUrl) && (
        <Card>
          {roleDetailsUrl && (
            <EmailText>
              <Link href={roleDetailsUrl} style={baseStyles.link}>View full role details →</Link>
            </EmailText>
          )}
          {companyProfileUrl && (
            <EmailText>
              <Link href={companyProfileUrl} style={baseStyles.link}>Learn more about {companyName} →</Link>
            </EmailText>
          )}
        </Card>
      )}
      
      <EmailHeading level={2}>Interested in learning more?</EmailHeading>
      
      <EmailText>
        I know you might not be actively looking right now, but I'd love to have a brief conversation 
        to share more about this opportunity and learn about your career goals. Even if the timing 
        isn't right, it could be valuable to connect for the future.
      </EmailText>
      
      <EmailText>
        <strong>Would you be open to a quick chat?</strong> Feel free to reply to this email 
        with your thoughts or any questions you might have. I'm happy to work around your schedule.
      </EmailText>
      
      <CTASection
        buttonText="Reply to express interest"
        buttonHref={`mailto:${replyEmail}?subject=Re: ${roleName} opportunity at ${companyName}`}
      />
      
      <EmailText>
        If this isn't the right fit or timing for you, no worries at all — I appreciate you taking 
        the time to read this, and I wish you all the best in your career journey.
      </EmailText>
      
      <EmailText>
        Looking forward to hearing from you!
      </EmailText>
      
      <Divider spacing="large" />
      
      <Signature
        name={yourName}
        title={yourTitle}
        company={companyName}
      />
    </EnhancedEmailLayout>
  );
};

export default InterestEmail;