import * as React from 'react';

import { Resend } from 'resend';
import { Repository } from 'typeorm';

import { emailConfig } from '@/config/email.config';
import { ActivityType } from '@/shared/types/activity.types';
import { WHITELISTS_SUPPORT_EMAIL_ADDRESSES } from '@/shared/types/constants';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { render } from '@react-email/render';
import { emailPreviewGenerator } from './email.preview';
import { EmailTemplates } from './email.templates';
import {
  EmailMetadataBuilder,
  formatEmailDate,
  getDashboardUrl,
  logEmailToEntity,
  needsCulturalFitSetup,
} from './email.utils';
import { EmailSubjectBuilder, getEmailTypeFromStatus } from './templates/template.utils';
import { EmailActivityLogger } from './email.activity';
import { EmailPatterns, EmailWrapper } from './email.wrapper';
import { EmailHelpers } from './email.helpers';
import { EmailHelpersService } from './email-helpers.service';

import { Candidate } from '../entities';
import { JobSeeker } from '../job-seeker/entities/job-seeker.entity';
import { ApplicationRejection } from './templates/ApplicationRejection';
import CandidateStatusUpdateEmail from './templates/CandidateStatusUpdateEmail';

import { InterviewInvitation } from './templates/InterviewInvitation';
import { JobApplicationSubmittedAutomated } from './templates/JobApplicationSubmittedAutomated';
import JobApplicationSubmittedEmail from './templates/JobApplicationSubmittedEmail';
import JobSeekerApprovalEmail from './templates/JobSeekerApprovalEmail';
import { ProfileApproved } from './templates/ProfileApproved';
import { ProfileRejected } from './templates/ProfileRejected';
import { ReEngagement } from './templates/ReEngagement';
import { CompanyReEngagement } from './templates/CompanyReEngagement';
import RecruitmentAssessmentResultEmail from './templates/RecruitmentAssessmentResultEmail';
import SoftLaunchEmail from './templates/SoftLaunchEmail';
import { VideoIntroductionActive } from './templates/VideoIntroductionActive';
import { VideoIntroductionPassive } from './templates/VideoIntroductionPassive';
import VipWelcomeEmail from './templates/VipWelcomeEmail';
import { JobApplicationWasSubmitted } from './templates/JobApplicationWasSubmitted';
import { TeamInvitationEmail } from './templates/TeamInvitationEmail';

const RATE_LIMIT_DELAY = 600; // 600ms between requests to stay under 2 req/sec
const VERIFIED_DOMAIN = 'kaleidotalent.com';
const DEFAULT_FROM_EMAIL = '<EMAIL>';

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

@Injectable()
export class EmailService {
  private readonly resend: Resend;
  private readonly logger = new Logger(EmailService.name);
  private readonly environment: string;

  constructor(
    private configService: ConfigService,
    @InjectRepository(JobSeeker)
    private jobSeekerRepository: Repository<JobSeeker>,
    @InjectRepository(Candidate)
    private candidateRepository: Repository<Candidate>,
    private moduleRef: ModuleRef,
    private emailHelpersService: EmailHelpersService,
  ) {
    const resendApiKey = this.configService.get<string>('RESEND_API_KEY');
    if (!resendApiKey) {
      this.logger.warn('RESEND_API_KEY not found in environment variables');
    }
    this.resend = new Resend(resendApiKey);
    this.environment = this.configService.get<string>('NODE_ENV') || 'development';
  }

  private shouldSendToAllWhitelisted(): boolean {
    return this.environment === 'development';
  }

  private async logEmailCorrespondence(
    to: string,
    subject: string,
    content: string,
    emailType: 'interview' | 'offer' | 'status' | 'general',
    metadata?: any,
  ): Promise<void> {
    // Log to both job seeker and candidate repositories
    await Promise.all([
      logEmailToEntity(
        this.jobSeekerRepository,
        to,
        subject,
        content,
        emailType,
        metadata,
        this.logger,
      ),
      logEmailToEntity(
        this.candidateRepository,
        to,
        subject,
        content,
        emailType,
        metadata,
        this.logger,
      ),
    ]);
  }

  async sendEmail({
    to,
    subject,
    html,
    from = DEFAULT_FROM_EMAIL,
    emailType = 'general',
    metadata,
  }: {
    to: string;
    subject: string;
    html: string;
    from?: string;
    emailType?: 'interview' | 'offer' | 'status' | 'general';
    metadata?: any;
  }) {
    try {
      const config = emailConfig();
      const shouldBroadcast = this.shouldSendToAllWhitelisted();

      // In dev/staging environment, send to whitelisted emails for testing
      if (shouldBroadcast) {
        const responses = [];

        // Send without environment prefix - clean subject line
        for (const email of config.whitelistedEmails) {
          try {
            await sleep(RATE_LIMIT_DELAY); // Wait between requests
            const response = await this.resend.emails.send({
              from: DEFAULT_FROM_EMAIL,
              to: email,
              subject: subject, // Use original subject without prefix
              html,
            });
            this.logger.log(
              `Email sent successfully to ${email} (${this.environment} environment)`,
            );
            responses.push(response);
          } catch (error) {
            this.logger.error(`Failed to send email to ${email}:`, error);
            responses.push({ error });
          }
        }
        return { id: 'MULTI_SEND', environment: this.environment, responses };
      } else {
        // In production, send to the actual recipient
        const response = await this.resend.emails.send({
          from, // Always use the verified domain email
          to,
          subject,
          html,
        });
        this.logger.log(`Email sent successfully to ${to}`);

        // Log email correspondence
        await this.logEmailCorrespondence(to, subject, html, emailType, metadata);

        return response;
      }
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}:`, error);
      throw error;
    }
  }

  async sendCandidateVideoIntroEmail(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    jobId: string,
    candidateId: string,
    companyName?: string,
    clientId?: string,
    candidateSource?: string,
  ) {
    // Determine which template to use based on candidate source
    let emailHtml: string;
    let subject: string;

    if (candidateSource === 'JOB_SEEKER') {
      // Use active template for candidates who applied through job-seeker flow
      emailHtml = await EmailTemplates.getVideoIntroActiveEmailHtml(
        candidateName,
        jobTitle,
        companyName || 'Kaleido Talent',
        jobId,
        candidateId,
      );
      subject = `Let's take the next step in your application for ${jobTitle} at ${companyName || 'our company'}`;
    } else {
      // Use interest email for scouted or uploaded candidates (passive candidates)
      // This email expresses interest and asks for a response before requesting video
      emailHtml = await EmailTemplates.getInterestEmailHtml(
        candidateName,
        jobTitle,
        companyName || 'Kaleido Talent',
        jobId,
        candidateId,
      );
      subject = `Your profile caught our attention for a ${jobTitle} role at ${companyName || 'our company'}`;
    }

    // Determine email type based on candidate source
    const emailType = candidateSource === 'JOB_SEEKER' ? 'video_intro' : 'interest';
    const emailCategory =
      candidateSource === 'JOB_SEEKER' ? 'video_intro_request' : 'interest_outreach';

    const metadata = new EmailMetadataBuilder()
      .jobId(jobId)
      .candidateId(candidateId)
      .jobTitle(jobTitle)
      .companyName(companyName || '')
      .emailType(emailType)
      .custom('candidateSource', candidateSource || 'UNKNOWN')
      .build();

    const result = await this.sendEmail({
      to: candidateEmail,
      subject,
      html: emailHtml,
      emailType: 'general',
      metadata,
    });

    // Log the email activity to candidate's activity history
    if (result && candidateId) {
      const activityLogger = EmailActivityLogger.create(this.candidateRepository, this.logger);
      const logSubject =
        candidateSource === 'JOB_SEEKER'
          ? `Video Introduction Request for ${jobTitle} 🎥`
          : `Interest Email for ${jobTitle} ✨`;

      await activityLogger.logEmailActivity({
        candidateId,
        candidateEmail,
        emailType,
        jobId,
        jobTitle,
        companyName,
        subject: logSubject,
        clientId,
        additionalMetadata: {
          emailCategory,
          sentAt: new Date().toISOString(),
        },
      });

      // Log success
      const logType = candidateSource === 'JOB_SEEKER' ? 'Video intro' : 'Interest';
      this.logger.log(`${logType} email activity logged for candidate ${candidateId}`);
    } else {
      this.logger.warn(
        `Failed to log video intro email activity - result: ${result}, candidateId: ${candidateId}`,
      );
    }

    return result;
  }

  async sendInterviewInviteEmail(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    interviewDetails: {
      date: string;
      type?: string;
      location?: string;
      meetingLink?: string;
    },
    companyName?: string,
    clientId?: string,
  ) {
    const metadata = new EmailMetadataBuilder()
      .candidateName(candidateName)
      .jobTitle(jobTitle)
      .companyName(companyName || '')
      .custom('interviewDate', interviewDetails.date)
      .custom('meetingLink', interviewDetails.meetingLink)
      .custom('location', interviewDetails.location)
      .build();

    return this.sendEmail({
      to: candidateEmail,
      subject: EmailSubjectBuilder.interview(jobTitle, companyName),
      html: await EmailTemplates.getInterviewInviteHtml(
        candidateName,
        jobTitle,
        interviewDetails,
        companyName,
      ),
      emailType: 'interview',
      metadata,
    });
  }

  async sendHiredEmail(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    startDate?: string,
    onboardingLink?: string,
    clientId?: string,
  ) {
    // Validate company name before sending email
    this.emailHelpersService.validateEmailCompanyData({ companyName });

    return this.sendEmail({
      to: candidateEmail,
      subject: EmailSubjectBuilder.hired(companyName),
      html: await EmailTemplates.getHiredEmailHtml(
        candidateName,
        jobTitle,
        companyName,
        startDate,
        onboardingLink,
      ),
    });
  }

  async sendApprovalRequestEmail(
    approverEmail: string,
    approverName: string,
    candidateName: string,
    jobTitle: string,
    approvalType: string,
    companyName?: string,
    approvalLink?: string,
    dueDate?: string,
    clientId?: string,
  ) {
    const html = await EmailTemplates.getApprovalRequestHtml(
      approverName,
      candidateName,
      jobTitle,
      approvalType,
      companyName,
      approvalLink,
      dueDate,
    );

    return this.sendEmail({
      to: approverEmail,
      subject: `Approval Request: ${approvalType} for ${candidateName}`,
      html,
    });
  }

  async sendOfferEmail(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    offerLetterUrl?: string,
    salary?: {
      amount: number;
      currency: string;
      period: string;
    },
    startDate?: string,
    offerExpirationDate?: string,
    responseLink?: string,
    clientId?: string,
  ) {
    // Validate company name before sending email
    this.emailHelpersService.validateEmailCompanyData({ companyName });
    const html = await EmailTemplates.getOfferEmailHtml(
      candidateName,
      jobTitle,
      companyName,
      offerLetterUrl,
      startDate,
      salary,
      offerExpirationDate,
      responseLink,
    );

    return this.sendEmail({
      to: candidateEmail,
      subject: EmailSubjectBuilder.offer(jobTitle, companyName),
      html,
      emailType: 'offer',
      metadata: {
        candidateName,
        jobTitle,
        companyName,
        offerLetterUrl,
        salary,
        startDate,
        offerExpirationDate,
        responseLink,
      },
    });
  }

  async sendCulturalFitCompletedEmail(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    companyName?: string,
    dashboardUrl?: string,
    job?: any,
    clientId?: string,
    skipCulturalFit?: boolean,
  ) {
    // Check if cultural fit questions are required
    const culturalFitCheck = needsCulturalFitSetup(job, skipCulturalFit);
    if (culturalFitCheck) {
      return culturalFitCheck;
    }

    return this.sendEmail({
      to: candidateEmail,
      subject: `Video Introduction Assessment Completed for ${jobTitle}`,
      html: await EmailTemplates.getCulturalFitCompletedHtml(
        candidateName,
        jobTitle,
        companyName,
        dashboardUrl,
      ),
    });
  }

  async sendCulturalFitEmployerNotificationEmail(
    employerEmail: string,
    employerName: string,
    candidateName: string,
    jobTitle: string,
    companyName?: string,
    dashboardUrl?: string,
    clientId?: string,
  ) {
    const emailHtml = await EmailTemplates.getCulturalFitEmployerNotificationHtml(
      employerName,
      candidateName,
      jobTitle,
      companyName,
      dashboardUrl,
    );

    return this.sendEmail({
      to: employerEmail,
      subject: `${candidateName} has completed their Video Introduction Assessment`,
      html: emailHtml,
    });
  }

  /**
   * Send a status update email to a candidate
   */
  async sendStatusUpdateEmail(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    statusType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status',
    message?: string,
    companyName?: string,
    additionalData?: {
      interviewDate?: string;
      meetingLink?: string;
      startDate?: string;
      onboardingLink?: string;
      expirationDate?: string;
      responseLink?: string;
      candidateStatus?: string;
      job?: any;
      skipCulturalFit?: boolean;
    },
    clientId?: string,
  ) {
    // Check if cultural fit questions are required
    const culturalFitCheck = needsCulturalFitSetup(
      additionalData?.job,
      additionalData?.skipCulturalFit,
      additionalData?.candidateStatus,
    );
    if (culturalFitCheck) {
      return culturalFitCheck;
    }

    // Generate appropriate subject based on status type
    let subject = 'Application Status Update';
    switch (statusType) {
      case 'interview':
        subject = `Interview Invitation: ${jobTitle} Position`;
        break;
      case 'hired':
        subject = `Welcome to ${companyName || 'Our Company'}!`;
        break;
      case 'offer':
        subject = `Job Offer: ${jobTitle} Position at ${companyName || 'Our Company'}`;
        break;
      case 'offerAccepted':
        subject = `Offer Acceptance Confirmed: ${jobTitle} Position`;
        break;
    }

    const emailHtml = await EmailTemplates.getStatusUpdateEmailHtml(
      candidateName,
      jobTitle,
      statusType,
      companyName,
      message,
      additionalData,
    );

    return this.sendEmail({
      to: candidateEmail,
      subject,
      html: emailHtml,
      emailType:
        statusType === 'interview' ? 'interview' : statusType === 'offer' ? 'offer' : 'status',
      metadata: {
        candidateName,
        jobTitle,
        companyName,
        statusType,
        ...additionalData,
      },
    });
  }

  /**
   * Send contact form message
   */
  async sendContactMessage(name: string, email: string, phone: string, message: string) {
    const contactHtml = await EmailTemplates.getContactFormHtml(name, email, phone, message);

    const batchSender = EmailPatterns.createBatchSender(
      this, // Use this EmailService instance which implements EmailServiceInterface
      this.logger,
      RATE_LIMIT_DELAY,
    );

    return batchSender(
      WHITELISTS_SUPPORT_EMAIL_ADDRESSES,
      `New Contact Form Submission from ${name}`,
      contactHtml,
      'contact form submission',
    );
  }

  /**
   * Send user registration notification email to support team
   */
  async sendUserRegistrationNotification(
    userType: 'Company' | 'Job Seeker' | 'Graduate',
    userName: string,
    userEmail: string,
    additionalInfo?: {
      companyName?: string;
      companyWebsite?: string;
      university?: string;
      graduationYear?: string;
      location?: string;
    },
  ) {
    try {
      const registrationDate = formatEmailDate();

      const emailHtml = await EmailTemplates.getUserRegistrationNotificationHtml(
        userType,
        userName,
        userEmail,
        registrationDate,
        additionalInfo,
      );

      const subject = `New ${userType} Registration: ${userName}`;

      // Send to all support email addresses
      const emailPromises = WHITELISTS_SUPPORT_EMAIL_ADDRESSES.map(async (supportEmail) => {
        try {
          await sleep(RATE_LIMIT_DELAY); // Rate limiting
          return await this.resend.emails.send({
            from: DEFAULT_FROM_EMAIL,
            to: supportEmail,
            subject,
            html: emailHtml,
          });
        } catch (error) {
          this.logger.error(`Failed to send registration notification to ${supportEmail}:`, error);
          return { error };
        }
      });

      const results = await Promise.all(emailPromises);
      this.logger.log(
        `User registration notification sent for ${userType}: ${userName} (${userEmail})`,
      );

      return {
        success: true,
        results,
        sentTo: WHITELISTS_SUPPORT_EMAIL_ADDRESSES,
      };
    } catch (error) {
      this.logger.error(`Failed to send user registration notification:`, error);
      throw error;
    }
  }

  /**
   * Send job application submitted email (candidate confirmation or company notification)
   */
  async sendJobApplicationSubmittedEmail(
    candidateName: string,
    jobTitle: string,
    companyName: string,
    recipientEmail: string,
    isConfirmation: boolean = true,
    applicationDate?: string,
    dashboardUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(JobApplicationSubmittedEmail, {
          candidateName,
          jobTitle,
          companyName,
          applicationDate,
          dashboardUrl,
          isConfirmation,
        }),
      );

      const subject = EmailSubjectBuilder.applicationSubmitted(
        isConfirmation ? jobTitle : candidateName,
        isConfirmation ? companyName : jobTitle,
        isConfirmation,
      );

      return this.sendEmail({
        to: recipientEmail,
        subject,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          candidateName,
          jobTitle,
          companyName,
          isConfirmation,
          applicationDate,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send job application email:`, error);
      throw error;
    }
  }

  /**
   * Send job seeker approval/decline email
   */
  async sendJobSeekerApprovalEmail(
    jobSeekerName: string,
    jobSeekerEmail: string,
    isApproved: boolean,
    declineReason?: string,
    dashboardUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(JobSeekerApprovalEmail, {
          jobSeekerName,
          isApproved,
          declineReason,
          dashboardUrl,
        }),
      );

      const subject = isApproved
        ? `Welcome to Kaleido Talent! Your profile has been approved`
        : `Profile Review Update - Action Required`;

      return this.sendEmail({
        to: jobSeekerEmail,
        subject,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          jobSeekerName,
          isApproved,
          declineReason,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send job seeker approval email:`, error);
      throw error;
    }
  }

  /**
   * Send enhanced candidate status update email
   */
  async sendCandidateStatusUpdateEmail(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    status: 'INTERVIEWING' | 'OFFER_EXTENDED' | 'HIRED' | 'REJECTED' | 'OFFER_ACCEPTED',
    message?: string,
    interviewDate?: string,
    meetingLink?: string,
    startDate?: string,
    onboardingLink?: string,
    offerExpirationDate?: string,
    responseLink?: string,
    dashboardUrl?: string,
    clientId?: string,
  ) {
    try {
      // Validate company name before sending email
      this.emailHelpersService.validateEmailCompanyData({ companyName });
      const emailHtml = await render(
        React.createElement(CandidateStatusUpdateEmail, {
          candidateName,
          jobTitle,
          companyName,
          status,
          message,
          interviewDate,
          meetingLink,
          startDate,
          onboardingLink,
          offerExpirationDate,
          responseLink,
          dashboardUrl,
        }),
      );

      const getSubject = () => {
        switch (status) {
          case 'INTERVIEWING':
            return EmailSubjectBuilder.interview(jobTitle, companyName);
          case 'OFFER_EXTENDED':
            return EmailSubjectBuilder.offer(jobTitle, companyName);
          case 'HIRED':
            return EmailSubjectBuilder.hired(companyName);
          case 'REJECTED':
            return EmailSubjectBuilder.rejection(jobTitle, companyName);
          case 'OFFER_ACCEPTED':
            return `Offer Acceptance Confirmed: ${jobTitle}`;
          default:
            return `Status Update: ${jobTitle} at ${companyName}`;
        }
      };

      const emailType = getEmailTypeFromStatus(status);

      return this.sendEmail({
        to: candidateEmail,
        subject: getSubject(),
        html: emailHtml,
        emailType,
        metadata: {
          candidateName,
          jobTitle,
          companyName,
          status,
          message,
          interviewDate,
          meetingLink,
          startDate,
          onboardingLink,
          offerExpirationDate,
          responseLink,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send candidate status update email:`, error);
      throw error;
    }
  }

  /**
   * Send waitlist welcome email for initial registrations
   */
  async sendWaitlistWelcomeEmail(
    recipientEmail: string,
    fullName: string,
    loginUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(VipWelcomeEmail, {
          fullName,
          isVip: false,
          loginUrl,
        }),
      );

      const subject = `Welcome to Kaleido Talent - You're on the waitlist!`;

      return this.sendEmail({
        to: recipientEmail,
        subject,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          fullName,
          isVip: false,
          loginUrl,
          emailType: 'waitlist_welcome',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send waitlist welcome email:`, error);
      throw error;
    }
  }

  /**
   * Send premium access confirmation email after payment
   */
  async sendPremiumAccessEmail(
    recipientEmail: string,
    fullName: string,
    loginUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(VipWelcomeEmail, {
          fullName,
          isVip: true,
          loginUrl,
        }),
      );

      const subject = `🚀 Premium Early Access Confirmed - You're First in Line!`;

      return this.sendEmail({
        to: recipientEmail,
        subject,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          fullName,
          isVip: true,
          loginUrl,
          emailType: 'premium_access',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send premium access email:`, error);
      throw error;
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  async sendVipWelcomeEmail(
    recipientEmail: string,
    fullName: string,
    isVip: boolean,
    loginUrl?: string,
    clientId?: string,
  ) {
    if (isVip) {
      return this.sendPremiumAccessEmail(recipientEmail, fullName, loginUrl, clientId);
    } else {
      return this.sendWaitlistWelcomeEmail(recipientEmail, fullName, loginUrl, clientId);
    }
  }

  /**
   * Send soft launch email for early access users
   */
  async sendSoftLaunchEmail(
    recipientEmail: string,
    fullName: string,
    loginUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(SoftLaunchEmail, {
          fullName,
        }),
      );

      const subject = `🚀 The wait is over! Kaleido Talent is officially live... but quietly`;

      return this.sendEmail({
        to: recipientEmail,
        subject,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          fullName,
          loginUrl,
          emailType: 'soft_launch',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send soft launch email:`, error);
      throw error;
    }
  }

  /**
   * Send exclusive access email after premium payment
   */
  async sendExclusiveAccessEmail(
    recipientEmail: string,
    fullName: string,
    loginUrl?: string,
    clientId?: string,
  ) {
    try {
      const { default: ExclusiveAccessEmail } = await import('./templates/ExclusiveAccessEmail');
      const emailHtml = await render(
        React.createElement(ExclusiveAccessEmail, {
          fullName,
        }),
      );

      const subject = `🚀 Welcome to Kaleido Talent - You're among our exclusive early adopters!`;

      return this.sendEmail({
        to: recipientEmail,
        subject,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          fullName,
          loginUrl,
          emailType: 'exclusive_access',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send exclusive access email:`, error);
      throw error;
    }
  }

  /**
   * Send team invitation email
   */
  async sendTeamInvitationEmail(
    recipientEmail: string,
    inviterName: string,
    companyName: string,
    role: string,
    invitationUrl: string,
    expiresAt: Date,
    message?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(TeamInvitationEmail, {
          recipientEmail,
          inviterName,
          companyName,
          role,
          invitationUrl,
          expiresAt,
          message,
        }),
      );

      return this.sendEmail({
        to: recipientEmail,
        subject: `Invitation to join ${companyName} on Kaleido Talent`,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          inviterName,
          companyName,
          role,
          emailType: 'team_invitation',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send team invitation email:`, error);
      throw error;
    }
  }

  /**
   * Send recruitment assessment result email
   */
  async sendRecruitmentAssessmentResultEmail(
    recipientEmail: string,
    fullName: string,
    companyName: string,
    position: string,
    totalScore: number,
    maturityLevel: 'Transformative' | 'Optimized' | 'Developing' | 'Foundational',
    whatYoureDoing: string[],
    whereYouMightBeLosingTalent: string[],
    strategicRecommendations: string[],
    suggestedNextStep: string,
    ctaUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(RecruitmentAssessmentResultEmail, {
          fullName,
          companyName,
          position,
          totalScore,
          maturityLevel,
          whatYoureDoing,
          whereYouMightBeLosingTalent,
          strategicRecommendations,
          suggestedNextStep,
          ctaUrl: ctaUrl || 'https://kaleidotalent.com/waitlist',
        }),
      );

      const subject = `Your Recruitment Maturity Assessment Results - ${companyName}`;

      return this.sendEmail({
        to: recipientEmail,
        subject,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          fullName,
          companyName,
          position,
          totalScore,
          maturityLevel,
          emailType: 'recruitment_assessment_result',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send recruitment assessment result email:`, error);
      throw error;
    }
  }

  /**
   * Send automated job application submitted email
   */
  async sendJobApplicationSubmittedAutomated(
    talentEmail: string,
    talentName: string,
    jobTitle: string,
    companyName: string,
    dashboardUrl?: string,
    companyProfileUrl?: string,
    clientId?: string,
  ) {
    try {
      // Validate company name before sending email
      this.emailHelpersService.validateEmailCompanyData({ companyName });
      const emailHtml = await render(
        React.createElement(JobApplicationSubmittedAutomated, {
          talentName,
          jobTitle,
          companyName,
          dashboardUrl: getDashboardUrl(dashboardUrl),
          companyProfileUrl,
        }),
      );

      return this.sendEmail({
        to: talentEmail,
        subject: `Your application is on its way! Here's what happens next 📨`,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          talentName,
          jobTitle,
          companyName,
          emailType: 'job_application_submitted_automated',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send automated job application email:`, error);
      throw error;
    }
  }

  /**
   * Send video introduction email to active candidates
   */
  async sendVideoIntroductionActive(
    candidateEmail: string,
    candidateName: string,
    roleName: string,
    companyName: string,
    recordingUrl: string,
    deadline: string,
    deadlineTime: string,
    jobDescriptionUrl?: string,
    companyProfileUrl?: string,
    loginUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(VideoIntroductionActive, {
          candidateName,
          roleName,
          companyName,
          jobDescriptionUrl,
          companyProfileUrl,
          loginUrl,
          recordingUrl,
          deadline,
          deadlineTime,
        }),
      );

      return this.sendEmail({
        to: candidateEmail,
        subject: EmailSubjectBuilder.videoIntroActive(roleName, companyName),
        html: emailHtml,
        emailType: 'general',
        metadata: {
          candidateName,
          roleName,
          companyName,
          deadline,
          emailType: 'video_introduction_active',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send video introduction active email:`, error);
      throw error;
    }
  }

  /**
   * Send video introduction email to passive/sourced candidates
   */
  async sendVideoIntroductionPassive(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    roleName: string,
    companyName: string,
    recordingUrl: string,
    deadline: string,
    deadlineTime: string,
    yourName: string,
    yourTitle: string,
    roleDetailsUrl?: string,
    companyProfileUrl?: string,
    loginUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(VideoIntroductionPassive, {
          candidateName,
          jobTitle,
          roleName,
          companyName,
          roleDetailsUrl,
          companyProfileUrl,
          loginUrl,
          recordingUrl,
          deadline,
          deadlineTime,
          yourName,
          yourTitle,
        }),
      );

      return this.sendEmail({
        to: candidateEmail,
        subject: EmailSubjectBuilder.videoIntroPassive(jobTitle, companyName),
        html: emailHtml,
        emailType: 'general',
        metadata: {
          candidateName,
          jobTitle,
          roleName,
          companyName,
          deadline,
          emailType: 'video_introduction_passive',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send video introduction passive email:`, error);
      throw error;
    }
  }

  /**
   * Send enhanced interview invitation email
   */
  async sendInterviewInvitation(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    proposedDate: string,
    proposedTime: string,
    format: string,
    locationOrLink: string,
    contactDetails: string,
    yourName: string,
    yourTitle: string,
    companyWebsiteUrl?: string,
    companyProfileUrl?: string,
    jobDescriptionUrl?: string,
    clientId?: string,
  ) {
    try {
      // Validate company name before sending email
      this.emailHelpersService.validateEmailCompanyData({ companyName });
      const emailHtml = await render(
        React.createElement(InterviewInvitation, {
          candidateName,
          jobTitle,
          companyName,
          proposedDate,
          proposedTime,
          format,
          locationOrLink,
          contactDetails,
          companyWebsiteUrl,
          companyProfileUrl,
          jobDescriptionUrl,
          yourName,
          yourTitle,
        }),
      );

      return this.sendEmail({
        to: candidateEmail,
        subject: `Let's Talk! Interview Invitation for the ${jobTitle} Role at ${companyName}`,
        html: emailHtml,
        emailType: 'interview',
        metadata: {
          candidateName,
          jobTitle,
          companyName,
          proposedDate,
          proposedTime,
          format,
          locationOrLink,
          emailType: 'interview_invitation',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send interview invitation email:`, error);
      throw error;
    }
  }

  /**
   * Send application rejection email
   */
  async sendApplicationRejection(
    candidateEmail: string,
    candidateName: string,
    jobTitle: string,
    companyName: string,
    yourName: string,
    yourTitle: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(ApplicationRejection, {
          candidateName,
          jobTitle,
          companyName,
          yourName,
          yourTitle,
        }),
      );

      return this.sendEmail({
        to: candidateEmail,
        subject: EmailSubjectBuilder.rejection(jobTitle, companyName),
        html: emailHtml,
        emailType: 'status',
        metadata: {
          candidateName,
          jobTitle,
          companyName,
          emailType: 'application_rejection',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send application rejection email:`, error);
      throw error;
    }
  }

  /**
   * Send profile approved email
   */
  async sendProfileApproved(
    talentEmail: string,
    talentName: string,
    topSkills?: string[],
    yearsExperience?: number,
    industry?: string,
    dashboardUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(ProfileApproved, {
          talentName,
          topSkills,
          yearsExperience,
          industry,
          dashboardUrl: getDashboardUrl(dashboardUrl),
        }),
      );

      return this.sendEmail({
        to: talentEmail,
        subject: `You're in! Your Kaleido profile is approved ✨`,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          talentName,
          topSkills,
          yearsExperience,
          industry,
          emailType: 'profile_approved',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send profile approved email:`, error);
      throw error;
    }
  }

  /**
   * Send profile rejected email
   */
  async sendProfileRejected(talentEmail: string, talentName: string, clientId?: string) {
    try {
      const emailHtml = await render(
        React.createElement(ProfileRejected, {
          talentName,
        }),
      );

      return this.sendEmail({
        to: talentEmail,
        subject: `Your Kaleido Profile Submission`,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          talentName,
          emailType: 'profile_rejected',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send profile rejected email:`, error);
      throw error;
    }
  }

  /**
   * Send re-engagement email to inactive users
   */
  async sendReEngagement(
    talentEmail: string,
    talentName: string,
    profileUrl?: string,
    dashboardUrl?: string,
    jobBoardUrl?: string,
    productRoadmapUrl?: string,
    clientId?: string,
  ) {
    try {
      const emailHtml = await render(
        React.createElement(ReEngagement, {
          talentName,
          profileUrl: EmailHelpers.getProfileUrl(profileUrl),
          dashboardUrl: getDashboardUrl(dashboardUrl),
          jobBoardUrl: EmailHelpers.getJobBoardUrl(jobBoardUrl),
          productRoadmapUrl: EmailHelpers.getProductRoadmapUrl(productRoadmapUrl),
        }),
      );

      return this.sendEmail({
        to: talentEmail,
        subject: `Your next opportunity might be waiting… 👀`,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          talentName,
          emailType: 're_engagement',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send re-engagement email:`, error);
      throw error;
    }
  }

  /**
   * Generate email preview HTML for admin dashboard
   */
  async generateEmailPreview(emailType: string, testData?: any): Promise<string> {
    return emailPreviewGenerator.generatePreview(emailType, testData);
  }

  /**
   * Send job application notification to company
   */
  async sendJobApplicationNotificationToCompany(
    companyName: string,
    recruiterName: string | undefined,
    jobTitle: string,
    candidateFullName: string,
    applicationDate: string,
    jobPostingUrl: string | undefined,
    viewApplicationUrl: string,
    matchRankUrl: string | undefined,
    recipientEmail: string,
    clientId?: string,
  ) {
    try {
      // Validate company name before sending email
      this.emailHelpersService.validateEmailCompanyData({ companyName });

      const emailHtml = await render(
        React.createElement(JobApplicationWasSubmitted, {
          companyName,
          recruiterName,
          jobTitle,
          candidateFullName,
          applicationDate,
          jobPostingUrl,
          viewApplicationUrl,
          matchRankUrl,
        }),
      );

      return this.sendEmail({
        to: recipientEmail,
        subject: `New Application for ${jobTitle}`,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          companyName,
          jobTitle,
          candidateFullName,
          applicationDate,
          emailType: 'job_application_notification',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send job application notification to company:`, error);
      throw error;
    }
  }

  /**
   * Send re-engagement email to company
   */
  async sendCompanyReEngagementEmail(
    companyName: string,
    jobTitle: string,
    recipientEmail: string,
    loginUrl?: string,
    profileUrl?: string,
    dashboardUrl?: string,
    clientId?: string,
  ) {
    try {
      // Validate company name before sending email
      this.emailHelpersService.validateEmailCompanyData({ companyName });

      const emailHtml = await render(
        React.createElement(CompanyReEngagement, {
          companyName,
          jobTitle,
          loginUrl,
          profileUrl,
          dashboardUrl,
        }),
      );

      return this.sendEmail({
        to: recipientEmail,
        subject: `Talent's Waiting — Don't Miss Out!`,
        html: emailHtml,
        emailType: 'general',
        metadata: {
          companyName,
          jobTitle,
          emailType: 'company_re_engagement',
        },
      });
    } catch (error) {
      this.logger.error(`Failed to send company re-engagement email:`, error);
      throw error;
    }
  }
}
