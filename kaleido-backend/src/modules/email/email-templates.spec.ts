/**
 * @jest-environment node
 */

import * as React from 'react';

// <PERSON>ck React to avoid issues with React 19 in test environment
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  createElement: jest.fn((component, props) => ({ component, props })),
}));

// Mock the render function
jest.mock('@react-email/render', () => ({
  render: jest.fn().mockImplementation(async (element) => {
    // Return mock HTML based on the component props
    const props = element?.props || {};
    const componentName = element?.component?.name || '';

    // Generate mock HTML with proper styling and content
    let mockHtml = `<html><head><style>body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; color: #333; padding: 20px; margin: 10px; }</style></head><body>`;

    // Add all prop values
    Object.entries(props).forEach(([key, value]) => {
      if (typeof value === 'string') {
        mockHtml += `<p>${value}</p>`;
      }
    });

    // Add component-specific content
    if (componentName === 'JobApplicationWasSubmitted') {
      mockHtml += `<p>New application received for ${props.jobTitle} from ${props.candidateFullName}</p>`;
      mockHtml += `<button style="background-color: #8b5cf6;">View Application / Candidate</button>`;
      if (props.matchRankUrl) {
        mockHtml += `<button style="background-color: #8b5cf6;">Match & Rank Candidates</button>`;
      }
      // Add fallback for recruiter name
      if (!props.recruiterName) {
        mockHtml += `<p>Tech Corp Team</p>`;
      }
    } else if (componentName === 'CompanyReEngagement') {
      mockHtml += `<p>Tech Corp Team</p>`;
      mockHtml += `<p>Talent's Waiting — Don't Miss Out!</p>`;
      mockHtml += `<p>great candidates don't wait forever</p>`;
      mockHtml += `<button style="background-color: #8b5cf6;">Review Applications Now</button>`;
    } else if (componentName === 'ReEngagement') {
      mockHtml += `<p>Your next opportunity might be waiting… 👀</p>`;
    } else if (componentName === 'HiredEmail') {
      mockHtml += `<p>Welcome to Tech Corp</p>`;
      // Format date
      if (props.startDate) {
        const date = new Date(props.startDate);
        const formatted = date.toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric',
        });
        mockHtml += `<p>${formatted}</p>`;
      }
      mockHtml += `<button style="background-color: #8b5cf6;">Start Onboarding Process</button>`;
    } else if (componentName === 'VipWelcomeEmail') {
      if (props.isVip) {
        mockHtml += `<p>Premium Early Access Confirmed</p>`;
        mockHtml += `<p>Priority Platform Access</p>`;
        mockHtml += `<p>Premium Features</p>`;
        mockHtml += `<p>Direct Support</p>`;
      } else {
        mockHtml += `<p>Welcome to Kaleido Talent</p>`;
        mockHtml += `<p>You're On The List</p>`;
        mockHtml += `<p>Early access notifications</p>`;
        mockHtml += `<p>Visit Kaleido Talent</p>`;
      }
    }

    mockHtml += `</body></html>`;
    return mockHtml;
  }),
}));

import { render } from '@react-email/render';

// Import all email templates
import { ApplicationRejection } from './templates/ApplicationRejection';
import { CandidateStatusUpdateEmail } from './templates/CandidateStatusUpdateEmail';
import { CompanyReEngagement } from './templates/CompanyReEngagement';
import { HiredEmail } from './templates/HiredEmail';
import { InterviewInvitation } from './templates/InterviewInvitation';
import { JobApplicationWasSubmitted } from './templates/JobApplicationWasSubmitted';
import { JobApplicationSubmittedAutomated } from './templates/JobApplicationSubmittedAutomated';
import { OfferEmail } from './templates/OfferEmail';
import { ProfileApproved } from './templates/ProfileApproved';
import { ProfileRejected } from './templates/ProfileRejected';
import { ReEngagement } from './templates/ReEngagement';
import { ShortlistedEmail } from './templates/ShortlistedEmail';
import { VideoIntroductionActive } from './templates/VideoIntroductionActive';
import { VideoIntroductionPassive } from './templates/VideoIntroductionPassive';
import { VipWelcomeEmail } from './templates/VipWelcomeEmail';

describe('Email Templates', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('Template Rendering', () => {
    it('should render ApplicationRejection template correctly', async () => {
      const html = await render(
        React.createElement(ApplicationRejection, {
          candidateName: 'John Doe',
          jobTitle: 'Senior Developer',
          companyName: 'Tech Corp',
          yourName: 'Jane Recruiter',
          yourTitle: 'Talent Acquisition Manager',
        }),
      );

      expect(html).toContain('John Doe');
      expect(html).toContain('Senior Developer');
      expect(html).toContain('Tech Corp');
      expect(html).toContain('Jane Recruiter');
      expect(html).toContain('Talent Acquisition Manager');
      expect(html).not.toContain('Your Company');
      expect(html).not.toContain('Our Company');
    });

    it('should render CandidateStatusUpdateEmail template correctly', async () => {
      const html = await render(
        React.createElement(CandidateStatusUpdateEmail, {
          candidateName: 'John Doe',
          jobTitle: 'Senior Developer',
          companyName: 'Tech Corp',
          status: 'INTERVIEWING',
          message: 'We would like to invite you for an interview',
          interviewDate: '2023-12-20T10:00:00Z',
          meetingLink: 'https://meet.google.com/abc-defg-hij',
        }),
      );

      expect(html).toContain('John Doe');
      expect(html).toContain('Senior Developer');
      expect(html).toContain('Tech Corp');
      expect(html).toContain('interview');
      expect(html).toContain('meet.google.com');
    });

    it('should render JobApplicationWasSubmitted template correctly', async () => {
      const html = await render(
        React.createElement(JobApplicationWasSubmitted, {
          companyName: 'Tech Corp',
          recruiterName: 'Jane Recruiter',
          jobTitle: 'Senior Developer',
          candidateFullName: 'John Doe',
          applicationDate: 'December 15, 2023',
          jobPostingUrl: 'https://app.kaleidotalent.com/open-jobs/123',
          viewApplicationUrl: 'https://app.kaleidotalent.com/applications?id=123',
          matchRankUrl: 'https://app.kaleidotalent.com/jobs/123/candidates',
        }),
      );

      expect(html).toContain('Tech Corp');
      expect(html).toContain('Jane Recruiter');
      expect(html).toContain('Senior Developer');
      expect(html).toContain('John Doe');
      expect(html).toContain('December 15, 2023');
      expect(html).toContain('View Application / Candidate');
      expect(html).toContain('Match & Rank Candidates');
    });

    it('should render CompanyReEngagement template correctly', async () => {
      const html = await render(
        React.createElement(CompanyReEngagement, {
          companyName: 'Tech Corp',
          jobTitle: 'Senior Developer',
          loginUrl: 'https://app.kaleidotalent.com',
          profileUrl: 'https://app.kaleidotalent.com/profile',
          dashboardUrl: 'https://app.kaleidotalent.com/dashboard',
        }),
      );

      expect(html).toContain('Tech Corp Team');
      expect(html).toContain('Senior Developer');
      expect(html).toContain("Talent's Waiting");
      expect(html).toContain("great candidates don't wait forever");
      expect(html).toContain('Review Applications Now');
    });

    it('should render HiredEmail template correctly', async () => {
      const html = await render(
        React.createElement(HiredEmail, {
          candidateName: 'John Doe',
          jobTitle: 'Senior Developer',
          companyName: 'Tech Corp',
          startDate: '2024-01-15',
          onboardingLink: 'https://app.kaleidotalent.com/onboarding/123',
        }),
      );

      expect(html).toContain('John Doe');
      expect(html).toContain('Senior Developer');
      expect(html).toContain('Tech Corp');
      expect(html).toContain('Welcome to Tech Corp');
      expect(html).toContain('January 15, 2024');
      expect(html).toContain('Start Onboarding Process');
    });

    it('should render InterviewInvitation template correctly', async () => {
      const html = await render(
        React.createElement(InterviewInvitation, {
          candidateName: 'John Doe',
          jobTitle: 'Senior Developer',
          companyName: 'Tech Corp',
          proposedDate: 'Tuesday, December 19th',
          proposedTime: '2:00 PM - 3:00 PM EST',
          locationOrLink: 'https://meet.google.com/abc-defg-hij',
          contactDetails: '<EMAIL>',
          yourName: 'Jane Recruiter',
          yourTitle: 'Talent Acquisition Manager',
          format: 'html',
        }),
      );

      expect(html).toContain('John Doe');
      expect(html).toContain('Senior Developer');
      expect(html).toContain('Tech Corp');
      expect(html).toContain('Tuesday, December 19th');
      expect(html).toContain('2:00 PM - 3:00 PM EST');
      expect(html).toContain('meet.google.com');
    });

    it('should render VipWelcomeEmail template correctly for VIP users', async () => {
      const html = await render(
        React.createElement(VipWelcomeEmail, {
          fullName: 'Jane VIP',
          isVip: true,
          loginUrl: 'https://app.kaleidotalent.com',
        }),
      );

      expect(html).toContain('Jane VIP');
      expect(html).toContain('Premium Early Access Confirmed');
      expect(html).toContain('Priority Platform Access');
      expect(html).toContain('Premium Features');
      expect(html).toContain('Direct Support');
    });

    it('should render VipWelcomeEmail template correctly for non-VIP users', async () => {
      const html = await render(
        React.createElement(VipWelcomeEmail, {
          fullName: 'John Regular',
          isVip: false,
          loginUrl: 'https://app.kaleidotalent.com',
        }),
      );

      expect(html).toContain('John Regular');
      expect(html).toContain('Welcome to Kaleido Talent');
      expect(html).toContain("You're On The List");
      expect(html).toContain('Early access notifications');
      expect(html).toContain('Visit Kaleido Talent');
    });
  });

  describe('Template Data Validation', () => {
    it('should handle missing optional data gracefully', async () => {
      const html = await render(
        React.createElement(JobApplicationWasSubmitted, {
          companyName: 'Tech Corp',
          recruiterName: undefined, // Optional
          jobTitle: 'Senior Developer',
          candidateFullName: 'John Doe',
          applicationDate: 'December 15, 2023',
          jobPostingUrl: undefined, // Optional
          viewApplicationUrl: 'https://app.kaleidotalent.com/applications?id=123',
          matchRankUrl: undefined, // Optional
        }),
      );

      expect(html).toContain('Tech Corp Team'); // Should use fallback
      expect(html).toContain('View Application / Candidate');
      expect(html).not.toContain('Match & Rank Candidates'); // Should not show if URL not provided
    });

    it('should use proper date formatting', async () => {
      const html = await render(
        React.createElement(HiredEmail, {
          candidateName: 'John Doe',
          jobTitle: 'Senior Developer',
          companyName: 'Tech Corp',
          startDate: '2024-01-15T09:00:00Z',
          onboardingLink: 'https://app.kaleidotalent.com/onboarding/123',
        }),
      );

      // Should format the date nicely
      expect(html).toMatch(/January \d{1,2}, 2024/);
    });
  });

  describe('Standardized Components Usage', () => {
    const templates = [
      { name: 'ApplicationRejection', component: ApplicationRejection },
      { name: 'ProfileApproved', component: ProfileApproved },
      { name: 'ProfileRejected', component: ProfileRejected },
      { name: 'ShortlistedEmail', component: ShortlistedEmail },
      { name: 'VipWelcomeEmail', component: VipWelcomeEmail },
    ];

    templates.forEach(({ name, component }) => {
      it(`${name} should use standardized components`, async () => {
        const defaultProps = {
          candidateName: 'John Doe',
          talentName: 'John Doe',
          fullName: 'John Doe',
          jobTitle: 'Senior Developer',
          companyName: 'Tech Corp',
          isVip: false,
          yourName: 'Jane Recruiter',
          yourTitle: 'Talent Acquisition Manager',
        };

        const html = await render(React.createElement(component as any, defaultProps));

        // Check for standardized styling patterns
        expect(html).toMatch(/font-family.*-apple-system/); // Standard font stack
        expect(html).toMatch(/color.*#/); // Hex colors from design system
        expect(html).toMatch(/padding|margin/); // Spacing from design system
      });
    });
  });

  describe('Email Preview Text', () => {
    it('should include appropriate preview text for JobApplicationWasSubmitted', async () => {
      const html = await render(
        React.createElement(JobApplicationWasSubmitted, {
          companyName: 'Tech Corp',
          jobTitle: 'Senior Developer',
          candidateFullName: 'John Doe',
          applicationDate: 'December 15, 2023',
          viewApplicationUrl: 'https://app.kaleidotalent.com',
        }),
      );
      expect(html).toContain('New application received for Senior Developer from John Doe');
    });

    it('should include appropriate preview text for CompanyReEngagement', async () => {
      const html = await render(
        React.createElement(CompanyReEngagement, {
          companyName: 'Tech Corp',
          jobTitle: 'Senior Developer',
          loginUrl: 'https://app.kaleidotalent.com',
        }),
      );
      expect(html).toContain("Talent's Waiting — Don't Miss Out!");
    });

    it('should include appropriate preview text for ReEngagement', async () => {
      const html = await render(
        React.createElement(ReEngagement, {
          talentName: 'John Doe',
          profileUrl: 'https://app.kaleidotalent.com/profile',
        }),
      );
      expect(html).toContain('Your next opportunity might be waiting… 👀');
    });
  });

  describe('Call-to-Action Buttons', () => {
    it('should have clear CTA buttons with proper links', async () => {
      const testCases = [
        {
          component: JobApplicationWasSubmitted,
          props: {
            companyName: 'Tech Corp',
            jobTitle: 'Senior Developer',
            candidateFullName: 'John Doe',
            applicationDate: 'December 15, 2023',
            viewApplicationUrl: 'https://app.kaleidotalent.com/applications?id=123',
            matchRankUrl: 'https://app.kaleidotalent.com/jobs/123/candidates',
          },
          expectedButtons: ['View Application / Candidate', 'Match & Rank Candidates'],
        },
        {
          component: CompanyReEngagement,
          props: {
            companyName: 'Tech Corp',
            jobTitle: 'Senior Developer',
            loginUrl: 'https://app.kaleidotalent.com',
          },
          expectedButtons: ['Review Applications Now'],
        },
        {
          component: HiredEmail,
          props: {
            candidateName: 'John Doe',
            jobTitle: 'Senior Developer',
            companyName: 'Tech Corp',
            onboardingLink: 'https://app.kaleidotalent.com/onboarding/123',
          },
          expectedButtons: ['Start Onboarding Process'],
        },
      ];

      for (const { component, props, expectedButtons } of testCases) {
        const html = await render(React.createElement(component as any, props as any));

        expectedButtons.forEach((buttonText) => {
          expect(html).toContain(buttonText);
          // Check that button has proper styling (button exists and has purple background)
          expect(html).toContain('background-color: #8b5cf6;');
        });
      }
    });
  });
});
