import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailService } from '../email.service';
import { EmailHelpersService } from '../email-helpers.service';
import { JobSeeker } from '../../job-seeker/entities/job-seeker.entity';
import { Candidate } from '../../candidate/entities/candidate.entity';
import { ModuleRef } from '@nestjs/core';
import * as EmailTemplates from '../email.templates';

// Mock Resend
jest.mock('resend', () => {
  return {
    Resend: jest.fn().mockImplementation(() => ({
      emails: {
        send: jest.fn().mockResolvedValue({ id: 'test-email-id' }),
      },
    })),
  };
});

// Mock React Email render
jest.mock('@react-email/render', () => ({
  render: jest.fn().mockResolvedValue('<html>Test Email</html>'),
}));

describe('EmailService - Video Intro Email with Candidate Source', () => {
  let service: EmailService;
  let emailHelpersService: EmailHelpersService;
  let jobSeekerRepository: Repository<JobSeeker>;
  let candidateRepository: Repository<Candidate>;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        RESEND_API_KEY: 'test-api-key',
        NODE_ENV: 'test',
        APP_URL: 'https://app.kaleidotalent.com',
      };
      return config[key];
    }),
  };

  const mockEmailHelpersService = {
    validateEmailCompanyData: jest.fn(),
    ensureCompanyName: jest.fn().mockResolvedValue('Test Company'),
    getCompanyNameForJob: jest.fn().mockResolvedValue('Test Company'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EmailHelpersService,
          useValue: mockEmailHelpersService,
        },
        {
          provide: getRepositoryToken(JobSeeker),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: ModuleRef,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    emailHelpersService = module.get<EmailHelpersService>(EmailHelpersService);
    jobSeekerRepository = module.get<Repository<JobSeeker>>(getRepositoryToken(JobSeeker));
    candidateRepository = module.get<Repository<Candidate>>(getRepositoryToken(Candidate));

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('sendCandidateVideoIntroEmail', () => {
    const baseEmailData = {
      candidateEmail: '<EMAIL>',
      candidateName: 'John Doe',
      jobTitle: 'Software Engineer',
      jobId: 'job123',
      candidateId: 'candidate123',
      companyName: 'Tech Corp',
    };

    it('should use active template when candidate source is JOB_SEEKER', async () => {
      const getVideoIntroActiveEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getVideoIntroActiveEmailHtml')
        .mockResolvedValue('<html>Active Template</html>');

      const getVideoIntroPassiveEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getVideoIntroPassiveEmailHtml')
        .mockResolvedValue('<html>Passive Template</html>');

      const sendEmailSpy = jest
        .spyOn(service as any, 'sendEmail')
        .mockResolvedValue({ id: 'email123' });

      await service.sendCandidateVideoIntroEmail(
        baseEmailData.candidateEmail,
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.jobId,
        baseEmailData.candidateId,
        baseEmailData.companyName,
        undefined,
        'JOB_SEEKER',
      );

      // Verify active template was used
      expect(getVideoIntroActiveEmailHtmlSpy).toHaveBeenCalledWith(
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.companyName,
        baseEmailData.jobId,
        baseEmailData.candidateId,
      );

      // Verify passive template was NOT used
      expect(getVideoIntroPassiveEmailHtmlSpy).not.toHaveBeenCalled();

      // Verify email was sent with correct subject
      expect(sendEmailSpy).toHaveBeenCalledWith({
        to: baseEmailData.candidateEmail,
        subject: `Let's take the next step in your application for ${baseEmailData.jobTitle} at ${baseEmailData.companyName}`,
        html: '<html>Active Template</html>',
        emailType: 'general',
        metadata: expect.objectContaining({
          candidateSource: 'JOB_SEEKER',
          emailType: 'video_intro',
        }),
      });
    });

    it('should use interest template when candidate source is RESUME_UPLOAD', async () => {
      const getVideoIntroActiveEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getVideoIntroActiveEmailHtml')
        .mockResolvedValue('<html>Active Template</html>');

      const getInterestEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getInterestEmailHtml')
        .mockResolvedValue('<html>Interest Template</html>');

      const sendEmailSpy = jest
        .spyOn(service as any, 'sendEmail')
        .mockResolvedValue({ id: 'email123' });

      await service.sendCandidateVideoIntroEmail(
        baseEmailData.candidateEmail,
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.jobId,
        baseEmailData.candidateId,
        baseEmailData.companyName,
        undefined,
        'RESUME_UPLOAD',
      );

      // Verify interest template was used
      expect(getInterestEmailHtmlSpy).toHaveBeenCalledWith(
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.companyName,
        baseEmailData.jobId,
        baseEmailData.candidateId,
      );

      // Verify active template was NOT used
      expect(getVideoIntroActiveEmailHtmlSpy).not.toHaveBeenCalled();

      // Verify email was sent with correct subject
      expect(sendEmailSpy).toHaveBeenCalledWith({
        to: baseEmailData.candidateEmail,
        subject: `Your profile caught our attention for a ${baseEmailData.jobTitle} role at ${baseEmailData.companyName}`,
        html: '<html>Interest Template</html>',
        emailType: 'general',
        metadata: expect.objectContaining({
          candidateSource: 'RESUME_UPLOAD',
          emailType: 'interest',
        }),
      });
    });

    it('should use interest template when candidate source is SCOUTED', async () => {
      const getInterestEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getInterestEmailHtml')
        .mockResolvedValue('<html>Interest Template</html>');

      const sendEmailSpy = jest
        .spyOn(service as any, 'sendEmail')
        .mockResolvedValue({ id: 'email123' });

      await service.sendCandidateVideoIntroEmail(
        baseEmailData.candidateEmail,
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.jobId,
        baseEmailData.candidateId,
        baseEmailData.companyName,
        undefined,
        'SCOUTED',
      );

      expect(getInterestEmailHtmlSpy).toHaveBeenCalled();
      expect(sendEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: `Your profile caught our attention for a ${baseEmailData.jobTitle} role at ${baseEmailData.companyName}`,
        }),
      );
    });

    it('should use interest template when candidate source is UPLOADED', async () => {
      const getInterestEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getInterestEmailHtml')
        .mockResolvedValue('<html>Interest Template</html>');

      const sendEmailSpy = jest
        .spyOn(service as any, 'sendEmail')
        .mockResolvedValue({ id: 'email123' });

      await service.sendCandidateVideoIntroEmail(
        baseEmailData.candidateEmail,
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.jobId,
        baseEmailData.candidateId,
        baseEmailData.companyName,
        undefined,
        'UPLOADED',
      );

      expect(getInterestEmailHtmlSpy).toHaveBeenCalled();
      expect(sendEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: `Your profile caught our attention for a ${baseEmailData.jobTitle} role at ${baseEmailData.companyName}`,
        }),
      );
    });

    it('should use interest template when candidate source is undefined', async () => {
      const getInterestEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getInterestEmailHtml')
        .mockResolvedValue('<html>Interest Template</html>');

      const sendEmailSpy = jest
        .spyOn(service as any, 'sendEmail')
        .mockResolvedValue({ id: 'email123' });

      await service.sendCandidateVideoIntroEmail(
        baseEmailData.candidateEmail,
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.jobId,
        baseEmailData.candidateId,
        baseEmailData.companyName,
        undefined,
        undefined,
      );

      expect(getInterestEmailHtmlSpy).toHaveBeenCalled();
      expect(sendEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            candidateSource: 'UNKNOWN',
            emailType: 'interest',
          }),
        }),
      );
    });

    it('should handle company name fallback correctly', async () => {
      const getVideoIntroActiveEmailHtmlSpy = jest
        .spyOn(EmailTemplates.EmailTemplates, 'getVideoIntroActiveEmailHtml')
        .mockResolvedValue('<html>Active Template</html>');

      const sendEmailSpy = jest
        .spyOn(service as any, 'sendEmail')
        .mockResolvedValue({ id: 'email123' });

      await service.sendCandidateVideoIntroEmail(
        baseEmailData.candidateEmail,
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.jobId,
        baseEmailData.candidateId,
        undefined, // No company name
        undefined,
        'JOB_SEEKER',
      );

      // Verify it uses default company name
      expect(getVideoIntroActiveEmailHtmlSpy).toHaveBeenCalledWith(
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        'Kaleido Talent', // Default company name
        baseEmailData.jobId,
        baseEmailData.candidateId,
      );

      expect(sendEmailSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: `Let's take the next step in your application for ${baseEmailData.jobTitle} at our company`,
        }),
      );
    });

    it('should log activity after sending email successfully', async () => {
      jest
        .spyOn(EmailTemplates.EmailTemplates, 'getVideoIntroActiveEmailHtml')
        .mockResolvedValue('<html>Test</html>');
      jest.spyOn(service as any, 'sendEmail').mockResolvedValue({ id: 'email123' });

      const mockCandidate = {
        id: baseEmailData.candidateId,
        activityHistory: [],
      };

      const findOneSpy = jest.fn().mockResolvedValue(mockCandidate);
      const saveSpy = jest.fn().mockResolvedValue(mockCandidate);

      (candidateRepository.findOne as jest.Mock) = findOneSpy;
      (candidateRepository.save as jest.Mock) = saveSpy;

      await service.sendCandidateVideoIntroEmail(
        baseEmailData.candidateEmail,
        baseEmailData.candidateName,
        baseEmailData.jobTitle,
        baseEmailData.jobId,
        baseEmailData.candidateId,
        baseEmailData.companyName,
        undefined,
        'JOB_SEEKER',
      );

      // Verify candidate was fetched
      expect(findOneSpy).toHaveBeenCalledWith({
        where: { id: baseEmailData.candidateId },
      });

      // Verify activity was logged (repository.save was called)
      expect(saveSpy).toHaveBeenCalled();
    });
  });
});
