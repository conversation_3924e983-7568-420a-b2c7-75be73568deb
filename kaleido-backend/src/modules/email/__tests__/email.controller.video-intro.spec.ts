import { Test, TestingModule } from '@nestjs/testing';
import { EmailController } from '../email.controller';
import { EmailService } from '../email.service';
import { CandidateService } from '../../candidate/candidate.service';
import { JobService } from '../../job/job.service';
import { BadRequestException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CandidateStatus } from '@/shared/types/candidate.types';
import { AuthService } from '../../../auth/auth.service';
import { Auth0Guard } from '../../../auth/auth.guard';

describe('EmailController - Video Intro Email Tests', () => {
  let controller: EmailController;
  let emailService: EmailService;
  let candidateService: CandidateService;
  let jobService: JobService;

  const mockEmailService = {
    sendCandidateVideoIntroEmail: jest.fn(),
  };

  const mockCandidateService = {
    findOne: jest.fn(),
  };

  const mockJobService = {
    findOne: jest.fn(),
    findByJobId: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmailController],
      providers: [
        Auth0Guard,
        Reflector,
        {
          provide: AuthService,
          useValue: {
            validateToken: jest.fn().mockResolvedValue(true),
            getUser: jest.fn().mockResolvedValue({ id: 'test-user' }),
          },
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: CandidateService,
          useValue: mockCandidateService,
        },
        {
          provide: JobService,
          useValue: mockJobService,
        },
      ],
    }).compile();

    controller = module.get<EmailController>(EmailController);
    emailService = module.get<EmailService>(EmailService);
    candidateService = module.get<CandidateService>(CandidateService);
    jobService = module.get<JobService>(JobService);

    jest.clearAllMocks();
  });

  describe('testShortlistEmail (single video intro)', () => {
    const mockJob = {
      id: 'job123',
      jobType: 'Software Engineer',
      department: 'Engineering',
      companyName: 'Tech Corp',
      cultureFitQuestions: ['Question 1', 'Question 2'],
    };

    it('should send video intro email with candidate source for JOB_SEEKER', async () => {
      const mockCandidate = {
        id: 'candidate123',
        fullName: 'John Doe',
        email: '<EMAIL>',
        source: 'JOB_SEEKER',
      };

      mockJobService.findByJobId.mockResolvedValue(mockJob);
      mockCandidateService.findOne.mockResolvedValue(mockCandidate);
      mockEmailService.sendCandidateVideoIntroEmail.mockResolvedValue({ id: 'email123' });

      const requestData = {
        candidateEmail: '<EMAIL>',
        candidateName: 'John Doe',
        jobTitle: '',
        jobId: 'job123',
        candidateId: 'candidate123',
        companyName: 'Tech Corp',
      };

      await controller.testShortlistEmail(requestData);

      expect(mockCandidateService.findOne).toHaveBeenCalledWith('candidate123');
      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'John Doe',
        'Engineering Software Engineer',
        'job123',
        'candidate123',
        'Tech Corp',
        undefined,
        'JOB_SEEKER',
      );
    });

    it('should send video intro email with candidate source for RESUME_UPLOAD', async () => {
      const mockCandidate = {
        id: 'candidate123',
        fullName: 'Jane Smith',
        email: '<EMAIL>',
        source: 'RESUME_UPLOAD',
      };

      mockJobService.findByJobId.mockResolvedValue(mockJob);
      mockCandidateService.findOne.mockResolvedValue(mockCandidate);
      mockEmailService.sendCandidateVideoIntroEmail.mockResolvedValue({ id: 'email123' });

      const requestData = {
        candidateEmail: '<EMAIL>',
        candidateName: 'Jane Smith',
        jobTitle: 'Custom Title',
        jobId: 'job123',
        candidateId: 'candidate123',
        companyName: 'Tech Corp',
      };

      await controller.testShortlistEmail(requestData);

      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Jane Smith',
        'Custom Title',
        'job123',
        'candidate123',
        'Tech Corp',
        undefined,
        'RESUME_UPLOAD',
      );
    });

    it('should handle case when candidate is not found', async () => {
      mockJobService.findByJobId.mockResolvedValue(mockJob);
      mockCandidateService.findOne.mockRejectedValue(new Error('Candidate not found'));
      mockEmailService.sendCandidateVideoIntroEmail.mockResolvedValue({ id: 'email123' });

      const requestData = {
        candidateEmail: '<EMAIL>',
        candidateName: 'Test User',
        jobTitle: 'Test Job',
        jobId: 'job123',
        candidateId: 'invalid-id',
        companyName: 'Tech Corp',
      };

      await controller.testShortlistEmail(requestData);

      // Should still send email but without source
      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        'Test Job',
        'job123',
        'invalid-id',
        'Tech Corp',
        undefined,
        undefined,
      );
    });

    it('should throw error if job has no cultural fit questions', async () => {
      const jobWithoutQuestions = { ...mockJob, cultureFitQuestions: [] };
      mockJobService.findByJobId.mockResolvedValue(jobWithoutQuestions);

      const requestData = {
        candidateEmail: '<EMAIL>',
        candidateName: 'Test User',
        jobTitle: 'Test Job',
        jobId: 'job123',
        candidateId: 'candidate123',
        companyName: 'Tech Corp',
      };

      await expect(controller.testShortlistEmail(requestData)).rejects.toThrow(BadRequestException);
    });
  });

  describe('sendBulkShortlistEmails', () => {
    const mockJob = {
      id: 'job123',
      jobType: 'Software Engineer',
      department: 'Engineering',
      companyName: 'Tech Corp',
    };

    const mockCandidates = [
      {
        id: 'candidate1',
        fullName: 'John Doe',
        email: '<EMAIL>',
        source: 'JOB_SEEKER',
        status: 'SHORTLISTED' as CandidateStatus,
      },
      {
        id: 'candidate2',
        fullName: 'Jane Smith',
        email: '<EMAIL>',
        source: 'RESUME_UPLOAD',
        status: 'SHORTLISTED' as CandidateStatus,
      },
      {
        id: 'candidate3',
        fullName: 'Bob Johnson',
        email: '<EMAIL>',
        source: 'SCOUTED',
        status: 'NEW' as CandidateStatus,
      },
    ];

    it('should send emails to all candidates with their respective sources', async () => {
      mockJobService.findOne.mockResolvedValue(mockJob);
      mockCandidateService.findOne
        .mockResolvedValueOnce(mockCandidates[0])
        .mockResolvedValueOnce(mockCandidates[1])
        .mockResolvedValueOnce(mockCandidates[2]);

      mockEmailService.sendCandidateVideoIntroEmail.mockResolvedValue({ id: 'email123' });

      const requestData = {
        jobId: 'job123',
        candidateIds: ['candidate1', 'candidate2', 'candidate3'],
      };

      const result = await controller.sendBulkShortlistEmails(requestData);

      // Verify each candidate was processed with correct source
      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenCalledTimes(3);

      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenNthCalledWith(
        1,
        '<EMAIL>',
        'John Doe',
        'Engineering Software Engineer',
        'job123',
        'candidate1',
        'Tech Corp',
        undefined,
        'JOB_SEEKER',
      );

      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenNthCalledWith(
        2,
        '<EMAIL>',
        'Jane Smith',
        'Engineering Software Engineer',
        'job123',
        'candidate2',
        'Tech Corp',
        undefined,
        'RESUME_UPLOAD',
      );

      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenNthCalledWith(
        3,
        '<EMAIL>',
        'Bob Johnson',
        'Engineering Software Engineer',
        'job123',
        'candidate3',
        'Tech Corp',
        undefined,
        'SCOUTED',
      );

      expect(result).toEqual({
        success: 3,
        failed: 0,
        total: 3,
        tier: undefined,
        shortlistedOnly: undefined,
      });
    });

    it('should only send to shortlisted candidates when shortlistedOnly is true', async () => {
      mockJobService.findOne.mockResolvedValue(mockJob);
      mockCandidateService.findOne
        .mockResolvedValueOnce(mockCandidates[0])
        .mockResolvedValueOnce(mockCandidates[1])
        .mockResolvedValueOnce(mockCandidates[2]);

      mockEmailService.sendCandidateVideoIntroEmail.mockResolvedValue({ id: 'email123' });

      const requestData = {
        jobId: 'job123',
        candidateIds: ['candidate1', 'candidate2', 'candidate3'],
        shortlistedOnly: true,
      };

      const result = await controller.sendBulkShortlistEmails(requestData);

      // Should only send to candidates with SHORTLISTED status
      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenCalledTimes(2);

      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'John Doe',
        'Engineering Software Engineer',
        'job123',
        'candidate1',
        'Tech Corp',
        undefined,
        'JOB_SEEKER',
      );

      expect(mockEmailService.sendCandidateVideoIntroEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Jane Smith',
        'Engineering Software Engineer',
        'job123',
        'candidate2',
        'Tech Corp',
        undefined,
        'RESUME_UPLOAD',
      );

      expect(result).toEqual({
        success: 2,
        failed: 0,
        total: 2,
        tier: undefined,
        shortlistedOnly: true,
      });
    });

    it('should handle mixed success and failure results', async () => {
      mockJobService.findOne.mockResolvedValue(mockJob);
      mockCandidateService.findOne
        .mockResolvedValueOnce(mockCandidates[0])
        .mockResolvedValueOnce(mockCandidates[1]);

      mockEmailService.sendCandidateVideoIntroEmail
        .mockResolvedValueOnce({ id: 'email123' })
        .mockRejectedValueOnce(new Error('Email failed'));

      const requestData = {
        jobId: 'job123',
        candidateIds: ['candidate1', 'candidate2'],
      };

      const result = await controller.sendBulkShortlistEmails(requestData);

      expect(result).toEqual({
        success: 1,
        failed: 1,
        total: 2,
        tier: undefined,
        shortlistedOnly: undefined,
      });
    });
  });
});
