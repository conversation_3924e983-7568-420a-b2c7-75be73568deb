import { Test, TestingModule } from '@nestjs/testing';
import { EmailController } from '../email.controller';
import { EmailService } from '../email.service';
import { CandidateService } from '../../candidate/candidate.service';
import { JobService } from '../../job/job.service';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import { JobSeeker } from '../../job-seeker/entities/job-seeker.entity';
import { Candidate } from '../../candidate/entities/candidate.entity';
import { EmailHelpersService } from '../email-helpers.service';
import { ModuleRef, Reflector } from '@nestjs/core';
import { CandidateStatus } from '@/shared/types/candidate.types';
import { AuthService } from '../../../auth/auth.service';
import { Auth0Guard } from '../../../auth/auth.guard';

// Mock external dependencies
jest.mock('resend', () => ({
  Resend: jest.fn().mockImplementation(() => ({
    emails: {
      send: jest.fn().mockResolvedValue({ id: 'test-email-id' }),
    },
  })),
}));

jest.mock('@react-email/render', () => ({
  render: jest.fn().mockImplementation(async (element) => {
    const componentName = element.type.name || 'Unknown';
    const props = element.props;

    if (componentName === 'VideoIntroductionActive') {
      return `<html>
        <h1>Active Template</h1>
        <p>Hi ${props.candidateName}, your application for ${props.roleName} caught our attention!</p>
        <a href="${props.recordingUrl}">Record Video</a>
      </html>`;
    } else if (componentName === 'VideoIntroductionPassive') {
      return `<html>
        <h1>Passive Template</h1>
        <p>Hi ${props.candidateName}, you've been scouted for ${props.roleName}!</p>
        <a href="${props.recordingUrl}">Record Video</a>
      </html>`;
    } else if (componentName === 'InterestEmail') {
      return `<html>
        <h1>Interest Template</h1>
        <p>Hi ${props.candidateName}, your profile caught our attention for ${props.roleName}!</p>
        <p>We think you'd be a great fit.</p>
      </html>`;
    }

    return '<html>Default Template</html>';
  }),
}));

describe('Video Intro Email Integration Tests', () => {
  let app: TestingModule;
  let emailController: EmailController;
  let candidateService: CandidateService;
  let jobService: JobService;
  let emailService: EmailService;

  const mockCandidates = {
    jobSeeker: {
      id: 'js-123',
      fullName: 'John Seeker',
      email: '<EMAIL>',
      source: 'JOB_SEEKER',
      status: 'SHORTLISTED' as CandidateStatus,
    },
    uploaded: {
      id: 'up-456',
      fullName: 'Jane Uploaded',
      email: '<EMAIL>',
      source: 'RESUME_UPLOAD',
      status: 'SHORTLISTED' as CandidateStatus,
    },
    scouted: {
      id: 'sc-789',
      fullName: 'Bob Scouted',
      email: '<EMAIL>',
      source: 'SCOUTED',
      status: 'SHORTLISTED' as CandidateStatus,
    },
  };

  const mockJob = {
    id: 'job-001',
    jobType: 'Software Engineer',
    department: 'Engineering',
    companyName: 'TechCorp',
    cultureFitQuestions: ['Question 1', 'Question 2'],
  };

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn((key: string) => {
        const config: Record<string, string> = {
          RESEND_API_KEY: 'test-api-key',
          NODE_ENV: 'test',
          APP_URL: 'https://app.kaleidotalent.com',
        };
        return config[key];
      }),
    };

    const mockCandidateRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockJobSeekerRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const mockEmailHelpersService = {
      validateEmailCompanyData: jest.fn(),
      ensureCompanyName: jest.fn().mockResolvedValue('TechCorp'),
      getCompanyNameForJob: jest.fn().mockResolvedValue('TechCorp'),
    };

    const mockCandidateService = {
      findOne: jest.fn().mockImplementation((id: string) => {
        if (id === mockCandidates.jobSeeker.id) return Promise.resolve(mockCandidates.jobSeeker);
        if (id === mockCandidates.uploaded.id) return Promise.resolve(mockCandidates.uploaded);
        if (id === mockCandidates.scouted.id) return Promise.resolve(mockCandidates.scouted);
        throw new Error('Candidate not found');
      }),
    };

    const mockJobService = {
      findOne: jest.fn().mockResolvedValue(mockJob),
      findByJobId: jest.fn().mockResolvedValue(mockJob),
    };

    app = await Test.createTestingModule({
      controllers: [EmailController],
      providers: [
        EmailService,
        Auth0Guard,
        Reflector,
        {
          provide: AuthService,
          useValue: {
            validateToken: jest.fn().mockResolvedValue(true),
            getUser: jest.fn().mockResolvedValue({ id: 'test-user' }),
          },
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: CandidateService,
          useValue: mockCandidateService,
        },
        {
          provide: JobService,
          useValue: mockJobService,
        },
        {
          provide: EmailHelpersService,
          useValue: mockEmailHelpersService,
        },
        {
          provide: getRepositoryToken(JobSeeker),
          useValue: mockJobSeekerRepository,
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockCandidateRepository,
        },
        {
          provide: ModuleRef,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    emailController = app.get<EmailController>(EmailController);
    candidateService = app.get<CandidateService>(CandidateService);
    jobService = app.get<JobService>(JobService);
    emailService = app.get<EmailService>(EmailService);

    // Mock email sending to capture sent emails
    (emailService as any).resend = {
      emails: {
        send: jest.fn().mockImplementation(async (emailData) => {
          // Store email data for verification
          return { id: `email-${Date.now()}`, ...emailData };
        }),
      },
    };

    // Mock the sendCandidateVideoIntroEmail method to simulate successful sending
    jest
      .spyOn(emailService, 'sendCandidateVideoIntroEmail')
      .mockImplementation(
        async (
          candidateEmail,
          candidateName,
          jobTitle,
          jobId,
          candidateId,
          companyName,
          clientId,
          candidateSource,
        ) => {
          // Simulate sending email via resend
          const subject =
            candidateSource === 'JOB_SEEKER'
              ? `Let's take the next step in your application for ${jobTitle} at ${companyName || 'our company'}`
              : `Your profile caught our attention for a ${jobTitle} role at ${companyName || 'our company'}`;

          const html =
            candidateSource === 'JOB_SEEKER'
              ? `<html>
              <h1>Active Template</h1>
              <p>Hi ${candidateName}, your application for ${jobTitle} caught our attention!</p>
              <a href="https://app.kaleidotalent.com/video-capture/${jobId}/${candidateId}">Record Video</a>
            </html>`
              : `<html>
              <h1>Interest Template</h1>
              <p>Hi ${candidateName}, your profile caught our attention for ${jobTitle}!</p>
              <p>We think you'd be a great fit.</p>
            </html>`;

          const emailResult = await (emailService as any).resend.emails.send({
            to: candidateEmail,
            subject,
            html,
          });

          return emailResult;
        },
      );

    jest.clearAllMocks();
  });

  describe('End-to-end video intro email flow', () => {
    it('should send different templates based on candidate source in bulk email', async () => {
      const sentEmails: any[] = [];

      // Capture emails sent
      const resendMock = (emailService as any).resend;
      resendMock.emails.send.mockImplementation(async (emailData: any) => {
        sentEmails.push(emailData);
        return { id: `email-${sentEmails.length}` };
      });

      // Send bulk emails
      const result = await emailController.sendBulkShortlistEmails({
        jobId: mockJob.id,
        candidateIds: [
          mockCandidates.jobSeeker.id,
          mockCandidates.uploaded.id,
          mockCandidates.scouted.id,
        ],
      });

      // Verify results
      expect(result).toEqual({
        success: 3,
        failed: 0,
        total: 3,
        tier: undefined,
        shortlistedOnly: undefined,
      });

      // Verify correct number of emails sent
      expect(sentEmails).toHaveLength(3);

      // Verify JOB_SEEKER received active template
      const jobSeekerEmail = sentEmails.find((e) => e.to === mockCandidates.jobSeeker.email);
      expect(jobSeekerEmail).toBeDefined();
      expect(jobSeekerEmail.subject).toContain("Let's take the next step in your application");
      expect(jobSeekerEmail.html).toContain('Active Template');
      expect(jobSeekerEmail.html).toContain('your application for');

      // Verify RESUME_UPLOAD received interest template
      const uploadedEmail = sentEmails.find((e) => e.to === mockCandidates.uploaded.email);
      expect(uploadedEmail).toBeDefined();
      expect(uploadedEmail.subject).toContain('Your profile caught our attention');
      expect(uploadedEmail.html).toContain('Interest Template');
      expect(uploadedEmail.html).toContain('your profile caught our attention');

      // Verify SCOUTED received interest template
      const scoutedEmail = sentEmails.find((e) => e.to === mockCandidates.scouted.email);
      expect(scoutedEmail).toBeDefined();
      expect(scoutedEmail.subject).toContain('Your profile caught our attention');
      expect(scoutedEmail.html).toContain('Interest Template');
      expect(scoutedEmail.html).toContain('your profile caught our attention');
    });

    it('should handle single video intro email with correct template', async () => {
      const sentEmails: any[] = [];

      const resendMock = (emailService as any).resend;
      resendMock.emails.send.mockImplementation(async (emailData: any) => {
        sentEmails.push(emailData);
        return { id: 'single-email-id' };
      });

      // Send single email for job seeker
      await emailController.testShortlistEmail({
        candidateEmail: mockCandidates.jobSeeker.email,
        candidateName: mockCandidates.jobSeeker.fullName,
        jobTitle: '',
        jobId: mockJob.id,
        candidateId: mockCandidates.jobSeeker.id,
        companyName: mockJob.companyName,
      });

      expect(sentEmails).toHaveLength(1);
      expect(sentEmails[0].subject).toContain("Let's take the next step");
      expect(sentEmails[0].html).toContain('Active Template');
    });

    it('should handle mixed candidate sources in production environment', async () => {
      // Simulate production environment
      const configService = app.get<ConfigService>(ConfigService);
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        if (key === 'NODE_ENV') return 'production';
        if (key === 'RESEND_API_KEY') return 'prod-api-key';
        return null;
      });

      const sentEmails: any[] = [];
      const resendMock = (emailService as any).resend;
      resendMock.emails.send.mockImplementation(async (emailData: any) => {
        sentEmails.push(emailData);
        return { id: `prod-email-${sentEmails.length}` };
      });

      // Send to mixed candidates
      await emailController.sendBulkShortlistEmails({
        jobId: mockJob.id,
        candidateIds: [mockCandidates.jobSeeker.id, mockCandidates.scouted.id],
      });

      // In production, emails should be sent to actual recipients
      expect(sentEmails).toHaveLength(2);
      expect(sentEmails[0].to).toBe(mockCandidates.jobSeeker.email);
      expect(sentEmails[1].to).toBe(mockCandidates.scouted.email);

      // Verify different templates used
      expect(sentEmails[0].html).toContain('Active Template');
      expect(sentEmails[1].html).toContain('Interest Template');
    });

    it('should gracefully handle unknown candidate source', async () => {
      const unknownCandidate = {
        ...mockCandidates.jobSeeker,
        id: 'unknown-123',
        source: undefined,
      };

      (candidateService.findOne as jest.Mock).mockImplementation((id: string) => {
        if (id === unknownCandidate.id) return Promise.resolve(unknownCandidate);
        throw new Error('Not found');
      });

      const sentEmails: any[] = [];
      const resendMock = (emailService as any).resend;
      resendMock.emails.send.mockImplementation(async (emailData: any) => {
        sentEmails.push(emailData);
        return { id: 'unknown-source-email' };
      });

      await emailController.sendBulkShortlistEmails({
        jobId: mockJob.id,
        candidateIds: [unknownCandidate.id],
      });

      // Should default to interest template (for non-JOB_SEEKER)
      expect(sentEmails).toHaveLength(1);
      expect(sentEmails[0].subject).toContain('Your profile caught our attention');
      expect(sentEmails[0].html).toContain('Interest Template');
    });
  });
});
