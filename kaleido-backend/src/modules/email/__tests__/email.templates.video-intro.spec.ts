import { EmailTemplates } from '../email.templates';
import * as React from 'react';
import { render } from '@react-email/render';

// Mock the email utils
jest.mock('../email.utils', () => ({
  getVideoCaptureUrl: jest.fn(
    (jobId: string, candidateId: string) =>
      `https://app.kaleidotalent.com/video-capture/${jobId}/${candidateId}`,
  ),
  getDashboardUrl: jest.fn((url?: string) => url || 'https://app.kaleidotalent.com/dashboard'),
}));

// Store created elements for inspection
let lastCreatedElement: any = null;

// Mock React.createElement to capture the element
jest.spyOn(React, 'createElement').mockImplementation((type: any, props: any) => {
  lastCreatedElement = { type, props };
  return { type, props } as any;
});

// Mock React Email render with a proper implementation
jest.mock('@react-email/render', () => ({
  render: jest.fn().mockImplementation(async (element) => {
    // Handle undefined element or missing type
    if (!element || !element.type) {
      return '<html><body><div class="email-template">Default Template</div></body></html>';
    }

    // Extract component name and props for testing
    const componentType = element.type;
    const props = element.props || {};

    // Return a mock HTML that includes key information we can test
    return `<html><body>
      <div class="email-template" data-template="${componentType.name || 'Unknown'}">
        <div class="candidate-name">${props.candidateName || ''}</div>
        <div class="job-title">${props.jobTitle || props.roleName || ''}</div>
        <div class="company-name">${props.companyName || ''}</div>
        <div class="recording-url">${props.recordingUrl || props.videoCaptureUrl || ''}</div>
        <div class="deadline">${props.deadline || ''}</div>
        <div class="deadline-time">${props.deadlineTime || ''}</div>
      </div>
    </body></html>`;
  }),
}));

describe('EmailTemplates - Video Intro Templates', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getVideoIntroActiveEmailHtml', () => {
    it('should render active template with correct props', async () => {
      const candidateName = 'John Doe';
      const jobTitle = 'Software Engineer';
      const companyName = 'Tech Corp';
      const jobId = 'job123';
      const candidateId = 'candidate123';

      const html = await EmailTemplates.getVideoIntroActiveEmailHtml(
        candidateName,
        jobTitle,
        companyName,
        jobId,
        candidateId,
      );

      // Verify render was called
      expect(render).toHaveBeenCalled();

      // Verify component type using the captured element
      expect(lastCreatedElement.type.name).toBe('VideoIntroductionActive');

      // Verify props
      expect(lastCreatedElement.props).toMatchObject({
        candidateName,
        roleName: jobTitle,
        companyName,
        recordingUrl: `https://app.kaleidotalent.com/video-capture/${jobId}/${candidateId}`,
        deadline: expect.any(String),
        deadlineTime: '11:59 PM',
      });

      // Verify HTML contains expected content
      expect(html).toContain(candidateName);
      expect(html).toContain(jobTitle);
      expect(html).toContain(companyName);
      expect(html).toContain(`video-capture/${jobId}/${candidateId}`);
    });

    it('should use default company name when not provided', async () => {
      const html = await EmailTemplates.getVideoIntroActiveEmailHtml(
        'Jane Smith',
        'Product Manager',
        undefined,
        'job456',
        'candidate456',
      );

      const renderCall = (render as jest.Mock).mock.calls[0][0];
      expect(renderCall.props.companyName).toBe('Kaleido Talent');
    });

    it('should set deadline to 7 days from now', async () => {
      const beforeDate = new Date();

      await EmailTemplates.getVideoIntroActiveEmailHtml(
        'Test User',
        'Test Role',
        'Test Company',
        'job789',
        'candidate789',
      );

      const deadlineString = lastCreatedElement.props.deadline;

      // Parse the deadline and verify it's approximately 7 days from now
      // The deadline is a formatted date string without time, so when parsed it defaults to midnight
      // This means it could be 6-7 days depending on the current time of day
      const deadlineDate = new Date(deadlineString);
      const daysDifference =
        (deadlineDate.getTime() - beforeDate.getTime()) / (1000 * 60 * 60 * 24);

      // Allow for 6-8 days since the deadline loses time precision when formatted
      expect(daysDifference).toBeGreaterThanOrEqual(6.0);
      expect(daysDifference).toBeLessThanOrEqual(8.0);
    });
  });

  describe('getVideoIntroPassiveEmailHtml', () => {
    it('should render passive template with correct props', async () => {
      const candidateName = 'John Doe';
      const jobTitle = 'Senior Developer';
      const companyName = 'Startup Inc';
      const jobId = 'job111';
      const candidateId = 'candidate111';

      const html = await EmailTemplates.getVideoIntroPassiveEmailHtml(
        candidateName,
        jobTitle,
        companyName,
        jobId,
        candidateId,
      );

      // Verify render was called
      expect(render).toHaveBeenCalled();

      // Get the React element that was passed to render
      const renderCall = (render as jest.Mock).mock.calls[0][0];

      // Verify component type
      expect(renderCall.type.name).toBe('VideoIntroductionPassive');

      // Verify props
      expect(renderCall.props).toMatchObject({
        candidateName,
        jobTitle,
        roleName: jobTitle,
        companyName,
        recordingUrl: `https://app.kaleidotalent.com/video-capture/${jobId}/${candidateId}`,
        deadline: expect.any(String),
        deadlineTime: '11:59 PM',
        yourName: 'Hiring Team',
        yourTitle: `${companyName} Recruitment`,
      });

      // Verify HTML contains expected content
      expect(html).toContain(candidateName);
      expect(html).toContain(jobTitle);
      expect(html).toContain(companyName);
    });

    it('should include hiring team info in passive template', async () => {
      const companyName = 'Big Corp';

      await EmailTemplates.getVideoIntroPassiveEmailHtml(
        'Candidate Name',
        'Role Title',
        companyName,
        'job222',
        'candidate222',
      );

      const renderCall = (render as jest.Mock).mock.calls[0][0];

      expect(renderCall.props.yourName).toBe('Hiring Team');
      expect(renderCall.props.yourTitle).toBe(`${companyName} Recruitment`);
    });

    it('should handle undefined company name in passive template', async () => {
      await EmailTemplates.getVideoIntroPassiveEmailHtml(
        'Test Candidate',
        'Test Position',
        undefined,
        'job333',
        'candidate333',
      );

      const renderCall = (render as jest.Mock).mock.calls[0][0];

      expect(renderCall.props.companyName).toBe('Kaleido Talent');
      expect(renderCall.props.yourTitle).toBe('Kaleido Talent Recruitment');
    });
  });

  describe('getVideoIntroShortlistedEmailHtml', () => {
    it('should still work for backward compatibility', async () => {
      const candidateName = 'Legacy User';
      const jobTitle = 'Legacy Role';
      const companyName = 'Legacy Company';
      const jobId = 'job999';
      const candidateId = 'candidate999';

      const html = await EmailTemplates.getVideoIntroShortlistedEmailHtml(
        candidateName,
        jobTitle,
        companyName,
        jobId,
        candidateId,
      );

      expect(render).toHaveBeenCalled();

      const renderCall = (render as jest.Mock).mock.calls[0][0];

      // Should use the original VideoIntroShortlistedEmail component
      expect(renderCall.type.name).toBe('VideoIntroShortlistedEmail');

      expect(renderCall.props).toMatchObject({
        candidateName,
        jobTitle,
        companyName,
        videoCaptureUrl: `https://app.kaleidotalent.com/video-capture/${jobId}/${candidateId}`,
      });
    });
  });
});
