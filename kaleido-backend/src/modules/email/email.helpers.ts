import * as React from 'react';
import { render } from '@react-email/render';
import { getDashboardUrl } from './email.utils';

/**
 * Common email rendering helpers
 */
export const EmailHelpers = {
  /**
   * Render email template and send with error handling
   */
  async renderAndSend(
    Component: React.ComponentType<any>,
    props: any,
    emailService: { sendEmail: (options: any) => Promise<any> },
    to: string,
    subject: string,
    options?: {
      emailType?: 'interview' | 'offer' | 'status' | 'general';
      metadata?: any;
      errorContext?: string;
    },
  ): Promise<any> {
    const emailHtml = await render(React.createElement(Component, props));

    return emailService.sendEmail({
      to,
      subject,
      html: emailHtml,
      emailType: options?.emailType || 'general',
      metadata: options?.metadata,
    });
  },

  /**
   * Build profile URL with defaults
   */
  getProfileUrl(customUrl?: string): string {
    return customUrl || `${process.env.APP_URL}/profile`;
  },

  /**
   * Build job board URL with defaults
   */
  getJobBoardUrl(customUrl?: string): string {
    return customUrl || `${process.env.APP_URL}/open-jobs`;
  },

  /**
   * Build product roadmap URL with defaults
   */
  getProductRoadmapUrl(customUrl?: string): string {
    return customUrl || `${process.env.APP_URL}/roadmap`;
  },

  /**
   * Build login URL with defaults
   */
  getLoginUrl(customUrl?: string): string {
    return customUrl || process.env.APP_URL || 'https://app.kaleidotalent.com';
  },

  /**
   * Build video environment URL
   */
  getVideoEnvUrl(): string {
    return process.env.VIDEO_ENV_URL || 'https://app.kaleidotalent.com';
  },

  /**
   * Build company profile URL
   */
  getCompanyProfileUrl(companyId?: string): string | undefined {
    if (!companyId) return undefined;
    return `${process.env.APP_URL}/companies/${companyId}`;
  },

  /**
   * Build job description URL
   */
  getJobDescriptionUrl(jobId?: string): string | undefined {
    if (!jobId) return undefined;
    return `${process.env.APP_URL}/open-jobs/${jobId}`;
  },

  /**
   * Format phone number for display
   */
  formatPhoneNumber(phone?: string): string | undefined {
    if (!phone) return undefined;
    // Basic formatting - can be enhanced
    return phone.replace(/[^\d]/g, '').replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
  },

  /**
   * Build contact details string
   */
  formatContactDetails(email?: string, phone?: string): string {
    const parts = [];
    if (email) parts.push(email);
    if (phone) parts.push(this.formatPhoneNumber(phone) || phone);
    return parts.join('\n');
  },

  /**
   * Create default test data for email previews
   */
  getDefaultTestData(emailType: string): any {
    const defaults = {
      candidateName: 'John Doe',
      talentName: 'Jane Smith',
      jobTitle: 'Senior Software Engineer',
      companyName: 'Tech Innovations Inc',
      fullName: 'Test User',
      dashboardUrl: getDashboardUrl(),
      loginUrl: this.getLoginUrl(),
      applicationDate: new Date().toISOString(),
      proposedDate: 'Tuesday, December 19th',
      proposedTime: '2:00 PM - 3:00 PM EST',
      deadline: 'Friday, December 15th',
      deadlineTime: '5:00 PM EST',
      yourName: 'Hiring Manager',
      yourTitle: 'Senior Talent Acquisition',
      meetingLink: 'https://meet.google.com/abc-defg-hij',
    };

    // Add type-specific defaults
    switch (emailType) {
      case 'profile_approved':
        return {
          ...defaults,
          topSkills: ['React', 'Node.js', 'TypeScript'],
          yearsExperience: 5,
          industry: 'Technology',
        };
      case 'video_introduction_passive':
        return {
          ...defaults,
          jobTitle: 'Senior Designer',
          roleName: 'Senior UI/UX Designer',
          companyName: 'Creative Agency',
          deadline: 'Monday, December 18th',
          deadlineTime: '11:59 PM PST',
          yourName: 'John Smith',
          yourTitle: 'Head of Talent',
        };
      default:
        return defaults;
    }
  },
};
