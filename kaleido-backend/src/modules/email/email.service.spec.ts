import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailService } from './email.service';
import { EmailHelpersService } from './email-helpers.service';
import { JobSeeker } from '../job-seeker/entities/job-seeker.entity';
import { Candidate } from '../candidate/entities/candidate.entity';
import { ModuleRef } from '@nestjs/core';
import { CandidateStatus } from '@/shared/types/candidate.types';

// Mock Resend
jest.mock('resend', () => {
  return {
    Resend: jest.fn().mockImplementation(() => ({
      emails: {
        send: jest.fn().mockResolvedValue({ id: 'test-email-id' }),
      },
    })),
  };
});

// Mock React Email render
jest.mock('@react-email/render', () => ({
  render: jest.fn().mockResolvedValue('<html>Test Email</html>'),
}));

describe('EmailService - Email Trigger Tests', () => {
  let service: EmailService;
  let emailHelpersService: EmailHelpersService;
  let jobSeekerRepository: Repository<JobSeeker>;
  let candidateRepository: Repository<Candidate>;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        RESEND_API_KEY: 'test-api-key',
        NODE_ENV: 'test',
        APP_URL: 'https://app.kaleidotalent.com',
      };
      return config[key];
    }),
  };

  const mockEmailHelpersService = {
    validateEmailCompanyData: jest.fn(),
    ensureCompanyName: jest.fn().mockResolvedValue('Test Company'),
    getCompanyNameForJob: jest.fn().mockResolvedValue('Test Company'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: EmailHelpersService,
          useValue: mockEmailHelpersService,
        },
        {
          provide: getRepositoryToken(JobSeeker),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: ModuleRef,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    emailHelpersService = module.get<EmailHelpersService>(EmailHelpersService);
    jobSeekerRepository = module.get<Repository<JobSeeker>>(getRepositoryToken(JobSeeker));
    candidateRepository = module.get<Repository<Candidate>>(getRepositoryToken(Candidate));

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('Job Application Email Triggers', () => {
    describe('When a candidate applies for a job', () => {
      it('should send confirmation email to candidate', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendJobApplicationSubmittedAutomated(
          '<EMAIL>',
          'John Doe',
          'Senior Developer',
          'Tech Corp',
          'https://app.kaleidotalent.com/dashboard',
          'https://app.kaleidotalent.com/companies/123',
          'client-123',
        );

        expect(mockEmailHelpersService.validateEmailCompanyData).toHaveBeenCalledWith({
          companyName: 'Tech Corp',
        });

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: `Your application is on its way! Here's what happens next 📨`,
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            talentName: 'John Doe',
            jobTitle: 'Senior Developer',
            companyName: 'Tech Corp',
            emailType: 'job_application_submitted_automated',
          }),
        });
      });

      it('should send notification email to company', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendJobApplicationNotificationToCompany(
          'Tech Corp',
          'Jane Recruiter',
          'Senior Developer',
          'John Doe',
          'December 15, 2023',
          'https://app.kaleidotalent.com/open-jobs/123',
          'https://app.kaleidotalent.com/applications?id=123',
          'https://app.kaleidotalent.com/jobs/123/candidates',
          '<EMAIL>',
          'client-123',
        );

        expect(mockEmailHelpersService.validateEmailCompanyData).toHaveBeenCalledWith({
          companyName: 'Tech Corp',
        });

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: 'New Application for Senior Developer',
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            companyName: 'Tech Corp',
            jobTitle: 'Senior Developer',
            candidateFullName: 'John Doe',
            applicationDate: 'December 15, 2023',
            emailType: 'job_application_notification',
          }),
        });
      });

      it('should not send email if company name is invalid', async () => {
        mockEmailHelpersService.validateEmailCompanyData.mockImplementationOnce(() => {
          throw new Error('Invalid company name: Your Company');
        });

        await expect(
          service.sendJobApplicationSubmittedAutomated(
            '<EMAIL>',
            'John Doe',
            'Senior Developer',
            'Your Company',
          ),
        ).rejects.toThrow('Invalid company name: Your Company');
      });
    });
  });

  describe('Candidate Status Change Email Triggers', () => {
    describe('When candidate status changes to INTERVIEWING', () => {
      it('should send interview invitation email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendCandidateStatusUpdateEmail(
          '<EMAIL>',
          'John Doe',
          'Senior Developer',
          'Tech Corp',
          'INTERVIEWING',
          'We would like to invite you for an interview',
          '2023-12-20T10:00:00Z',
          'https://meet.google.com/abc-defg-hij',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: 'Interview Invitation: Senior Developer at Tech Corp',
          html: expect.any(String),
          emailType: 'interview',
          metadata: expect.objectContaining({
            candidateName: 'John Doe',
            jobTitle: 'Senior Developer',
            companyName: 'Tech Corp',
            status: 'INTERVIEWING',
            message: 'We would like to invite you for an interview',
            interviewDate: '2023-12-20T10:00:00Z',
            meetingLink: 'https://meet.google.com/abc-defg-hij',
          }),
        });
      });
    });

    describe('When candidate status changes to OFFER_EXTENDED', () => {
      it('should send offer email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendCandidateStatusUpdateEmail(
          '<EMAIL>',
          'John Doe',
          'Senior Developer',
          'Tech Corp',
          'OFFER_EXTENDED',
          'We are pleased to offer you the position',
          undefined,
          undefined,
          undefined,
          undefined,
          '2023-12-31',
          'https://app.kaleidotalent.com/offer/123',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: expect.any(String),
          html: expect.any(String),
          emailType: 'offer',
          metadata: expect.objectContaining({
            candidateName: 'John Doe',
            jobTitle: 'Senior Developer',
            companyName: 'Tech Corp',
            status: 'OFFER_EXTENDED',
            message: 'We are pleased to offer you the position',
            offerExpirationDate: '2023-12-31',
            responseLink: 'https://app.kaleidotalent.com/offer/123',
          }),
        });
      });
    });

    describe('When candidate status changes to HIRED', () => {
      it('should send hired/welcome email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendHiredEmail(
          '<EMAIL>',
          'John Doe',
          'Senior Developer',
          'Tech Corp',
          '2024-01-15',
          'https://app.kaleidotalent.com/onboarding/123',
        );

        expect(mockEmailHelpersService.validateEmailCompanyData).toHaveBeenCalledWith({
          companyName: 'Tech Corp',
        });

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: expect.stringContaining('Tech Corp'),
          html: expect.any(String),
        });
      });
    });

    describe('When candidate status changes to REJECTED', () => {
      it('should send rejection email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendApplicationRejection(
          '<EMAIL>',
          'John Doe',
          'Senior Developer',
          'Tech Corp',
          'Jane Recruiter',
          'Talent Acquisition Manager',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: expect.any(String),
          html: expect.any(String),
          emailType: 'status',
          metadata: expect.objectContaining({
            candidateName: 'John Doe',
            jobTitle: 'Senior Developer',
            companyName: 'Tech Corp',
            emailType: 'application_rejection',
          }),
        });
      });
    });

    describe('When candidate status changes to SHORTLISTED', () => {
      it('should send video introduction request (active) email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendVideoIntroductionActive(
          '<EMAIL>',
          'John Doe',
          'Senior Developer',
          'Tech Corp',
          'https://app.kaleidotalent.com/video/record/123',
          'December 20, 2023',
          '11:59 PM PST',
          'Jane Recruiter',
          'Talent Acquisition Manager',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: expect.any(String),
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            candidateName: 'John Doe',
            roleName: 'Senior Developer',
            companyName: 'Tech Corp',
            deadline: 'December 20, 2023',
            emailType: 'video_introduction_active',
          }),
        });
      });
    });
  });

  describe('Profile/Registration Email Triggers', () => {
    describe('When a job seeker profile is approved', () => {
      it('should send profile approved email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendProfileApproved(
          '<EMAIL>',
          'John Doe',
          ['React', 'Node.js', 'TypeScript'],
          5,
          'Technology',
          'https://app.kaleidotalent.com/dashboard',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: "You're in! Your Kaleido profile is approved ✨",
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            talentName: 'John Doe',
            topSkills: ['React', 'Node.js', 'TypeScript'],
          }),
        });
      });
    });

    describe('When a job seeker profile is rejected', () => {
      it('should send profile rejected email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendProfileRejected('<EMAIL>', 'John Doe', 'client-123');

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: 'Your Kaleido Profile Submission',
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            talentName: 'John Doe',
            emailType: 'profile_rejected',
          }),
        });
      });
    });

    describe('When a user joins the waitlist', () => {
      it('should send VIP welcome email for VIP users', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendVipWelcomeEmail(
          '<EMAIL>',
          'Jane VIP',
          true, // isVip
          'https://app.kaleidotalent.com',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: expect.stringContaining('Premium Early Access'),
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            fullName: 'Jane VIP',
            isVip: true,
          }),
        });
      });

      it('should send regular waitlist welcome email for non-VIP users', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendVipWelcomeEmail(
          '<EMAIL>',
          'John Regular',
          false, // isVip
          'https://app.kaleidotalent.com',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: expect.stringContaining('Welcome to Kaleido'),
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            fullName: 'John Regular',
            isVip: false,
          }),
        });
      });
    });
  });

  describe('Re-engagement Email Triggers', () => {
    describe('When a talent has been inactive', () => {
      it('should send talent re-engagement email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendReEngagement(
          '<EMAIL>',
          'John Doe',
          'https://app.kaleidotalent.com/profile',
          'https://app.kaleidotalent.com/dashboard',
          'https://app.kaleidotalent.com/jobs',
          'https://app.kaleidotalent.com/roadmap',
        );

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: 'Your next opportunity might be waiting… 👀',
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            talentName: 'John Doe',
            emailType: 're_engagement',
          }),
        });
      });
    });

    describe('When a company has unreviewed applications', () => {
      it('should send company re-engagement email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendCompanyReEngagementEmail(
          'Tech Corp',
          'Senior Developer',
          '<EMAIL>',
          'https://app.kaleidotalent.com',
          'https://app.kaleidotalent.com/profile',
          'https://app.kaleidotalent.com/dashboard',
        );

        expect(mockEmailHelpersService.validateEmailCompanyData).toHaveBeenCalledWith({
          companyName: 'Tech Corp',
        });

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: "Talent's Waiting — Don't Miss Out!",
          html: expect.any(String),
          emailType: 'general',
          metadata: expect.objectContaining({
            companyName: 'Tech Corp',
            jobTitle: 'Senior Developer',
            emailType: 'company_re_engagement',
          }),
        });
      });
    });
  });

  describe('Interview Email Triggers', () => {
    describe('When scheduling an interview', () => {
      it('should send interview invitation email', async () => {
        const sendEmailSpy = jest.spyOn(service as any, 'sendEmail');

        await service.sendInterviewInvitation(
          '<EMAIL>',
          'John Doe',
          'Senior Developer',
          'Tech Corp',
          'December 20, 2023',
          '2:00 PM - 3:00 PM PST',
          'Virtual',
          'https://meet.google.com/abc-defg-hij',
          '<EMAIL> | (555) 123-4567',
          'Jane Recruiter',
          'Talent Acquisition Manager',
          'https://techcorp.com',
          'https://app.kaleidotalent.com/companies/techcorp',
          'https://app.kaleidotalent.com/open-jobs/123',
        );

        expect(mockEmailHelpersService.validateEmailCompanyData).toHaveBeenCalledWith({
          companyName: 'Tech Corp',
        });

        expect(sendEmailSpy).toHaveBeenCalledWith({
          to: '<EMAIL>',
          subject: expect.any(String),
          html: expect.any(String),
          emailType: 'interview',
          metadata: expect.objectContaining({
            candidateName: 'John Doe',
            jobTitle: 'Senior Developer',
            companyName: 'Tech Corp',
            proposedDate: 'December 20, 2023',
          }),
        });
      });
    });
  });

  describe('Email Validation', () => {
    it('should prevent sending emails with placeholder company names', async () => {
      const invalidCompanyNames = ['Your Company', 'Our Company', 'Company Name', '[Company Name]'];

      for (const invalidName of invalidCompanyNames) {
        mockEmailHelpersService.validateEmailCompanyData.mockImplementationOnce(() => {
          throw new Error(`Invalid company name: ${invalidName}`);
        });

        await expect(
          service.sendJobApplicationSubmittedAutomated(
            '<EMAIL>',
            'John Doe',
            'Senior Developer',
            invalidName,
          ),
        ).rejects.toThrow(`Invalid company name: ${invalidName}`);
      }
    });

    it('should validate company name for all critical email methods', async () => {
      const emailMethods = [
        {
          method: 'sendHiredEmail',
          args: ['<EMAIL>', 'John Doe', 'Developer', 'Tech Corp'],
        },
        {
          method: 'sendOfferEmail',
          args: ['<EMAIL>', 'John Doe', 'Developer', 'Tech Corp'],
        },
        {
          method: 'sendInterviewInvitation',
          args: [
            '<EMAIL>',
            'John Doe',
            'Developer',
            'Tech Corp',
            'Dec 20',
            '2 PM',
            'Virtual',
            'link',
            'contact',
            'Jane',
            'Recruiter',
          ],
        },
        {
          method: 'sendApplicationRejection',
          args: ['<EMAIL>', 'John Doe', 'Developer', 'Tech Corp', 'Jane', 'Recruiter'],
        },
      ];

      for (const { method, args } of emailMethods) {
        await (service as any)[method](...args);
        expect(mockEmailHelpersService.validateEmailCompanyData).toHaveBeenCalledWith({
          companyName: 'Tech Corp',
        });
      }
    });
  });

  describe('Email History Logging', () => {
    it('should log email correspondence to both job seeker and candidate repositories', async () => {
      const jobSeekerSpy = jest.spyOn(jobSeekerRepository, 'findOne');
      const candidateSpy = jest.spyOn(candidateRepository, 'findOne');

      await service.sendJobApplicationSubmittedAutomated(
        '<EMAIL>',
        'John Doe',
        'Senior Developer',
        'Tech Corp',
      );

      // The private logEmailCorrespondence method should attempt to log to both repositories
      expect(jobSeekerSpy).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(candidateSpy).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
    });
  });
});
