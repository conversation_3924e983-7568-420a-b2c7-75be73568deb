import * as React from 'react';
import { render } from '@react-email/render';
import { getVideoCaptureUrl, getDashboardUrl } from './email.utils';

import VideoIntroShortlistedEmail from './templates/VideoIntroShortlistedEmail';
import { VideoIntroductionActive } from './templates/VideoIntroductionActive';
import { VideoIntroductionPassive } from './templates/VideoIntroductionPassive';
import { InterestEmail } from './templates/InterestEmail';
import { InterviewInviteEmail } from './templates/InterviewInviteEmail';
import { HiredEmail } from './templates/HiredEmail';
import { CulturalFitCompletedEmail } from './templates/CulturalFitCompletedEmail';
import { CulturalFitEmployerNotificationEmail } from './templates/CulturalFitEmployerNotificationEmail';
import { ApprovalRequestEmail } from './templates/ApprovalRequestEmail';
import { OfferEmail } from './templates/OfferEmail';
import StatusUpdateEmailTemplate from './templates/StatusUpdateEmailTemplate';
import { ContactFormEmail } from './templates/ContactFormEmail';
import { UserRegistrationNotificationEmail } from './templates/UserRegistrationNotificationEmail';

/**
 * Email template HTML generators
 */
export class EmailTemplates {
  static async getVideoIntroShortlistedEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId: string,
    candidateId: string,
  ): Promise<string> {
    const videoCaptureUrl = getVideoCaptureUrl(jobId, candidateId);

    return await render(
      React.createElement(VideoIntroShortlistedEmail, {
        candidateName,
        jobTitle,
        companyName,
        videoCaptureUrl,
      }),
    );
  }

  static async getVideoIntroActiveEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId: string,
    candidateId: string,
  ): Promise<string> {
    const recordingUrl = getVideoCaptureUrl(jobId, candidateId);
    const deadline = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const deadlineTime = '11:59 PM';

    return await render(
      React.createElement(VideoIntroductionActive, {
        candidateName,
        roleName: jobTitle,
        companyName,
        recordingUrl,
        deadline,
        deadlineTime,
      }),
    );
  }

  static async getVideoIntroPassiveEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId: string,
    candidateId: string,
  ): Promise<string> {
    const recordingUrl = getVideoCaptureUrl(jobId, candidateId);
    const deadline = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const deadlineTime = '11:59 PM';

    return await render(
      React.createElement(VideoIntroductionPassive, {
        candidateName,
        jobTitle,
        roleName: jobTitle,
        companyName,
        recordingUrl,
        deadline,
        deadlineTime,
        yourName: 'Hiring Team',
        yourTitle: `${companyName} Recruitment`,
      }),
    );
  }

  static async getInterestEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    jobId?: string,
    candidateId?: string,
  ): Promise<string> {
    // For interest emails, we don't include the video recording URL
    // Just express interest and ask for a response
    return await render(
      React.createElement(InterestEmail, {
        candidateName,
        jobTitle,
        roleName: jobTitle,
        companyName,
        yourName: 'Hiring Team',
        yourTitle: `${companyName} Talent Acquisition`,
      }),
    );
  }

  static async getInterviewInviteHtml(
    candidateName: string,
    jobTitle: string,
    interviewDetails: {
      date: string;
      type?: string;
      location?: string;
      meetingLink?: string;
    },
    companyName = 'Kaleido Talent',
  ): Promise<string> {
    return await render(
      React.createElement(InterviewInviteEmail, {
        candidateName,
        jobTitle,
        companyName,
        interviewDate: interviewDetails.date,
        interviewType: interviewDetails.type,
        interviewLocation: interviewDetails.location,
        meetingLink: interviewDetails.meetingLink,
      }),
    );
  }

  static async getHiredEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName: string,
    startDate?: string,
    onboardingLink?: string,
  ): Promise<string> {
    return await render(
      React.createElement(HiredEmail, {
        candidateName,
        jobTitle,
        companyName,
        startDate,
        onboardingLink,
      }),
    );
  }

  static async getCulturalFitCompletedHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    dashboardUrl?: string,
  ): Promise<string> {
    return await render(
      React.createElement(CulturalFitCompletedEmail, {
        candidateName,
        jobTitle,
        companyName,
        dashboardUrl: getDashboardUrl(dashboardUrl),
      }),
    );
  }

  static async getCulturalFitEmployerNotificationHtml(
    employerName: string,
    candidateName: string,
    jobTitle: string,
    companyName?: string,
    dashboardUrl?: string,
  ): Promise<string> {
    return await render(
      React.createElement(CulturalFitEmployerNotificationEmail, {
        employerName,
        candidateName,
        jobTitle,
        companyName,
        dashboardUrl,
      }),
    );
  }

  static async getApprovalRequestHtml(
    approverName: string,
    candidateName: string,
    jobTitle: string,
    approvalType: string,
    companyName = 'Kaleido Talent',
    approvalLink?: string,
    dueDate?: string,
  ): Promise<string> {
    return await render(
      React.createElement(ApprovalRequestEmail, {
        approverName,
        candidateName,
        jobTitle,
        companyName,
        approvalType,
        approvalLink,
        dueDate,
      }),
    );
  }

  static async getOfferEmailHtml(
    candidateName: string,
    jobTitle: string,
    companyName = 'Kaleido Talent',
    offerLetterUrl?: string,
    startDate?: string,
    salary?: {
      amount: number;
      currency: string;
      period: string;
    },
    offerExpirationDate?: string,
    responseLink?: string,
  ): Promise<string> {
    return await render(
      React.createElement(OfferEmail, {
        candidateName,
        jobTitle,
        companyName,
        offerLetterUrl,
        startDate,
        salary,
        offerExpirationDate,
        responseLink,
      }),
    );
  }

  static async getStatusUpdateEmailHtml(
    candidateName: string,
    jobTitle: string,
    statusType: 'interview' | 'hired' | 'offer' | 'offerAccepted' | 'status',
    companyName?: string,
    message?: string,
    additionalData?: any,
  ): Promise<string> {
    return await render(
      React.createElement(StatusUpdateEmailTemplate, {
        candidateName,
        jobTitle,
        companyName,
        message,
        statusType,
        ...additionalData,
      }),
    );
  }

  static async getContactFormHtml(
    name: string,
    email: string,
    phone?: string,
    message?: string,
  ): Promise<string> {
    return await render(
      React.createElement(ContactFormEmail, {
        name,
        email,
        phone,
        message: message || '',
      }),
    );
  }

  static async getUserRegistrationNotificationHtml(
    userType: 'Company' | 'Job Seeker' | 'Graduate',
    userName: string,
    userEmail: string,
    registrationDate: string,
    additionalInfo?: any,
  ): Promise<string> {
    return await render(
      React.createElement(UserRegistrationNotificationEmail, {
        userType,
        userName,
        userEmail,
        registrationDate,
        additionalInfo,
      }),
    );
  }
}
