import { GetUser } from '@/shared/decorators/get-user.decorator';
import { NotificationService } from '@modules/notification/notification.service';
import { CreditValidationEngine } from '@modules/subscription/credit-validation.engine';
import { CreditService } from '@modules/subscription/credit.service';
import { QueueService } from '@/shared/services/queue.service';
import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  forwardRef,
  Get,
  HttpException,
  Inject,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
// import { FileInterceptor } from '@nestjs/platform-express';
import { FastifyFileInterceptor } from '@/shared/interceptors/fastify-file.interceptor';
import { ApiBearerAuth, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { User } from '@sentry/node';
import { NotificationType } from '@shared/types';
import { VideoJDType, VirtualBackgroundType } from '@shared/types/video.types';

import { Auth0Guard } from '../../auth/auth.guard';
import { multerConfig } from '../../config/multer.config';
import { AppError } from '../../shared/types/error.types';
import { JobsResponse } from '../job/jobs.types';
import { CreateVideoJDDto } from './dto/create-video-jd.dto';
import { CreateVideoRecordingDto } from './dto/create-video-recording.dto';
import { DownloadVideoDto } from './dto/download-video.dto';
import { UploadVideoRecordingDto } from './dto/upload-video-recording.dto';
import { VideoJD } from './entities/video-jd.entity';
import { SynthesiaCreatePayload } from './interfaces/synthesia.interface';
import { VideoJDScriptSettings, VideoJDService } from './video-jd.service';

@ApiTags('video-jd')
@ApiBearerAuth()
@UseGuards(Auth0Guard)
@Controller('video-jd')
export class VideoJDController {
  private readonly logger = new Logger(VideoJDController.name);

  constructor(
    private readonly videoJDService: VideoJDService,
    private readonly notificationService: NotificationService,
    @Inject(forwardRef(() => CreditService))
    private readonly creditService: CreditService,
    @Inject(forwardRef(() => CreditValidationEngine))
    private readonly creditValidationEngine: CreditValidationEngine,
    private readonly queueService: QueueService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new video JD' })
  async create(@Body() createVideoJDDto: CreateVideoJDDto): Promise<VideoJD> {
    try {
      return await this.videoJDService.create(createVideoJDDto);
    } catch (error) {
      if (error instanceof AppError) {
        throw new HttpException(
          {
            type: error.type,
            message: error.message,
          },
          error.statusCode,
        );
      }
      throw error;
    }
  }

  @Post('/generate-script')
  @ApiOperation({ summary: 'Generate video script text for a video JD' })
  async generateScript(
    @Body() body: VideoJDScriptSettings,
    @GetUser() user: User,
  ): Promise<VideoJD> {
    try {
      // Script generation is FREE - no credit validation needed

      // Generate the script
      const result = await this.videoJDService.generateScript(body);

      // Script generation is FREE - no credits consumed
      this.logger.log(
        `Generated free video script for user ${user?.userId}, jobId: ${result.jobId}`,
      );

      return result;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      if (error instanceof AppError) {
        throw new HttpException(
          {
            type: error.type,
            message: error.message,
          },
          error.statusCode,
        );
      }
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a video JD by ID' })
  async findOne(@Param('id') id: string): Promise<VideoJD | null> {
    try {
      return await this.videoJDService.findOne(id);
    } catch (error) {
      if (error instanceof AppError) {
        throw new HttpException(
          {
            type: error.type,
            message: error.message,
          },
          error.statusCode,
        );
      }
      throw error;
    }
  }

  @Get('/status/:id')
  @ApiOperation({ summary: 'Get a video status' })
  async getStatus(@Param('id') id: string): Promise<VideoJD | null> {
    const videoJD = await this.videoJDService.getStatus(id);

    // If video is completed, create notification
    if (videoJD && videoJD.status.toLowerCase() === 'completed') {
      await this.notificationService.createNotification({
        type: NotificationType.VIDEO_JD_READY,
        title: 'Video JD Ready',
        message: 'Your video job description is now ready to view!',
        clientId: videoJD.clientId,
        jobId: videoJD.jobId,
        completed: false,
        read: false,
      });
    }

    return videoJD;
  }

  @Get('/client/:clientId')
  @ApiOperation({ summary: 'Get all video JDs by client ID including job resolution' })
  async findAllByClientId(@Param('clientId') clientId: string): Promise<VideoJD[]> {
    return await this.videoJDService.findAllByClientId(clientId);
  }

  @Get('/job/:jobId')
  @ApiOperation({ summary: 'Get all video JDs for a specific job' })
  async findByJobId(@Param('jobId') jobId: string): Promise<VideoJD[]> {
    return await this.videoJDService.findAllByJobId(jobId);
  }

  @Post(':id/generate')
  @ApiOperation({ summary: 'Generate video for a video JD' })
  async generateVideo(
    @Param('id') id: string,
    @Query('clientId') clientId: string,
    @Body() synthesiaPayload: SynthesiaCreatePayload,
    @GetUser() user: User,
  ): Promise<VideoJD> {
    try {
      // Note: We don't need to validate subscription limits here since we already
      // validated and updated them when generating the script. This is just the
      // second step of the same operation.

      const result = await this.videoJDService.generateVideo(id, clientId, synthesiaPayload);

      this.logger.log(
        `Video generation started for video JD ${id} by user ${user?.userId || clientId}`,
      );

      return result;
    } catch (error: any) {
      this.logger.error(`Error generating video for JD ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a video JD' })
  async delete(@Param('id') id: string): Promise<void> {
    await this.videoJDService.delete(id);
  }

  @Get('/jobs-with-videos/:clientId')
  @ApiOperation({ summary: 'Get all jobs with their associated video JDs for a client' })
  async getJobsWithVideo(@Param('clientId') clientId: string): Promise<JobsResponse> {
    const result = await this.videoJDService.getJobsWithVideos(clientId);
    return {
      ...result,
      pagination: {
        totalItems: Array.isArray((result as any).jobs) ? (result as any).jobs.length : 0,
        currentPage: 1,
        itemsPerPage: Array.isArray((result as any).jobs) ? (result as any).jobs.length : 0,
        totalPages: 1,
      },
    };
  }

  @Patch(':id/patch')
  @ApiOperation({ summary: 'Patch specific video JD properties' })
  async patchVideoJD(
    @Param('id') id: string,
    @Body() patchData: Partial<VideoJD>,
  ): Promise<VideoJD> {
    return await this.videoJDService.patch(id, patchData);
  }

  @Post(':id/download')
  @ApiOperation({ summary: 'Get download URL for video in specific aspect ratio' })
  async getDownloadUrl(
    @Param('id') id: string,
    @Body() downloadRequest: { aspectRatio: string; format: string },
  ): Promise<{ downloadUrl: string }> {
    return await this.videoJDService.getDownloadUrl(id, downloadRequest);
  }

  @Post(':id/download-platform')
  @ApiOperation({ summary: 'Download video optimized for specific social media platform' })
  async downloadForPlatform(
    @Param('id') id: string,
    @Body() downloadDto: DownloadVideoDto,
    @GetUser() user: User,
  ): Promise<{
    downloadUrl?: string;
    filename: string;
    platformSpecs: any;
    jobId?: string;
    processing?: boolean;
  }> {
    return await this.videoJDService.downloadForPlatform(id, downloadDto, user.userId);
  }

  @Get('conversion-status/:jobId')
  @ApiOperation({ summary: 'Check video conversion job status' })
  async getConversionStatus(@Param('jobId') jobId: string): Promise<{
    status: string;
    progress: number;
    result?: any;
    error?: string;
  }> {
    return await this.videoJDService.getVideoConversionStatus(jobId);
  }

  @Post(':id/process-video')
  @ApiOperation({ summary: 'Process video directly and update VideoJD record' })
  async processVideoDirectly(
    @Param('id') id: string,
    @Body() processRequest: { platform: string; quality: string; filename?: string },
    @GetUser() user: User,
  ): Promise<{ downloadUrl: string; filename: string }> {
    try {
      // Get platform specs (you'll need to import or define these)
      const platformSpecs = this.getPlatformSpecs(processRequest.platform);

      const options = {
        platform: processRequest.platform,
        quality: processRequest.quality,
        filename: processRequest.filename || `video-${processRequest.platform}-${Date.now()}.mp4`,
      };

      // You'll need to inject the upload service (Digital Ocean Spaces)
      // For now, this is a placeholder - you'll need to add the actual service
      const uploadService = null; // TODO: Inject DigitalOceanSpacesService

      return await this.videoJDService.processVideoAndUpdateJD(
        id,
        platformSpecs,
        options,
        uploadService,
      );
    } catch (error) {
      this.logger.error(`Error processing video for VideoJD ${id}:`, error);
      throw error;
    }
  }

  private getPlatformSpecs(platform: string) {
    // Define platform specifications
    const specs: Record<string, any> = {
      instagram: {
        aspectRatio: '1:1',
        maxDuration: 60,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        recommendedResolution: '1080x1080',
        format: 'mp4',
        description: 'Instagram Square Video',
      },
      youtube: {
        aspectRatio: '16:9',
        maxDuration: 3600,
        maxFileSize: 2 * 1024 * 1024 * 1024, // 2GB
        recommendedResolution: '1920x1080',
        format: 'mp4',
        description: 'YouTube Video',
      },
      linkedin: {
        aspectRatio: '16:9',
        maxDuration: 600,
        maxFileSize: 200 * 1024 * 1024, // 200MB
        recommendedResolution: '1920x1080',
        format: 'mp4',
        description: 'LinkedIn Video',
      },
      tiktok: {
        aspectRatio: '9:16',
        maxDuration: 180,
        maxFileSize: 500 * 1024 * 1024, // 500MB
        recommendedResolution: '1080x1920',
        format: 'mp4',
        description: 'TikTok Video',
      },
    };

    return specs[platform.toLowerCase()] || specs.youtube; // Default to YouTube specs
  }

  // New endpoints for video recording functionality
  @Post('recording')
  @ApiOperation({ summary: 'Create a new video recording setup' })
  async createVideoRecording(
    @Body() createVideoRecordingDto: CreateVideoRecordingDto,
    @GetUser() user: User,
  ): Promise<VideoJD> {
    try {
      // Video recording is FREE - no credit validation needed
      // Following the pattern from script generation which is also free
      this.logger.log(
        `Creating free video recording for user ${user?.userId}, jobId: ${createVideoRecordingDto.jobId}`,
      );

      return await this.videoJDService.createVideoRecording(
        createVideoRecordingDto,
        user.userId,
        user.sub, // Pass Auth0 sub as avatarId
      );
    } catch (error) {
      this.logger.error('Error creating video recording:', error);
      throw error;
    }
  }

  @Post(':id/start-recording')
  @ApiOperation({ summary: 'Start recording for a video JD' })
  async startRecording(@Param('id') id: string): Promise<VideoJD> {
    return await this.videoJDService.startRecording(id);
  }

  @Post('upload-recording')
  @ApiOperation({ summary: 'Upload recorded video file' })
  async uploadRecordedVideo(
    @Body() uploadDto: UploadVideoRecordingDto,
    @GetUser() user: User,
  ): Promise<VideoJD> {
    try {
      const result = await this.videoJDService.uploadRecordedVideo(uploadDto);

      return result;
    } catch (error) {
      this.logger.error('Error uploading recorded video:', error);
      throw error;
    }
  }

  @Post('complete-recording')
  @UseInterceptors(new FastifyFileInterceptor('video'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Complete video recording - upload file and finalize in one call' })
  async completeRecording(
    @UploadedFile() file: Express.Multer.File,
    @GetUser() user: User,
    @Body('jobId') jobId: string,
    @Body('generatedScript') generatedScript?: string,
    @Body('virtualBackgroundType') virtualBackgroundType?: string,
    @Body('virtualBackgroundImageUrl') virtualBackgroundImageUrl?: string,
    @Body('recordingSettings') recordingSettings?: string,
    @Body('actualDuration') actualDuration?: string,
    @Body('fileSize') fileSize?: string,
    @Body('format') format?: string,
  ): Promise<VideoJD> {
    try {
      // Validate that jobId is provided
      if (!jobId) {
        throw new BadRequestException('Job ID is required for video recording');
      }

      // Validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(jobId)) {
        throw new BadRequestException(`Invalid job ID format: ${jobId}. Must be a valid UUID`);
      }

      this.logger.log(
        `[COMPLETE_RECORDING] Starting complete recording process for jobId: ${jobId}`,
      );

      // Create the video recording record first
      const createVideoRecordingDto: CreateVideoRecordingDto = {
        jobId,
        type: VideoJDType.LIVE_RECORDING,
        virtualBackgroundType:
          (virtualBackgroundType as VirtualBackgroundType) || VirtualBackgroundType.NONE,
        virtualBackgroundImageUrl,
        recordingSettings: recordingSettings ? JSON.parse(recordingSettings) : undefined,
        generatedScript,
      };

      const videoJD = await this.videoJDService.createVideoRecording(
        createVideoRecordingDto,
        user.userId,
        user.sub,
      );

      this.logger.log(`[COMPLETE_RECORDING] VideoJD created with ID: ${videoJD.id}`);

      // Upload the file and complete the recording
      const result = await this.videoJDService.completeRecordingWithUpload(
        videoJD.id,
        file,
        {
          actualDuration: actualDuration ? Number(actualDuration) : undefined,
          fileSize: fileSize ? Number(fileSize) : file.size,
          format: format || file.mimetype,
        },
        user,
      );

      this.logger.log(
        `[COMPLETE_RECORDING] Recording completed successfully for VideoJD: ${result.id}`,
      );

      return result;
    } catch (error) {
      this.logger.error('Error completing recording:', error);
      throw error;
    }
  }

  // ==============================
  // Queue-related endpoints
  // ==============================

  @Get('queue/status/:jobId')
  @ApiOperation({ summary: 'Get Video JD queue job status' })
  async getQueueStatus(@Param('jobId') jobId: string) {
    try {
      const status = await this.queueService.getVideoJDStatus(jobId);
      return status;
    } catch (error) {
      this.logger.error(`Error getting Video JD queue status for job ${jobId}:`, error);
      throw new HttpException('Failed to get queue status', 500);
    }
  }

  @Post('queue/cancel/:jobId')
  @ApiOperation({ summary: 'Cancel Video JD queue job' })
  async cancelQueueJob(@Param('jobId') jobId: string) {
    try {
      const result = await this.queueService.cancelVideoJDJob(jobId);
      return result;
    } catch (error) {
      this.logger.error(`Error canceling Video JD queue job ${jobId}:`, error);
      throw new HttpException('Failed to cancel queue job', 500);
    }
  }
}
