import { Test, TestingModule } from '@nestjs/testing';

import { Candidate } from '../../candidate/entities/candidate.entity';
import { Company } from '../../company/entities/company.entity';
import { DashboardService } from '../dashboard.service';
import { DashboardStatsDto } from '../dto/dashboard-stats.dto';
import { Graduate } from '../../graduate/entities/graduate.entity';
import { Job } from '../../job/entities/job.entity';
import { JobApplication } from '../../job-seeker/entities/job-application.entity';
import { JobSeeker } from '../../job-seeker/entities/job-seeker.entity';
import { NotFoundException } from '@nestjs/common';
import { Notification } from '../../notification/entities/notification.entity';
import { SubscriptionService } from '../../subscription/subscription.service';
import { UserRole } from '../../../common/enums/role.enum';
import { UserRoleEntity } from '../../roles/entities/user-role.entity';
import { VideoJD } from '../../video-jd/entities/video-jd.entity';
import { VideoResponse } from '../../video-response/entities/video-response.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('DashboardService', () => {
  let service: DashboardService;

  const createMockDashboardStats = (
    userRole: UserRole,
    extraProps: any = {},
  ): Partial<DashboardStatsDto> => ({
    userRole,
    notifications: { total: 0, unread: 0, today: 0, recent: [] },
    ...extraProps,
  });

  const mockRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockCompanyRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockSubscriptionService = {
    getSubscriptionStats: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DashboardService,
        {
          provide: getRepositoryToken(Job),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(VideoJD),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Notification),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(UserRoleEntity),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(VideoResponse),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(JobApplication),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(JobSeeker),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Company),
          useValue: mockCompanyRepository,
        },
        {
          provide: getRepositoryToken(Graduate),
          useValue: mockRepository,
        },
        {
          provide: SubscriptionService,
          useValue: mockSubscriptionService,
        },
      ],
    }).compile();

    service = module.get<DashboardService>(DashboardService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDashboardStats', () => {
    it('should throw NotFoundException when user role is not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.getDashboardStats('test-client-id')).rejects.toThrow(NotFoundException);
    });

    it('should return employer stats for employer role', async () => {
      const mockUserRole = { role: UserRole.EMPLOYER };
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      // Mock all the required methods for employer stats
      jest.spyOn(service as any, 'getEmployerStats').mockResolvedValue(
        createMockDashboardStats(UserRole.EMPLOYER, {
          jobs: { total: 5 },
          candidates: { total: 10 },
        }) as DashboardStatsDto,
      );

      const result = await service.getDashboardStats('test-client-id');

      expect(result.userRole).toBe(UserRole.EMPLOYER);
      expect(result.jobs).toEqual({ total: 5 });
    });

    it('should return admin stats with admin role preserved', async () => {
      const mockUserRole = { role: UserRole.ADMIN };
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      jest.spyOn(service as any, 'getEmployerStats').mockResolvedValue(
        createMockDashboardStats(UserRole.ADMIN, {
          jobs: { total: 5 },
          candidates: { total: 10 },
        }) as DashboardStatsDto,
      );

      const result = await service.getDashboardStats('test-client-id');

      expect(result.userRole).toBe(UserRole.ADMIN);
    });

    it('should return job seeker stats for job seeker role', async () => {
      const mockUserRole = { role: UserRole.JOB_SEEKER };
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      jest.spyOn(service as any, 'getJobSeekerStats').mockResolvedValue(
        createMockDashboardStats(UserRole.JOB_SEEKER, {
          applications: { total: 3 },
          matchedJobs: { total: 7 },
        }) as DashboardStatsDto,
      );

      const result = await service.getDashboardStats('test-client-id');

      expect(result.userRole).toBe(UserRole.JOB_SEEKER);
      expect(result.applications).toEqual({ total: 3 });
    });
  });

  describe('getEnhancedDashboardStats', () => {
    it('should throw NotFoundException when user role is not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.getEnhancedDashboardStats('test-client-id')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should include company data for employer role', async () => {
      const mockUserRole = { role: UserRole.EMPLOYER };
      const mockDashboardStats = createMockDashboardStats(UserRole.EMPLOYER, {
        jobs: { total: 5 },
      }) as DashboardStatsDto;
      const mockCompany = {
        id: 'company-1',
        companyName: 'Test Company',
        clientId: 'test-client-id',
      };

      // Mock user role repository calls
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      // Mock company repository query builder for getCompanyData
      const mockCompanyQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockCompany),
      };
      mockCompanyRepository.createQueryBuilder.mockReturnValue(mockCompanyQueryBuilder as any);

      jest.spyOn(service, 'getDashboardStats').mockResolvedValue(mockDashboardStats);

      const result = await service.getEnhancedDashboardStats('test-client-id');

      expect(result.userRole).toBe(UserRole.EMPLOYER);
      expect(result.company).toEqual(mockCompany);
    });

    it('should include company data for admin role', async () => {
      const mockUserRole = { role: UserRole.ADMIN };
      const mockDashboardStats = createMockDashboardStats(UserRole.ADMIN, {
        jobs: { total: 5 },
      }) as DashboardStatsDto;
      const mockCompany = {
        id: 'company-1',
        companyName: 'Test Company',
        clientId: 'test-client-id',
      };

      // Mock user role repository calls
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      // Mock company repository query builder for getCompanyData
      const mockCompanyQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockCompany),
      };
      mockCompanyRepository.createQueryBuilder.mockReturnValue(mockCompanyQueryBuilder as any);

      jest.spyOn(service, 'getDashboardStats').mockResolvedValue(mockDashboardStats);

      const result = await service.getEnhancedDashboardStats('test-client-id');

      expect(result.userRole).toBe(UserRole.ADMIN);
      expect(result.company).toEqual(mockCompany);
    });

    it('should not include company data for job seeker role', async () => {
      const mockUserRole = { role: UserRole.JOB_SEEKER };
      const mockDashboardStats = createMockDashboardStats(UserRole.JOB_SEEKER, {
        applications: { total: 3 },
      }) as DashboardStatsDto;

      // Mock user role repository calls
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      jest.spyOn(service, 'getDashboardStats').mockResolvedValue(mockDashboardStats);

      const result = await service.getEnhancedDashboardStats('test-client-id');

      expect(result.userRole).toBe(UserRole.JOB_SEEKER);
      expect(result.company).toBe(null);
    });

    it('should handle company data fetch failure gracefully', async () => {
      const mockUserRole = { role: UserRole.EMPLOYER };
      const mockDashboardStats = createMockDashboardStats(UserRole.EMPLOYER, {
        jobs: { total: 5 },
      }) as DashboardStatsDto;

      // Mock user role repository calls
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      // Mock company repository query builder to throw error
      const mockCompanyQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockRejectedValue(new Error('Company fetch failed')),
      };
      mockCompanyRepository.createQueryBuilder.mockReturnValue(mockCompanyQueryBuilder as any);

      jest.spyOn(service, 'getDashboardStats').mockResolvedValue(mockDashboardStats);

      const result = await service.getEnhancedDashboardStats('test-client-id');

      expect(result.userRole).toBe(UserRole.EMPLOYER);
      expect(result.company).toBe(null);
    });

    it('should create basic company when none exists', async () => {
      const mockUserRole = { role: UserRole.EMPLOYER };
      const mockDashboardStats = createMockDashboardStats(UserRole.EMPLOYER, {
        jobs: { total: 5 },
      }) as DashboardStatsDto;
      const mockCreatedCompany = {
        id: 'new-company-id',
        clientId: 'test-client-id',
        companyName: '',
      };

      // Mock user role repository calls
      mockRepository.findOne.mockResolvedValue(mockUserRole);

      // Mock company repository query builder for getCompanyData - return null to trigger creation
      const mockCompanyQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      mockCompanyRepository.createQueryBuilder.mockReturnValue(mockCompanyQueryBuilder as any);
      
      // Mock company creation
      mockCompanyRepository.create.mockReturnValue(mockCreatedCompany);
      mockCompanyRepository.save.mockResolvedValue(mockCreatedCompany);

      jest.spyOn(service, 'getDashboardStats').mockResolvedValue(mockDashboardStats);

      const result = await service.getEnhancedDashboardStats('test-client-id');

      expect(result.company).toEqual(mockCreatedCompany);
      expect(mockCompanyRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          clientId: 'test-client-id',
          companyName: '',
          isDemoMode: true,
        }),
      );
    });
  });
});
