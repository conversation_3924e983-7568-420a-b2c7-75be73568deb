import { Repository } from 'typeorm';

import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CandidateStatus, UserRole } from '@shared/types';

import { Candidate } from '../candidate/entities/candidate.entity';
import { Company } from '../company/entities/company.entity';
import { Graduate } from '../graduate/entities/graduate.entity';
import { JobApplication } from '../job-seeker/entities/job-application.entity';
import { JobSeeker } from '../job-seeker/entities/job-seeker.entity';
import { Job } from '../job/entities/job.entity';
import { Notification } from '../notification/entities/notification.entity';
import { UserRoleEntity } from '../roles/entities/user-role.entity';
import { SubscriptionService } from '../subscription/subscription.service';
import { VideoJD } from '../video-jd/entities/video-jd.entity';
import { VideoResponse } from '../video-response/entities/video-response.entity';
import { DashboardService } from './dashboard.service';

describe('DashboardService', () => {
  let service: DashboardService;
  let jobRepository: jest.Mocked<Repository<Job>>;
  let candidateRepository: jest.Mocked<Repository<Candidate>>;
  let videoJDRepository: jest.Mocked<Repository<VideoJD>>;
  let notificationRepository: jest.Mocked<Repository<Notification>>;
  let userRoleRepository: jest.Mocked<Repository<UserRoleEntity>>;
  let videoResponseRepository: jest.Mocked<Repository<VideoResponse>>;
  let jobApplicationRepository: jest.Mocked<Repository<JobApplication>>;
  let jobSeekerRepository: jest.Mocked<Repository<JobSeeker>>;
  let companyRepository: jest.Mocked<Repository<Company>>;
  let graduateRepository: jest.Mocked<Repository<Graduate>>;
  let subscriptionService: jest.Mocked<SubscriptionService>;

  const mockUserRole = {
    id: 'role-1',
    clientId: 'auth0-user-123',
    role: UserRole.EMPLOYER,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockJob = {
    id: 'job-1',
    clientId: 'auth0-user-123',
    title: 'Software Engineer',
    status: 'ACTIVE',
    department: 'Engineering',
    jobType: 'Full-time',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCandidate = {
    id: 'candidate-1',
    jobId: 'job-1',
    clientId: 'auth0-user-123',
    fullName: 'John Doe',
    email: '<EMAIL>',
    status: CandidateStatus.MATCHED,
    source: 'JOB_SEEKER',
    hasCompletedVideoInterview: true,
    evaluation: { matchScore: 0.85 },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockVideoJD = {
    id: 'video-1',
    clientId: 'auth0-user-123',
    jobId: 'job-1',
    status: 'COMPLETED',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockNotification = {
    id: 'notification-1',
    clientId: 'auth0-user-123',
    type: 'INFO',
    message: 'Test notification',
    isRead: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCompany = {
    id: 'company-1',
    clientId: 'auth0-user-123',
    companyName: 'Test Company',
    industry: 'Technology',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockJobSeeker = {
    id: 'jobseeker-1',
    clientId: 'auth0-user-123',
    fullName: 'Jane Smith',
    email: '<EMAIL>',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockSubscriptionStats = {
    plan: 'PROFESSIONAL',
    status: 'ACTIVE',
    creditsRemaining: 150,
    creditsUsed: 100,
    totalCredits: 250,
    renewalDate: new Date('2024-01-01'),
    usageThisMonth: 50,
  };

  beforeEach(async () => {
    const mockRepository = () => ({
      find: jest.fn(),
      findOne: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn(() => ({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
        getOne: jest.fn().mockResolvedValue(null),
        getCount: jest.fn().mockResolvedValue(0),
        select: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([]),
      })),
    });

    const mockSubscriptionService = {
      getSubscriptionStats: jest.fn(),
      // Add other methods that might be needed
      createSubscription: jest.fn(),
      updateSubscription: jest.fn(),
      cancelSubscription: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DashboardService,
        {
          provide: getRepositoryToken(Job),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(VideoJD),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(Notification),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(UserRoleEntity),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(VideoResponse),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(JobApplication),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(JobSeeker),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(Company),
          useValue: mockRepository(),
        },
        {
          provide: getRepositoryToken(Graduate),
          useValue: mockRepository(),
        },
        {
          provide: SubscriptionService,
          useValue: mockSubscriptionService,
        },
      ],
    }).compile();

    service = module.get<DashboardService>(DashboardService);
    jobRepository = module.get(getRepositoryToken(Job));
    candidateRepository = module.get(getRepositoryToken(Candidate));
    videoJDRepository = module.get(getRepositoryToken(VideoJD));
    notificationRepository = module.get(getRepositoryToken(Notification));
    userRoleRepository = module.get(getRepositoryToken(UserRoleEntity));
    videoResponseRepository = module.get(getRepositoryToken(VideoResponse));
    jobApplicationRepository = module.get(getRepositoryToken(JobApplication));
    jobSeekerRepository = module.get(getRepositoryToken(JobSeeker));
    companyRepository = module.get(getRepositoryToken(Company));
    graduateRepository = module.get(getRepositoryToken(Graduate));
    subscriptionService = module.get(SubscriptionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDashboardStats', () => {
    it('should get employer dashboard stats successfully', async () => {
      userRoleRepository.findOne.mockResolvedValue(mockUserRole as any);

      // Mock query builder for jobs
      const mockJobQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockJob]),
      };
      jobRepository.createQueryBuilder.mockReturnValue(mockJobQueryBuilder as any);

      // Mock query builder for candidates
      const mockCandidateQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockCandidate]),
      };
      candidateRepository.createQueryBuilder.mockReturnValue(mockCandidateQueryBuilder as any);

      // Mock query builder for videoJDs
      const mockVideoJDQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockVideoJD]),
      };
      videoJDRepository.createQueryBuilder.mockReturnValue(mockVideoJDQueryBuilder as any);

      // Mock query builder for notifications
      const mockNotificationQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockNotification]),
      };
      notificationRepository.createQueryBuilder.mockReturnValue(
        mockNotificationQueryBuilder as any,
      );

      // Mock company repository for subscription stats
      companyRepository.findOne.mockResolvedValue(mockCompany as any);

      // Mock job seeker repository for employer metrics
      jobSeekerRepository.find.mockResolvedValue([mockJobSeeker] as any);

      const result = await service.getDashboardStats('auth0-user-123');

      expect(result).toBeDefined();
      expect(result.userRole).toBe(UserRole.EMPLOYER);
      expect(userRoleRepository.findOne).toHaveBeenCalledWith({
        where: { clientId: 'auth0-user-123' },
      });
    });

    it('should get job seeker dashboard stats successfully', async () => {
      const jobSeekerRole = { ...mockUserRole, role: UserRole.JOB_SEEKER };
      userRoleRepository.findOne.mockResolvedValue(jobSeekerRole as any);

      // Mock query builder for job applications
      const mockJobApplicationQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };
      jobApplicationRepository.createQueryBuilder.mockReturnValue(
        mockJobApplicationQueryBuilder as any,
      );

      // Mock query builder for candidates (matched jobs)
      const mockCandidateQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockCandidate]),
      };
      candidateRepository.createQueryBuilder.mockReturnValue(mockCandidateQueryBuilder as any);

      // Mock query builder for jobs
      const mockJobQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockJob]),
      };
      jobRepository.createQueryBuilder.mockReturnValue(mockJobQueryBuilder as any);

      const result = await service.getDashboardStats('auth0-user-123');

      expect(result).toBeDefined();
      expect(result.userRole).toBe(UserRole.JOB_SEEKER);
    });

    it('should get graduate dashboard stats successfully', async () => {
      const graduateRole = { ...mockUserRole, role: UserRole.GRADUATE };
      userRoleRepository.findOne.mockResolvedValue(graduateRole as any);

      // Mock repository calls for graduate stats
      graduateRepository.findOne.mockResolvedValue({} as any);

      // Mock query builder for candidates (skill stats)
      const mockCandidateQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      candidateRepository.createQueryBuilder.mockReturnValue(mockCandidateQueryBuilder as any);

      const result = await service.getDashboardStats('auth0-user-123');

      expect(result).toBeDefined();
      expect(result.userRole).toBe(UserRole.GRADUATE);
    });

    it('should throw NotFoundException when user role not found', async () => {
      userRoleRepository.findOne.mockResolvedValue(null);

      await expect(service.getDashboardStats('invalid-user-id')).rejects.toThrow(NotFoundException);
    });

    it('should default to employer stats for unknown role', async () => {
      const unknownRole = { ...mockUserRole, role: 'UNKNOWN' as UserRole };
      userRoleRepository.findOne.mockResolvedValue(unknownRole as any);

      // Mock query builder for jobs
      const mockJobQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockJob]),
      };
      jobRepository.createQueryBuilder.mockReturnValue(mockJobQueryBuilder as any);

      // Mock query builder for candidates
      const mockCandidateQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockCandidate]),
      };
      candidateRepository.createQueryBuilder.mockReturnValue(mockCandidateQueryBuilder as any);

      // Mock query builder for videoJDs
      const mockVideoJDQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockVideoJD]),
      };
      videoJDRepository.createQueryBuilder.mockReturnValue(mockVideoJDQueryBuilder as any);

      // Mock query builder for notifications
      const mockNotificationQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockNotification]),
      };
      notificationRepository.createQueryBuilder.mockReturnValue(
        mockNotificationQueryBuilder as any,
      );

      // Mock company repository for subscription stats
      companyRepository.findOne.mockResolvedValue(mockCompany as any);

      // Mock job seeker repository for employer metrics
      jobSeekerRepository.find.mockResolvedValue([mockJobSeeker] as any);

      const result = await service.getDashboardStats('auth0-user-123');

      expect(result).toBeDefined();
      expect(result.userRole).toBe('UNKNOWN');
    });
  });

  describe('getRegistrationStats', () => {
    it('should get registration stats successfully', async () => {
      // Mock query builder for companies
      const mockCompanyQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockCompany]),
      };
      companyRepository.createQueryBuilder.mockReturnValue(mockCompanyQueryBuilder as any);
      
      jobSeekerRepository.find.mockResolvedValue([mockJobSeeker] as any);
      graduateRepository.find.mockResolvedValue([]);

      const result = await service.getRegistrationStats();

      expect(result).toBeDefined();
      expect(result.employers).toBeDefined();
      expect(result.jobSeekers).toBeDefined();
      expect(result.graduates).toBeDefined();
      expect(companyRepository.createQueryBuilder).toHaveBeenCalled();
      expect(jobSeekerRepository.find).toHaveBeenCalled();
      expect(graduateRepository.find).toHaveBeenCalled();
    });

    it('should handle empty registration data', async () => {
      // Mock query builder for companies
      const mockCompanyQueryBuilder = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };
      companyRepository.createQueryBuilder.mockReturnValue(mockCompanyQueryBuilder as any);
      
      jobSeekerRepository.find.mockResolvedValue([]);
      graduateRepository.find.mockResolvedValue([]);

      const result = await service.getRegistrationStats();

      expect(result).toBeDefined();
      expect(result.employers.total).toBe(0);
      expect(result.jobSeekers.total).toBe(0);
      expect(result.graduates.total).toBe(0);
    });
  });

  describe('getUsageStats', () => {
    it('should get usage stats successfully', async () => {
      // Mock query builder for company count
      const mockCompanyCountQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(1),
      };
      
      // Mock query builder for active companies count
      const mockActiveCompaniesQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(1),
      };
      
      // Mock query builder for industry distribution
      const mockIndustryQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { industry: 'Technology', count: '1' }
        ]),
      };
      
      // Setup createQueryBuilder to return different builders based on usage
      let queryCount = 0;
      companyRepository.createQueryBuilder.mockImplementation(() => {
        queryCount++;
        if (queryCount === 1) return mockCompanyCountQueryBuilder as any;
        if (queryCount === 2) return mockActiveCompaniesQueryBuilder as any;
        if (queryCount === 3) return mockIndustryQueryBuilder as any;
        return mockCompanyCountQueryBuilder as any;
      });
      
      // Mock job count
      jobRepository.count.mockResolvedValue(1);
      
      // Mock videoJD count
      videoJDRepository.count.mockResolvedValue(1);
      
      // Mock candidate count
      candidateRepository.count.mockResolvedValue(1);
      
      // Mock platform activity queries
      const mockPlatformActivityQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(0),
      };
      
      jobRepository.createQueryBuilder.mockReturnValue(mockPlatformActivityQueryBuilder as any);
      videoJDRepository.createQueryBuilder.mockReturnValue(mockPlatformActivityQueryBuilder as any);
      candidateRepository.createQueryBuilder.mockReturnValue(mockPlatformActivityQueryBuilder as any);
      
      // Mock company usage data method
      jest.spyOn(service as any, 'getCompanyUsageDataPaginated').mockResolvedValue({
        data: [],
        meta: {
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 0,
        },
      });

      const result = await service.getUsageStats();

      expect(result).toBeDefined();
      expect(result.totalCompanies).toBeDefined();
      expect(result.totalJobs).toBeDefined();
      expect(result.totalVideoJDs).toBeDefined();
      expect(result.totalCandidates).toBeDefined();
      expect(result.activeCompanies).toBeDefined();
      expect(result.industryDistribution).toBeDefined();
    });

    it('should calculate industry distribution correctly', async () => {
      // Mock query builder for company count
      const mockCompanyCountQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(3),
      };
      
      // Mock query builder for active companies count
      const mockActiveCompaniesQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(3),
      };
      
      // Mock query builder for industry distribution
      const mockIndustryQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getRawMany: jest.fn().mockResolvedValue([
          { industry: 'Technology', count: '2' },
          { industry: 'Finance', count: '1' }
        ]),
      };
      
      // Setup createQueryBuilder to return different builders based on usage
      let queryCount = 0;
      companyRepository.createQueryBuilder.mockImplementation(() => {
        queryCount++;
        if (queryCount === 1) return mockCompanyCountQueryBuilder as any;
        if (queryCount === 2) return mockActiveCompaniesQueryBuilder as any;
        if (queryCount === 3) return mockIndustryQueryBuilder as any;
        return mockCompanyCountQueryBuilder as any;
      });
      
      // Mock other counts
      jobRepository.count.mockResolvedValue(0);
      videoJDRepository.count.mockResolvedValue(0);
      candidateRepository.count.mockResolvedValue(0);
      
      // Mock platform activity queries
      const mockPlatformActivityQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(0),
      };
      
      jobRepository.createQueryBuilder.mockReturnValue(mockPlatformActivityQueryBuilder as any);
      videoJDRepository.createQueryBuilder.mockReturnValue(mockPlatformActivityQueryBuilder as any);
      candidateRepository.createQueryBuilder.mockReturnValue(mockPlatformActivityQueryBuilder as any);
      
      // Mock company usage data method
      jest.spyOn(service as any, 'getCompanyUsageDataPaginated').mockResolvedValue({
        data: [],
        meta: {
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 0,
        },
      });

      const result = await service.getUsageStats();

      expect(result.industryDistribution).toEqual([
        { name: 'Technology', value: 2 },
        { name: 'Finance', value: 1 },
      ]);
    });
  });

  describe('getHiredStatus', () => {
    it('should return hired status when candidate is hired', async () => {
      const hiredCandidate = {
        ...mockCandidate,
        status: CandidateStatus.HIRED,
        job: mockJob,
      };

      // Mock job seeker repository
      jobSeekerRepository.findOne.mockResolvedValue(mockJobSeeker as any);

      // Mock candidate query builder
      const mockCandidateQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([hiredCandidate]),
      };
      candidateRepository.createQueryBuilder.mockReturnValue(mockCandidateQueryBuilder as any);

      const result = await service.getHiredStatus('auth0-user-123');

      expect(result).toBeDefined();
      expect(result.isHired).toBe(true);
      expect(result.jobInfo).toBeDefined();
    });

    it('should return not hired status when candidate is not hired', async () => {
      jobSeekerRepository.findOne.mockResolvedValue(null);

      const result = await service.getHiredStatus('auth0-user-123');

      expect(result).toBeDefined();
      expect(result.isHired).toBe(false);
      expect(result.jobInfo).toBeNull();
    });
  });

  describe('private methods', () => {
    describe('getJobStats', () => {
      it('should calculate job statistics correctly', async () => {
        const jobs = [
          { ...mockJob, status: 'OPEN' },
          { ...mockJob, status: 'OPEN' },
          { ...mockJob, status: 'NEW' },
        ];

        const mockQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(jobs),
        };

        jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

        const result = await service['getJobStats']('auth0-user-123');

        expect(result.total).toBe(3);
        expect(result.statusBreakdown.open).toBe(2);
        expect(result.statusBreakdown.new).toBe(1);
      });

      it('should handle empty job list', async () => {
        const mockQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue([]),
        };

        jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

        const result = await service['getJobStats']('auth0-user-123');

        expect(result.total).toBe(0);
        expect(result.statusBreakdown.open).toBe(0);
        expect(result.statusBreakdown.new).toBe(0);
      });
    });

    describe('getCandidateStats', () => {
      it('should calculate candidate statistics correctly', async () => {
        const candidates = [
          { ...mockCandidate, status: CandidateStatus.MATCHED },
          { ...mockCandidate, status: CandidateStatus.MATCHED },
          { ...mockCandidate, status: CandidateStatus.APPLIED },
        ];

        const mockQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(candidates),
        };

        candidateRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

        const result = await service['getCandidateStats']('auth0-user-123');

        expect(result.total).toBe(3);
        expect(result.matched).toBe(2);
        expect(result.byStatus[CandidateStatus.MATCHED]).toBe(2);
        expect(result.byStatus[CandidateStatus.APPLIED]).toBe(1);
      });

      it('should calculate source breakdown correctly', async () => {
        const candidates = [
          { ...mockCandidate, source: 'JOB_SEEKER' },
          { ...mockCandidate, source: 'JOB_SEEKER' },
          { ...mockCandidate, source: 'RESUME_UPLOAD' },
        ];

        const mockQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          select: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(candidates),
        };

        candidateRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

        const result = await service['getCandidateStats']('auth0-user-123');

        expect(result.bySource.JOB_SEEKER).toBe(2);
        expect(result.bySource.RESUME_UPLOAD).toBe(1);
      });
    });

    describe('getVideoJDStats', () => {
      it('should calculate video JD statistics correctly', async () => {
        const videoJDs = [
          { ...mockVideoJD, status: 'COMPLETED' },
          { ...mockVideoJD, status: 'COMPLETED' },
          { ...mockVideoJD, status: 'PENDING' },
        ];

        const mockQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(videoJDs),
        };

        videoJDRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

        const result = await service['getVideoJDStats']('auth0-user-123');

        expect(result.total).toBe(3);
        expect(result.completed).toBe(2);
        expect(result.pending).toBe(1);
        expect(result.byStatus.COMPLETED).toBe(2);
        expect(result.byStatus.PENDING).toBe(1);
      });
    });

    describe('getNotificationStats', () => {
      it('should calculate notification statistics correctly', async () => {
        const notifications = [
          { ...mockNotification, read: false, type: 'INFO' },
          { ...mockNotification, read: false, type: 'WARNING' },
          { ...mockNotification, read: true, type: 'INFO' },
        ];

        const mockQueryBuilder = {
          where: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(notifications),
        };

        notificationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

        const result = await service['getNotificationStats']('auth0-user-123');

        expect(result.total).toBe(3);
        expect(result.unread).toBe(2);
        expect(result.byType.INFO).toBe(2);
        expect(result.byType.WARNING).toBe(1);
      });

      it('should calculate today notifications correctly', async () => {
        const today = new Date();
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

        const notifications = [
          { ...mockNotification, createdAt: today },
          { ...mockNotification, createdAt: today },
          { ...mockNotification, createdAt: yesterday },
        ];

        const mockQueryBuilder = {
          where: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue(notifications),
        };

        notificationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

        const result = await service['getNotificationStats']('auth0-user-123');

        expect(result.today).toBe(2);
      });
    });

    describe('getEmployerMetrics', () => {
      it('should calculate employer metrics correctly', async () => {
        jobSeekerRepository.find.mockResolvedValue([mockJobSeeker] as any);

        // Mock candidate query builder
        const mockCandidateQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue([mockCandidate]),
        };
        candidateRepository.createQueryBuilder.mockReturnValue(mockCandidateQueryBuilder as any);

        // Mock job query builder
        const mockJobQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue([mockJob]),
        };
        jobRepository.createQueryBuilder.mockReturnValue(mockJobQueryBuilder as any);

        const result = await service['getEmployerMetrics']('auth0-user-123');

        expect(result.totalJobSeekers).toBeDefined();
        expect(result.totalCandidates).toBeDefined();
        expect(result.totalJobs).toBeDefined();
        expect(result.applicationToInterviewRate).toBeDefined();
        expect(result.interviewToHireRate).toBeDefined();
        expect(result.overallConversionRate).toBeDefined();
      });

      it('should handle zero division in conversion rates', async () => {
        jobSeekerRepository.find.mockResolvedValue([]);

        // Mock candidate query builder
        const mockCandidateQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue([]),
        };
        candidateRepository.createQueryBuilder.mockReturnValue(mockCandidateQueryBuilder as any);

        // Mock job query builder
        const mockJobQueryBuilder = {
          leftJoinAndSelect: jest.fn().mockReturnThis(),
          where: jest.fn().mockReturnThis(),
          getMany: jest.fn().mockResolvedValue([]),
        };
        jobRepository.createQueryBuilder.mockReturnValue(mockJobQueryBuilder as any);

        const result = await service['getEmployerMetrics']('auth0-user-123');

        expect(result.applicationToInterviewRate).toBe(0);
        expect(result.interviewToHireRate).toBe(0);
        expect(result.overallConversionRate).toBe(0);
      });
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      userRoleRepository.findOne.mockRejectedValue(new Error('Database connection error'));

      await expect(service.getDashboardStats('auth0-user-123')).rejects.toThrow(
        'Database connection error',
      );
    });

    it('should handle repository errors in stats calculation', async () => {
      userRoleRepository.findOne.mockResolvedValue(mockUserRole as any);

      // Mock job query builder to throw error
      const mockJobQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockRejectedValue(new Error('Job repository error')),
      };
      jobRepository.createQueryBuilder.mockReturnValue(mockJobQueryBuilder as any);

      await expect(service.getDashboardStats('auth0-user-123')).rejects.toThrow(
        'Job repository error',
      );
    });

    it('should handle company repository errors', async () => {
      userRoleRepository.findOne.mockResolvedValue(mockUserRole as any);

      // Mock all query builders to return empty arrays
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };

      jobRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      candidateRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      videoJDRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      notificationRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      jobSeekerRepository.find.mockResolvedValue([]);
      companyRepository.findOne.mockRejectedValue(new Error('Company repository error'));

      // This shouldn't throw an error since company lookup is for subscription stats which is optional
      const result = await service.getDashboardStats('auth0-user-123');
      expect(result).toBeDefined();
    });
  });
});
