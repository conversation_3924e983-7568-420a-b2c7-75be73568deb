import { CandidateStatus, ContactMethod } from '@shared/types';

import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { Graduate } from '@modules/graduate/entities/graduate.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';

export interface StandardizedProfile {
  // Basic Info
  id: string;
  userId: string;
  clientId?: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  myProfileImage?: string;

  // Professional Info
  summary?: string;
  skills: string[];
  experience?: Array<{
    title: string;
    company: string;
    startDate?: string;
    endDate?: string;
    duration?: string | number;
    location?: string;
    description?: string;
    achievements?: string[];
  }>;
  education?: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate?: string;
    endDate?: string;
    description?: string;
  }>;

  // Links & Documents
  resumeUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  videoIntroUrl?: string;

  // Additional Info
  certifications?: any[];
  languages?: string[];
  myValues?: string[];

  // Preferences & Availability
  preferences?: {
    jobTypes?: string[];
    locations?: string[];
    industries?: string[];
    desiredSalary?: {
      min: number;
      max: number;
      currency: string;
      period: string;
    };
    remotePreference?: string;
  };
  workAvailability?: {
    noticePeriod?: string;
    willingToRelocate?: boolean;
    immediatelyAvailable?: boolean;
  };

  // System Fields
  hasCompletedOnboarding?: boolean;
  onboardingProgress?: any;
  verifications?: any;

  // Application Info
  status?: string;
  source?: string;
  yearsOfExperience?: number;
  currentCompany?: string;
  appliedJobs?: string[];

  // Additional Metadata
  metadata?: any;
  validation?: any;

  // Candidate History Fields
  interviews?: Array<{
    id: string;
    type: string;
    scheduledDate: Date;
    location?: string;
    meetingLink?: string;
    interviewers: string[];
    completed: boolean;
    feedback?: {
      strengths: string[];
      weaknesses: string[];
      rating: number;
      notes: string;
      recommendation: 'HIRE' | 'REJECT' | 'CONTINUE';
    };
  }>;

  statusHistory?: Array<{
    previousStatus: string;
    newStatus: string;
    changedAt: Date;
    changedBy?: string;
    reason?: string;
  }>;

  communicationLogs?: Array<{
    id: string;
    type: 'EMAIL' | 'CALL' | 'TEXT' | 'IN_PERSON' | 'OTHER';
    date: Date;
    initiatedBy: string;
    summary: string;
    details?: string;
  }>;

  tasks?: Array<{
    id: string;
    title: string;
    description?: string;
    dueDate: Date;
    assignedTo: string;
    completed: boolean;
    completedAt?: Date;
    priority: 'LOW' | 'MEDIUM' | 'HIGH';
  }>;

  // Additional candidate-specific fields
  assignedRecruiter?: string;
  assignedHiringManager?: string;
  referralSource?: string;
  referredBy?: string;
  referralBonusPaid?: boolean;
  onboardingStatus?: string;
  lastContactDate?: Date;
  isRemoteOnly?: boolean;
  availableFrom?: Date;
  graduateId?: string;
  // activityHistory removed from response
  extractionMetadata?: any[];
  __job__?: any;
  videoResponses?: any[];
  evaluations?: any[];
  jobSeeker?: any;
}

export class ProfileFormatter {
  static toStandardProfile(
    source: JobSeeker | Candidate | Graduate | any,
    includeValidation = false,
  ): StandardizedProfile {
    // Base profile with common fields
    const profile: StandardizedProfile = {
      id: source.id,
      userId: source.userId || source.clientId,
      clientId: source.clientId,
      firstName: source.firstName,
      lastName: source.lastName,
      fullName: source.fullName || `${source.firstName} ${source.lastName}`,
      email: source.email,
      phone: source.phone,
      location: source.location,
      myProfileImage: source.myProfileImage,
      summary: source.summary,
      skills: source.skills || [],
      experience: source.experience || [],
      education: source.education || [],
      resumeUrl: source.resumeUrl || source.profileUrl,
      linkedinUrl: source.linkedinUrl,
      githubUrl: source.githubUrl,
      portfolioUrl: source.portfolioUrl,
      videoIntroUrl: source.videoIntroUrl,
      certifications: source.certifications || [],
      languages: source.languages || [],
      myValues: source.myValues || [],
      preferences: source.preferences,
      workAvailability: source.workAvailability,
      hasCompletedOnboarding: source.hasCompletedOnboarding,
      onboardingProgress: source.onboardingProgress,
      verifications: source.verifications,
      status: source.status,
      source: source.source,
      yearsOfExperience: source.yearsOfExperience,
      currentCompany: source.currentCompany,
      appliedJobs: source.appliedJobs,
      metadata: source.metadata,
      // Add candidate-specific fields
      interviews: source.interviews,
      statusHistory: source.statusHistory,
      communicationLogs: source.communicationLogs,
      tasks: source.tasks,
      assignedRecruiter: source.assignedRecruiter,
      assignedHiringManager: source.assignedHiringManager,
      referralSource: source.referralSource,
      referredBy: source.referredBy,
      referralBonusPaid: source.referralBonusPaid,
      onboardingStatus: source.onboardingStatus,
      lastContactDate: source.lastContactDate,
      isRemoteOnly: source.isRemoteOnly,
      availableFrom: source.availableFrom,
      graduateId: source.graduateId,
      // activityHistory removed from response
      extractionMetadata: source.extractionMetadata,
      __job__: source.__job__,
      videoResponses: source.videoResponses,
      evaluations: source.evaluations,
      jobSeeker: source.jobSeeker,
    };

    // Add validation info if requested
    if (includeValidation && source.validation) {
      profile.validation = source.validation;
    }

    return profile;
  }

  static toCandidate(profile: StandardizedProfile, jobId: string): Partial<Candidate> {
    // Map experience to ensure it matches CandidateExperience type
    const mappedExperience = profile.experience?.map((exp) => ({
      title: exp.title,
      company: exp.company,
      startDate: exp.startDate || null,
      endDate: exp.endDate || null,
      duration: typeof exp.duration === 'string' ? exp.duration : exp.duration?.toString() || '',
      location: exp.location || null,
      description: exp.description || null,
      achievements: exp.achievements || null,
    }));

    return {
      jobId,
      clientId: profile.clientId,
      fullName: profile.fullName,
      firstName: profile.firstName,
      lastName: profile.lastName,
      email: profile.email,
      phone: profile.phone,
      location: profile.location,
      summary: profile.summary,
      skills: profile.skills,
      experience: mappedExperience || null,
      linkedinUrl: profile.linkedinUrl,
      githubUrl: profile.githubUrl,
      profileUrl: profile.resumeUrl,
      myProfileImage: profile.myProfileImage,
      source: profile.source || 'PROFILE',
      status: CandidateStatus.NEW,
      contacted: false,
      contactMethod: ContactMethod.EMAIL,
      yearsOfExperience: profile.yearsOfExperience,
      currentCompany: profile.currentCompany,
      appliedJobs: profile.appliedJobs || [jobId],
    };
  }
}
