import { Test, TestingModule } from '@nestjs/testing';
import { Job } from 'bull';
import { FileUploadProcessor } from './file-upload.processor';
import { CandidateService } from '@modules/candidate/candidate.service';
import { CandidateServiceUtils } from '@modules/candidate/candidate.service.utils';
import { CreditService } from '@modules/subscription/credit.service';
import { ResumePerformanceMonitorService } from '../services/resume-performance-monitor.service';
import { CandidateStatus } from '@shared/types';

describe('FileUploadProcessor - Duplicate Handling', () => {
  let processor: FileUploadProcessor;
  let candidateService: CandidateService;
  let creditService: CreditService;
  let performanceMonitor: ResumePerformanceMonitorService;

  const mockJob: Partial<Job> = {
    id: 'queue-job-1',
    data: {
      files: [],
      jobId: 'job-123',
      userId: 'user-1',
      clientId: 'client-1',
    },
    progress: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileUploadProcessor,
        {
          provide: CandidateService,
          useValue: {
            findByJob: jest.fn(),
            update: jest.fn(),
            processResumeWithDuplicateCheck: jest.fn(),
          },
        },
        {
          provide: CreditService,
          useValue: {},
        },
        {
          provide: ResumePerformanceMonitorService,
          useValue: {
            startSession: jest.fn().mockReturnValue({}),
            startStage: jest.fn(),
            endStage: jest.fn(),
            updateResults: jest.fn(),
            endSession: jest.fn().mockReturnValue({}),
          },
        },
      ],
    }).compile();

    processor = module.get<FileUploadProcessor>(FileUploadProcessor);
    candidateService = module.get<CandidateService>(CandidateService);
    creditService = module.get<CreditService>(CreditService);
    performanceMonitor = module.get<ResumePerformanceMonitorService>(
      ResumePerformanceMonitorService,
    );
  });

  describe('Duplicate Detection - Before Queue Processing', () => {
    it('should handle duplicate file with same filename and clientId for same job', async () => {
      const mockFile = {
        originalname: 'john-doe.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('test'),
      };

      const existingCandidate = {
        id: 'candidate-1',
        jobId: 'job-123',
        appliedJobs: ['job-123'],
        originalFilename: 'john-doe.pdf',
        email: '<EMAIL>',
        clientId: 'client-1',
      };

      // Mock existing candidates
      jest.spyOn(candidateService, 'findByJob').mockResolvedValue([existingCandidate] as any);
      jest
        .spyOn(CandidateServiceUtils, 'filterCandidatesByClient')
        .mockReturnValue([existingCandidate] as any);
      jest.spyOn(CandidateServiceUtils, 'isDuplicateFile').mockReturnValue(true);
      jest.spyOn(candidateService, 'update').mockResolvedValue(existingCandidate as any);

      const jobData = {
        ...mockJob,
        data: {
          ...mockJob.data,
          files: [mockFile],
        },
      };

      const result = await processor.handleFileUpload(jobData as Job);

      // Should recognize as duplicate
      expect(result.duplicateCount).toBe(1);
      expect(result.candidateIds).toHaveLength(1);
      expect(result.candidateIds[0].isDuplicate).toBe(true);

      // Should NOT create new candidate
      expect(candidateService.processResumeWithDuplicateCheck).not.toHaveBeenCalled();

      // Should NOT update appliedJobs since candidate already has this job
      expect(candidateService.update).not.toHaveBeenCalled();
    });

    it('should handle duplicate file for DIFFERENT job - same client', async () => {
      const mockFile = {
        originalname: 'john-doe.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('test'),
      };

      const existingCandidate = {
        id: 'candidate-1',
        jobId: 'job-456', // Different original job
        appliedJobs: ['job-456'],
        originalFilename: 'john-doe.pdf',
        email: '<EMAIL>',
        clientId: 'client-1',
      };

      // Mock finding existing candidate from different job
      jest.spyOn(candidateService, 'findByJob').mockResolvedValue([existingCandidate] as any);
      jest
        .spyOn(CandidateServiceUtils, 'filterCandidatesByClient')
        .mockReturnValue([existingCandidate] as any);
      jest.spyOn(CandidateServiceUtils, 'isDuplicateFile').mockReturnValue(true);
      jest.spyOn(candidateService, 'update').mockResolvedValue({
        ...existingCandidate,
        appliedJobs: ['job-456', 'job-123'],
      } as any);

      const jobData = {
        ...mockJob,
        data: {
          ...mockJob.data,
          files: [mockFile],
          jobId: 'job-123', // New job
        },
      };

      const result = await processor.handleFileUpload(jobData as Job);

      // Should update appliedJobs array to include new job
      expect(candidateService.update).toHaveBeenCalledWith('candidate-1', {
        appliedJobs: ['job-456', 'job-123'],
      });

      // Should mark as duplicate but successful
      expect(result.duplicateCount).toBe(1);
      expect(result.successCount).toBe(1);
      expect(result.candidateIds[0].isDuplicate).toBe(true);
    });
  });

  describe('Duplicate Detection - During Processing', () => {
    it('should handle duplicate detected by processResumeWithDuplicateCheck', async () => {
      const mockFile = {
        originalname: 'jane-smith.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('test'),
      };

      // No existing candidates initially
      jest.spyOn(candidateService, 'findByJob').mockResolvedValue([]);
      jest.spyOn(CandidateServiceUtils, 'isDuplicateFile').mockReturnValue(false);

      // But processResumeWithDuplicateCheck finds duplicate by email
      const duplicateCandidate = {
        id: 'candidate-2',
        jobId: 'job-789',
        appliedJobs: ['job-789'],
        email: '<EMAIL>',
        isDuplicate: true,
        duplicateMessage: 'Resume data reused from existing candidate',
      };

      jest
        .spyOn(candidateService, 'processResumeWithDuplicateCheck')
        .mockResolvedValue(duplicateCandidate as any);

      jest.spyOn(candidateService, 'update').mockResolvedValue({
        ...duplicateCandidate,
        appliedJobs: ['job-789', 'job-123'],
      } as any);

      const jobData = {
        ...mockJob,
        data: {
          ...mockJob.data,
          files: [mockFile],
        },
      };

      const result = await processor.handleFileUpload(jobData as Job);

      // Should update appliedJobs for the duplicate
      expect(candidateService.update).toHaveBeenCalledWith('candidate-2', {
        appliedJobs: ['job-789', 'job-123'],
      });

      expect(result.duplicateCount).toBe(1);
      expect(result.candidateIds[0].isDuplicate).toBe(true);
    });
  });

  describe('Multiple Files with Mixed Duplicates', () => {
    it('should handle batch upload with some duplicates and some new', async () => {
      const files = [
        {
          originalname: 'duplicate1.pdf',
          mimetype: 'application/pdf',
          size: 1024,
          buffer: Buffer.from('1'),
        },
        {
          originalname: 'new1.pdf',
          mimetype: 'application/pdf',
          size: 1024,
          buffer: Buffer.from('2'),
        },
        {
          originalname: 'duplicate2.pdf',
          mimetype: 'application/pdf',
          size: 1024,
          buffer: Buffer.from('3'),
        },
        {
          originalname: 'new2.pdf',
          mimetype: 'application/pdf',
          size: 1024,
          buffer: Buffer.from('4'),
        },
      ];

      const existingCandidates = [
        {
          id: 'candidate-1',
          jobId: 'job-123',
          appliedJobs: ['job-123'],
          originalFilename: 'duplicate1.pdf',
          clientId: 'client-1',
        },
        {
          id: 'candidate-2',
          jobId: 'job-456',
          appliedJobs: ['job-456'],
          originalFilename: 'duplicate2.pdf',
          clientId: 'client-1',
        },
      ];

      jest.spyOn(candidateService, 'findByJob').mockResolvedValue(existingCandidates as any);
      jest
        .spyOn(CandidateServiceUtils, 'filterCandidatesByClient')
        .mockReturnValue(existingCandidates as any);

      // Mock isDuplicateFile to return true for duplicates
      jest
        .spyOn(CandidateServiceUtils, 'isDuplicateFile')
        .mockImplementation((file, candidates, clientId) => {
          return file.originalname === 'duplicate1.pdf' || file.originalname === 'duplicate2.pdf';
        });

      // Mock processResumeWithDuplicateCheck for new files
      jest
        .spyOn(candidateService, 'processResumeWithDuplicateCheck')
        .mockImplementation(async (file) => {
          if (file.originalname === 'new1.pdf') {
            return { id: 'candidate-3', jobId: 'job-123', appliedJobs: ['job-123'] } as any;
          } else {
            return { id: 'candidate-4', jobId: 'job-123', appliedJobs: ['job-123'] } as any;
          }
        });

      jest.spyOn(candidateService, 'update').mockImplementation(async (id, data) => {
        const candidate = existingCandidates.find((c) => c.id === id);
        return { ...candidate, ...data } as any;
      });

      const jobData = {
        ...mockJob,
        data: {
          ...mockJob.data,
          files,
        },
      };

      const result = await processor.handleFileUpload(jobData as Job);

      // Should process all files
      expect(result.totalFiles).toBe(4);
      expect(result.successCount).toBe(4);
      expect(result.duplicateCount).toBe(2);

      // Should update appliedJobs for duplicate from different job
      expect(candidateService.update).toHaveBeenCalledWith('candidate-2', {
        appliedJobs: ['job-456', 'job-123'],
      });

      // Should process new files
      expect(candidateService.processResumeWithDuplicateCheck).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling with Duplicates', () => {
    it('should handle errors during duplicate processing', async () => {
      const mockFile = {
        originalname: 'error.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('test'),
      };

      jest.spyOn(candidateService, 'findByJob').mockRejectedValue(new Error('Database error'));

      const jobData = {
        ...mockJob,
        data: {
          ...mockJob.data,
          files: [mockFile],
        },
      };

      await expect(processor.handleFileUpload(jobData as Job)).rejects.toThrow('Database error');
    });

    it('should handle partial failures in batch upload', async () => {
      const files = [
        {
          originalname: 'good.pdf',
          mimetype: 'application/pdf',
          size: 1024,
          buffer: Buffer.from('1'),
        },
        {
          originalname: 'bad.pdf',
          mimetype: 'application/pdf',
          size: 1024,
          buffer: Buffer.from('2'),
        },
      ];

      jest.spyOn(candidateService, 'findByJob').mockResolvedValue([]);
      jest.spyOn(CandidateServiceUtils, 'isDuplicateFile').mockReturnValue(false);

      jest
        .spyOn(candidateService, 'processResumeWithDuplicateCheck')
        .mockImplementation(async (file) => {
          if (file.originalname === 'bad.pdf') {
            throw new Error('Processing failed');
          }
          return { id: 'candidate-1', jobId: 'job-123', appliedJobs: ['job-123'] } as any;
        });

      const jobData = {
        ...mockJob,
        data: {
          ...mockJob.data,
          files,
        },
      };

      const result = await processor.handleFileUpload(jobData as Job);

      expect(result.partialSuccess).toBe(true);
      expect(result.successCount).toBe(1);
      expect(result.failureCount).toBe(1);
      expect(result.failedUploads).toHaveLength(1);
      expect(result.failedUploads[0].filename).toBe('bad.pdf');
    });
  });

  describe('Performance Monitoring', () => {
    it('should track duplicate detection performance', async () => {
      const mockFile = {
        originalname: 'test.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('test'),
      };

      jest.spyOn(candidateService, 'findByJob').mockResolvedValue([]);
      jest
        .spyOn(candidateService, 'processResumeWithDuplicateCheck')
        .mockResolvedValue({ id: 'candidate-1', jobId: 'job-123' } as any);

      const jobData = {
        ...mockJob,
        data: {
          ...mockJob.data,
          files: [mockFile],
        },
      };

      await processor.handleFileUpload(jobData as Job);

      expect(performanceMonitor.startSession).toHaveBeenCalled();
      expect(performanceMonitor.startStage).toHaveBeenCalledWith(
        expect.any(String),
        'duplicate-detection',
      );
      expect(performanceMonitor.endStage).toHaveBeenCalledWith(
        expect.any(String),
        'duplicate-detection',
        expect.any(Object),
      );
      expect(performanceMonitor.endSession).toHaveBeenCalled();
    });
  });
});
