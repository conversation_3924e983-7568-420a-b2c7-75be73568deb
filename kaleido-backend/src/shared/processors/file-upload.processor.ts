import { Job } from 'bull';

import { CreditService } from '@/modules/subscription/credit.service';
import * as Bull from '@nestjs/bull';
import { Logger } from '@nestjs/common';

import { CandidateService } from '../../modules/candidate/candidate.service';
import { CandidateServiceUtils } from '../../modules/candidate/candidate.service.utils';
import { Candidate } from '../../modules/candidate/entities/candidate.entity';
import { ResumePerformanceMonitorService } from '../services/resume-performance-monitor.service';

// Define an interface that extends Candidate to include our custom properties
interface CandidateWithDuplicateInfo extends Candidate {
  isDuplicate?: boolean;
  duplicateMessage?: string;
  duplicateInfo?: {
    filename: string;
    status: string;
    message: string;
  };
}

@Bull.Processor('file-upload')
export class FileUploadProcessor {
  private readonly logger = new Logger(FileUploadProcessor.name);

  constructor(
    private readonly candidateService: CandidateService,
    private readonly creditService: CreditService,
    private readonly performanceMonitor: ResumePerformanceMonitorService,
  ) {}

  @Bull.Process('process-uploads')
  async handleFileUpload(job: Job) {
    const { files, jobId, userId, clientId } = job.data;

    if (!files || !Array.isArray(files)) {
      throw new Error('No files provided for upload');
    }

    const totalFiles = files.length;
    let successfulUploads = 0;

    // Start performance monitoring session
    const sessionId = `upload-${job.id}-${Date.now()}`;
    const session = this.performanceMonitor.startSession(sessionId, jobId, totalFiles, {
      aiProvider: 'multi-provider',
      concurrencyLevel: totalFiles,
    });

    try {
      this.logger.log(`Starting concurrent processing of ${totalFiles} files for job ${jobId}`);
      this.performanceMonitor.startStage(sessionId, 'duplicate-detection');

      // First check for duplicates within the same job for this specific client
      const existingCandidates = (await this.candidateService.findByJob(jobId, clientId)) || [];

      this.performanceMonitor.endStage(sessionId, 'duplicate-detection', {
        existingCandidatesCount: existingCandidates.length,
      });

      // Process all files concurrently using Promise.allSettled for better error handling
      this.performanceMonitor.startStage(sessionId, 'concurrent-processing');
      this.logger.log(`🚀 Starting concurrent processing of ${totalFiles} files`);

      // Track progress with thread-safe approach for concurrent processing
      const progressTracker = { count: 0 };
      const updateProgress = async () => {
        progressTracker.count++;
        // Start at 10% and go up to 100%
        const progress = Math.round(10 + (progressTracker.count / totalFiles) * 90);
        await job.progress(progress);
        this.logger.log(`Progress: ${progressTracker.count}/${totalFiles} files (${progress}%)`);
      };

      // Initial progress update - start at 10% to show immediate activity
      await job.progress(10);

      const fileProcessingPromises = files.map(async (file: any, index: number) => {
        try {
          this.logger.log(
            `Starting processing of file ${index + 1}/${totalFiles}: ${file.originalname}`,
          );

          // Check if file is a duplicate within the same job for this client
          if (CandidateServiceUtils.isDuplicateFile(file, existingCandidates, clientId)) {
            this.logger.log(
              `Duplicate file detected: ${file.originalname} for client ${clientId} - handled as success`,
            );

            const duplicateInfo = {
              filename: file.originalname,
              status: 'duplicate',
              message: 'Resume already exists for this client and job combination',
            };

            // Find the existing candidate with this filename for this client to ensure it's linked to the job
            const existingCandidate = CandidateServiceUtils.filterCandidatesByClient(
              existingCandidates,
              clientId,
            ).find((c) => c.originalFilename === file.originalname);

            if (existingCandidate) {
              // Ensure the candidate is linked to the job
              if (!existingCandidate.appliedJobs) {
                existingCandidate.appliedJobs = [jobId];
                await this.candidateService.update(existingCandidate.id, { appliedJobs: [jobId] });
              } else if (!existingCandidate.appliedJobs.includes(jobId)) {
                const updatedAppliedJobs = [...existingCandidate.appliedJobs, jobId];
                await this.candidateService.update(existingCandidate.id, {
                  appliedJobs: updatedAppliedJobs,
                });
              }

              // Update progress for duplicate file
              await updateProgress();

              return {
                success: true,
                candidate: {
                  ...existingCandidate,
                  isDuplicate: true,
                  duplicateInfo,
                } as any,
                filename: file.originalname,
                isDuplicate: true,
              };
            } else {
              // Update progress for duplicate file without existing candidate
              await updateProgress();

              return {
                success: true,
                candidate: {
                  id: `duplicate-${Date.now()}`,
                  originalFilename: file.originalname,
                  isDuplicate: true,
                  duplicateInfo,
                } as any,
                filename: file.originalname,
                isDuplicate: true,
              };
            }
          } else {
            // Ensure file buffer is properly handled
            const processedFile = {
              ...file,
              buffer: this.ensureBuffer(file.buffer),
            };

            // Process the file
            const candidate = await this.candidateService.processResumeWithDuplicateCheck(
              processedFile,
              jobId,
              clientId,
            );

            // Check if the candidate is marked as a duplicate
            const candidateWithInfo = candidate as CandidateWithDuplicateInfo;
            if (candidateWithInfo.isDuplicate) {
              this.logger.log(`Duplicate candidate detected for file ${file.originalname}`);

              // Ensure the candidate is linked to the job
              if (!candidateWithInfo.appliedJobs) {
                candidateWithInfo.appliedJobs = [jobId];
                await this.candidateService.update(candidateWithInfo.id, { appliedJobs: [jobId] });
              } else if (!candidateWithInfo.appliedJobs.includes(jobId)) {
                const updatedAppliedJobs = [...candidateWithInfo.appliedJobs, jobId];
                await this.candidateService.update(candidateWithInfo.id, {
                  appliedJobs: updatedAppliedJobs,
                });
              }

              // Update progress for duplicate processed candidate
              await updateProgress();

              return {
                success: true,
                candidate: {
                  ...candidate,
                  isDuplicate: true,
                  duplicateInfo: {
                    filename: file.originalname,
                    status: 'duplicate',
                    message:
                      candidateWithInfo.duplicateMessage ||
                      'Resume already exists for this client and job combination',
                  },
                } as any,
                filename: file.originalname,
                isDuplicate: true,
              };
            } else {
              // Regular successful candidate
              // Ensure the candidate is linked to the job
              if (!candidate.appliedJobs) {
                candidate.appliedJobs = [jobId];
                await this.candidateService.update(candidate.id, { appliedJobs: [jobId] });
              } else if (!candidate.appliedJobs.includes(jobId)) {
                const updatedAppliedJobs = [...candidate.appliedJobs, jobId];
                await this.candidateService.update(candidate.id, {
                  appliedJobs: updatedAppliedJobs,
                });
              }

              this.logger.log(`✅ Successfully processed file ${file.originalname}`);

              // Update progress after processing this file
              await updateProgress();

              return {
                success: true,
                candidate,
                filename: file.originalname,
                isDuplicate: false,
              };
            }
          }
        } catch (error: any) {
          const errorMessage = error?.message || 'Unknown error occurred';
          this.logger.error(`❌ Error processing file ${file.originalname}:`, error);

          if (error.stack) {
            this.logger.error(`Stack trace for ${file.originalname}:`, error.stack);
          }

          // Update progress even for failed files
          await updateProgress();

          return {
            success: false,
            error: errorMessage,
            filename: file.originalname,
          };
        }
      });

      // Wait for all files to be processed concurrently
      const results = await Promise.allSettled(fileProcessingPromises);

      this.performanceMonitor.endStage(sessionId, 'concurrent-processing');
      this.performanceMonitor.startStage(sessionId, 'results-processing');

      // Process results
      const successfulCandidates: any[] = [];
      const newCandidates: any[] = [];
      const duplicateCandidates: any[] = [];
      const failedUploads: any[] = [];
      let processedFiles = 0;
      let failedFiles = 0;
      let duplicateCount = 0;
      let reuseCount = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successfulCandidates.push(result.value.candidate);
          if (result.value.isDuplicate) {
            duplicateCount++;
            duplicateCandidates.push(result.value.candidate);
            if (result.value.candidate.duplicateMessage?.includes('reused')) {
              reuseCount++;
            }
          } else {
            successfulUploads++;
            newCandidates.push(result.value.candidate);
          }
          processedFiles++;
        } else {
          failedFiles++;
          const error = result.status === 'rejected' ? result.reason : result.value.error;
          const filename =
            result.status === 'rejected' ? files[index].originalname : result.value.filename;
          failedUploads.push({
            filename,
            error: error?.message || error || 'Unknown error occurred',
          });
        }
      });

      // Update performance monitoring results
      this.performanceMonitor.updateResults(sessionId, {
        successful: processedFiles,
        failed: failedFiles,
        duplicates: duplicateCount,
        reused: reuseCount,
      });

      this.performanceMonitor.endStage(sessionId, 'results-processing');

      // Update final progress
      await job.progress(100);

      // Now update the final result message to include information about duplicates
      // Standardize the result format to match CandidateService.uploadResumes
      const successCount = successfulCandidates.length;
      const failureCount = failedUploads.length;

      let message = '';
      if (failureCount > 0) {
        message = `Completed with ${failureCount} errors. Successfully processed ${successCount - duplicateCount} of ${totalFiles} files.`;
        if (duplicateCount > 0) {
          message += ` ${duplicateCount} files were duplicates and have been handled successfully.`;
        }
      } else if (duplicateCount > 0) {
        message = `Successfully processed all ${totalFiles} files. ${duplicateCount} were duplicates and have been handled successfully.`;
      } else {
        message = `Successfully processed all ${totalFiles} files.`;
      }

      // Resume upload is now free - no credit consumption needed

      // Get total candidate count for the job after upload
      const allJobCandidates = (await this.candidateService.findByJob(jobId, clientId)) || [];
      const totalCandidatesInJob = allJobCandidates.length;

      // Count only unevaluated candidates (those without match scores)
      const unevaluatedCandidates = allJobCandidates.filter(
        (candidate) => !candidate.evaluation?.matchScore || candidate.evaluation.matchScore === 0,
      );
      const unevaluatedCount = unevaluatedCandidates.length;

      // Calculate credit consumption data for match & rank
      // This is the data needed by TotalCandidatesSection
      const creditData = {
        totalCandidates: totalCandidatesInJob,
        newCandidatesAdded: newCandidates.length,
        unevaluatedCandidatesCount: unevaluatedCount, // Only unevaluated candidates need credits
        creditCostPerCandidate: 1, // Default cost per candidate for match & rank
        estimatedCreditCost: unevaluatedCount * 1, // Only charge for unevaluated candidates
        uploadCreditCost: 0, // Resume upload is free
        availableCredits: 0, // This will be fetched separately by the frontend
      };

      // End performance monitoring session
      const finalSession = this.performanceMonitor.endSession(sessionId);

      // Return simplified response with only essential data
      return {
        jobId, // Include the actual job entity ID for consistency
        success: true, // Always return success if we've completed processing
        partialSuccess: failedUploads.length > 0,
        message,
        totalFiles,
        successCount,
        failureCount,
        duplicateCount,
        newCandidatesCount: newCandidates.length,
        // Only include basic candidate info, not full objects
        candidateIds: successfulCandidates.map((c: any) => ({
          id: c.id,
          name: c.fullName || c.firstName + ' ' + c.lastName,
          isDuplicate: c.isDuplicate || false,
        })),
        // Failed uploads info
        failedUploads: failedUploads.map((f: any) => ({
          filename: f.filename,
          error: f.error,
        })),
        // Credit consumption data for TotalCandidatesSection
        creditData,
        // Performance metrics (optional, keep it minimal)
        performanceMetrics: finalSession
          ? {
              totalDuration: finalSession.totalDuration,
              throughput: finalSession.throughput,
              averageTimePerFile: finalSession.averageTimePerFile,
            }
          : undefined,
      };
    } catch (error: any) {
      // End performance monitoring session on error
      this.performanceMonitor.endSession(sessionId);
      this.logger.error('Error in batch upload processor:', error);
      throw new Error(error?.message || 'Failed to process uploads');
    }
  }

  /**
   * Ensures that the file buffer is a proper Buffer instance
   * This is needed because Bull serializes the Buffer to JSON during queue operations
   */
  private ensureBuffer(buffer: any): Buffer {
    if (Buffer.isBuffer(buffer)) {
      return buffer;
    }

    // If buffer is a serialized Buffer object with data property
    if (
      buffer &&
      typeof buffer === 'object' &&
      buffer.type === 'Buffer' &&
      Array.isArray(buffer.data)
    ) {
      return Buffer.from(buffer.data);
    }

    // If it's just an array
    if (Array.isArray(buffer)) {
      return Buffer.from(buffer);
    }

    this.logger.warn('Unable to convert buffer to proper Buffer, returning as is', {
      bufferType: typeof buffer,
      isArray: Array.isArray(buffer),
      bufferKeys: buffer && typeof buffer === 'object' ? Object.keys(buffer) : [],
    });

    // Return as is if we can't determine the format
    return buffer;
  }
}
