import { Job, Queue } from 'bull';

import { PROCESSOR_NAMES, QUEUE_NAMES } from '@/shared/constants/queue.constants';
import * as Bull from '@nestjs/bull';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';

import { RedisConfigService } from './redis-config.service';

export interface JobStatus {
  id?: string; // Worker job ID for reference
  jobId: string; // Actual job entity ID
  status: string;
  progress: number;
  data?: any;
  result?: any;
  message?: string;
  partialSuccess?: boolean;
  successCount?: number;
  failureCount?: number;
  failedFiles?: Array<{ filename: string; error: string }>;
  errorSummary?: Array<{ error: string; count: number }>;
}

export interface QueueJobResult {
  jobId: string;
  status: string;
}

export interface UploadData {
  files: Express.Multer.File[];
  jobId: string;
  userId: string;
  clientId?: string; // Optional for backward compatibility, defaults to userId
}

export interface ScoutData {
  jobId: string;
  userId: string;
  count: number;
  source?: 'linkedin' | 'apollo' | 'pdl'; // Source to use for scouting, default to PDL
}

export interface MatchRankData {
  jobId: string;
  clientId: string;
  topTierThreshold?: number;
  secondTierThreshold?: number;
}

export interface VideoJDData {
  videoJDId: string;
  synthesiaVideoId: string;
  jobId: string;
  userId: string;
}

@Injectable()
export class QueueService implements OnModuleInit {
  private readonly logger = new Logger(QueueService.name);
  private connectionRetries = 0;
  private readonly MAX_RETRIES = 15; // Increased from 10 to 15
  private readonly QUEUE_OPERATION_TIMEOUT = 180000; // Increased to 180 seconds (3 minutes)
  private readonly QUEUE_VERIFY_TIMEOUT = 90000; // Increased to 90 seconds for verification operations
  private isRedisHealthy = true;

  constructor(
    @Bull.InjectQueue(QUEUE_NAMES.FILE_UPLOAD) private readonly fileUploadQueue: Queue,
    @Bull.InjectQueue(QUEUE_NAMES.SCOUT) private readonly scoutQueue: Queue,
    @Bull.InjectQueue(QUEUE_NAMES.MATCH_RANK) private readonly matchRankQueue: Queue,
    @Bull.InjectQueue(QUEUE_NAMES.VIDEO_JD) private readonly videoJDQueue: Queue,
    private readonly redisConfigService: RedisConfigService,
  ) {}

  async onModuleInit() {
    // Verify Redis connection on startup
    await this.checkRedisHealth();

    // Set up periodic health check
    setInterval(() => this.checkRedisHealth(), 600000); // Check every 10 minutes (reduced frequency)
  }

  private async checkRedisHealth(): Promise<boolean> {
    try {
      // Use the centralized Redis configuration service to check connectivity
      const isConnected = await this.redisConfigService.checkRedisConnection();

      if (!isConnected) {
        this.isRedisHealthy = false;
        this.logger.error('❌ Redis health check failed using RedisConfigService');
        return false;
      }

      // If the Redis connection is healthy, we'll consider the queue healthy too
      // This simplifies the health check and avoids potential issues with Bull's internal clients
      this.isRedisHealthy = true;
      return true;
    } catch (error: any) {
      this.isRedisHealthy = false;
      this.logger.error(`❌ Redis health check failed: ${error.message}`, error.stack);
      return false;
    }
  }

  async addToUploadQueue(data: UploadData): Promise<QueueJobResult> {
    // Check Redis health before attempting to add to queue
    const isHealthy = await this.checkRedisHealth();
    if (!isHealthy) {
      throw new Error('Queue service is currently unavailable (Redis connection issue)');
    }

    try {
      // Try direct queue addition with a shorter timeout (30 seconds)
      // This is much shorter than the 180 seconds we were seeing in production
      const directTimeoutPromise = new Promise<QueueJobResult>((_, reject) => {
        setTimeout(() => reject(new Error('Direct queue addition timed out')), 30000); // 30 second timeout
      });

      this.logger.log(`🚀 Attempting direct queue addition with 30 second timeout`);
      this.connectionRetries = 0;
      return await Promise.race([this.attemptAddToQueue(data), directTimeoutPromise]);
    } catch (error: any) {
      // If direct addition times out or fails, fall back to background processing
      this.logger.log(
        `⏳ Falling back to background processing for upload job: ${
          error.message || 'Unknown error'
        }`,
      );

      // Generate a job ID that will be used for polling
      const jobId = `job-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      // Start the queue operation in the background
      this.backgroundAddToQueue(data, jobId);

      // Return immediately with the job ID
      return {
        jobId,
        status: 'pending',
      };
    }
  }

  private async backgroundAddToQueue(data: UploadData, jobId: string): Promise<void> {
    try {
      // Reset connection retries for the background attempt
      this.connectionRetries = 0;

      // Clone the data and set the provided job ID
      const clonedData = { ...data, jobId };

      // Attempt to add the job to the queue
      await this.attemptAddToQueue(clonedData);

      this.logger.log(`✅ Background upload job successfully added with ID ${jobId}`);
    } catch (error: any) {
      this.logger.error(
        `❌ Background upload job addition failed for ID ${jobId}: ${error.message}`,
        error.stack,
      );
    }
  }

  async addToScoutQueue(data: ScoutData): Promise<QueueJobResult> {
    // Check Redis health before attempting to add to queue
    const isHealthy = await this.checkRedisHealth();
    if (!isHealthy) {
      throw new Error('Queue service is currently unavailable (Redis connection issue)');
    }

    try {
      // Try direct queue addition with a shorter timeout (30 seconds)
      // This is much shorter than the 180 seconds we were seeing in production
      const directTimeoutPromise = new Promise<QueueJobResult>((_, reject) => {
        setTimeout(() => reject(new Error('Direct queue addition timed out')), 30000); // 30 second timeout
      });

      this.logger.log(`🚀 Attempting direct scout queue addition with 30 second timeout`);
      this.connectionRetries = 0;
      return await Promise.race([this.attemptAddToScoutQueue(data), directTimeoutPromise]);
    } catch (error: any) {
      // If direct addition times out or fails, fall back to background processing
      this.logger.log(
        `⏳ Falling back to background processing for scout job: ${
          error.message || 'Unknown error'
        }`,
      );

      // Generate a job ID that will be used for polling
      const jobId = `job-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      // Start the queue operation in the background
      this.backgroundAddToScoutQueue(data, jobId);

      // Return immediately with the job ID
      return {
        jobId,
        status: 'pending',
      };
    }
  }

  private async backgroundAddToScoutQueue(data: ScoutData, jobId: string): Promise<void> {
    try {
      // Reset connection retries for the background attempt
      this.connectionRetries = 0;

      // Clone the data and set the provided job ID
      const clonedData = { ...data, jobId };

      // Attempt to add the job to the queue
      await this.attemptAddToScoutQueue(clonedData);

      this.logger.log(`✅ Background scout job successfully added with ID ${jobId}`);
    } catch (error: any) {
      this.logger.error(
        `❌ Background scout job addition failed for ID ${jobId}: ${error.message}`,
        error.stack,
      );
    }
  }

  private async attemptAddToQueue(data: UploadData): Promise<QueueJobResult> {
    try {
      this.logger.log(
        `🚀 Adding upload job to queue for jobId: ${data.jobId}, files: ${data.files.length}`,
      );

      // Add timeout to queue.add operation with increased timeout
      const addJobPromise = this.fileUploadQueue.add(PROCESSOR_NAMES.PROCESS_UPLOADS, data, {
        attempts: 5, // Increased from 3 to 5
        backoff: {
          type: 'exponential',
          delay: 5000, // Increased from 2000 to 5000
        },
        removeOnComplete: false,
        removeOnFail: false,
        timeout: 600000, // 10 minutes job timeout (increased from 5 minutes)
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Queue add operation timed out after ${
                  this.QUEUE_OPERATION_TIMEOUT / 1000
                } seconds`,
              ),
            ),
          this.QUEUE_OPERATION_TIMEOUT,
        );
      });

      // Add a catch to the addJobPromise to get better error information
      const enhancedAddJobPromise = addJobPromise.catch((err) => {
        this.logger.error(`❌ Error in queue.add operation: ${err.message}`, err.stack);

        // Check if this is a Redis connectivity issue
        if (
          err.message?.includes('ECONNREFUSED') ||
          err.message?.includes('timed out') ||
          err.message?.includes('connection error') ||
          err.message?.includes('maxRetriesPerRequest')
        ) {
          this.logger.error(`❌ Redis connection issue detected: ${err.message}`);

          // Log more detailed information for specific errors
          if (err.message?.includes('maxRetriesPerRequest')) {
            this.logger.error(
              '❌ Redis connection hitting maxRetriesPerRequest limit - this is a common issue with Bull and TLS Redis',
            );
          }
        }

        throw err; // Re-throw to be caught by the outer catch
      });

      const job = (await Promise.race([enhancedAddJobPromise, timeoutPromise])) as Job;

      this.logger.log(`✨ Created Bull job with ID: ${job.id} (${typeof job.id})`);

      // Verify job was added correctly with timeout
      const verifyPromise = this.fileUploadQueue.getJob(job.id);
      const verifyTimeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Job verification timed out after ${this.QUEUE_VERIFY_TIMEOUT / 1000} seconds`,
              ),
            ),
          this.QUEUE_VERIFY_TIMEOUT,
        );
      });

      // Add a catch to the verifyPromise to get better error information
      const enhancedVerifyPromise = verifyPromise.catch((err) => {
        this.logger.error(`❌ Error in job verification: ${err.message}`, err.stack);
        throw err; // Re-throw to be caught by the outer catch
      });

      const addedJob = (await Promise.race([
        enhancedVerifyPromise,
        verifyTimeoutPromise,
      ])) as Job | null;

      if (addedJob) {
        this.logger.log(`🔍 Successfully verified job ${job.id} exists in queue`);
      } else {
        this.logger.error(`❌ Failed to verify job ${job.id} in queue`);
        throw new Error(`Job ${job.id} was not properly added to queue`);
      }

      return {
        jobId: job.id.toString(),
        status: 'queued',
      };
    } catch (error: any) {
      this.logger.error(`💥 Error adding job to upload queue: ${error.message}`, error.stack);

      // Check if this is a Redis connectivity issue
      if (
        error.message?.includes('ECONNREFUSED') ||
        error.message?.includes('timed out') ||
        error.message?.includes('connection error') ||
        error.message?.includes('maxRetriesPerRequest')
      ) {
        // Trigger a Redis health check
        await this.checkRedisHealth();

        // Log more detailed information for specific errors
        if (error.message?.includes('maxRetriesPerRequest')) {
          this.logger.error(
            '❌ Redis connection hitting maxRetriesPerRequest limit - this is a common issue with Bull and TLS Redis',
          );
        } else if (error.message?.includes('ECONNREFUSED')) {
          this.logger.error(
            '❌ Redis connection refused - check if Redis server is running and accessible',
          );
        } else if (error.message?.includes('timed out')) {
          this.logger.error(
            '❌ Redis connection timed out - check network latency or firewall settings',
          );
        }
      }

      // Log queue state for debugging
      try {
        // Add timeout to getJobCounts operation
        const queueStatePromise = this.fileUploadQueue.getJobCounts();
        const queueStateTimeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('getJobCounts timed out after 15 seconds')), 15000);
        });

        const queueState = await Promise.race([queueStatePromise, queueStateTimeoutPromise]);
        this.logger.log(`📊 Queue state: ${JSON.stringify(queueState)}`);
      } catch (queueError: any) {
        this.logger.error(`❌ Failed to get queue state: ${queueError.message}`);
      }

      if (this.connectionRetries < this.MAX_RETRIES) {
        this.connectionRetries++;
        // Exponential backoff with jitter
        const baseDelay = Math.min(Math.pow(2, this.connectionRetries) * 1000, 30000);
        const jitter = Math.floor(Math.random() * 1000);
        const delay = baseDelay + jitter;

        this.logger.log(
          `🔄 Retrying job addition ${this.connectionRetries}/${this.MAX_RETRIES} after ${delay}ms`,
        );

        await new Promise((resolve) => setTimeout(resolve, delay));
        return this.attemptAddToQueue(data);
      }

      throw new Error(
        `Failed to add job to queue after ${this.MAX_RETRIES} retries: ${error.message}`,
      );
    }
  }

  async getJobStatus(jobId: string): Promise<JobStatus> {
    try {
      this.logger.log(`🔍 Fetching status for job ID: ${jobId}`);

      // Check if this is a background job ID
      if (jobId.startsWith('job-')) {
        // For background job IDs that haven't been processed yet or are still in progress
        this.logger.log(`⏳ Background job ${jobId} status check`);
        return {
          jobId,
          status: 'pending',
          progress: 0,
          message: 'Job is being processed',
        };
      }

      // Try to get job from queue with timeout
      const getJobPromise = this.fileUploadQueue.getJob(jobId);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Get job operation timed out after ${this.QUEUE_VERIFY_TIMEOUT / 1000} seconds`,
              ),
            ),
          this.QUEUE_VERIFY_TIMEOUT,
        );
      });

      const job = (await Promise.race([getJobPromise, timeoutPromise])) as Job;

      if (!job) {
        this.logger.log(`❌ Job ${jobId} not found in queue`);

        // Check active jobs to see what's available
        const activeJobs = await this.fileUploadQueue.getActive();
        const waitingJobs = await this.fileUploadQueue.getWaiting();
        this.logger.log(
          `📊 Active jobs: ${activeJobs.length}, Waiting jobs: ${waitingJobs.length}`,
        );

        if (activeJobs.length > 0) {
          this.logger.log(`📋 Active job IDs: ${activeJobs.map((j) => j.id)}`);
        }

        return { status: 'not_found', jobId, progress: 0 };
      }

      const state = await job.getState();
      const progress = await job.progress();

      this.logger.log(`📈 Job ${jobId} found with state: ${state}, progress: ${progress}`);

      // Log the return value for debugging
      if (state === 'completed') {
        this.logger.log(`✅ Job ${jobId} completed with return value:`, {
          hasReturnValue: !!job.returnvalue,
          returnValueKeys: job.returnvalue ? Object.keys(job.returnvalue) : [],
          successCount: job.returnvalue?.successCount,
          jobId: job.returnvalue?.jobId,
        });
      }

      // Extract the actual job entity ID from the job data or result
      // Priority: result.jobId -> data.jobId -> worker job ID (fallback)
      const actualJobId = job.returnvalue?.jobId || job.data?.jobId || job.id.toString();

      // Clean the job data to remove large buffer data
      const cleanedData = job.data
        ? {
            ...job.data,
            files:
              job.data.files?.map((file: any) => ({
                ...file,
                buffer: undefined, // Remove buffer data
                stream: undefined, // Remove stream data
              })) || [],
          }
        : job.data;

      return {
        id: job.id.toString(), // Keep worker job ID for reference
        jobId: actualJobId, // Use actual job entity ID
        status: state,
        progress: state === 'completed' ? 100 : progress, // Ensure completed jobs show 100% progress
        data: cleanedData,
        result: job.returnvalue,
      };
    } catch (error) {
      this.logger.error(`💥 Error getting job status for ${jobId}:`, error);
      return { status: 'error', jobId, progress: 0, message: 'Error retrieving job status' };
    }
  }

  async cancelJob(
    jobId: string,
    queueType: 'upload' | 'scout' = 'upload',
  ): Promise<{ success: boolean; message?: string }> {
    try {
      // Check if this is a background job ID
      if (jobId.startsWith('job-')) {
        // For background job IDs, we can just return success
        this.logger.log(`⏳ Cancelling background job ${jobId}`);
        return {
          success: true,
          message: 'Background job cancelled',
        };
      }
      const queue = queueType === 'upload' ? this.fileUploadQueue : this.scoutQueue;
      const getJobPromise = queue.getJob(jobId);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Get job operation timed out after ${this.QUEUE_VERIFY_TIMEOUT / 1000} seconds`,
              ),
            ),
          this.QUEUE_VERIFY_TIMEOUT,
        );
      });

      const job = (await Promise.race([getJobPromise, timeoutPromise])) as Job;

      if (!job) {
        return {
          success: false,
          message: 'Job not found',
        };
      }

      // Check if job can be cancelled
      const state = await job.getState();
      if (state !== 'active' && state !== 'waiting' && state !== 'delayed' && state !== 'paused') {
        return {
          success: false,
          message: `Cannot cancel job in '${state}' state`,
        };
      }

      // Remove the job from the queue
      await job.remove();

      return {
        success: true,
        message: 'Job cancelled successfully',
      };
    } catch (error) {
      this.logger.error('💥 Error cancelling job:', error);
      throw new Error('Failed to cancel job');
    }
  }

  private async attemptAddToScoutQueue(data: ScoutData): Promise<QueueJobResult> {
    try {
      this.logger.log(
        `🚀 Adding scout job to queue for jobId: ${data.jobId}, count: ${data.count}`,
      );

      // Add timeout to queue.add operation with increased timeout
      const addJobPromise = this.scoutQueue.add(PROCESSOR_NAMES.SCOUT_PROFILES, data, {
        attempts: 5, // Increased from 3 to 5
        backoff: {
          type: 'exponential',
          delay: 5000, // Increased from 2000 to 5000
        },
        removeOnComplete: false,
        removeOnFail: false,
        timeout: 600000, // 10 minutes job timeout (increased from 5 minutes)
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Queue add operation timed out after ${
                  this.QUEUE_OPERATION_TIMEOUT / 1000
                } seconds`,
              ),
            ),
          this.QUEUE_OPERATION_TIMEOUT,
        );
      });

      // Add a catch to the addJobPromise to get better error information
      const enhancedAddJobPromise = addJobPromise.catch((err) => {
        this.logger.error(`❌ Error in queue.add operation: ${err.message}`, err.stack);

        // Check if this is a Redis connectivity issue
        if (
          err.message?.includes('ECONNREFUSED') ||
          err.message?.includes('timed out') ||
          err.message?.includes('connection error') ||
          err.message?.includes('maxRetriesPerRequest')
        ) {
          this.logger.error(`❌ Redis connection issue detected: ${err.message}`);

          // Log more detailed information for specific errors
          if (err.message?.includes('maxRetriesPerRequest')) {
            this.logger.error(
              '❌ Redis connection hitting maxRetriesPerRequest limit - this is a common issue with Bull and TLS Redis',
            );
          }
        }

        throw err; // Re-throw to be caught by the outer catch
      });

      const job = (await Promise.race([enhancedAddJobPromise, timeoutPromise])) as Job;

      this.logger.log(`✨ Created Bull job with ID: ${job.id} (${typeof job.id})`);

      // Verify job was added correctly with timeout
      const verifyPromise = this.scoutQueue.getJob(job.id);
      const verifyTimeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Job verification timed out after ${this.QUEUE_VERIFY_TIMEOUT / 1000} seconds`,
              ),
            ),
          this.QUEUE_VERIFY_TIMEOUT,
        );
      });

      // Add a catch to the verifyPromise to get better error information
      const enhancedVerifyPromise = verifyPromise.catch((err) => {
        this.logger.error(`❌ Error in job verification: ${err.message}`, err.stack);
        throw err; // Re-throw to be caught by the outer catch
      });

      const addedJob = (await Promise.race([
        enhancedVerifyPromise,
        verifyTimeoutPromise,
      ])) as Job | null;

      if (addedJob) {
        this.logger.log(`🔍 Successfully verified job ${job.id} exists in queue`);
      } else {
        this.logger.error(`❌ Failed to verify job ${job.id} in queue`);
        throw new Error(`Job ${job.id} was not properly added to queue`);
      }

      return {
        jobId: job.id.toString(),
        status: 'queued',
      };
    } catch (error: any) {
      this.logger.error(`💥 Error adding job to scout queue: ${error.message}`, error.stack);

      // Check if this is a Redis connectivity issue
      if (
        error.message?.includes('ECONNREFUSED') ||
        error.message?.includes('timed out') ||
        error.message?.includes('connection error') ||
        error.message?.includes('maxRetriesPerRequest')
      ) {
        // Trigger a Redis health check
        await this.checkRedisHealth();

        // Log more detailed information for specific errors
        if (error.message?.includes('maxRetriesPerRequest')) {
          this.logger.error(
            '❌ Redis connection hitting maxRetriesPerRequest limit - this is a common issue with Bull and TLS Redis',
          );
        } else if (error.message?.includes('ECONNREFUSED')) {
          this.logger.error(
            '❌ Redis connection refused - check if Redis server is running and accessible',
          );
        } else if (error.message?.includes('timed out')) {
          this.logger.error(
            '❌ Redis connection timed out - check network latency or firewall settings',
          );
        }
      }

      // Log queue state for debugging
      try {
        // Add timeout to getJobCounts operation
        const queueStatePromise = this.scoutQueue.getJobCounts();
        const queueStateTimeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('getJobCounts timed out after 15 seconds')), 15000);
        });

        const queueState = await Promise.race([queueStatePromise, queueStateTimeoutPromise]);
        this.logger.log(`📊 Queue state: ${JSON.stringify(queueState)}`);
      } catch (queueError: any) {
        this.logger.error(`❌ Failed to get queue state: ${queueError.message}`);
      }

      if (this.connectionRetries < this.MAX_RETRIES) {
        this.connectionRetries++;
        // Exponential backoff with jitter
        const baseDelay = Math.min(Math.pow(2, this.connectionRetries) * 1000, 30000);
        const jitter = Math.floor(Math.random() * 1000);
        const delay = baseDelay + jitter;

        this.logger.log(
          `🔄 Retrying job addition ${this.connectionRetries}/${this.MAX_RETRIES} after ${delay}ms`,
        );

        await new Promise((resolve) => setTimeout(resolve, delay));
        return this.attemptAddToScoutQueue(data);
      }

      throw new Error(
        `Failed to add job to queue after ${this.MAX_RETRIES} retries: ${error.message}`,
      );
    }
  }

  async getScoutJobStatus(jobId: string): Promise<JobStatus> {
    try {
      this.logger.log(`🔍 Fetching status for scout job ID: ${jobId}`);

      // Check if this is a background job ID
      if (jobId.startsWith('job-')) {
        // For background job IDs that haven't been processed yet or are still in progress
        this.logger.log(`⏳ Background scout job ${jobId} status check`);
        return {
          jobId,
          status: 'pending',
          progress: 0,
          message: 'Scout job is being processed',
        };
      }

      // Try to get job from queue with timeout
      const getJobPromise = this.scoutQueue.getJob(jobId);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Get job operation timed out after ${this.QUEUE_VERIFY_TIMEOUT / 1000} seconds`,
              ),
            ),
          this.QUEUE_VERIFY_TIMEOUT,
        );
      });

      const job = (await Promise.race([getJobPromise, timeoutPromise])) as Job;

      if (!job) {
        this.logger.log(`❌ Scout job ${jobId} not found in queue`);

        // Check active jobs to see what's available
        const activeJobs = await this.scoutQueue.getActive();
        const waitingJobs = await this.scoutQueue.getWaiting();
        this.logger.log(
          `📊 Active scout jobs: ${activeJobs.length}, Waiting scout jobs: ${waitingJobs.length}`,
        );

        if (activeJobs.length > 0) {
          this.logger.log(`📋 Active scout job IDs: ${activeJobs.map((j) => j.id)}`);
        }

        return { status: 'not_found', jobId, progress: 0 };
      }

      const state = await job.getState();
      const progress = await job.progress();

      this.logger.log(`📈 Scout job ${jobId} found with state: ${state}, progress: ${progress}`);

      // Extract the actual job entity ID from the job data or result
      // Priority: result.jobId -> data.jobId -> worker job ID (fallback)
      const actualJobId = job.returnvalue?.jobId || job.data?.jobId || job.id.toString();

      return {
        id: job.id.toString(), // Keep worker job ID for reference
        jobId: actualJobId, // Use actual job entity ID
        status: state,
        progress: state === 'completed' ? 100 : progress, // Ensure completed jobs show 100% progress
        data: job.data,
        result: job.returnvalue,
      };
    } catch (error) {
      this.logger.error(`💥 Error getting scout job status for ${jobId}:`, error);
      return { status: 'error', jobId, progress: 0, message: 'Error retrieving job status' };
    }
  }

  async addMatchRankTask(
    jobId: string,
    clientId: string,
    options: { topTierThreshold?: number; secondTierThreshold?: number } = {},
  ) {
    // Check Redis health before attempting to add to queue
    const isHealthy = await this.checkRedisHealth();
    if (!isHealthy) {
      throw new Error('Queue service is currently unavailable (Redis connection issue)');
    }

    try {
      this.logger.log(
        `🚀 Adding match rank job to queue for jobId: ${jobId}, clientId: ${clientId}`,
      );

      // Add timeout to queue.add operation with increased timeout
      const addJobPromise = this.matchRankQueue.add(
        PROCESSOR_NAMES.PROCESS_MATCH_RANK,
        {
          jobId,
          clientId,
          topTierThreshold: options.topTierThreshold,
          secondTierThreshold: options.secondTierThreshold,
        },
        {
          attempts: 5, // Increased from 3 to 5 for better reliability
          backoff: {
            type: 'exponential',
            delay: 2000, // Increased from 1000 to 2000 for better spacing between retries
          },
          removeOnComplete: false, // Keep completed jobs for debugging
          removeOnFail: false, // Keep failed jobs for debugging
        },
      );

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Queue add operation timed out after ${
                  this.QUEUE_OPERATION_TIMEOUT / 1000
                } seconds`,
              ),
            ),
          this.QUEUE_OPERATION_TIMEOUT,
        );
      });

      // Add a catch to the addJobPromise to get better error information
      const enhancedAddJobPromise = addJobPromise.catch((err) => {
        this.logger.error(`❌ Error in queue.add operation: ${err.message}`, err.stack);
        throw err; // Re-throw to be caught by the outer catch
      });

      const job = (await Promise.race([enhancedAddJobPromise, timeoutPromise])) as Job;

      this.logger.log(`✨ Created Bull job with ID: ${job.id} (${typeof job.id})`);

      return job;
    } catch (error: any) {
      this.logger.error(`💥 Error adding job to match rank queue: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getMatchRankStatus(jobId: string): Promise<JobStatus> {
    try {
      this.logger.log(`🔍 Fetching status for match rank job ID: ${jobId}`);

      // Check if this is a background job ID
      if (jobId.startsWith('job-')) {
        // For background job IDs that haven't been processed yet or are still in progress
        this.logger.log(`⏳ Background match rank job ${jobId} status check`);
        return {
          jobId,
          status: 'pending',
          progress: 0,
          message: 'Match rank job is being processed',
        };
      }

      // Try to get job from queue with timeout
      const getJobPromise = this.matchRankQueue.getJob(jobId);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Get job operation timed out after ${this.QUEUE_VERIFY_TIMEOUT / 1000} seconds`,
              ),
            ),
          this.QUEUE_VERIFY_TIMEOUT,
        );
      });

      const job = (await Promise.race([getJobPromise, timeoutPromise])) as Job;

      if (!job) {
        this.logger.log(`❌ Match rank job ${jobId} not found in queue`);

        // Check active jobs to see what's available
        const activeJobs = await this.matchRankQueue.getActive();
        const waitingJobs = await this.matchRankQueue.getWaiting();
        this.logger.log(
          `📊 Active match rank jobs: ${activeJobs.length}, Waiting match rank jobs: ${waitingJobs.length}`,
        );

        if (activeJobs.length > 0) {
          this.logger.log(`📋 Active match rank job IDs: ${activeJobs.map((j) => j.id)}`);
        }

        return { status: 'not_found', jobId, progress: 0 };
      }

      const state = await job.getState();
      const progress = await job.progress();

      this.logger.log(
        `📈 Match rank job ${jobId} found with state: ${state}, progress: ${progress}`,
      );

      return {
        jobId: job.id.toString(),
        status: state,
        progress,
        data: job.data,
        result: job.returnvalue,
      };
    } catch (error) {
      this.logger.error(`💥 Error getting match rank job status for ${jobId}:`, error);
      return { status: 'error', jobId, progress: 0, message: 'Error retrieving job status' };
    }
  }

  async cancelMatchRankJob(jobId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Check if the jobId is a queue job ID (numeric) or a job entity ID (UUID)
      const isQueueJobId = /^\d+$/.test(jobId);

      if (isQueueJobId) {
        // If it's a queue job ID, get the job directly by its ID
        this.logger.log(`Cancelling match rank queue job with ID: ${jobId}`);

        // Get the job directly from the queue by its ID
        const job = await this.matchRankQueue.getJob(jobId);

        if (!job) {
          return {
            success: false,
            message: `Queue job with ID ${jobId} not found`,
          };
        }

        // Remove the job from the queue
        await job.remove();

        return {
          success: true,
          message: `Match rank job ${jobId} cancelled successfully`,
        };
      } else {
        // If it's a job entity ID (UUID), find the job in the queue by its data.jobId
        // Get all jobs from the queue
        const jobs = await this.matchRankQueue.getJobs(['active', 'waiting', 'delayed']);

        // Find the job with the matching jobId in the data
        const job = jobs.find((job) => job.data.jobId === jobId);

        if (!job) {
          this.logger.warn(
            `Job with jobId ${jobId} not found in match-rank queue or already completed/failed`,
          );
          return {
            success: false,
            message: 'Job not found or already completed/failed',
          };
        }

        // Remove the job from the queue
        await job.remove();

        return {
          success: true,
          message: `Match rank job ${jobId} cancelled successfully`,
        };
      }
    } catch (error: any) {
      this.logger.error(`Error cancelling match rank job ${jobId}:`, error);
      return {
        success: false,
        message: `Error cancelling job: ${error.message}`,
      };
    }
  }

  // ==============================
  // Video JD Queue Operations
  // ==============================

  async addVideoJDMonitoringTask(data: VideoJDData): Promise<QueueJobResult> {
    try {
      this.logger.log(`🎬 Adding Video JD monitoring task: ${data.videoJDId}`);

      const job = await this.videoJDQueue.add(PROCESSOR_NAMES.MONITOR_VIDEO_JD, data, {
        attempts: 1, // Single attempt per monitoring job
        removeOnComplete: false,
        removeOnFail: false,
        delay: 30000, // Start monitoring after 30 seconds
      });

      this.logger.log(`✅ Video JD monitoring task added with Bull job ID: ${job.id}`);

      return {
        jobId: job.id.toString(),
        status: 'queued',
      };
    } catch (error: any) {
      this.logger.error(`❌ Failed to add Video JD monitoring task: ${error.message}`, error.stack);
      throw new Error(`Failed to add Video JD monitoring task: ${error.message}`);
    }
  }

  async getVideoJDStatus(jobId: string): Promise<JobStatus | null> {
    try {
      const job = await this.videoJDQueue.getJob(jobId);

      if (!job) {
        this.logger.warn(`Video JD job ${jobId} not found`);
        return {
          jobId,
          status: 'not_found',
          progress: 0,
          message: 'Job not found',
        };
      }

      let status = 'unknown';
      let message = '';

      if (job.finishedOn) {
        if (job.failedReason) {
          status = 'failed';
          message = job.failedReason;
        } else {
          status = 'completed';
          message = 'Video JD monitoring completed';
        }
      } else if (job.processedOn) {
        status = 'active';
        message = 'Monitoring Video JD status';
      } else {
        status = 'queued';
        message = 'Waiting to monitor Video JD status';
      }

      // Extract the actual job entity ID from the job data or result
      // Priority: result.jobId -> data.jobId -> worker job ID (fallback)
      const actualJobId = job.returnvalue?.jobId || job.data?.jobId || jobId;

      return {
        id: job.id.toString(), // Keep worker job ID for reference
        jobId: actualJobId, // Use actual job entity ID
        status,
        progress: status === 'completed' ? 100 : job.progress(), // Ensure completed jobs show 100% progress
        data: job.data,
        result: job.returnvalue,
        message,
      };
    } catch (error: any) {
      this.logger.error(`Error getting Video JD job status ${jobId}:`, error);
      return {
        jobId,
        status: 'error',
        progress: 0,
        message: error.message,
      };
    }
  }

  async cancelVideoJDJob(jobId: string): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`🛑 Attempting to cancel Video JD job: ${jobId}`);

      const job = await this.videoJDQueue.getJob(jobId);

      if (!job) {
        this.logger.warn(`Video JD job ${jobId} not found for cancellation`);
        return {
          success: false,
          message: 'Job not found or already completed/failed',
        };
      }

      // Remove the job from the queue
      await job.remove();

      return {
        success: true,
        message: `Video JD monitoring job ${jobId} cancelled successfully`,
      };
    } catch (error: any) {
      this.logger.error(`Error cancelling Video JD job ${jobId}:`, error);
      return {
        success: false,
        message: `Error cancelling job: ${error.message}`,
      };
    }
  }
}
