import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissingColumns1754373000000 implements MigrationInterface {
  name = 'AddMissingColumns1754373000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add missing additionalRoles column to user_roles table
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD "additionalRoles" text array DEFAULT '{}'`,
    );

    // Add missing referralPartnerId column to companies table
    await queryRunner.query(`ALTER TABLE "companies" ADD "referralPartnerId" uuid`);

    // Add missing columns to referrals table
    await queryRunner.query(`ALTER TABLE "referrals" ADD "stripeTransferId" character varying`);
    await queryRunner.query(`ALTER TABLE "referrals" ADD "stripePayoutId" character varying`);

    // Add missing columns to job_seekers table
    await queryRunner.query(`ALTER TABLE "job_seekers" ADD "referralPartnerId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "job_seekers" ADD "isReferralPartner" boolean DEFAULT false`,
    );

    // Add missing jobSeekerId column to referral_partners table
    await queryRunner.query(`ALTER TABLE "referral_partners" ADD "jobSeekerId" character varying`);

    // Add missing clientId column to bounty_configurations table
    await queryRunner.query(`ALTER TABLE "bounty_configurations" ADD "clientId" character varying`);

    // Fix email_history table column names (rename camelCase to snake_case)
    await queryRunner.query(`ALTER TABLE "email_history" RENAME COLUMN "sentAt" TO "sent_at"`);
    await queryRunner.query(
      `ALTER TABLE "email_history" RENAME COLUMN "createdAt" TO "created_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "email_history" RENAME COLUMN "updatedAt" TO "updated_at"`,
    );
    await queryRunner.query(`ALTER TABLE "email_history" RENAME COLUMN "openedAt" TO "opened_at"`);
    await queryRunner.query(
      `ALTER TABLE "email_history" RENAME COLUMN "clickedAt" TO "clicked_at"`,
    );

    // Add missing columns to email_history table
    await queryRunner.query(
      `ALTER TABLE "email_history" ADD "template_id" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "email_history" ADD "template_name" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "email_history" ADD "recipient_email" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(`ALTER TABLE "email_history" ADD "content" text`);
    await queryRunner.query(`ALTER TABLE "email_history" ADD "sent_by" character varying`);
    await queryRunner.query(`ALTER TABLE "email_history" ADD "client_id" character varying`);
    await queryRunner.query(`ALTER TABLE "email_history" ADD "external_id" character varying`);
    await queryRunner.query(
      `ALTER TABLE "email_history" ADD "delivered_at" timestamp without time zone`,
    );

    // Create indexes for performance
    await queryRunner.query(
      `CREATE INDEX "idx_user_roles_additional_roles" ON "user_roles" ("additionalRoles")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_companies_referral_partner" ON "companies" ("referralPartnerId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_job_seekers_referral_partner" ON "job_seekers" ("referralPartnerId")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_email_history_template" ON "email_history" ("template_id")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_email_history_recipient" ON "email_history" ("recipient_email")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "idx_email_history_recipient"`);
    await queryRunner.query(`DROP INDEX "idx_email_history_template"`);
    await queryRunner.query(`DROP INDEX "idx_job_seekers_referral_partner"`);
    await queryRunner.query(`DROP INDEX "idx_companies_referral_partner"`);
    await queryRunner.query(`DROP INDEX "idx_user_roles_additional_roles"`);

    // Revert email_history changes
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "delivered_at"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "external_id"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "client_id"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "sent_by"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "content"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "recipient_email"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "template_name"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN "template_id"`);

    await queryRunner.query(
      `ALTER TABLE "email_history" RENAME COLUMN "clicked_at" TO "clickedAt"`,
    );
    await queryRunner.query(`ALTER TABLE "email_history" RENAME COLUMN "opened_at" TO "openedAt"`);
    await queryRunner.query(
      `ALTER TABLE "email_history" RENAME COLUMN "updated_at" TO "updatedAt"`,
    );
    await queryRunner.query(
      `ALTER TABLE "email_history" RENAME COLUMN "created_at" TO "createdAt"`,
    );
    await queryRunner.query(`ALTER TABLE "email_history" RENAME COLUMN "sent_at" TO "sentAt"`);

    // Revert other changes
    await queryRunner.query(`ALTER TABLE "bounty_configurations" DROP COLUMN "clientId"`);
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN "jobSeekerId"`);
    await queryRunner.query(`ALTER TABLE "job_seekers" DROP COLUMN "isReferralPartner"`);
    await queryRunner.query(`ALTER TABLE "job_seekers" DROP COLUMN "referralPartnerId"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN "stripePayoutId"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN "stripeTransferId"`);
    await queryRunner.query(`ALTER TABLE "companies" DROP COLUMN "referralPartnerId"`);
    await queryRunner.query(`ALTER TABLE "user_roles" DROP COLUMN "additionalRoles"`);
  }
}
