import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveAllUnusedColumns1754378164397 implements MigrationInterface {
  name = 'RemoveAllUnusedColumns1754378164397';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🧹 Removing all unused columns from production database...');

    // Removing unused columns from referrals
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN IF EXISTS "referrerId"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN IF EXISTS "paidAt"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN IF EXISTS "referralPartner"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN IF EXISTS "referredEmail"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN IF EXISTS "stripePaymentId"`);
    await queryRunner.query(`ALTER TABLE "referrals" DROP COLUMN IF EXISTS "bountyType"`);

    // Removing unused columns from referral_partners
    await queryRunner.query(
      `ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "totalReferrals"`,
    );
    await queryRunner.query(
      `ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "successfulReferrals"`,
    );
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "bankDetails"`);
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "companyName"`);
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "email"`);
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "name"`);
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "status"`);
    await queryRunner.query(`ALTER TABLE "referral_partners" DROP COLUMN IF EXISTS "role"`);

    // Removing unused columns from jobs
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN IF EXISTS "companyId"`);
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN IF EXISTS "salary"`);
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN IF EXISTS "remote"`);
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN IF EXISTS "postedAt"`);
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN IF EXISTS "expiresAt"`);
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN IF EXISTS "description"`);
    await queryRunner.query(`ALTER TABLE "jobs" DROP COLUMN IF EXISTS "title"`);

    // Removing unused columns from notifications
    await queryRunner.query(`ALTER TABLE "notifications" DROP COLUMN IF EXISTS "metadata"`);
    await queryRunner.query(`ALTER TABLE "notifications" DROP COLUMN IF EXISTS "isRead"`);
    await queryRunner.query(`ALTER TABLE "notifications" DROP COLUMN IF EXISTS "readAt"`);

    // Removing unused columns from bounty_configurations
    await queryRunner.query(
      `ALTER TABLE "bounty_configurations" DROP COLUMN IF EXISTS "bountyValue"`,
    );
    await queryRunner.query(
      `ALTER TABLE "bounty_configurations" DROP COLUMN IF EXISTS "conditions"`,
    );
    await queryRunner.query(`ALTER TABLE "bounty_configurations" DROP COLUMN IF EXISTS "currency"`);

    // Removing unused columns from candidates
    await queryRunner.query(`ALTER TABLE "candidates" DROP COLUMN IF EXISTS "companyId"`);
    await queryRunner.query(`ALTER TABLE "candidates" DROP COLUMN IF EXISTS "education"`);
    await queryRunner.query(`ALTER TABLE "candidates" DROP COLUMN IF EXISTS "aiInsights"`);
    await queryRunner.query(`ALTER TABLE "candidates" DROP COLUMN IF EXISTS "matchScore"`);
    await queryRunner.query(`ALTER TABLE "candidates" DROP COLUMN IF EXISTS "isActive"`);

    // Removing unused columns from video_responses
    await queryRunner.query(`ALTER TABLE "video_responses" DROP COLUMN IF EXISTS "aiAnalysis"`);
    await queryRunner.query(`ALTER TABLE "video_responses" DROP COLUMN IF EXISTS "score"`);
    await queryRunner.query(`ALTER TABLE "video_responses" DROP COLUMN IF EXISTS "transcript"`);

    // Removing unused columns from feedback
    await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN IF EXISTS "category"`);

    // Removing unused columns from email_history
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "attachments"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "to"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "from"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "body"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "type"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "cc"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "bcc"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "replyTo"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "htmlBody"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "messageId"`);
    await queryRunner.query(`ALTER TABLE "email_history" DROP COLUMN IF EXISTS "campaignId"`);

    console.log('✅ Cleanup completed successfully!');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // WARNING: This migration cannot be reverted as it deletes columns and their data
    // The down migration is provided for completeness but should not be used
    console.log('⚠️  WARNING: This migration cannot be fully reverted - data has been deleted');

    // Note: Cannot restore data for referrals.referrerId
    // Note: Cannot restore data for referrals.paidAt
    // Note: Cannot restore data for referrals.referralPartner
    // Note: Cannot restore data for referrals.referredEmail
    // Note: Cannot restore data for referrals.stripePaymentId
    // Note: Cannot restore data for referrals.bountyType
    // Note: Cannot restore data for referral_partners.totalReferrals
    // Note: Cannot restore data for referral_partners.successfulReferrals
    // Note: Cannot restore data for referral_partners.bankDetails
    // Note: Cannot restore data for referral_partners.companyName
    // Note: Cannot restore data for referral_partners.email
    // Note: Cannot restore data for referral_partners.name
    // Note: Cannot restore data for referral_partners.status
    // Note: Cannot restore data for referral_partners.role
    // Note: Cannot restore data for jobs.companyId
    // Note: Cannot restore data for jobs.salary
    // Note: Cannot restore data for jobs.remote
    // Note: Cannot restore data for jobs.postedAt
    // Note: Cannot restore data for jobs.expiresAt
    // Note: Cannot restore data for jobs.description
    // Note: Cannot restore data for jobs.title
    // Note: Cannot restore data for notifications.metadata
    // Note: Cannot restore data for notifications.isRead
    // Note: Cannot restore data for notifications.readAt
    // Note: Cannot restore data for bounty_configurations.bountyValue
    // Note: Cannot restore data for bounty_configurations.conditions
    // Note: Cannot restore data for bounty_configurations.currency
    // Note: Cannot restore data for candidates.companyId
    // Note: Cannot restore data for candidates.education
    // Note: Cannot restore data for candidates.aiInsights
    // Note: Cannot restore data for candidates.matchScore
    // Note: Cannot restore data for candidates.isActive
    // Note: Cannot restore data for video_responses.aiAnalysis
    // Note: Cannot restore data for video_responses.score
    // Note: Cannot restore data for video_responses.transcript
    // Note: Cannot restore data for feedback.category
    // Note: Cannot restore data for email_history.attachments
    // Note: Cannot restore data for email_history.to
    // Note: Cannot restore data for email_history.from
    // Note: Cannot restore data for email_history.body
    // Note: Cannot restore data for email_history.type
    // Note: Cannot restore data for email_history.cc
    // Note: Cannot restore data for email_history.bcc
    // Note: Cannot restore data for email_history.replyTo
    // Note: Cannot restore data for email_history.htmlBody
    // Note: Cannot restore data for email_history.messageId
    // Note: Cannot restore data for email_history.campaignId
  }
}
