// Enhanced Migration Configuration with Better Error Handling
import * as path from 'path';
import { register } from 'tsconfig-paths';
import { config } from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';
import { getDatabaseConfig, getEntityPaths, getMigrationPaths } from './database.config';
import * as entities from '../entities';

// Register TypeScript path aliases
const baseUrl = path.join(__dirname, '../..');
register({
  baseUrl,
  paths: {
    '@/*': ['src/*'],
    '@modules/*': ['src/modules/*'],
    '@shared/*': ['src/shared/*'],
  },
});

// Load environment variables
config();

// Determine if we're in production
const isProduction = process.env.NODE_ENV === 'production';

// Enhanced logging for debugging
const enhancedLogging = process.env.MIGRATION_DEBUG === 'true';

// Create enhanced DataSource configuration
const dataSourceConfig: DataSourceOptions = {
  ...getDatabaseConfig(),
  entities: isProduction ? getEntityPaths(__dirname) : Object.values(entities),
  migrations: getMigrationPaths(__dirname),
  // Enhanced logging configuration
  logging: enhancedLogging ? 'all' : ['error', 'warn', 'migration'],
  logger: 'advanced-console',
  // Additional options for better error handling
  connectTimeoutMS: 30000,
  extra: {
    // Increase statement timeout for migrations
    statement_timeout: '60000', // 60 seconds
    // Enable query logging in debug mode
    ...(enhancedLogging && {
      log_statement: 'all',
      log_duration: true,
    }),
  },
};

// Create DataSource with error handling
const AppDataSource = new DataSource(dataSourceConfig);

// Only initialize if not in migration generation context
if (process.argv.some((arg) => arg.includes('migration:generate'))) {
  // Skip initialization for migration generation
  if (enhancedLogging) {
    console.log('📋 Migration generation mode - skipping auto-initialization');
  }
} else {
  // Initialize for other operations
  AppDataSource.initialize()
    .then(() => {
      if (enhancedLogging) {
        console.log('✅ Data Source has been initialized successfully');
        console.log(`📁 Entity paths: ${JSON.stringify(dataSourceConfig.entities, null, 2)}`);
        console.log(`📁 Migration paths: ${JSON.stringify(dataSourceConfig.migrations, null, 2)}`);
      }
    })
    .catch((err) => {
      console.error('❌ Error during Data Source initialization:', err);
      console.error(
        '📋 Configuration used:',
        JSON.stringify(
          {
            ...dataSourceConfig,
            password: '***', // Hide sensitive info
          },
          null,
          2,
        ),
      );
      // Don't exit in migration context
      if (!process.argv.some((arg) => arg.includes('migration'))) {
        process.exit(1);
      }
    });
}

export default AppDataSource;
