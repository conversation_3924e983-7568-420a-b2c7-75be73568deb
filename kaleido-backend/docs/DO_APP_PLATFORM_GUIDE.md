# DigitalOcean App Platform Deployment Guide

## Overview

This guide explains how to deploy Kaleido Backend on DigitalOcean App Platform with database safety features. The deployment system ensures automatic recovery from migration failures and zero-downtime deployments.

## Architecture

### Key Components

1. **DO-Specific Start Script** (`scripts/do-app-start.js`)
   - Handles database connectivity checks
   - Runs migrations with automatic fallback to safe sync
   - Starts the application with proper error handling
   - Creates deployment reports

2. **Pre-Deploy Job** (`scripts/do-migration-check.js`)
   - Validates database connectivity before deployment
   - Checks for pending migrations
   - Analyzes schema differences
   - Non-blocking by default (deployment continues even if checks fail)

3. **App Configuration** (`.do/app-safe.yaml`)
   - Defines service configuration
   - Sets up environment variables
   - Configures health checks and alerts
   - Includes pre-deploy job

## Setup Instructions

### 1. Initial Setup

```bash
# Ensure all scripts are executable
chmod +x scripts/*.js
chmod +x scripts/*.sh

# Test locally
node scripts/do-app-start.js
```

### 2. Configure Environment Variables

In DigitalOcean App Platform dashboard:

#### Required Variables:
- `DATABASE_URL` - Full PostgreSQL connection string (usually auto-populated by <PERSON>O)
- `NODE_ENV` - Set to `production`
- `PORT` - Usually `8080` (DO sets this automatically)

#### Migration Control:
- `ALLOW_SAFE_SYNC` - Set to `true` to enable automatic schema sync
- `FORCE_START_ON_SYNC_FAILURE` - Set to `true` to start even if sync fails
- `SKIP_MIGRATIONS` - Set to `true` to skip all migration attempts
- `MIGRATION_TIMEOUT` - Timeout in ms (default: 300000)

#### Redis Configuration:
- `REDIS_HOST` - Redis host (if using managed Redis)
- `REDIS_PORT` - Redis port
- `REDIS_PASSWORD` - Redis password

### 3. Deploy Using CLI

```bash
# Install DO CLI
brew install doctl

# Authenticate
doctl auth init

# Create app from spec
doctl apps create --spec .do/app-safe.yaml

# Or update existing app
doctl apps update <app-id> --spec .do/app-safe.yaml
```

### 4. Deploy Using GitHub

1. Connect your GitHub repository in DO App Platform
2. Set deployment branch (e.g., `production`)
3. Enable automatic deployments
4. Push to trigger deployment

## Deployment Flow

### Standard Deployment

1. **Pre-Deploy Job** runs migration checks
2. **Build Phase** compiles TypeScript and prepares assets
3. **Deploy Phase** starts new container
4. **Start Script** executes:
   - Checks database connection
   - Runs migrations
   - Falls back to safe sync if needed
   - Starts application
5. **Health Checks** verify application is running
6. **Old Container** terminated after successful health checks

### Migration Handling

```
┌─────────────────┐
│ Start Deployment│
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Check Database  │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌──────────────┐
│ Run Migrations  │────►│ Success?     │
└─────────────────┘     └──────┬───────┘
                               │ No
                               ▼
                      ┌─────────────────┐
                      │ Safe Sync Mode  │
                      └────────┬────────┘
                               │
                               ▼
                      ┌─────────────────┐
                      │ Start App       │
                      └─────────────────┘
```

## Monitoring and Debugging

### 1. View Logs

```bash
# Using DO CLI
doctl apps logs <app-id> --type=run --follow

# In DO Dashboard
# Navigate to: App > Runtime Logs
```

### 2. Check Deployment Status

```bash
# List deployments
doctl apps list-deployments <app-id>

# Get deployment details
doctl apps get-deployment <app-id> <deployment-id>
```

### 3. Access Deployment Reports

The start script creates reports in `/tmp/`:

```bash
# SSH into container (if enabled)
doctl apps console <app-id> --type=shell

# View deployment report
cat /tmp/do-deployment-report.json

# View error report (if exists)
cat /tmp/do-deployment-error.json
```

### 4. Monitor Health

- DO Dashboard shows health check status
- Access `/health` endpoint directly
- Set up alerts for failures

## Troubleshooting

### Common Issues

#### 1. "Database connection failed"

Check:
- `DATABASE_URL` is correctly set
- Database is accessible from DO network
- SSL mode is set to `require`

```bash
# Test connection in build logs
doctl apps logs <app-id> --type=build
```

#### 2. "Migrations failed but app started"

This is expected behavior when safe sync succeeds:
- Check logs for what was synchronized
- Verify application functionality
- Review deployment report

#### 3. "Container keeps restarting"

Possible causes:
- Memory limit exceeded (increase instance size)
- Port mismatch (ensure PORT env var is used)
- Health check failing

```bash
# Check recent logs
doctl apps logs <app-id> --type=run --tail 100
```

#### 4. "Build timeout"

Solutions:
- Increase build timeout in app spec
- Use lighter build process
- Cache dependencies

### Emergency Procedures

#### Skip All Database Operations

```bash
# Set environment variable
doctl apps update <app-id> --env SKIP_MIGRATIONS=true

# Trigger redeployment
doctl apps create-deployment <app-id>
```

#### Force Start Despite Failures

```bash
# Set environment variables
doctl apps update <app-id> \
  --env FORCE_START_ON_SYNC_FAILURE=true \
  --env ALLOW_SAFE_SYNC=true
```

#### Rollback Deployment

```bash
# List deployments
doctl apps list-deployments <app-id>

# Rollback to previous
doctl apps create-deployment <app-id> \
  --rollback <previous-deployment-id>
```

## Best Practices

### 1. Environment Variables

- Use DO's secret management for sensitive data
- Set `scope` appropriately (BUILD_TIME vs RUN_TIME)
- Document all required variables

### 2. Database Management

- Use DO's managed databases when possible
- Enable connection pooling
- Set appropriate timeouts

### 3. Monitoring

```yaml
# Add comprehensive alerts
alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
- rule: CPU_UTILIZATION
  value: 85
  window: FIVE_MINUTES
- rule: MEM_UTILIZATION
  value: 85
  window: FIVE_MINUTES
- rule: RESTART_COUNT
  value: 5
  window: FIVE_MINUTES
```

### 4. Scaling

- Start with `professional-xs` for testing
- Monitor metrics and scale as needed
- Consider horizontal scaling for production

### 5. CI/CD Integration

```yaml
# GitHub Actions example
- name: Deploy to DO App Platform
  run: |
    doctl auth init --access-token ${{ secrets.DO_ACCESS_TOKEN }}
    doctl apps update ${{ secrets.DO_APP_ID }} --spec .do/app-safe.yaml
```

## Migration Strategy

### Development Workflow

1. Develop and test migrations locally
2. Deploy to staging environment
3. Verify migrations succeed
4. Deploy to production

### Production Safety

1. **Always backup** before major migrations
2. **Test migrations** on staging first
3. **Monitor closely** during deployment
4. **Have rollback plan** ready

### Migration Flags

Control migration behavior with environment variables:

```bash
# Conservative approach
ALLOW_SAFE_SYNC=false
FORCE_START_ON_SYNC_FAILURE=false

# Balanced approach (recommended)
ALLOW_SAFE_SYNC=true
FORCE_START_ON_SYNC_FAILURE=false

# Aggressive approach (use with caution)
ALLOW_SAFE_SYNC=true
FORCE_START_ON_SYNC_FAILURE=true
```

## Cost Optimization

1. **Right-size instances** - Start small and scale up
2. **Use managed databases** - More cost-effective than self-hosting
3. **Enable autoscaling** - Scale down during low traffic
4. **Monitor bandwidth** - Optimize API responses

## Security Considerations

1. **Always use SSL** - DO provides free certificates
2. **Secure environment variables** - Use SECRET type
3. **Network isolation** - Use private networking for database
4. **Regular updates** - Keep dependencies current

## Summary

The DigitalOcean App Platform deployment with database safety ensures:

- **Automatic recovery** from migration failures
- **Zero-downtime deployments** with health checks
- **Complete visibility** through logs and reports
- **Easy rollback** capabilities
- **Scalability** with DO's infrastructure

This setup provides production-grade reliability while maintaining the simplicity of Platform-as-a-Service deployment.