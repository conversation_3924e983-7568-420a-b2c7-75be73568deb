# Docker Deployment Guide with Database Safety

## Overview

This guide explains how to deploy the Kaleido Backend using Docker with built-in database safety features. The deployment system ensures that database schema issues never cause deployment failures.

## Quick Start

### 1. Build and Deploy with Safety

```bash
# Build the safe Docker image
chmod +x scripts/docker-build-safe.sh
./scripts/docker-build-safe.sh --deploy

# Or use docker-compose directly
docker-compose -f docker-compose.prod-safe.yml up -d
```

### 2. Monitor Deployment

```bash
# Check service health
docker-compose -f docker-compose.prod-safe.yml ps

# View logs
docker-compose -f docker-compose.prod-safe.yml logs -f app

# Check migration reports
docker exec kaleido-backend-app-1 cat /tmp/deployment-report.json
```

## Architecture

### Safe Entrypoint Flow

1. **Redis Startup** - Ensures caching layer is ready
2. **Database Connection Check** - Verifies database accessibility
3. **Schema Analysis** - Checks current database state
4. **Migration Attempt** - Tries to run TypeORM migrations
5. **Automatic Fallback** - If migrations fail, switches to safe sync
6. **Final Verification** - Ensures schema matches entities
7. **Application Start** - Only starts if database is ready

### Key Components

#### 1. Enhanced Dockerfile (`Dockerfile.prod`)

- Includes PostgreSQL client for database operations
- Copies all necessary scripts for safety checks
- Uses safe entrypoint script

#### 2. Safe Entrypoint Script (`docker-entrypoint-safe.sh`)

- Comprehensive database checks before startup
- Automatic fallback from migrations to safe sync
- Detailed logging and error reporting
- Creates deployment reports

#### 3. Docker Compose (`docker-compose.prod-safe.yml`)

- Database connectivity pre-check service
- Optional automatic backup service
- Health checks for all services
- Volume mounts for logs and reports

## Configuration

### Environment Variables

```env
# Database Configuration
DB_HOST=your-database-host
DB_PORT=5432
DB_USERNAME=your-username
DB_PASSWORD=your-password
DB_NAME=your-database
DB_SSL=require  # For production

# Migration Control
SKIP_MIGRATIONS=false      # Set to true to skip migrations entirely
FORCE_SAFE_SYNC=false     # Set to true to always use safe sync
MIGRATION_TIMEOUT=300000  # Timeout in milliseconds (5 minutes)

# Redis Configuration
REDIS_PASSWORD=your-redis-password
```

### Docker Compose Profiles

```bash
# Run with automatic database backups
docker-compose -f docker-compose.prod-safe.yml --profile with-backup up -d

# Run without backups (default)
docker-compose -f docker-compose.prod-safe.yml up -d
```

## Deployment Scenarios

### 1. Fresh Deployment

```bash
# Build and deploy
./scripts/docker-build-safe.sh --deploy

# The system will:
# - Create all missing tables
# - Add all required columns
# - Set up indexes
# - Start the application
```

### 2. Update Deployment

```bash
# Build new image
./scripts/docker-build-safe.sh --build-only

# Stop current deployment
docker-compose -f docker-compose.prod-safe.yml down

# Deploy new version
docker-compose -f docker-compose.prod-safe.yml up -d
```

### 3. Emergency Recovery

If deployment fails:

```bash
# Check what went wrong
docker-compose -f docker-compose.prod-safe.yml logs app

# Try with forced safe sync
FORCE_SAFE_SYNC=true docker-compose -f docker-compose.prod-safe.yml up -d

# Or skip migrations temporarily
SKIP_MIGRATIONS=true docker-compose -f docker-compose.prod-safe.yml up -d
```

## Monitoring and Debugging

### 1. Check Deployment Status

```bash
# View deployment report
docker exec kaleido-backend-app-1 cat /tmp/deployment-report.json

# Check database sync status
docker exec kaleido-backend-app-1 cat /tmp/db-check.log

# View migration attempts
docker exec kaleido-backend-app-1 cat /tmp/migration.log
```

### 2. Health Monitoring

```bash
# Check all services
docker-compose -f docker-compose.prod-safe.yml ps

# Test application health endpoint
curl http://localhost:8080/health

# Check Redis
docker exec kaleido-backend-redis-1 redis-cli -a $REDIS_PASSWORD ping
```

### 3. Log Analysis

```bash
# Application logs
docker-compose -f docker-compose.prod-safe.yml logs -f app

# Database check logs
docker-compose -f docker-compose.prod-safe.yml logs db-check

# All services
docker-compose -f docker-compose.prod-safe.yml logs -f
```

## Backup and Recovery

### 1. Automatic Backups

Enable automatic daily backups:

```bash
# Deploy with backup profile
docker-compose -f docker-compose.prod-safe.yml --profile with-backup up -d

# Backups are stored in ./backups/
ls -la ./backups/
```

### 2. Manual Backup

```bash
# Create manual backup
docker-compose -f docker-compose.prod-safe.yml exec app \
  sh -c 'PGPASSWORD=$DB_PASSWORD pg_dump -h $DB_HOST -U $DB_USERNAME $DB_NAME' \
  > backup-$(date +%Y%m%d-%H%M%S).sql
```

### 3. Restore from Backup

```bash
# Stop application
docker-compose -f docker-compose.prod-safe.yml stop app

# Restore database
docker-compose -f docker-compose.prod-safe.yml exec -T app \
  sh -c 'PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USERNAME $DB_NAME' \
  < backup-20240104-120000.sql

# Restart application
docker-compose -f docker-compose.prod-safe.yml start app
```

## PM2 Integration

If using PM2 inside Docker:

```bash
# Use PM2 ecosystem config
docker run -d \
  --name kaleido-backend \
  --env-file .env \
  -p 8080:8080 \
  kaleido-backend:latest \
  pm2-runtime start ecosystem.config.js --env production
```

## CI/CD Integration

### GitHub Actions Example

```yaml
- name: Build and push Docker image
  run: |
    ./scripts/docker-build-safe.sh \
      --push \
      --skip-tests
  env:
    DOCKER_REGISTRY: ${{ secrets.DOCKER_REGISTRY }}
    DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
    DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}

- name: Deploy to production
  run: |
    ssh ${{ secrets.DEPLOY_HOST }} "
      cd /opt/kaleido-backend &&
      docker-compose -f docker-compose.prod-safe.yml pull &&
      docker-compose -f docker-compose.prod-safe.yml up -d
    "
```

## Troubleshooting

### Common Issues

#### 1. "Migrations failed but safe sync succeeded"

This is normal behavior. The system automatically recovered by adding missing schema elements.

#### 2. "Container keeps restarting"

Check logs for specific errors:
```bash
docker-compose -f docker-compose.prod-safe.yml logs --tail=100 app
```

#### 3. "Database connection failed"

Verify connection parameters:
```bash
# Test connection from container
docker-compose -f docker-compose.prod-safe.yml run --rm app \
  sh -c 'PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -U $DB_USERNAME $DB_NAME -c "SELECT 1"'
```

### Debug Mode

Enable detailed logging:

```bash
# Set debug environment variables
MIGRATION_DEBUG=true \
NODE_ENV=development \
docker-compose -f docker-compose.prod-safe.yml up
```

## Best Practices

1. **Always test locally first**
   ```bash
   docker-compose -f docker-compose.prod-safe.yml up
   ```

2. **Monitor first deployment**
   - Watch logs during initial startup
   - Check deployment reports
   - Verify all services are healthy

3. **Keep backups**
   - Enable automatic backups in production
   - Test restore procedures regularly

4. **Version your images**
   ```bash
   docker tag kaleido-backend:latest kaleido-backend:v1.2.3
   ```

5. **Use health checks**
   - Application provides `/health` endpoint
   - Docker Compose includes health checks
   - Monitor external health checking service

## Summary

The Docker deployment with database safety ensures:

- **Zero-downtime deployments** - Database issues don't stop deployment
- **Automatic recovery** - Falls back to safe sync if migrations fail
- **Complete visibility** - Detailed logs and reports
- **Data safety** - Never removes columns or data
- **Easy rollback** - Docker images can be quickly reverted

This system provides production-grade reliability while maintaining development flexibility.