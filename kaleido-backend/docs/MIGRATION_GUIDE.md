# TypeORM Migration Guide

## Overview

This guide explains how to work with TypeORM migrations in the Kaleido Backend project. We've implemented enhanced migration tools to prevent silent failures and ensure migrations are generated correctly.

## Quick Start

### 1. Generate a Migration

Use the enhanced migration generator:

```bash
pnpm run migration:generate:enhanced
```

This will:
- Check database connection
- Validate entities
- Detect schema changes
- Generate migration with proper error handling
- Validate the generated migration
- Optionally run the migration

### 2. Troubleshoot Issues

If migrations are failing:

```bash
pnpm run migration:troubleshoot
```

This will check:
- Environment variables
- Database connection
- Entity files
- TypeORM configuration
- Common issues

### 3. Manual Migration Generation

If automatic generation fails, you can use the standard TypeORM command with debug mode:

```bash
MIGRATION_DEBUG=true NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:generate -d src/config/migration-enhanced.config.ts src/migrations/YourMigrationName
```

## Common Issues and Solutions

### 1. "No changes in database schema were found"

**Causes:**
- Entity changes not detected
- Entity not exported in `src/entities/index.ts`
- TypeScript compilation errors

**Solution:**
```bash
# Update entity exports
pnpm run entities:update

# Check TypeScript compilation
pnpm run typecheck

# Retry migration generation
pnpm run migration:generate:enhanced
```

### 2. Silent Failures

**Causes:**
- Database connection issues
- Missing environment variables
- TypeORM configuration errors

**Solution:**
```bash
# Run troubleshooter
pnpm run migration:troubleshoot

# Check with debug mode
MIGRATION_DEBUG=true pnpm run migration:generate:enhanced
```

### 3. Entity Not Found

**Causes:**
- Entity file not following naming convention (*.entity.ts)
- Entity not decorated with @Entity
- Import path issues

**Solution:**
- Ensure entity files end with `.entity.ts`
- Check that entity class has `@Entity('table_name')` decorator
- Verify imports use correct paths (@modules/*, @shared/*, etc.)

## Best Practices

### 1. Before Creating Entities

```bash
# Check current database state
pnpm run migration:show

# Verify no pending migrations
pnpm run check:migrations
```

### 2. Entity Creation Workflow

1. Create your entity file:
   ```typescript
   // src/modules/your-module/entities/your-entity.entity.ts
   import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';
   import { BaseEntity } from '@shared/entities/base.entity';

   @Entity('your_table_name')
   export class YourEntity extends BaseEntity {
     @PrimaryGeneratedColumn('uuid')
     id!: string;

     @Column()
     name!: string;
   }
   ```

2. Update entity exports:
   ```bash
   pnpm run entities:update
   ```

3. Verify TypeScript compilation:
   ```bash
   pnpm run typecheck
   ```

4. Generate migration:
   ```bash
   pnpm run migration:generate:enhanced
   ```

### 3. Testing Migrations

Always test migrations before committing:

```bash
# Run migration
pnpm run migration:run

# Verify it worked
pnpm run migration:show

# Test rollback
pnpm run migration:revert

# Re-run after testing rollback
pnpm run migration:run
```

### 4. Migration Naming Conventions

Use descriptive names:
- ✅ `AddUserEmailIndex`
- ✅ `CreateCompanyTable`
- ✅ `UpdateJobStatusEnum`
- ❌ `Fix`
- ❌ `Update`
- ❌ `Change`

## Environment Variables

Required for migrations:
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_NAME=your_database

# Optional - for debugging
MIGRATION_DEBUG=true
NODE_OPTIONS="--max-old-space-size=8192"
```

## Scripts Reference

| Script | Description |
|--------|-------------|
| `migration:generate:enhanced` | Enhanced migration generator with validation |
| `migration:troubleshoot` | Diagnose migration issues |
| `migration:generate` | Original migration generator |
| `migration:create` | Create empty migration |
| `migration:run` | Run pending migrations |
| `migration:revert` | Revert last migration |
| `migration:show` | Show migration status |
| `entities:update` | Update entity exports |
| `check:migrations` | Check for pending migrations |

## Advanced Usage

### Manual Migration Creation

If you need to create a migration manually:

```typescript
// scripts/create-manual-migration.ts
import MigrationHelper from './migration-helper';

const filename = MigrationHelper.createManualMigration(
  'AddCustomIndex',
  `await queryRunner.query(\`CREATE INDEX "idx_users_email" ON "users" ("email")\`);`,
  `await queryRunner.query(\`DROP INDEX "idx_users_email"\`);`
);

console.log(`Created migration: ${filename}`);
```

### Debugging TypeORM Queries

Enable query logging:

```typescript
// src/config/migration-enhanced.config.ts
logging: ['query', 'error', 'schema', 'warn', 'info', 'log']
```

Or use environment variable:
```bash
MIGRATION_DEBUG=true pnpm run migration:generate:enhanced
```

## Migration Safety Checklist

Before running migrations in production:

- [ ] Test migration locally
- [ ] Test rollback locally
- [ ] Review generated SQL
- [ ] Backup database
- [ ] Check for data loss operations (DROP, DELETE, TRUNCATE)
- [ ] Verify migration is idempotent
- [ ] Ensure migration handles existing data correctly

## Getting Help

1. Run the troubleshooter: `pnpm run migration:troubleshoot`
2. Check this guide for common issues
3. Enable debug mode: `MIGRATION_DEBUG=true`
4. Check TypeORM documentation: https://typeorm.io/migrations
5. Ask team for help with specific issues