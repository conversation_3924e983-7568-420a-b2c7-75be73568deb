# Database Safety and Recovery Guide

## Overview

This guide explains the database safety mechanisms implemented to ensure production deployments never fail due to schema synchronization issues. Our system provides multiple layers of protection and automatic recovery options.

## Key Components

### 1. Database Sync Check (`db:sync:check`)

Analyzes the current state of your database compared to TypeORM entities:

```bash
# Development check
pnpm run db:sync:check

# Production check with data analysis
pnpm run db:sync:check:prod
```

Features:
- Identifies missing tables and columns
- Detects extra columns in database
- Shows which columns contain data (important for cleanup decisions)
- Generates detailed JSON reports

### 2. Safe Database Sync (`db:safe:sync`)

**Critical:** This tool ONLY adds missing tables and columns. It NEVER removes anything.

```bash
# Dry run - see what would be changed
pnpm run db:safe:sync -- --dry-run

# Execute synchronization
pnpm run db:safe:sync -- --execute

# Production force sync (skips prompts)
pnpm run db:safe:sync:prod
```

Safety features:
- Transaction-based execution (all or nothing)
- Detailed logging of all operations
- JSON reports for audit trail
- Never removes existing data or columns

### 3. Safe Deployment Script (`deploy:safe`)

Comprehensive deployment wrapper that ensures database integrity:

```bash
# Production deployment with all safety checks
NODE_ENV=production pnpm run deploy:safe:prod

# Force deployment (skip prompts)
NODE_ENV=production pnpm run deploy:safe -- --force

# Only run database sync steps
NODE_ENV=production pnpm run deploy:safe -- --sync-only
```

## Deployment Workflow

### Standard Safe Deployment

1. **Pre-deployment validation**
   - Checks Node.js version
   - Validates environment variables
   - Ensures dependencies are installed

2. **Database backup** (if pg_dump is available)
   - Creates timestamped backup
   - Stores in project directory

3. **Database sync check**
   - Analyzes current schema state
   - Identifies any discrepancies

4. **Migration attempt**
   - Tries to run pending migrations
   - If migrations fail → **Automatic fallback to safe sync**

5. **Verification**
   - Confirms database matches entities
   - Critical step - deployment stops if this fails

6. **Build and deploy**
   - Builds application
   - Starts production server

### Migration Failure Recovery

When migrations fail, the system automatically:

1. Logs the migration error
2. Switches to safe sync mode
3. Adds only missing tables/columns
4. Continues deployment if sync succeeds

## Emergency Procedures

### If deployment fails:

1. **Check logs:**
   ```bash
   # Find latest deployment log
   ls -la scripts/deployment-log-*.log
   
   # View the log
   cat scripts/deployment-log-TIMESTAMP.log
   ```

2. **Review deployment report:**
   ```bash
   # Find latest report
   ls -la scripts/deployment-report-*.json
   
   # Pretty print the report
   cat scripts/deployment-report-TIMESTAMP.json | jq .
   ```

3. **Manual recovery:**
   ```bash
   # Step 1: Check current state
   NODE_ENV=production pnpm run db:sync:check:prod
   
   # Step 2: Run safe sync if needed
   NODE_ENV=production pnpm run db:safe:sync -- --execute
   
   # Step 3: Verify state
   NODE_ENV=production pnpm run db:sync:check
   ```

### Database Backup Recovery

If you need to restore from backup:

```bash
# List available backups
ls -la backup-*.sql

# Restore specific backup
PGPASSWORD="$DB_PASSWORD" psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_NAME < backup-TIMESTAMP.sql
```

## Best Practices

### 1. Always Test Locally First

```bash
# Run full deployment simulation
pnpm run deploy:safe -- --dry-run

# Check what would be synced
pnpm run db:safe:sync -- --dry-run
```

### 2. Monitor Production Deployments

```bash
# Watch deployment in real-time
NODE_ENV=production pnpm run deploy:safe:prod

# Check logs in another terminal
tail -f scripts/deployment-log-*.log
```

### 3. Regular Health Checks

```bash
# Weekly database sync check
NODE_ENV=production pnpm run db:sync:check:prod

# Save reports for trend analysis
mkdir -p db-health-reports
mv scripts/db-sync-report-*.json db-health-reports/
```

## Configuration

### Environment Variables

Required for all database operations:
```env
DB_HOST=your-host
DB_PORT=5432
DB_USERNAME=your-username
DB_PASSWORD=your-password
DB_NAME=your-database
DB_SSL=require  # For production
```

### Safe Sync Behavior

The safe sync tool:
- ✅ Creates missing tables
- ✅ Adds missing columns (always nullable initially)
- ✅ Creates missing indexes
- ❌ Never drops tables
- ❌ Never removes columns
- ❌ Never modifies existing column types

## Troubleshooting

### "Migration failed but sync succeeded"

This is expected behavior. It means:
- Your migration had complex operations
- Safe sync added the basic structure
- You may need to manually run data transformations

### "Critical step failed: Verify database state"

This means even after sync, entities don't match database:
1. Check for TypeScript compilation errors
2. Ensure all entities are properly exported
3. Review entity decorators for issues

### "Cannot connect to database"

1. Verify environment variables
2. Check network connectivity
3. Ensure database server is running
4. Validate SSL settings for production

## Scripts Reference

| Script | Purpose | When to Use |
|--------|---------|-------------|
| `db:sync:check` | Analyze schema differences | Before deployments, debugging |
| `db:safe:sync` | Add missing schema elements | When migrations fail |
| `deploy:safe` | Full deployment with safety | Production deployments |
| `migration:run:safe` | Run migrations with confirmations | Manual migration execution |

## Audit Trail

All operations generate JSON reports:
- `db-sync-report-*.json` - Schema analysis results
- `db-safe-sync-report-*.json` - Sync operation plans
- `deployment-report-*.json` - Full deployment summary
- `deployment-log-*.log` - Detailed execution logs

Keep these for:
- Debugging issues
- Compliance requirements
- Historical analysis
- Rollback planning

## Summary

The database safety system ensures:
1. **Production never breaks** due to schema issues
2. **Automatic recovery** from migration failures
3. **Complete audit trail** of all operations
4. **Zero data loss** - only additive changes
5. **Confidence in deployments** - multiple safety checks

Use `pnpm run deploy:safe:prod` for all production deployments to leverage these protections.