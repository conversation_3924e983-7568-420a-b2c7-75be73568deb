module.exports = {
  apps: [
    {
      name: 'kaleido-backend',
      script: './dist/main.js',
      instances: process.env.PM2_INSTANCES || 1,
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '2G',
      node_args: '--max-old-space-size=8192',
      
      // Environment variables
      env: {
        NODE_ENV: 'development',
        PORT: 8080
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: process.env.PORT || 8080
      },
      
      // Error handling
      error_file: './logs/pm2-error.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true,
      
      // Graceful shutdown
      kill_timeout: 10000,
      listen_timeout: 10000,
      
      // Pre-start hook for database safety
      pre_start: async function() {
        console.log('🔍 Running pre-start database checks...');
        
        const { execSync } = require('child_process');
        const fs = require('fs');
        const path = require('path');
        
        try {
          // Check database connection
          console.log('🔌 Checking database connection...');
          execSync('node -e "const pg = require(\'pg\'); const client = new pg.Client({ host: process.env.DB_HOST, port: process.env.DB_PORT, user: process.env.DB_USERNAME, password: process.env.DB_PASSWORD, database: process.env.DB_NAME, ssl: process.env.DB_SSL === \'require\' ? { rejectUnauthorized: false } : false }); client.connect().then(() => { console.log(\'Connected\'); return client.end(); }).catch(err => { console.error(err); process.exit(1); });"', {
            stdio: 'inherit'
          });
          
          // Run database safety checks
          console.log('🔄 Running database safety checks...');
          
          // Try migrations first
          try {
            execSync('npx ts-node scripts/database-migration-runner.ts --force', {
              stdio: 'inherit',
              timeout: 300000 // 5 minutes timeout
            });
            console.log('✅ Migrations completed successfully');
          } catch (migrationError) {
            console.log('⚠️  Migrations failed, attempting safe sync...');
            
            // Fall back to safe sync
            try {
              execSync('npx ts-node scripts/database-safe-sync.ts --execute --force', {
                stdio: 'inherit',
                timeout: 300000
              });
              console.log('✅ Safe sync completed successfully');
            } catch (syncError) {
              console.error('❌ Both migrations and safe sync failed');
              throw syncError;
            }
          }
          
          // Final verification
          execSync('npx ts-node scripts/database-sync-check.ts', {
            stdio: 'inherit'
          });
          
          console.log('✅ Pre-start checks completed successfully');
          
        } catch (error) {
          console.error('❌ Pre-start checks failed:', error.message);
          throw error;
        }
      },
      
      // Post-start verification
      post_start: async function() {
        console.log('🔍 Running post-start verification...');
        
        const axios = require('axios');
        const maxRetries = 30;
        let retries = 0;
        
        const checkHealth = async () => {
          try {
            const response = await axios.get(`http://localhost:${process.env.PORT || 8080}/health`);
            if (response.status === 200) {
              console.log('✅ Application is healthy');
              return true;
            }
          } catch (error) {
            retries++;
            if (retries < maxRetries) {
              console.log(`⏳ Waiting for application to start... (${retries}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, 2000));
              return checkHealth();
            }
            throw new Error('Application failed to start within timeout');
          }
        };
        
        await checkHealth();
      }
    }
  ],
  
  // Deploy configuration
  deploy: {
    production: {
      user: process.env.DEPLOY_USER || 'deploy',
      host: process.env.DEPLOY_HOST,
      ref: 'origin/production',
      repo: process.env.DEPLOY_REPO,
      path: process.env.DEPLOY_PATH || '/var/www/kaleido-backend',
      'pre-deploy': 'npm run db:sync:check:prod',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      env: {
        NODE_ENV: 'production'
      }
    }
  }
};