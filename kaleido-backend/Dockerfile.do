# DigitalOcean App Platform optimized Dockerfile
FROM node:20.9.0-alpine AS builder

# Install pnpm
RUN corepack enable && corepack prepare pnpm@10.13.1 --activate

WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install all dependencies (including dev for TypeScript compilation)
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build the application
RUN pnpm run build

# Production stage
FROM node:20.9.0-alpine

# Install pnpm and required tools
RUN corepack enable && corepack prepare pnpm@10.13.1 --activate && \
    apk add --no-cache bash curl postgresql-client

WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install production dependencies + required build tools for migrations
RUN pnpm install --prod --frozen-lockfile && \
    pnpm add -D typescript ts-node tsconfig-paths @types/node && \
    pnpm add pg

# Copy built application
COPY --from=builder /app/dist ./dist

# Copy source files needed for migrations
COPY --from=builder /app/src ./src
COPY --from=builder /app/tsconfig.json ./tsconfig.json

# Copy scripts directory
COPY scripts ./scripts

# Make scripts executable
RUN chmod +x ./scripts/*.sh && \
    chmod +x ./scripts/*.js

# DO App Platform handles port exposure
# The PORT env var is set by DO

# Health check endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8080}/health || exit 1

# Use the DO-specific start script
CMD ["node", "scripts/do-app-start.js"]