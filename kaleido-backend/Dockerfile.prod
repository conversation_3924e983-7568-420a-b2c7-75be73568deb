# Build stage
FROM node:20.9.0-alpine AS builder

# Install pnpm
RUN corepack enable && corepack prepare pnpm@10.10.0 --activate

WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies without running scripts (avoids husky error)
RUN pnpm install --frozen-lockfile --ignore-scripts

# Copy source code
COPY . .

# Build the application without Sentry source maps
RUN SKIP_SENTRY_RELEASE=true pnpm nest build

# Production stage
FROM node:20.9.0-alpine

# Install pnpm, redis, ffmpeg, postgresql-client, and other dependencies
RUN corepack enable && corepack prepare pnpm@10.10.0 --activate && \
    apk add --no-cache redis bash curl ffmpeg postgresql-client

WORKDIR /app

# Copy package files and install production dependencies + TypeScript for migrations
COPY --from=builder /app/package.json /app/pnpm-lock.yaml ./
COPY --from=builder /app/node_modules ./node_modules

# Install TypeScript and ts-node for migration support
RUN pnpm add -D typescript ts-node tsconfig-paths @types/node
# Ensure pg is available for direct database connections
RUN pnpm add pg

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy source files needed for migrations
COPY --from=builder /app/src ./src
COPY --from=builder /app/tsconfig.json ./tsconfig.json

# Copy scripts directory
COPY scripts ./scripts

# Make scripts executable
RUN chmod +x ./scripts/*.sh && \
    chmod +x ./scripts/*.js

# Expose application port
EXPOSE 8080

# Set environment variable for port
ENV PORT=8080

# Create Redis data directory
RUN mkdir -p /data/redis && chmod 777 /data/redis

# Add health check for Redis
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD ./scripts/check-redis-docker.sh || echo "❌ Health check failed but continuing"

# Use safe entrypoint script with database protection
ENTRYPOINT ["./scripts/docker-entrypoint-safe.sh"]