#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import * as path from 'path';
import chalk from 'chalk';
import { config } from 'dotenv';
import { register } from 'tsconfig-paths';

// Register TypeScript path aliases
const baseUrl = path.join(__dirname, '..');
register({
  baseUrl,
  paths: {
    '@/*': ['src/*'],
    '@modules/*': ['src/modules/*'],
    '@shared/*': ['src/shared/*'],
  },
});

// Load environment variables
config();

async function troubleshootMigrations() {
  console.log(chalk.bold.blue('🔍 TypeORM Migration Troubleshooter\n'));

  // Step 1: Check environment variables
  console.log(chalk.blue('📋 Checking environment variables...'));
  const requiredEnvVars = ['DB_HOST', 'DB_PORT', 'DB_USERNAME', 'DB_PASSWORD', 'DB_NAME'];
  const missingVars = requiredEnvVars.filter(v => !process.env[v]);
  
  if (missingVars.length > 0) {
    console.log(chalk.red(`❌ Missing environment variables: ${missingVars.join(', ')}`));
    return;
  }
  console.log(chalk.green('✅ All required environment variables are set'));

  // Step 2: Test database connection
  console.log(chalk.blue('\n🔌 Testing database connection...'));
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    synchronize: false,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log(chalk.green('✅ Database connection successful'));
    
    // Check if migrations table exists
    const migrationTableExists = await dataSource.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'migrations'
      )`
    );
    
    if (migrationTableExists[0].exists) {
      console.log(chalk.green('✅ Migrations table exists'));
      
      // Get executed migrations
      const executedMigrations = await dataSource.query(
        `SELECT * FROM migrations ORDER BY timestamp DESC LIMIT 5`
      );
      
      console.log(chalk.blue('\n📚 Latest executed migrations:'));
      executedMigrations.forEach((m: any) => {
        console.log(chalk.gray(`  • ${m.name} (${new Date(parseInt(m.timestamp)).toISOString()})`));
      });
    } else {
      console.log(chalk.yellow('⚠️  Migrations table does not exist'));
    }
    
  } catch (error) {
    console.log(chalk.red('❌ Database connection failed:'));
    console.error(error);
    return;
  } finally {
    await dataSource.destroy();
  }

  // Step 3: Check entity files
  console.log(chalk.blue('\n📁 Checking entity files...'));
  try {
    const entities = await import('../src/entities');
    const entityCount = Object.keys(entities).length;
    console.log(chalk.green(`✅ Found ${entityCount} entities`));
    
    // List entities
    console.log(chalk.gray('  Entities:'));
    Object.keys(entities).forEach(entity => {
      console.log(chalk.gray(`    • ${entity}`));
    });
  } catch (error) {
    console.log(chalk.red('❌ Failed to load entities:'));
    console.error(error);
  }

  // Step 4: Check TypeORM configuration
  console.log(chalk.blue('\n⚙️  Checking TypeORM configuration...'));
  try {
    const migrationConfig = await import('../src/config/migration.config');
    console.log(chalk.green('✅ Migration configuration loaded successfully'));
    
    // Test if we can get schema information
    const testDataSource = migrationConfig.default;
    await testDataSource.initialize();
    
    const tables = await testDataSource.query(
      `SELECT table_name FROM information_schema.tables 
       WHERE table_schema = 'public' 
       AND table_type = 'BASE TABLE'
       ORDER BY table_name`
    );
    
    console.log(chalk.blue('\n📊 Database tables:'));
    tables.forEach((t: any) => {
      console.log(chalk.gray(`  • ${t.table_name}`));
    });
    
    await testDataSource.destroy();
  } catch (error) {
    console.log(chalk.red('❌ Failed to load migration configuration:'));
    console.error(error);
  }

  // Step 5: Check for common issues
  console.log(chalk.blue('\n🔎 Checking for common issues...'));
  
  // Check if running in production mode
  if (process.env.NODE_ENV === 'production') {
    console.log(chalk.yellow('⚠️  Running in production mode - make sure compiled JS files exist'));
  }
  
  // Check NODE_OPTIONS
  if (!process.env.NODE_OPTIONS?.includes('max-old-space-size')) {
    console.log(chalk.yellow('⚠️  Consider setting NODE_OPTIONS="--max-old-space-size=8192" for large schemas'));
  }
  
  console.log(chalk.green('\n✅ Troubleshooting complete'));
  
  // Provide recommendations
  console.log(chalk.bold.blue('\n💡 Recommendations:'));
  console.log(chalk.gray('1. Always run "pnpm run entities:update" after adding new entities'));
  console.log(chalk.gray('2. Use "MIGRATION_DEBUG=true" environment variable for detailed logs'));
  console.log(chalk.gray('3. Check that all entity decorators are properly imported from typeorm'));
  console.log(chalk.gray('4. Ensure entities extend BaseEntity if using active record pattern'));
  console.log(chalk.gray('5. Run "pnpm run typecheck" to catch TypeScript errors before migration'));
}

// Run if called directly
if (require.main === module) {
  troubleshootMigrations().catch(console.error);
}