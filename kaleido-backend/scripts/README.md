# Database Management Scripts

This directory contains essential scripts for managing database synchronization and migrations.

## Core Database Scripts

### 🔍 database-sync-check.ts
Checks if your database schema is synchronized with TypeORM entities.

**Usage:**
```bash
# Check local database
npm run db:sync:check

# Check production database with data analysis
npm run db:sync:check:prod
```

**Features:**
- Compares entities with database tables
- Identifies missing columns, extra columns
- Shows which columns contain data (with --check-data flag)
- Generates detailed JSON reports

### 🚀 database-migration-runner.ts
Safely runs database migrations with confirmation prompts for production.

**Usage:**
```bash
# Run migrations in development
npm run migration:run:safe

# Run migrations in production (requires confirmation)
npm run migration:run:prod

# Dry run to see pending migrations
npx ts-node scripts/database-migration-runner.ts --dry-run
```

**Features:**
- Environment-aware (loads correct .env file)
- Production safety checks
- Dry run mode
- Detailed migration status reporting

## Migration Management Scripts

- `generate-migration.ts` - Generates new TypeORM migrations from entity changes
- `check-migrations.ts` - Validates migration files before deployment
- `validate-migrations.js` - Ensures migrations are valid (used in deploy script)
- `update-entity-exports.ts` - Updates entity export files

## Other Utility Scripts

## Best Practices

1. **Before deploying to production:**
   ```bash
   npm run db:sync:check:prod
   ```

2. **After adding/modifying entities:**
   ```bash
   npm run migration:generate -- MigrationName
   npm run db:sync:check
   ```

3. **Regular maintenance:**
   - Run sync checks monthly
   - Review extra columns quarterly
   - Keep migrations up to date

## Troubleshooting

If you encounter database sync issues:
1. Run `npm run db:sync:check` to identify problems
2. Generate migrations for missing columns
3. Review extra columns and decide whether to add to entities or remove
4. Test migrations locally before running in production

For detailed documentation, see `/__COMPLETED/001_DATABASE_SYNCHRONIZATION_RESOLUTION.md`