#!/usr/bin/env ts-node
/**
 * Database Safe Sync Tool
 * 
 * This script provides a safe way to synchronize database schema with entities
 * when migrations fail. It only adds missing columns and tables, never removes them.
 * 
 * Usage:
 *   # Check what would be synchronized (dry run)
 *   npx ts-node scripts/database-safe-sync.ts --dry-run
 *   
 *   # Actually synchronize (adds missing columns/tables only)
 *   npx ts-node scripts/database-safe-sync.ts --execute
 *   
 *   # Production mode with extra safety checks
 *   NODE_ENV=production npx ts-node scripts/database-safe-sync.ts --execute --force
 */

import { DataSource, Table, TableColumn, TableIndex, TableForeignKey } from 'typeorm';
import * as path from 'path';
import * as dotenv from 'dotenv';
import chalk from 'chalk';
import * as fs from 'fs';
import * as readline from 'readline';

// Load environment variables
const envFile = process.env.NODE_ENV === 'production' ? '.env.production.local' : '.env';
dotenv.config({ path: envFile });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

interface SyncOperation {
  type: 'CREATE_TABLE' | 'ADD_COLUMN' | 'CREATE_INDEX';
  tableName: string;
  details: any;
  sql: string;
}

class DatabaseSafeSync {
  private dataSource: DataSource;
  private operations: SyncOperation[] = [];

  constructor(dataSource: DataSource) {
    this.dataSource = dataSource;
  }

  async analyze(): Promise<SyncOperation[]> {
    const queryRunner = this.dataSource.createQueryRunner();
    const entityMetadatas = this.dataSource.entityMetadatas;

    try {
      console.log(chalk.blue('🔍 Analyzing database schema differences...\n'));

      for (const metadata of entityMetadatas) {
        const tableName = metadata.tableName;
        const tableExists = await queryRunner.hasTable(tableName);

        if (!tableExists) {
          // Table doesn't exist - need to create it
          const newTable = Table.create(metadata, this.dataSource.driver);
          
          this.operations.push({
            type: 'CREATE_TABLE',
            tableName,
            details: {
              columns: newTable.columns.map(c => ({
                name: c.name,
                type: c.type,
                isNullable: c.isNullable,
                isPrimary: c.isPrimary,
                default: c.default
              }))
            },
            sql: this.getCreateTableSQL(newTable)
          });

          console.log(chalk.yellow(`📦 Table '${tableName}' needs to be created`));
        } else {
          // Table exists - check for missing columns
          const table = await queryRunner.getTable(tableName);
          if (!table) continue;

          for (const column of metadata.columns) {
            const existingColumn = table.columns.find(c => c.name === column.databaseName);
            
            if (!existingColumn) {
              // Column doesn't exist - need to add it
              const newColumn = TableColumn.create(column, this.dataSource.driver);
              
              this.operations.push({
                type: 'ADD_COLUMN',
                tableName,
                details: {
                  columnName: newColumn.name,
                  type: newColumn.type,
                  isNullable: newColumn.isNullable,
                  default: newColumn.default
                },
                sql: this.getAddColumnSQL(tableName, newColumn)
              });

              console.log(chalk.yellow(`➕ Column '${tableName}.${newColumn.name}' needs to be added`));
            }
          }

          // Check for missing indexes
          for (const index of metadata.indices) {
            const indexName = this.dataSource.namingStrategy.indexName(
              tableName,
              index.columns.map(c => c.databaseName),
              index.where
            );

            const existingIndex = table.indices.find(i => i.name === indexName);
            
            if (!existingIndex && !index.isPrimary) {
              this.operations.push({
                type: 'CREATE_INDEX',
                tableName,
                details: {
                  indexName,
                  columns: index.columns.map(c => c.databaseName),
                  isUnique: index.isUnique
                },
                sql: this.getCreateIndexSQL(tableName, indexName, index.columns.map(c => c.databaseName), index.isUnique)
              });

              console.log(chalk.cyan(`🔍 Index '${indexName}' on '${tableName}' needs to be created`));
            }
          }
        }
      }

      await queryRunner.release();
      return this.operations;

    } catch (error) {
      await queryRunner.release();
      throw error;
    }
  }

  private getCreateTableSQL(table: Table): string {
    const columns = table.columns.map(column => {
      let sql = `"${column.name}" ${column.type}`;
      
      if (column.length) {
        sql += `(${column.length})`;
      }
      
      if (column.isPrimary) {
        sql += ' PRIMARY KEY';
      }
      
      if (column.isGenerated && column.generationStrategy === 'uuid') {
        sql += ' DEFAULT uuid_generate_v4()';
      }
      
      if (!column.isNullable) {
        sql += ' NOT NULL';
      }
      
      if (column.default !== undefined && column.default !== null) {
        sql += ` DEFAULT ${column.default}`;
      }
      
      return sql;
    }).join(',\n  ');

    return `CREATE TABLE "${table.name}" (\n  ${columns}\n);`;
  }

  private getAddColumnSQL(tableName: string, column: TableColumn): string {
    let sql = `ALTER TABLE "${tableName}" ADD COLUMN "${column.name}" ${column.type}`;
    
    if (column.length) {
      sql += `(${column.length})`;
    }
    
    // For new columns, always make them nullable initially to avoid issues
    sql += ' NULL';
    
    if (column.default !== undefined && column.default !== null) {
      sql += ` DEFAULT ${column.default}`;
    }
    
    return sql + ';';
  }

  private getCreateIndexSQL(tableName: string, indexName: string, columns: string[], isUnique: boolean): string {
    const uniqueStr = isUnique ? 'UNIQUE ' : '';
    const columnList = columns.map(c => `"${c}"`).join(', ');
    return `CREATE ${uniqueStr}INDEX "${indexName}" ON "${tableName}" (${columnList});`;
  }

  async execute(operations: SyncOperation[]): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    
    try {
      await queryRunner.startTransaction();
      
      console.log(chalk.blue('\n🚀 Executing synchronization operations...\n'));

      for (const operation of operations) {
        console.log(chalk.gray(`Executing: ${operation.type} on ${operation.tableName}`));
        console.log(chalk.gray(`SQL: ${operation.sql}`));
        
        try {
          await queryRunner.query(operation.sql);
          console.log(chalk.green(`✅ Success: ${operation.type} on ${operation.tableName}\n`));
        } catch (error: any) {
          console.log(chalk.red(`❌ Failed: ${error.message}\n`));
          throw error;
        }
      }

      await queryRunner.commitTransaction();
      console.log(chalk.green('\n✅ All operations completed successfully!'));
      
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.log(chalk.red('\n❌ Transaction rolled back due to error'));
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  generateReport(operations: SyncOperation[]): string {
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: `${process.env.DB_NAME}@${process.env.DB_HOST}`,
      operationCount: operations.length,
      operations: operations.map(op => ({
        type: op.type,
        table: op.tableName,
        details: op.details,
        sql: op.sql
      }))
    };

    return JSON.stringify(report, null, 2);
  }
}

async function main() {
  const args = process.argv.slice(2);
  const isDryRun = args.includes('--dry-run');
  const shouldExecute = args.includes('--execute');
  const force = args.includes('--force');
  const help = args.includes('--help') || args.includes('-h');

  if (help || (!isDryRun && !shouldExecute)) {
    console.log(chalk.blue('Database Safe Sync Tool\n'));
    console.log('This tool safely synchronizes your database with TypeORM entities.');
    console.log('It only ADDS missing tables and columns, never removes anything.\n');
    console.log('Usage: npx ts-node scripts/database-safe-sync.ts [options]\n');
    console.log('Options:');
    console.log('  --dry-run     Show what would be synchronized without making changes');
    console.log('  --execute     Actually perform the synchronization');
    console.log('  --force       Skip confirmation prompts (use with caution)');
    console.log('  -h, --help    Show this help message');
    console.log('\nExamples:');
    console.log('  # Check what needs to be synchronized');
    console.log('  npx ts-node scripts/database-safe-sync.ts --dry-run\n');
    console.log('  # Perform synchronization');
    console.log('  npx ts-node scripts/database-safe-sync.ts --execute\n');
    console.log('  # Production sync with force flag');
    console.log('  NODE_ENV=production npx ts-node scripts/database-safe-sync.ts --execute --force');
    process.exit(0);
  }

  console.log(chalk.bold.blue('🛡️  Database Safe Sync Tool\n'));
  console.log(chalk.yellow(`Environment: ${process.env.NODE_ENV || 'development'}`));
  console.log(chalk.yellow(`Database: ${process.env.DB_NAME}@${process.env.DB_HOST}`));
  console.log(chalk.yellow(`Mode: ${isDryRun ? 'DRY RUN' : 'EXECUTE'}\n`));

  if (shouldExecute && process.env.NODE_ENV === 'production' && !force) {
    console.log(chalk.red.bold('⚠️  WARNING: You are about to modify the PRODUCTION database!'));
    console.log(chalk.yellow('This will add missing tables and columns to match your entities.'));
    const answer = await askQuestion(chalk.yellow('\nAre you absolutely sure? Type "yes" to continue: '));
    if (answer !== 'yes') {
      console.log(chalk.yellow('\nOperation cancelled.'));
      rl.close();
      return;
    }
  }

  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
    entities: [path.join(__dirname, '..', 'src', '**', '*.entity.{ts,js}')],
    synchronize: false,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log(chalk.green('✅ Connected to database\n'));

    const sync = new DatabaseSafeSync(dataSource);
    const operations = await sync.analyze();

    if (operations.length === 0) {
      console.log(chalk.green('\n✅ Database is already synchronized with entities!'));
      await dataSource.destroy();
      rl.close();
      return;
    }

    console.log(chalk.yellow(`\n📊 Found ${operations.length} synchronization operations needed:\n`));

    // Group operations by type
    const grouped = operations.reduce((acc, op) => {
      acc[op.type] = (acc[op.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    Object.entries(grouped).forEach(([type, count]) => {
      console.log(chalk.cyan(`  • ${type}: ${count}`));
    });

    // Save report
    const reportPath = path.join(__dirname, `db-safe-sync-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, sync.generateReport(operations));
    console.log(chalk.gray(`\n📄 Detailed report saved to: ${reportPath}`));

    if (isDryRun) {
      console.log(chalk.yellow('\n🔍 DRY RUN complete - no changes were made.'));
      console.log(chalk.yellow('Review the report and run with --execute to apply changes.'));
    } else if (shouldExecute) {
      console.log(chalk.yellow('\n🔨 Preparing to execute synchronization...'));
      
      if (!force) {
        const confirm = await askQuestion(chalk.yellow('\nProceed with synchronization? (yes/no): '));
        if (confirm.toLowerCase() !== 'yes') {
          console.log(chalk.yellow('\nOperation cancelled.'));
          await dataSource.destroy();
          rl.close();
          return;
        }
      }

      await sync.execute(operations);
      
      // Save success report
      const successReportPath = path.join(
        __dirname, 
        `db-safe-sync-success-${Date.now()}.json`
      );
      fs.writeFileSync(successReportPath, JSON.stringify({
        timestamp: new Date().toISOString(),
        status: 'success',
        operationsExecuted: operations.length,
        operations: operations
      }, null, 2));
      
      console.log(chalk.green(`\n📄 Success report saved to: ${successReportPath}`));
    }

    await dataSource.destroy();
    
  } catch (error) {
    console.error(chalk.red('\n❌ Error:'), error);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the tool
main().catch(console.error);