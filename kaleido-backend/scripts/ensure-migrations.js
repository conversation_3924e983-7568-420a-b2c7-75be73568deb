#!/usr/bin/env node

/**
 * Ensure Migrations Script
 * 
 * This script ensures database migrations are properly executed during Docker deployment.
 * It includes fallback to safe sync mode if migrations fail.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n🔄 ${description}...`, 'blue');
  try {
    const output = execSync(command, {
      encoding: 'utf8',
      stdio: 'pipe',
      cwd: path.join(__dirname, '..')
    });
    log(`✅ ${description} completed`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return { success: false, error: error.message || error.toString() };
  }
}

async function main() {
  log('🚀 Starting database migration process', 'blue');
  
  // Step 1: Check database connection
  const dbCheck = runCommand(
    'node -e "const pg = require(\'pg\'); const client = new pg.Client({ host: process.env.DB_HOST, port: process.env.DB_PORT, user: process.env.DB_USERNAME, password: process.env.DB_PASSWORD, database: process.env.DB_NAME, ssl: process.env.DB_SSL === \'require\' ? { rejectUnauthorized: false } : false }); client.connect().then(() => { console.log(\'Connected\'); return client.end(); }).then(() => process.exit(0)).catch(err => { console.error(err); process.exit(1); });"',
    'Checking database connection'
  );
  
  if (!dbCheck.success) {
    log('Cannot connect to database', 'red');
    process.exit(1);
  }
  
  // Step 2: Run TypeORM migrations
  log('\n📋 Running TypeORM migrations...', 'cyan');
  const migrationResult = runCommand(
    'NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:run -d src/config/migration.config.ts',
    'TypeORM migrations'
  );
  
  if (migrationResult.success) {
    log('✅ All migrations completed successfully!', 'green');
    process.exit(0);
  }
  
  // Step 3: If migrations failed, try safe sync
  log('\n⚠️  Migrations failed, attempting safe synchronization...', 'yellow');
  
  // First check what needs to be synced
  const syncCheckResult = runCommand(
    'npx ts-node scripts/database-sync-check.ts',
    'Database sync check'
  );
  
  if (syncCheckResult.output && syncCheckResult.output.includes('fully synchronized')) {
    log('✅ Database is already synchronized despite migration failure', 'green');
    process.exit(0);
  }
  
  // Run safe sync
  log('\n🔧 Running safe database synchronization...', 'cyan');
  const safeSyncResult = runCommand(
    'npx ts-node scripts/database-safe-sync.ts --execute --force',
    'Safe database sync'
  );
  
  if (safeSyncResult.success) {
    log('✅ Safe synchronization completed successfully!', 'green');
    
    // Verify final state
    const finalCheck = runCommand(
      'npx ts-node scripts/database-sync-check.ts',
      'Final database verification'
    );
    
    if (finalCheck.output && finalCheck.output.includes('fully synchronized')) {
      log('✅ Database is now fully synchronized', 'green');
      process.exit(0);
    } else {
      log('⚠️  Database synchronized with minor differences', 'yellow');
      process.exit(0); // Still exit successfully as safe sync worked
    }
  }
  
  // Step 4: Both migrations and safe sync failed
  log('\n❌ Both migration and safe sync failed', 'red');
  log('Please check database permissions and entity configurations', 'red');
  
  // Create error report
  const errorReport = {
    timestamp: new Date().toISOString(),
    migrationError: migrationResult.error,
    safeSyncError: safeSyncResult ? safeSyncResult.error : 'Not attempted',
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      DB_HOST: process.env.DB_HOST,
      DB_NAME: process.env.DB_NAME
    }
  };
  
  fs.writeFileSync(
    path.join(__dirname, `migration-error-${Date.now()}.json`),
    JSON.stringify(errorReport, null, 2)
  );
  
  process.exit(1);
}

// Run the script
main().catch(error => {
  log(`\n❌ Unexpected error: ${error.message}`, 'red');
  process.exit(1);
});