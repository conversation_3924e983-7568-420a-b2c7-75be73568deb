#!/bin/bash

# Docker Build Script with Safety Features
# This script builds and optionally deploys the Docker image with database safety

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="${DOCKER_IMAGE_NAME:-kaleido-backend}"
IMAGE_TAG="${DOCKER_IMAGE_TAG:-latest}"
REGISTRY="${DOCKER_REGISTRY:-}"
DOCKERFILE="${DOCKERFILE:-Dockerfile.prod}"
COMPOSE_FILE="${COMPOSE_FILE:-docker-compose.prod-safe.yml}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse arguments
BUILD_ONLY=false
PUSH_IMAGE=false
DEPLOY=false
SKIP_TESTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --build-only)
            BUILD_ONLY=true
            shift
            ;;
        --push)
            PUSH_IMAGE=true
            shift
            ;;
        --deploy)
            DEPLOY=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --build-only    Only build the image, don't run it"
            echo "  --push          Push image to registry after build"
            echo "  --deploy        Deploy using docker-compose after build"
            echo "  --skip-tests    Skip pre-build tests"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Main script
log_info "🚀 Starting Docker build process with safety features"

# Step 1: Pre-build checks
if [ "$SKIP_TESTS" != "true" ]; then
    log_info "Running pre-build checks..."
    
    # Check TypeScript compilation
    log_info "Checking TypeScript compilation..."
    if npm run typecheck; then
        log_success "TypeScript compilation check passed"
    else
        log_error "TypeScript compilation failed"
        exit 1
    fi
    
    # Check if migrations are valid
    log_info "Validating migrations..."
    if npm run validate:migrations; then
        log_success "Migrations validation passed"
    else
        log_warning "Migration validation failed - build will continue but deployment may fail"
    fi
fi

# Step 2: Build the Docker image
log_info "Building Docker image: ${IMAGE_NAME}:${IMAGE_TAG}"

# Create build timestamp
BUILD_TIMESTAMP=$(date +%Y%m%d-%H%M%S)
BUILD_ARGS="--build-arg BUILD_TIMESTAMP=${BUILD_TIMESTAMP}"

# Add registry if specified
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

# Build the image
if docker build ${BUILD_ARGS} -t "${FULL_IMAGE_NAME}" -f "${DOCKERFILE}" .; then
    log_success "Docker image built successfully: ${FULL_IMAGE_NAME}"
else
    log_error "Docker build failed"
    exit 1
fi

# Tag with additional tags
docker tag "${FULL_IMAGE_NAME}" "${IMAGE_NAME}:${BUILD_TIMESTAMP}"
docker tag "${FULL_IMAGE_NAME}" "${IMAGE_NAME}:latest"

# Step 3: Run safety tests on the built image
log_info "Running safety tests on built image..."

# Test that the image can start
log_info "Testing image startup..."
CONTAINER_NAME="test-${IMAGE_NAME}-${BUILD_TIMESTAMP}"

if docker run -d \
    --name "${CONTAINER_NAME}" \
    -e NODE_ENV=test \
    -e SKIP_MIGRATIONS=true \
    -e DB_HOST=localhost \
    -e DB_PORT=5432 \
    -e DB_USERNAME=test \
    -e DB_PASSWORD=test \
    -e DB_NAME=test \
    "${FULL_IMAGE_NAME}"; then
    
    # Wait a moment for container to start
    sleep 5
    
    # Check if container is still running
    if docker ps | grep -q "${CONTAINER_NAME}"; then
        log_success "Container started successfully"
        
        # Check logs for errors
        if docker logs "${CONTAINER_NAME}" 2>&1 | grep -q "ERROR\|FATAL"; then
            log_warning "Errors found in container logs"
            docker logs "${CONTAINER_NAME}"
        fi
        
        # Stop and remove test container
        docker stop "${CONTAINER_NAME}" > /dev/null 2>&1
        docker rm "${CONTAINER_NAME}" > /dev/null 2>&1
    else
        log_error "Container failed to start"
        docker logs "${CONTAINER_NAME}"
        docker rm "${CONTAINER_NAME}" > /dev/null 2>&1
        exit 1
    fi
else
    log_error "Failed to run test container"
    exit 1
fi

# Step 4: Push to registry if requested
if [ "$PUSH_IMAGE" = true ] && [ -n "$REGISTRY" ]; then
    log_info "Pushing image to registry: ${REGISTRY}"
    
    # Login to registry if credentials are provided
    if [ -n "$DOCKER_USERNAME" ] && [ -n "$DOCKER_PASSWORD" ]; then
        echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin "$REGISTRY"
    fi
    
    # Push all tags
    docker push "${FULL_IMAGE_NAME}"
    docker push "${REGISTRY}/${IMAGE_NAME}:${BUILD_TIMESTAMP}"
    
    log_success "Image pushed to registry"
fi

# Step 5: Deploy if requested
if [ "$DEPLOY" = true ]; then
    log_info "Deploying with docker-compose..."
    
    # Export image name for docker-compose
    export DOCKER_IMAGE="${FULL_IMAGE_NAME}"
    
    # Check if we should backup the database first
    if [ "$SKIP_DB_BACKUP" != "true" ]; then
        log_info "Creating database backup before deployment..."
        docker-compose -f "${COMPOSE_FILE}" run --rm db-backup || log_warning "Database backup failed but continuing"
    fi
    
    # Deploy with docker-compose
    if docker-compose -f "${COMPOSE_FILE}" up -d; then
        log_success "Deployment started successfully"
        
        # Wait for health checks
        log_info "Waiting for health checks..."
        sleep 30
        
        # Check deployment status
        if docker-compose -f "${COMPOSE_FILE}" ps | grep -q "healthy"; then
            log_success "Deployment is healthy"
        else
            log_warning "Some services may not be healthy"
            docker-compose -f "${COMPOSE_FILE}" ps
        fi
        
        # Show logs
        log_info "Recent logs:"
        docker-compose -f "${COMPOSE_FILE}" logs --tail=50
    else
        log_error "Deployment failed"
        exit 1
    fi
fi

# Summary
log_success "🎉 Docker build process completed successfully!"
log_info "Image: ${FULL_IMAGE_NAME}"
log_info "Additional tags: ${IMAGE_NAME}:${BUILD_TIMESTAMP}, ${IMAGE_NAME}:latest"

if [ "$BUILD_ONLY" = true ]; then
    log_info "Build-only mode - image ready for manual deployment"
else
    log_info "Image tested and ready for use"
fi

# Provide next steps
echo ""
log_info "📋 Next steps:"
echo "  - To run locally: docker run -p 8080:8080 --env-file .env ${FULL_IMAGE_NAME}"
echo "  - To deploy: docker-compose -f ${COMPOSE_FILE} up -d"
echo "  - To check logs: docker-compose -f ${COMPOSE_FILE} logs -f"
echo "  - To check health: docker-compose -f ${COMPOSE_FILE} ps"