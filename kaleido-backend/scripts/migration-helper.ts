#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import chalk from 'chalk';
import { DataSource } from 'typeorm';

export class MigrationHelper {
  private static MIGRATION_DIR = path.join(__dirname, '../src/migrations');
  private static CONFIG_PATH = 'src/config/migration.config.ts';

  /**
   * Ensure migrations directory exists
   */
  static ensureMigrationDirectory(): void {
    if (!fs.existsSync(this.MIGRATION_DIR)) {
      fs.mkdirSync(this.MIGRATION_DIR, { recursive: true });
      console.log(chalk.green(`✅ Created migrations directory: ${this.MIGRATION_DIR}`));
    }
  }

  /**
   * Run a command with proper error handling
   */
  static runCommand(command: string, options: any = {}): { success: boolean; output?: string; error?: string } {
    try {
      const output = execSync(command, {
        encoding: 'utf8',
        cwd: path.join(__dirname, '..'),
        ...options
      });
      return { success: true, output };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || String(error),
        output: error.stdout || error.output?.toString()
      };
    }
  }

  /**
   * Validate TypeORM setup
   */
  static async validateSetup(): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = [];

    // Check if TypeORM is installed
    try {
      require('typeorm');
    } catch {
      issues.push('TypeORM is not installed');
    }

    // Check if config file exists
    const configPath = path.join(__dirname, '..', this.CONFIG_PATH);
    if (!fs.existsSync(configPath)) {
      issues.push(`Migration config file not found: ${this.CONFIG_PATH}`);
    }

    // Check if entities directory exists
    const entitiesPath = path.join(__dirname, '../src/entities');
    if (!fs.existsSync(entitiesPath)) {
      issues.push('Entities directory not found: src/entities');
    }

    // Check if entities index exists
    const entitiesIndexPath = path.join(__dirname, '../src/entities/index.ts');
    if (!fs.existsSync(entitiesIndexPath)) {
      issues.push('Entities index file not found: src/entities/index.ts');
    }

    return { valid: issues.length === 0, issues };
  }

  /**
   * Get all entity files
   */
  static getEntityFiles(): string[] {
    const entityPaths: string[] = [];
    const directories = [
      'src/entities',
      'src/modules',
    ];

    directories.forEach(dir => {
      const fullPath = path.join(__dirname, '..', dir);
      if (fs.existsSync(fullPath)) {
        this.findEntityFiles(fullPath, entityPaths);
      }
    });

    return entityPaths;
  }

  private static findEntityFiles(dir: string, result: string[]): void {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        this.findEntityFiles(filePath, result);
      } else if (file.endsWith('.entity.ts')) {
        result.push(filePath);
      }
    });
  }

  /**
   * Validate entity file
   */
  static validateEntityFile(filePath: string): { valid: boolean; issues: string[] } {
    const issues: string[] = [];
    const content = fs.readFileSync(filePath, 'utf8');

    // Check for @Entity decorator
    if (!content.includes('@Entity')) {
      issues.push('Missing @Entity decorator');
    }

    // Check for imports
    if (!content.includes("from 'typeorm'") && !content.includes('from "typeorm"')) {
      issues.push('Missing TypeORM imports');
    }

    // Check for at least one @Column
    if (!content.includes('@Column')) {
      issues.push('No @Column decorators found');
    }

    // Check for primary key
    if (!content.includes('@PrimaryGeneratedColumn') && !content.includes('@PrimaryColumn')) {
      issues.push('No primary key decorator found');
    }

    return { valid: issues.length === 0, issues };
  }

  /**
   * Generate migration with retries
   */
  static async generateMigrationWithRetry(name: string, maxRetries: number = 3): Promise<{ success: boolean; filename?: string; error?: string }> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(chalk.blue(`\n🔄 Attempt ${attempt}/${maxRetries} to generate migration...`));
      
      const result = this.runCommand(
        `NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:generate -d ${this.CONFIG_PATH} src/migrations/${name}`,
        { stdio: 'pipe' }
      );

      if (result.success) {
        // Find the generated file
        const files = fs.readdirSync(this.MIGRATION_DIR);
        const generatedFile = files.find(f => f.includes(name) && f.endsWith('.ts'));
        
        if (generatedFile) {
          return { success: true, filename: generatedFile };
        }
      }

      if (attempt < maxRetries) {
        console.log(chalk.yellow(`⚠️  Attempt ${attempt} failed, retrying...`));
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      } else {
        return { success: false, error: result.error };
      }
    }

    return { success: false, error: 'Max retries reached' };
  }

  /**
   * Compare database schema with entities
   */
  static async compareSchemaWithEntities(dataSource: DataSource): Promise<{ differences: string[] }> {
    const differences: string[] = [];

    try {
      const metadata = dataSource.entityMetadatas;
      
      for (const entityMetadata of metadata) {
        const tableName = entityMetadata.tableName;
        
        // Check if table exists
        const tableExists = await dataSource.query(
          `SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          )`,
          [tableName]
        );

        if (!tableExists[0].exists) {
          differences.push(`Table '${tableName}' does not exist in database`);
          continue;
        }

        // Check columns
        for (const column of entityMetadata.columns) {
          const columnExists = await dataSource.query(
            `SELECT EXISTS (
              SELECT FROM information_schema.columns 
              WHERE table_schema = 'public' 
              AND table_name = $1 
              AND column_name = $2
            )`,
            [tableName, column.databaseName]
          );

          if (!columnExists[0].exists) {
            differences.push(`Column '${column.databaseName}' in table '${tableName}' does not exist`);
          }
        }
      }
    } catch (error) {
      differences.push(`Error comparing schema: ${error}`);
    }

    return { differences };
  }

  /**
   * Fix common migration issues
   */
  static fixCommonIssues(): void {
    console.log(chalk.blue('\n🔧 Attempting to fix common issues...'));

    // 1. Update entity exports
    console.log(chalk.gray('  • Updating entity exports...'));
    const updateResult = this.runCommand('pnpm run entities:update');
    if (updateResult.success) {
      console.log(chalk.green('  ✅ Entity exports updated'));
    } else {
      console.log(chalk.yellow('  ⚠️  Failed to update entity exports'));
    }

    // 2. Clear TypeORM cache
    console.log(chalk.gray('  • Clearing TypeORM cache...'));
    const cacheDir = path.join(__dirname, '../.typeorm-cache');
    if (fs.existsSync(cacheDir)) {
      fs.rmSync(cacheDir, { recursive: true, force: true });
      console.log(chalk.green('  ✅ TypeORM cache cleared'));
    }

    // 3. Ensure migrations directory exists
    this.ensureMigrationDirectory();

    // 4. Check TypeScript compilation
    console.log(chalk.gray('  • Checking TypeScript compilation...'));
    const tscResult = this.runCommand('npx tsc --noEmit', { stdio: 'pipe' });
    if (tscResult.success) {
      console.log(chalk.green('  ✅ TypeScript compilation successful'));
    } else {
      console.log(chalk.yellow('  ⚠️  TypeScript compilation errors found'));
    }
  }

  /**
   * Create a migration manually
   */
  static createManualMigration(name: string, up: string, down: string): string {
    const timestamp = Date.now();
    const className = `${name}${timestamp}`;
    const filename = `${timestamp}-${name}.ts`;
    
    const content = `import { MigrationInterface, QueryRunner } from "typeorm";

export class ${className} implements MigrationInterface {
    name = '${className}'

    public async up(queryRunner: QueryRunner): Promise<void> {
${up.split('\n').map(line => '        ' + line).join('\n')}
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
${down.split('\n').map(line => '        ' + line).join('\n')}
    }
}
`;

    const filePath = path.join(this.MIGRATION_DIR, filename);
    fs.writeFileSync(filePath, content);
    
    return filename;
  }
}

// Export for use in other scripts
export default MigrationHelper;