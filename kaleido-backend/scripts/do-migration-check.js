#!/usr/bin/env node

/**
 * DigitalOcean Pre-Deploy Migration Check
 * 
 * This script runs as a pre-deploy job to validate database state
 * and prepare for the main deployment.
 */

const { execSync } = require('child_process');
const path = require('path');

// Simple logging
const log = (message, type = 'info') => {
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : '🔍';
  console.log(`${prefix} ${message}`);
};

async function main() {
  log('Starting pre-deploy database checks for DigitalOcean App Platform');
  
  try {
    // Check database connection
    log('Checking database connection...');
    
    const dbCheckCmd = `node -e "
      const pg = require('pg');
      const connectionString = process.env.DATABASE_URL || 
        \\\`postgresql://\\\${process.env.DB_USERNAME}:\\\${process.env.DB_PASSWORD}@\\\${process.env.DB_HOST}:\\\${process.env.DB_PORT}/\\\${process.env.DB_NAME}?sslmode=require\\\`;
      
      const client = new pg.Client({ connectionString });
      
      client.connect()
        .then(() => client.query('SELECT current_database()'))
        .then(result => {
          console.log('Connected to database:', result.rows[0].current_database);
          return client.end();
        })
        .then(() => process.exit(0))
        .catch(err => {
          console.error('Database connection failed:', err.message);
          process.exit(1);
        });
    "`;
    
    execSync(dbCheckCmd, { stdio: 'inherit' });
    log('Database connection successful', 'success');
    
    // Check for pending migrations
    log('Checking migration status...');
    
    try {
      const output = execSync(
        'npx typeorm-ts-node-commonjs migration:show -d src/config/migration.config.ts',
        { encoding: 'utf8' }
      );
      
      if (output.includes('pending')) {
        log('⚠️  Pending migrations detected - they will be run during deployment');
      } else {
        log('No pending migrations', 'success');
      }
    } catch (error) {
      log('⚠️  Could not check migration status - will attempt during deployment');
    }
    
    // Check database schema sync
    log('Analyzing database schema...');
    
    try {
      const syncOutput = execSync(
        'npx ts-node scripts/database-sync-check.ts',
        { encoding: 'utf8' }
      );
      
      if (syncOutput.includes('fully synchronized')) {
        log('Database schema is synchronized', 'success');
      } else {
        log('⚠️  Database schema differences detected - will be handled during deployment');
      }
    } catch (error) {
      log('⚠️  Could not analyze schema - will check during deployment');
    }
    
    log('Pre-deploy checks completed', 'success');
    process.exit(0);
    
  } catch (error) {
    log(`Pre-deploy check failed: ${error.message}`, 'error');
    
    // In pre-deploy, we might want to continue anyway
    if (process.env.ALLOW_DEPLOY_ON_CHECK_FAILURE === 'true') {
      log('⚠️  Continuing deployment despite check failures');
      process.exit(0);
    } else {
      process.exit(1);
    }
  }
}

// Run the checks
main().catch(error => {
  log(`Fatal error: ${error.message}`, 'error');
  process.exit(1);
});