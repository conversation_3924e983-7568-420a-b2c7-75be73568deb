#!/usr/bin/env node

/**
 * DigitalOcean App Platform Start Script with Database Safety
 * 
 * This script ensures database migrations are handled safely in DO App Platform
 * where we have limited control over the deployment environment.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description, options = {}) {
  log(`\n🔄 ${description}...`, 'blue');
  try {
    const output = execSync(command, {
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      cwd: path.join(__dirname, '..'),
      timeout: options.timeout || 300000, // 5 minutes default
      env: { ...process.env, NODE_OPTIONS: '--max-old-space-size=2048' }
    });
    log(`✅ ${description} completed`, 'green');
    return { success: true, output };
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    if (!options.silent) {
      console.error(error.message || error.toString());
    }
    return { success: false, error: error.message || error.toString() };
  }
}

async function checkDatabase() {
  log('🔌 Checking database connection...', 'cyan');
  
  // DO App Platform provides DATABASE_URL
  if (!process.env.DATABASE_URL && !process.env.DB_HOST) {
    log('❌ No database configuration found', 'red');
    log('Please ensure DATABASE_URL or DB_* environment variables are set', 'yellow');
    return false;
  }
  
  const dbCheck = runCommand(
    `node -e "
      const pg = require('pg');
      const connectionString = process.env.DATABASE_URL || 
        \\\`postgresql://\\\${process.env.DB_USERNAME}:\\\${process.env.DB_PASSWORD}@\\\${process.env.DB_HOST}:\\\${process.env.DB_PORT}/\\\${process.env.DB_NAME}?sslmode=require\\\`;
      
      const client = new pg.Client({ connectionString });
      
      client.connect()
        .then(() => {
          console.log('✅ Database connection successful');
          return client.end();
        })
        .then(() => process.exit(0))
        .catch(err => {
          console.error('❌ Database connection failed:', err.message);
          process.exit(1);
        });
    "`,
    'Database connection check',
    { timeout: 30000 }
  );
  
  return dbCheck.success;
}

async function runMigrations() {
  log('\n📋 Starting database synchronization...', 'cyan');
  
  // First, check current database state
  const syncCheck = runCommand(
    'npx ts-node scripts/database-sync-check.ts',
    'Database sync check',
    { silent: true }
  );
  
  if (syncCheck.output && syncCheck.output.includes('fully synchronized')) {
    log('✅ Database is already synchronized', 'green');
    return true;
  }
  
  // Try to run migrations
  log('\n🔄 Attempting to run migrations...', 'cyan');
  const migrationResult = runCommand(
    'npx typeorm-ts-node-commonjs migration:run -d src/config/migration.config.ts',
    'TypeORM migrations',
    { timeout: 300000 }
  );
  
  if (migrationResult.success) {
    log('✅ Migrations completed successfully', 'green');
    return true;
  }
  
  // If migrations failed, try safe sync
  log('\n⚠️  Migrations failed, attempting safe synchronization...', 'yellow');
  
  // Check if we're allowed to use safe sync
  if (process.env.ALLOW_SAFE_SYNC === 'false') {
    log('❌ Safe sync is disabled. Please fix migrations manually.', 'red');
    return false;
  }
  
  const safeSyncResult = runCommand(
    'npx ts-node scripts/database-safe-sync.ts --execute --force',
    'Safe database synchronization',
    { timeout: 300000 }
  );
  
  if (safeSyncResult.success) {
    log('✅ Safe synchronization completed', 'green');
    
    // Verify final state
    const finalCheck = runCommand(
      'npx ts-node scripts/database-sync-check.ts',
      'Final verification',
      { silent: true }
    );
    
    if (finalCheck.output && finalCheck.output.includes('fully synchronized')) {
      log('✅ Database is now fully synchronized', 'green');
      return true;
    }
  }
  
  return false;
}

async function startApplication() {
  log('\n🚀 Starting application...', 'cyan');
  
  // Check if we should use PM2
  if (fs.existsSync(path.join(__dirname, '..', 'ecosystem.config.js'))) {
    log('Using PM2 for process management', 'blue');
    
    // Install PM2 if not available
    try {
      execSync('pm2 --version', { stdio: 'ignore' });
    } catch {
      log('Installing PM2...', 'blue');
      execSync('npm install -g pm2', { stdio: 'inherit' });
    }
    
    // Start with PM2
    execSync('pm2-runtime start ecosystem.config.js --env production', {
      stdio: 'inherit',
      env: { ...process.env }
    });
  } else {
    // Direct node start
    log('Starting with Node.js directly', 'blue');
    
    // Use exec to replace the current process
    require('child_process').spawn('node', ['dist/main'], {
      stdio: 'inherit',
      env: { ...process.env }
    });
  }
}

async function main() {
  log('🚀 DigitalOcean App Platform Start Script', 'blue');
  log(`Environment: ${process.env.NODE_ENV || 'production'}`, 'blue');
  log(`Region: ${process.env.DO_REGION || 'unknown'}`, 'blue');
  
  // Create deployment report
  const report = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'production',
    region: process.env.DO_REGION,
    appId: process.env.DO_APP_ID,
    deployment: process.env.DO_DEPLOYMENT_ID,
    database: {
      configured: !!(process.env.DATABASE_URL || process.env.DB_HOST)
    }
  };
  
  try {
    // Step 1: Check database
    if (!await checkDatabase()) {
      log('❌ Cannot start without database connection', 'red');
      report.status = 'failed';
      report.error = 'Database connection failed';
      process.exit(1);
    }
    
    report.database.connected = true;
    
    // Step 2: Handle migrations
    if (process.env.SKIP_MIGRATIONS === 'true') {
      log('⚠️  Skipping migrations (SKIP_MIGRATIONS=true)', 'yellow');
      report.migrations = { skipped: true };
    } else {
      const migrationSuccess = await runMigrations();
      report.migrations = { success: migrationSuccess };
      
      if (!migrationSuccess) {
        log('❌ Database synchronization failed', 'red');
        report.status = 'failed';
        report.error = 'Database synchronization failed';
        
        // In DO App Platform, we might want to start anyway
        if (process.env.FORCE_START_ON_SYNC_FAILURE === 'true') {
          log('⚠️  Starting anyway due to FORCE_START_ON_SYNC_FAILURE=true', 'yellow');
        } else {
          process.exit(1);
        }
      }
    }
    
    // Step 3: Start the application
    report.status = 'starting';
    
    // Save report
    const reportPath = '/tmp/do-deployment-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log(`📊 Deployment report saved: ${reportPath}`, 'blue');
    
    // Start the app
    await startApplication();
    
  } catch (error) {
    log(`\n❌ Unexpected error: ${error.message}`, 'red');
    report.status = 'failed';
    report.error = error.message;
    
    // Save error report
    fs.writeFileSync('/tmp/do-deployment-error.json', JSON.stringify(report, null, 2));
    
    process.exit(1);
  }
}

// Handle signals gracefully
process.on('SIGTERM', () => {
  log('\n📛 Received SIGTERM, shutting down gracefully...', 'yellow');
  process.exit(0);
});

process.on('SIGINT', () => {
  log('\n📛 Received SIGINT, shutting down gracefully...', 'yellow');
  process.exit(0);
});

// Run the main function
main().catch(error => {
  log(`\n❌ Fatal error: ${error.message}`, 'red');
  process.exit(1);
});