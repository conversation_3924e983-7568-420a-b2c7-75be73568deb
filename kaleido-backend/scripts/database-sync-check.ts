#!/usr/bin/env ts-node
/**
 * Database Synchronization Check Tool
 * 
 * This script checks if your database schema is synchronized with your TypeORM entities.
 * It can be run against any environment by setting NODE_ENV.
 * 
 * Usage:
 *   # Check local database
 *   npx ts-node scripts/database-sync-check.ts
 *   
 *   # Check production database
 *   NODE_ENV=production npx ts-node -r tsconfig-paths/register scripts/database-sync-check.ts
 * 
 * Features:
 * - Compares entities with database tables
 * - Identifies missing columns, extra columns, and type mismatches
 * - Shows which columns have data (important for cleanup decisions)
 * - Generates a detailed report
 */

import { DataSource } from 'typeorm';
import * as path from 'path';
import * as dotenv from 'dotenv';
import chalk from 'chalk';
import * as fs from 'fs';

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production.local' : '.env';
dotenv.config({ path: envFile });

interface ColumnDifference {
  columnName: string;
  entityType: string;
  databaseType?: string;
  issue: 'missing' | 'type_mismatch' | 'extra';
  hasData?: boolean;
  rowCount?: number;
}

interface TableDifference {
  tableName: string;
  entityName: string;
  issue: 'missing_table' | 'extra_table' | 'column_differences';
  columnDifferences?: ColumnDifference[];
}

async function checkDatabaseSync(checkData: boolean = false) {
  console.log(chalk.blue.bold('🔍 Database Schema Synchronization Check\n'));
  console.log(chalk.gray(`Environment: ${process.env.NODE_ENV || 'development'}`));
  console.log(chalk.gray(`Database: ${process.env.DB_NAME}@${process.env.DB_HOST}\n`));
  
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
    entities: [path.join(__dirname, '..', 'src', '**', '*.entity.{ts,js}')],
    synchronize: false,
    logging: false,
  });

  try {
    await dataSource.initialize();
    console.log(chalk.green('✓ Connected to database\n'));

    const entityMetadatas = dataSource.entityMetadatas;
    const queryRunner = dataSource.createQueryRunner();
    
    const databaseTables = await queryRunner.getTables();
    const userTables = databaseTables.filter(t => 
      !t.name.startsWith('pg_') && 
      !t.name.startsWith('information_schema') &&
      t.name !== 'migrations' &&
      !t.name.startsWith('typeorm_')
    );

    console.log(chalk.yellow(`Found ${entityMetadatas.length} entities and ${userTables.length} database tables\n`));

    const differences: TableDifference[] = [];
    let totalMissingColumns = 0;
    let totalExtraColumns = 0;

    // Check each entity
    for (const metadata of entityMetadatas) {
      const tableName = metadata.tableName;
      const entityName = metadata.name;
      
      process.stdout.write(chalk.blue(`Checking ${entityName} (${tableName})... `));

      const databaseTable = userTables.find(t => t.name === tableName);
      
      if (!databaseTable) {
        differences.push({
          tableName,
          entityName,
          issue: 'missing_table'
        });
        console.log(chalk.red('✗ TABLE MISSING'));
        continue;
      }

      const columnDifferences: ColumnDifference[] = [];
      const dbColumns = new Map(databaseTable.columns.map(col => [col.name, col]));

      // Check for missing columns
      for (const column of metadata.columns) {
        const dbColumn = dbColumns.get(column.databaseName);
        if (!dbColumn) {
          columnDifferences.push({
            columnName: column.databaseName,
            entityType: getReadableType(column),
            issue: 'missing'
          });
          totalMissingColumns++;
        }
      }

      // Check for extra columns
      for (const [colName, dbColumn] of dbColumns) {
        const entityColumn = metadata.columns.find(c => c.databaseName === colName);
        if (!entityColumn) {
          let hasData = false;
          let rowCount = 0;
          
          if (checkData) {
            try {
              const result = await dataSource.query(`
                SELECT COUNT(*) as count 
                FROM "${tableName}" 
                WHERE "${colName}" IS NOT NULL
              `);
              rowCount = parseInt(result[0].count);
              hasData = rowCount > 0;
            } catch (e) {
              // Ignore errors
            }
          }
          
          columnDifferences.push({
            columnName: colName,
            databaseType: dbColumn.type,
            entityType: '',
            issue: 'extra',
            hasData,
            rowCount
          });
          totalExtraColumns++;
        }
      }

      if (columnDifferences.length > 0) {
        differences.push({
          tableName,
          entityName,
          issue: 'column_differences',
          columnDifferences
        });
        console.log(chalk.red(`✗ ${columnDifferences.length} ISSUES`));
      } else {
        console.log(chalk.green('✓'));
      }
    }

    // Display results
    console.log(chalk.blue.bold('\n\n📊 SYNCHRONIZATION REPORT\n'));
    
    if (differences.length === 0) {
      console.log(chalk.green.bold('✅ Database schema is fully synchronized with entities!'));
    } else {
      console.log(chalk.red.bold(`❌ Found synchronization issues:\n`));
      
      const missingTables = differences.filter(d => d.issue === 'missing_table');
      const tablesWithIssues = differences.filter(d => d.issue === 'column_differences');

      if (missingTables.length > 0) {
        console.log(chalk.red(`\n🚫 Missing tables (${missingTables.length}):`));
        missingTables.forEach(t => {
          console.log(chalk.red(`  - ${t.tableName} (${t.entityName})`));
        });
      }

      if (tablesWithIssues.length > 0) {
        console.log(chalk.yellow(`\n⚠️  Tables with column issues (${tablesWithIssues.length}):\n`));
        
        tablesWithIssues.forEach(table => {
          console.log(chalk.yellow.bold(`${table.tableName} (${table.entityName}):`));
          
          const missing = table.columnDifferences?.filter(c => c.issue === 'missing') || [];
          const extra = table.columnDifferences?.filter(c => c.issue === 'extra') || [];
          
          if (missing.length > 0) {
            console.log(chalk.red('  Missing columns:'));
            missing.forEach(col => {
              console.log(chalk.red(`    - ${col.columnName}: ${col.entityType}`));
            });
          }
          
          if (extra.length > 0) {
            console.log(chalk.yellow('  Extra columns:'));
            extra.forEach(col => {
              const dataInfo = checkData && col.hasData 
                ? chalk.red(` [HAS DATA: ${col.rowCount} rows]`)
                : checkData ? chalk.green(' [empty]') : '';
              console.log(chalk.yellow(`    - ${col.columnName}: ${col.databaseType}${dataInfo}`));
            });
          }
          console.log('');
        });
      }

      console.log(chalk.yellow.bold('\nSummary:'));
      console.log(chalk.red(`  - Missing columns: ${totalMissingColumns}`));
      console.log(chalk.yellow(`  - Extra columns: ${totalExtraColumns}`));
      console.log(chalk.red(`  - Missing tables: ${missingTables.length}`));
      
      console.log(chalk.yellow.bold('\n💡 Recommendations:'));
      if (totalMissingColumns > 0) {
        console.log(chalk.cyan('  1. Generate and run migrations to add missing columns'));
      }
      if (totalExtraColumns > 0) {
        console.log(chalk.cyan('  2. Review extra columns - add to entities or remove from database'));
      }
    }

    // Save detailed report
    const reportPath = path.join(__dirname, `db-sync-report-${Date.now()}.json`);
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      database: `${process.env.DB_NAME}@${process.env.DB_HOST}`,
      differences,
      summary: {
        totalIssues: differences.length,
        missingColumns: totalMissingColumns,
        extraColumns: totalExtraColumns,
        missingTables: missingTables.length
      }
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(chalk.gray(`\n📄 Detailed report saved to: ${reportPath}`));

    await queryRunner.release();
    await dataSource.destroy();
    
  } catch (error) {
    console.error(chalk.red('\n❌ Error checking database sync:'), error);
    process.exit(1);
  }
}

function getReadableType(column: any): string {
  let type = column.type;
  
  if (typeof type === 'function') {
    type = type.name.toLowerCase();
  } else if (typeof type === 'string') {
    type = type.toLowerCase();
  }
  
  if (column.isArray) {
    type = `${type}[]`;
  }
  
  if (column.isNullable) {
    type += ' (nullable)';
  }
  
  if (column.default !== undefined && column.default !== null) {
    type += ` (default: ${column.default})`;
  }
  
  return type;
}

// Parse command line arguments
const args = process.argv.slice(2);
const checkData = args.includes('--check-data') || args.includes('-d');

if (args.includes('--help') || args.includes('-h')) {
  console.log(chalk.blue('Database Synchronization Check Tool\n'));
  console.log('Usage: npx ts-node scripts/database-sync-check.ts [options]\n');
  console.log('Options:');
  console.log('  -d, --check-data    Also check if columns contain data');
  console.log('  -h, --help          Show this help message');
  console.log('\nExamples:');
  console.log('  # Check local database');
  console.log('  npx ts-node scripts/database-sync-check.ts\n');
  console.log('  # Check production database with data');
  console.log('  NODE_ENV=production npx ts-node -r tsconfig-paths/register scripts/database-sync-check.ts --check-data');
  process.exit(0);
}

// Run the check
checkDatabaseSync(checkData).catch(console.error);