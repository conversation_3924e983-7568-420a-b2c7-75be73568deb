#!/usr/bin/env ts-node
/**
 * Database Migration Runner
 * 
 * Safe migration runner for production and development environments.
 * Loads the appropriate environment variables and runs pending migrations.
 * 
 * Usage:
 *   # Run migrations in development
 *   npx ts-node scripts/database-migration-runner.ts
 *   
 *   # Run migrations in production
 *   NODE_ENV=production npx ts-node scripts/database-migration-runner.ts
 *   
 *   # Dry run (show pending migrations without running)
 *   npx ts-node scripts/database-migration-runner.ts --dry-run
 */

import * as dotenv from 'dotenv';
import { DataSource } from 'typeorm';
import * as path from 'path';
import chalk from 'chalk';
import * as readline from 'readline';

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production' ? '.env.production.local' : '.env';
dotenv.config({ path: envFile });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

async function runMigrations(dryRun: boolean = false) {
  console.log(chalk.blue.bold('🚀 Database Migration Runner\n'));
  console.log(chalk.yellow(`Environment: ${process.env.NODE_ENV || 'development'}`));
  console.log(chalk.yellow(`Database: ${process.env.DB_NAME}@${process.env.DB_HOST}`));
  console.log(chalk.yellow(`Mode: ${dryRun ? 'DRY RUN' : 'LIVE'}\n`));

  if (process.env.NODE_ENV === 'production' && !dryRun) {
    console.log(chalk.red.bold('⚠️  WARNING: You are about to run migrations on PRODUCTION database!'));
    const answer = await askQuestion(chalk.yellow('Are you sure you want to continue? (yes/no): '));
    if (answer.toLowerCase() !== 'yes') {
      console.log(chalk.yellow('\nMigration cancelled.'));
      rl.close();
      return;
    }
  }

  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false,
    entities: [path.join(__dirname, '..', 'src', '**', '*.entity.{ts,js}')],
    migrations: [path.join(__dirname, '..', 'src', 'migrations', '*.{ts,js}')],
    migrationsTableName: 'migrations',
    logging: true,
  });

  try {
    await dataSource.initialize();
    console.log(chalk.green('\n✓ Connected to database\n'));
    
    // Show pending migrations
    const pendingMigrations = await dataSource.showMigrations();
    console.log(chalk.yellow('Checking for pending migrations...\n'));
    
    const migrations = await dataSource.migrations;
    const executedMigrations = await dataSource.query(
      `SELECT * FROM migrations ORDER BY timestamp`
    );
    
    const executedMigrationNames = new Set(executedMigrations.map((m: any) => m.name));
    const pendingMigrationsList = migrations.filter(
      m => !executedMigrationNames.has(m.name)
    );
    
    if (pendingMigrationsList.length === 0) {
      console.log(chalk.green('✅ No pending migrations to run.'));
      await dataSource.destroy();
      rl.close();
      return;
    }
    
    console.log(chalk.yellow(`Found ${pendingMigrationsList.length} pending migration(s):\n`));
    pendingMigrationsList.forEach((migration, index) => {
      console.log(chalk.cyan(`  ${index + 1}. ${migration.name}`));
    });
    
    if (dryRun) {
      console.log(chalk.yellow('\n🔍 DRY RUN mode - migrations were NOT executed.'));
      console.log(chalk.yellow('Remove --dry-run flag to execute these migrations.'));
    } else {
      console.log(chalk.yellow('\nRunning migrations...\n'));
      
      try {
        await dataSource.runMigrations({ transaction: 'all' });
        console.log(chalk.green('\n✅ All migrations completed successfully!'));
        
        // Show final migration status
        const finalExecuted = await dataSource.query(
          `SELECT * FROM migrations ORDER BY timestamp DESC LIMIT 5`
        );
        
        console.log(chalk.blue('\n📋 Recent migrations:'));
        finalExecuted.forEach((m: any) => {
          const timestamp = new Date(parseInt(m.timestamp)).toLocaleString();
          console.log(chalk.gray(`  - ${m.name} (${timestamp})`));
        });
        
      } catch (error) {
        console.error(chalk.red('\n❌ Migration failed:'), error);
        console.log(chalk.yellow('\n💡 Tips:'));
        console.log(chalk.yellow('  - Check the error message above'));
        console.log(chalk.yellow('  - Ensure all entities are properly defined'));
        console.log(chalk.yellow('  - Verify database connectivity'));
        console.log(chalk.yellow('  - Run database-sync-check.ts to see current state'));
        throw error;
      }
    }
    
    await dataSource.destroy();
    
  } catch (error) {
    console.error(chalk.red('\n❌ Error:'), error);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run') || args.includes('-n');
const help = args.includes('--help') || args.includes('-h');

if (help) {
  console.log(chalk.blue('Database Migration Runner\n'));
  console.log('Usage: npx ts-node scripts/database-migration-runner.ts [options]\n');
  console.log('Options:');
  console.log('  -n, --dry-run    Show pending migrations without running them');
  console.log('  -h, --help       Show this help message');
  console.log('\nExamples:');
  console.log('  # Run migrations in development');
  console.log('  npx ts-node scripts/database-migration-runner.ts\n');
  console.log('  # Check pending migrations in production');
  console.log('  NODE_ENV=production npx ts-node scripts/database-migration-runner.ts --dry-run\n');
  console.log('  # Run migrations in production');
  console.log('  NODE_ENV=production npx ts-node scripts/database-migration-runner.ts');
  process.exit(0);
}

// Run migrations
runMigrations(dryRun).catch(console.error);