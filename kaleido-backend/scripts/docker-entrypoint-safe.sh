#!/bin/sh

# Docker Entrypoint Script with Database Safety
# This script ensures database integrity even if migrations fail
# It includes automatic fallback to safe sync mode

set -e

echo "🚀 Starting container services with database safety..."

# Function to check if database is accessible
check_database() {
  echo "🔌 Checking database connection..."
  node -e "
    const pg = require('pg');
    const client = new pg.Client({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      ssl: process.env.DB_SSL === 'require' ? { rejectUnauthorized: false } : false
    });
    
    client.connect()
      .then(() => {
        console.log('✅ Database connection successful');
        return client.end();
      })
      .then(() => process.exit(0))
      .catch(err => {
        console.error('❌ Database connection failed:', err.message);
        process.exit(1);
      });
  "
  return $?
}

# Start Redis
echo "🔄 Starting Redis..."
sh /app/scripts/start-redis-docker.sh

# Check Redis status (but don't fail if check fails)
echo "🔄 Checking Redis status..."
sh /app/scripts/check-redis-docker.sh || echo "⚠️  Redis check failed but continuing..."

# Set Node.js memory options
export NODE_OPTIONS="--max-old-space-size=8192"
echo "🔧 Set Node.js memory limit to 8GB"

# Check database connection before attempting migrations
if ! check_database; then
  echo "❌ Cannot connect to database. Exiting..."
  exit 1
fi

# Run database safety checks and migrations
echo "🔄 Starting database synchronization process..."
if [ "$SKIP_MIGRATIONS" != "true" ]; then
  echo "📋 Running database safety checks..."

  # First, check current database state
  echo "🔍 Analyzing database schema..."
  npx ts-node scripts/database-sync-check.ts > /tmp/db-check.log 2>&1
  DB_CHECK_CODE=$?
  
  if [ $DB_CHECK_CODE -eq 0 ]; then
    echo "✅ Database schema check completed"
    
    # Check if there are differences
    if grep -q "Database schema is fully synchronized" /tmp/db-check.log; then
      echo "✅ Database is already synchronized - no migrations needed"
    else
      echo "⚠️  Database schema differences detected"
      cat /tmp/db-check.log
      
      # Try to run migrations first
      echo "🔄 Attempting to run migrations..."
      npx ts-node scripts/database-migration-runner.ts --force > /tmp/migration.log 2>&1
      MIGRATION_CODE=$?
      
      if [ $MIGRATION_CODE -eq 0 ]; then
        echo "✅ Migrations completed successfully"
      else
        echo "⚠️  Migrations failed - falling back to safe sync mode"
        cat /tmp/migration.log
        
        # Fall back to safe sync
        echo "🔧 Running safe database synchronization..."
        npx ts-node scripts/database-safe-sync.ts --execute --force > /tmp/safe-sync.log 2>&1
        SYNC_CODE=$?
        
        if [ $SYNC_CODE -eq 0 ]; then
          echo "✅ Safe sync completed successfully"
          
          # Verify final state
          echo "🔍 Verifying final database state..."
          npx ts-node scripts/database-sync-check.ts > /tmp/final-check.log 2>&1
          FINAL_CHECK=$?
          
          if [ $FINAL_CHECK -eq 0 ] && grep -q "Database schema is fully synchronized" /tmp/final-check.log; then
            echo "✅ Database is now fully synchronized"
          else
            echo "⚠️  Database may still have minor differences but proceeding..."
            cat /tmp/final-check.log
          fi
        else
          echo "❌ Safe sync also failed"
          cat /tmp/safe-sync.log
          
          # Last resort - check if we can proceed anyway
          echo "🤔 Checking if application can start despite sync issues..."
          node -e "
            console.log('Testing TypeORM connection...');
            const dataSource = require('./dist/config/database.config.js');
            dataSource.initialize()
              .then(() => {
                console.log('✅ TypeORM can connect - proceeding with startup');
                return dataSource.destroy();
              })
              .then(() => process.exit(0))
              .catch(err => {
                console.error('❌ TypeORM cannot initialize:', err.message);
                process.exit(1);
              });
          "
          TYPEORM_TEST=$?
          
          if [ $TYPEORM_TEST -ne 0 ]; then
            echo ""
            echo "❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌"
            echo "❌ CRITICAL: Database synchronization failed"
            echo "❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌❌"
            echo ""
            echo "The application cannot start because:"
            echo "1. Migrations failed"
            echo "2. Safe sync failed"
            echo "3. TypeORM cannot connect with current schema"
            echo ""
            echo "Please check:"
            echo "- Database permissions (CREATE/ALTER table required)"
            echo "- Entity definitions match expected schema"
            echo "- All TypeORM decorators are correct"
            echo ""
            exit 1
          fi
        fi
      fi
    fi
  else
    echo "⚠️  Database check failed but attempting to continue..."
  fi
  
  # Create deployment report
  echo "📊 Creating deployment report..."
  node -e "
    const fs = require('fs');
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'production',
      database: \`\${process.env.DB_NAME}@\${process.env.DB_HOST}\`,
      container: process.env.HOSTNAME || 'unknown',
      dbCheckStatus: ${DB_CHECK_CODE},
      migrationStatus: ${MIGRATION_CODE:-'skipped'},
      safeSyncStatus: ${SYNC_CODE:-'skipped'},
      finalCheckStatus: ${FINAL_CHECK:-'skipped'}
    };
    fs.writeFileSync('/tmp/deployment-report.json', JSON.stringify(report, null, 2));
    console.log('✅ Deployment report created: /tmp/deployment-report.json');
  "
else
  echo "⚠️  Migrations skipped (SKIP_MIGRATIONS=true)"
fi

# Wait a moment to ensure everything is ready
echo "⏳ Waiting for services to stabilize..."
sleep 2

# Start the application
echo "🚀 Starting application on port ${PORT:-8080}..."
echo "📋 Deployment completed at: $(date)"
echo ""

# Execute the main application
exec node dist/main