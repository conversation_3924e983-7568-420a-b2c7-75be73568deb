#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';
import chalk from 'chalk';

const MIGRATION_DIR = path.join(__dirname, '../src/migrations');
const CONFIG_PATH = 'src/config/migration-enhanced.config.ts';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      resolve(answer);
    });
  });
}

function validateMigrationName(name: string): boolean {
  return /^[a-zA-Z][a-zA-Z0-9]*$/.test(name);
}

function formatMigrationName(name: string): string {
  return name
    .split(/[-_\s]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

async function checkDatabaseConnection() {
  console.log(chalk.blue('🔌 Checking database connection...'));
  
  try {
    const output = execSync(
      `NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs query "SELECT 1" -d ${CONFIG_PATH}`,
      { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..'),
        env: { ...process.env, MIGRATION_DEBUG: 'false' }
      }
    );
    
    console.log(chalk.green('✅ Database connection successful'));
    return true;
  } catch (error) {
    console.error(chalk.red('❌ Database connection failed:'));
    console.error(error instanceof Error ? error.message : error);
    return false;
  }
}

async function validateEntities() {
  console.log(chalk.blue('🔍 Validating entities...'));
  
  try {
    // Run TypeScript compilation check on entities
    execSync(
      'npx tsc --noEmit --project tsconfig.json',
      { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..'),
        stdio: 'pipe'
      }
    );
    
    console.log(chalk.green('✅ Entity validation successful'));
    return true;
  } catch (error) {
    console.error(chalk.red('❌ Entity validation failed:'));
    console.error(error instanceof Error ? error.message : error);
    return false;
  }
}

async function detectSchemaChanges(debug: boolean = false): Promise<{ hasChanges: boolean; details?: string }> {
  console.log(chalk.blue('🔄 Detecting schema changes...'));
  
  const tempName = `TempCheck${Date.now()}`;
  
  try {
    const env = debug ? { ...process.env, MIGRATION_DEBUG: 'true' } : process.env;
    
    const output = execSync(
      `NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:generate -d ${CONFIG_PATH} src/migrations/${tempName} 2>&1`,
      { 
        encoding: 'utf8',
        cwd: path.join(__dirname, '..'),
        env
      }
    );
    
    // Clean up temp file if created
    const tempFiles = fs.readdirSync(MIGRATION_DIR).filter(f => f.includes(tempName));
    tempFiles.forEach(f => fs.unlinkSync(path.join(MIGRATION_DIR, f)));
    
    if (output.includes('No changes in database schema were found')) {
      return { hasChanges: false };
    }
    
    // Extract migration details from output
    const migrationMatch = output.match(/Migration .* has been generated successfully/);
    const details = migrationMatch ? migrationMatch[0] : 'Changes detected';
    
    return { hasChanges: true, details };
  } catch (error) {
    // If the command fails, it might be due to actual schema changes
    // Let's check if a migration file was created
    const tempFiles = fs.readdirSync(MIGRATION_DIR).filter(f => f.includes(tempName));
    
    if (tempFiles.length > 0) {
      // Read the migration file to get details
      const migrationContent = fs.readFileSync(path.join(MIGRATION_DIR, tempFiles[0]), 'utf8');
      
      // Clean up temp files
      tempFiles.forEach(f => fs.unlinkSync(path.join(MIGRATION_DIR, f)));
      
      // Extract changes from migration
      const upMatch = migrationContent.match(/async up\(queryRunner: QueryRunner\): Promise<void> \{([\s\S]*?)\}/);
      const changes = upMatch ? upMatch[1].trim() : 'Changes detected';
      
      return { hasChanges: true, details: changes };
    }
    
    // If no temp file was created, there might be an actual error
    throw error;
  }
}

async function generateMigration(name: string, debug: boolean = false) {
  console.log(chalk.blue(`\n📝 Generating migration: ${name}...`));
  
  try {
    const env = debug ? { ...process.env, MIGRATION_DEBUG: 'true' } : process.env;
    
    const output = execSync(
      `NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:generate -d ${CONFIG_PATH} src/migrations/${name}`,
      { 
        stdio: 'pipe',
        encoding: 'utf8',
        cwd: path.join(__dirname, '..'),
        env
      }
    );
    
    // Find the generated file
    const files = fs.readdirSync(MIGRATION_DIR);
    const generatedFile = files.find(f => f.includes(name) && f.endsWith('.ts'));
    
    if (generatedFile) {
      return { success: true, filename: generatedFile, output };
    } else {
      return { success: false, error: 'Migration file not found after generation' };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: errorMessage };
  }
}

async function validateMigration(filename: string) {
  console.log(chalk.blue(`\n🔍 Validating migration: ${filename}...`));
  
  const filePath = path.join(MIGRATION_DIR, filename);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check for common issues
  const issues: string[] = [];
  
  // Check for empty up/down methods
  if (content.includes('async up(queryRunner: QueryRunner): Promise<void> {\n    }')) {
    issues.push('Empty up() method detected');
  }
  
  if (content.includes('async down(queryRunner: QueryRunner): Promise<void> {\n    }')) {
    issues.push('Empty down() method detected');
  }
  
  // Check for potentially dangerous operations
  if (content.match(/DROP\s+TABLE/i)) {
    issues.push('⚠️  Contains DROP TABLE statements');
  }
  
  if (content.match(/DELETE\s+FROM/i)) {
    issues.push('⚠️  Contains DELETE statements');
  }
  
  if (content.match(/TRUNCATE/i)) {
    issues.push('⚠️  Contains TRUNCATE statements');
  }
  
  return { valid: issues.length === 0, issues };
}

async function main() {
  try {
    console.log(chalk.bold.blue('🚀 Enhanced TypeORM Migration Generator\n'));
    
    // Step 1: Check database connection
    if (!await checkDatabaseConnection()) {
      console.log(chalk.red('\n❌ Cannot proceed without database connection'));
      rl.close();
      return;
    }
    
    // Step 2: Validate entities
    if (!await validateEntities()) {
      const proceed = await question(chalk.yellow('\n⚠️  Entity validation failed. Continue anyway? (y/n): '));
      if (proceed.toLowerCase() !== 'y') {
        rl.close();
        return;
      }
    }
    
    // Step 3: Detect schema changes
    const { hasChanges, details } = await detectSchemaChanges();
    
    if (!hasChanges) {
      console.log(chalk.green('\n✅ No schema changes detected. No migration needed.'));
      rl.close();
      return;
    }
    
    console.log(chalk.green('\n✅ Schema changes detected:'));
    if (details) {
      console.log(chalk.gray(details));
    }
    
    // Step 4: Get migration name
    let migrationName = await question(chalk.cyan('\nEnter migration name (e.g., AddUserTable, UpdateCompanyIndex): '));
    
    if (!migrationName) {
      console.log(chalk.red('❌ Migration name is required.'));
      rl.close();
      return;
    }
    
    migrationName = formatMigrationName(migrationName);
    
    if (!validateMigrationName(migrationName)) {
      console.log(chalk.red('❌ Invalid migration name. Use only letters and numbers.'));
      rl.close();
      return;
    }
    
    // Step 5: Confirm generation
    console.log(chalk.yellow(`\n📋 Migration Summary:`));
    console.log(chalk.gray(`  • Name: ${migrationName}`));
    console.log(chalk.gray(`  • Config: ${CONFIG_PATH}`));
    console.log(chalk.gray(`  • Output: src/migrations/`));
    
    const confirm = await question(chalk.cyan('\nProceed with generation? (y/n): '));
    
    if (confirm.toLowerCase() !== 'y') {
      console.log(chalk.gray('Cancelled.'));
      rl.close();
      return;
    }
    
    // Step 6: Generate migration
    const result = await generateMigration(migrationName);
    
    if (!result.success) {
      console.log(chalk.red(`\n❌ Failed to generate migration: ${result.error}`));
      
      // Offer debug mode
      const debug = await question(chalk.yellow('\nRun in debug mode? (y/n): '));
      if (debug.toLowerCase() === 'y') {
        const debugResult = await generateMigration(migrationName, true);
        if (!debugResult.success) {
          console.log(chalk.red(`\n❌ Debug mode also failed: ${debugResult.error}`));
        }
      }
      
      rl.close();
      return;
    }
    
    console.log(chalk.green(`\n✅ Migration generated: ${result.filename}`));
    
    // Step 7: Validate migration
    const validation = await validateMigration(result.filename!);
    
    if (!validation.valid) {
      console.log(chalk.yellow('\n⚠️  Migration validation warnings:'));
      validation.issues.forEach(issue => console.log(chalk.yellow(`  • ${issue}`)));
    }
    
    // Step 8: Show migration content
    const showContent = await question(chalk.cyan('\nShow migration content? (y/n): '));
    
    if (showContent.toLowerCase() === 'y') {
      const content = fs.readFileSync(path.join(MIGRATION_DIR, result.filename!), 'utf8');
      console.log(chalk.gray('\n--- Migration Content ---'));
      console.log(content);
      console.log(chalk.gray('--- End of Migration ---\n'));
    }
    
    // Step 9: Next steps
    console.log(chalk.bold.blue('\n📌 Next steps:'));
    console.log(chalk.gray('1. Review the generated migration file'));
    console.log(chalk.gray('2. Test locally: pnpm run migration:run'));
    console.log(chalk.gray('3. Test rollback: pnpm run migration:revert'));
    console.log(chalk.gray('4. Commit both entity changes and migration'));
    
    // Step 10: Offer to run migration
    const runNow = await question(chalk.cyan('\nRun migration now? (y/n): '));
    
    if (runNow.toLowerCase() === 'y') {
      console.log(chalk.blue('\n🏃 Running migration...'));
      
      try {
        execSync(
          `NODE_OPTIONS="--max-old-space-size=8192" npx typeorm-ts-node-commonjs migration:run -d ${CONFIG_PATH}`,
          { 
            stdio: 'inherit',
            cwd: path.join(__dirname, '..')
          }
        );
        
        console.log(chalk.green('\n✅ Migration executed successfully'));
      } catch (error) {
        console.error(chalk.red('\n❌ Migration execution failed:'));
        console.error(error instanceof Error ? error.message : error);
      }
    }
    
  } catch (error) {
    console.error(chalk.red('\n❌ Unexpected error:'));
    console.error(error instanceof Error ? error.stack : error);
  } finally {
    rl.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}