#!/usr/bin/env ts-node
/**
 * Safe Deployment Script with Database Protection
 * 
 * This script ensures your production deployment never fails due to database schema issues.
 * It runs migrations and falls back to safe sync if migrations fail.
 * 
 * Usage:
 *   # Deploy with automatic database safety
 *   NODE_ENV=production npx ts-node scripts/deploy-with-db-safety.ts
 *   
 *   # Skip migrations and only sync
 *   NODE_ENV=production npx ts-node scripts/deploy-with-db-safety.ts --sync-only
 *   
 *   # Force deployment without prompts
 *   NODE_ENV=production npx ts-node scripts/deploy-with-db-safety.ts --force
 */

import { execSync } from 'child_process';
import * as path from 'path';
import chalk from 'chalk';
import * as fs from 'fs';
import * as dotenv from 'dotenv';
import * as readline from 'readline';

// Load environment variables
const envFile = process.env.NODE_ENV === 'production' ? '.env.production.local' : '.env';
dotenv.config({ path: envFile });

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

interface DeploymentStep {
  name: string;
  description: string;
  command: string;
  critical: boolean;
  fallback?: string;
}

class SafeDeployment {
  private logFile: string;
  private steps: DeploymentStep[] = [];
  private results: Array<{ step: string; status: 'success' | 'failed' | 'skipped'; message?: string }> = [];

  constructor() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.logFile = path.join(__dirname, `deployment-log-${timestamp}.log`);
    
    // Initialize deployment steps
    this.steps = [
      {
        name: 'Pre-deployment checks',
        description: 'Validate environment and dependencies',
        command: 'npx ts-node scripts/validate-deployment.ts',
        critical: true
      },
      {
        name: 'Database backup',
        description: 'Create database backup before changes',
        command: this.getBackupCommand(),
        critical: false
      },
      {
        name: 'Check database sync',
        description: 'Analyze current database state',
        command: 'npx ts-node scripts/database-sync-check.ts --check-data',
        critical: false
      },
      {
        name: 'Run migrations',
        description: 'Apply pending database migrations',
        command: 'npx ts-node scripts/database-migration-runner.ts',
        critical: false,
        fallback: 'npx ts-node scripts/database-safe-sync.ts --execute --force'
      },
      {
        name: 'Verify database state',
        description: 'Ensure database matches entities',
        command: 'npx ts-node scripts/database-sync-check.ts',
        critical: true
      },
      {
        name: 'Build application',
        description: 'Compile TypeScript and build assets',
        command: 'pnpm build',
        critical: true
      },
      {
        name: 'Start application',
        description: 'Launch the production server',
        command: 'NODE_OPTIONS="--max-old-space-size=8192" pm2 restart ecosystem.config.js --env production',
        critical: true
      }
    ];
  }

  private getBackupCommand(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFile = `backup-${process.env.DB_NAME}-${timestamp}.sql`;
    
    return `PGPASSWORD="${process.env.DB_PASSWORD}" pg_dump -h ${process.env.DB_HOST} -p ${process.env.DB_PORT} -U ${process.env.DB_USERNAME} -d ${process.env.DB_NAME} -f ${backupFile}`;
  }

  private log(message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info'): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    
    // Write to log file
    fs.appendFileSync(this.logFile, logMessage + '\n');
    
    // Console output with colors
    switch (type) {
      case 'success':
        console.log(chalk.green(message));
        break;
      case 'error':
        console.log(chalk.red(message));
        break;
      case 'warning':
        console.log(chalk.yellow(message));
        break;
      default:
        console.log(chalk.blue(message));
    }
  }

  private async executeStep(step: DeploymentStep): Promise<boolean> {
    this.log(`\n🔄 ${step.name}`, 'info');
    this.log(`   ${step.description}`, 'info');
    
    try {
      if (step.command.includes('pg_dump')) {
        // Special handling for backup command to hide password
        this.log('   Executing: Database backup...', 'info');
      } else {
        this.log(`   Executing: ${step.command}`, 'info');
      }
      
      execSync(step.command, {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..'),
        env: { ...process.env }
      });
      
      this.log(`✅ ${step.name} completed successfully`, 'success');
      this.results.push({ step: step.name, status: 'success' });
      return true;
      
    } catch (error) {
      this.log(`❌ ${step.name} failed`, 'error');
      this.results.push({ 
        step: step.name, 
        status: 'failed', 
        message: error instanceof Error ? error.message : String(error) 
      });
      
      if (step.fallback) {
        this.log(`🔧 Attempting fallback: ${step.fallback}`, 'warning');
        
        try {
          execSync(step.fallback, {
            stdio: 'inherit',
            cwd: path.join(__dirname, '..'),
            env: { ...process.env }
          });
          
          this.log(`✅ Fallback successful`, 'success');
          return true;
        } catch (fallbackError) {
          this.log(`❌ Fallback also failed`, 'error');
          return false;
        }
      }
      
      return false;
    }
  }

  async deploy(options: { force?: boolean; syncOnly?: boolean } = {}): Promise<void> {
    this.log('🚀 Starting Safe Deployment Process', 'info');
    this.log(`Environment: ${process.env.NODE_ENV}`, 'info');
    this.log(`Database: ${process.env.DB_NAME}@${process.env.DB_HOST}`, 'info');
    this.log(`Log file: ${this.logFile}`, 'info');

    if (!options.force && process.env.NODE_ENV === 'production') {
      console.log(chalk.yellow('\n⚠️  You are about to deploy to PRODUCTION!'));
      const answer = await askQuestion('Are you sure you want to continue? (yes/no): ');
      if (answer.toLowerCase() !== 'yes') {
        this.log('Deployment cancelled by user', 'warning');
        return;
      }
    }

    // Filter steps if sync-only mode
    let stepsToRun = this.steps;
    if (options.syncOnly) {
      stepsToRun = this.steps.filter(s => 
        s.name.includes('database') || s.name.includes('sync') || s.name.includes('Check')
      );
    }

    // Execute deployment steps
    for (const step of stepsToRun) {
      const success = await this.executeStep(step);
      
      if (!success && step.critical) {
        this.log(`\n❌ Critical step failed: ${step.name}`, 'error');
        this.log('Deployment aborted to prevent damage', 'error');
        break;
      }
    }

    // Generate deployment report
    this.generateReport();
  }

  private generateReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      database: `${process.env.DB_NAME}@${process.env.DB_HOST}`,
      results: this.results,
      summary: {
        total: this.results.length,
        successful: this.results.filter(r => r.status === 'success').length,
        failed: this.results.filter(r => r.status === 'failed').length,
        skipped: this.results.filter(r => r.status === 'skipped').length
      }
    };

    const reportPath = path.join(
      __dirname, 
      `deployment-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
    );
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log('\n📊 Deployment Summary:', 'info');
    this.log(`   Total steps: ${report.summary.total}`, 'info');
    this.log(`   ✅ Successful: ${report.summary.successful}`, 'success');
    
    if (report.summary.failed > 0) {
      this.log(`   ❌ Failed: ${report.summary.failed}`, 'error');
    }
    
    if (report.summary.skipped > 0) {
      this.log(`   ⏭️  Skipped: ${report.summary.skipped}`, 'warning');
    }
    
    this.log(`\n📄 Detailed report: ${reportPath}`, 'info');
    this.log(`📄 Full log: ${this.logFile}`, 'info');
  }
}

// Create validation script if it doesn't exist
const validationScriptPath = path.join(__dirname, 'validate-deployment.ts');
if (!fs.existsSync(validationScriptPath)) {
  const validationScript = `#!/usr/bin/env ts-node
import chalk from 'chalk';
import * as fs from 'fs';
import * as path from 'path';

console.log(chalk.blue('🔍 Validating deployment environment...\\n'));

const checks = [
  { name: 'Node.js version', pass: process.version.match(/^v1[89]|^v2[0-9]/) !== null },
  { name: 'Environment variables', pass: !!process.env.DB_HOST && !!process.env.DB_NAME },
  { name: 'Build directory', pass: fs.existsSync(path.join(__dirname, '..', 'dist')) || true },
  { name: 'TypeScript compilation', pass: true } // Would run tsc --noEmit
];

let allPassed = true;
checks.forEach(check => {
  if (check.pass) {
    console.log(chalk.green(\`✅ \${check.name}\`));
  } else {
    console.log(chalk.red(\`❌ \${check.name}\`));
    allPassed = false;
  }
});

if (!allPassed) {
  console.log(chalk.red('\\n❌ Validation failed!'));
  process.exit(1);
} else {
  console.log(chalk.green('\\n✅ All validations passed!'));
}
`;
  fs.writeFileSync(validationScriptPath, validationScript);
}

async function main() {
  const args = process.argv.slice(2);
  const force = args.includes('--force');
  const syncOnly = args.includes('--sync-only');
  const help = args.includes('--help') || args.includes('-h');

  if (help) {
    console.log(chalk.blue('Safe Deployment Script\n'));
    console.log('Ensures production deployments never fail due to database issues.\n');
    console.log('Usage: npx ts-node scripts/deploy-with-db-safety.ts [options]\n');
    console.log('Options:');
    console.log('  --force       Skip confirmation prompts');
    console.log('  --sync-only   Only run database synchronization steps');
    console.log('  -h, --help    Show this help message');
    console.log('\nExamples:');
    console.log('  # Normal deployment with safety checks');
    console.log('  NODE_ENV=production npx ts-node scripts/deploy-with-db-safety.ts\n');
    console.log('  # Force deployment without prompts');
    console.log('  NODE_ENV=production npx ts-node scripts/deploy-with-db-safety.ts --force');
    rl.close();
    return;
  }

  const deployment = new SafeDeployment();
  
  try {
    await deployment.deploy({ force, syncOnly });
    console.log(chalk.green('\n✅ Deployment completed!'));
  } catch (error) {
    console.error(chalk.red('\n❌ Deployment failed:'), error);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n\n⚠️  Deployment interrupted by user'));
  rl.close();
  process.exit(1);
});

// Run deployment
main().catch(console.error);