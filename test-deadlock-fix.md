# Deadlock Fix Testing Guide

## Issue Summary
The application was experiencing PostgreSQL deadlock errors when saving job threshold changes in the JobForm component. The error occurred when updating candidate tiers after threshold modifications.

## Root Causes Identified

1. **Backend Issue**: The `updateCandidateTiersEfficiently` method was updating tables without proper row locking and consistent ordering, causing deadlocks when concurrent transactions tried to update the same rows.

2. **Frontend Issue**: The JobForm component was making duplicate API calls (`updateJob` and `updateJobMatchRankCriteria`) that could trigger concurrent database updates.

## Fixes Applied

### Backend Fix (kaleido-backend/src/modules/job/job.service.ts:515-601)
- Added explicit row locking with `FOR UPDATE` clause
- Implemented consistent ordering by candidate ID to prevent deadlock
- Added transaction isolation level (`SERIALIZ<PERSON>LE`)
- Implemented retry logic with exponential backoff for deadlock scenarios
- Maximum 3 retry attempts with increasing delays (100ms, 200ms, 400ms)

### Frontend Fix (kaleido-app/src/components/MatchRank/components/JobForm.tsx:276-331)
- Added concurrent save prevention check
- Removed duplicate `updateJobMatchRankCriteria` API call
- Kept only the main `updateJob` API call which handles all updates
- Retained local store updates for UI consistency

## Testing Steps

1. **Single Update Test**
   - Open a job in the MatchRank view
   - Change the Top Candidate Threshold
   - Click Save
   - Verify it saves without errors

2. **Rapid Update Test**
   - Change thresholds multiple times quickly
   - Click Save multiple times rapidly
   - Verify no deadlock errors occur

3. **Concurrent Update Test**
   - Open the same job in two browser tabs
   - Change thresholds in both tabs
   - Save in both tabs simultaneously
   - Verify retry logic handles any conflicts

## Expected Behavior
- Updates should complete successfully
- If a deadlock occurs, the backend will automatically retry
- Users will see "Job updated successfully" toast
- No "deadlock detected" errors in console

## Monitoring
Check backend logs for:
- "Deadlock detected for job X, retrying" (indicates retry is working)
- "Updated candidate tiers based on new thresholds" (indicates success)
- No "QueryFailedError: deadlock detected" errors