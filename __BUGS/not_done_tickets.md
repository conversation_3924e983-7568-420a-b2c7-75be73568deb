# Tickets Not Marked as Done

## KAL-67: Separate onboarding process, make talent flow simpler

**Description:**
Basic application info and job related info should be separate. All onboarding steps should not be mandotary and user should be able to close out of them. Should be one smooth process instead of disjointed ones. Onboarding should only need specific info for the profile, info for jobs/industry should be separate.

- **Status:** Todo
- **Priority:** High
- **Project:** Talent
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-79: Video JD issues

**Description:**

- When I expanded the Video JD to full size I couldn’t close it and needed to close the whole tab
- When downloading the video J<PERSON> starts playing in the background but the video is not playing. So there is no opportunity to stop it

* **Status:** Backlog
* **Priority:** No priority
* **Project:** Solution
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-83: Culture Fit add question improvements

**Description:**
Need to improve adding question functionality, a bit confusing right now.

- **Status:** Backlog
- **Priority:** No priority
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-86: Ensure email process can be tracked e2e

- **Status:** Backlog
- **Priority:** No priority
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-91: Privacy settings page dropdown menu not working

**Description:**
Talent onboarding

- **Status:** Backlog
- **Priority:** High
- **Project:** Talent
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-92: Some of the icons next to input fields are not centred on various pages.

- **Status:** Todo
- **Priority:** Medium
- **Project:** Talent
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-100: Functionality to review email being sent

**Description:**
Employers should be able to see an editable preview of the email notifications they send out.

- **Status:** Backlog
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-101: Potential functionality to have teams/team members to have a shared database

- **Status:** Backlog
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-105: Need to add functionality for users to delete their profiles

- **Status:** Todo
- **Priority:** High
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-1: General: Data Collection

**Description:**
how do we become data company: what data are we focusing on

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-2: Partner: Connect with PI.AI

**Description:**
as underlying technology and advantage / USP (Ask Fausto to connect with them?)

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-3: Both: Communication

**Description:**
Enable Communication between Companies & Talent: Companies connect propose to Talent to connect and Talent can accept or not.

Possibility to set up video call?

- **Status:** Triage
- **Priority:** High
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-4: Company: Follow & Progress Tracking

**Description:**
Companies can follow a Talent Profile and their progress / can receive regular updates if they want on progress

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-5: Talent: Social Media / Pod Cast for Talent / Centralize Interviews

**Description:**
Film Interviews with Talent that they can make public / or publish in insta every week (for screened good candidates) - an opportunity for companies to skip interviews and centralize interviews

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-6: Mindset Shift: Push Project based Hiring to expedite hiring

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-7: P1: Talent: Career Path Creation

**Description:**

- People can define their aspirations and life goals
- AI suggests different career paths they can choose from
- AI breaks it down in actionable steps that they can follow and achieve milestones (unlocks benefits - visibility to companies?)
- Gamification like Duolingo

* **Status:** Triage
* **Priority:** Urgent
* **Project:** nan
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL2-8: Talent: Job Proposals - Define the Unknown

**Description:**
Categories: - with AI help people to find jobs suggestions that are connected to their skillsets and strengths which leads to diversification of job applications

- **the known known:** The jobs talent applies to automatically like everyone (PM…)
- **the known unknown:** Jobs talent _knows exist_ in theory but doesn’t know how to access, understand, or apply for or maybe thinks they are not qualified even though they are.
- **the unknown unknown:** Jobs talent doesn't even realize exist — or are a great fit for them — until they’re surfaced. (due to market change, innovation, times of AI)

* **Status:** Triage
* **Priority:** No priority
* **Project:** nan
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-107: Add company website and individual URL to job posts

- **Status:** Backlog
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-108: Connect / Integrate with whatsapp?

- **Status:** Backlog
- **Priority:** No priority
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-9: Community: People support each other on achieving milestones - reddit for hiring & application questions

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-10: Both: Problem & Superpowers

**Description:**
Companies change from JD to Problem Description & Talent from CVs to Superpowers

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-11: Talent: Questionnaire to understand people better?

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-12: Ideal Candiate

**Description:**
Upload profile of person they have worked with before and was a good team fit and great for the job - AI will suggest someone from the talent pool

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-13: Talent: How to assess superpower?

- **Status:** Triage
- **Priority:** No priority
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-14: Both: Referral

**Description:**
People can refer people to a position (through our platform? if you have a profile, look for name and add to JD - easy) - can earn money - bonus us paid from person who looks for the talent

- **Status:** Triage
- **Priority:** High
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-109: Talent: Add willingness to relocate to Talent Profile

- **Status:** Todo
- **Priority:** Medium
- **Project:** Talent
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-112: Search bar on talent hub page not working

- **Status:** Backlog
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-15: Michael to check CRM pipeline

- **Status:** Triage
- **Priority:** High
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-114: Add functionality to send multiple video introductions at the same time

- **Status:** Backlog
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-116: Referral Button for Website & Solution (Soft Launch)

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-117: trigger emails for engagement

**Description:**
Set up trigger emails for us and for customers

- **Status:** Duplicate
- **Priority:** High
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-118: P2: COMPLIANCE Preparation Checks

**Description:**
Cyber Security Check

Stress Test

GDPR Tests

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-122: Email Draft Visible?

**Description:**
TBD: Users need to be able to see the email draft / notification that will be sent out to the talent?

- **Status:** Backlog
- **Priority:** Low
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-123: When downloading Video JD, it should automatically download or open the video in new tab to be saved

- **Status:** Todo
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-125: Talent: Wallet CV and Dashbaord sharing

**Description:**
Talent needs to be able to save their CV and Dashboard somewhere on their phone and share it. (Can be something to get people talking and for marketing)

- **Status:** Backlog
- **Priority:** Medium
- **Project:** Talent
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-130: Facebook/Insta/Twitter social media connections not working

**Description:**
![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/0edd2d52-fd5f-4078-adf3-0f0d46c89d7d/f5db282f-bc34-4ae1-9621-cc175f276eea?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi8wZWRkMmQ1Mi1mZDVmLTQwNzgtYWRmMy0wZjBkNDZjODlkN2QvZjVkYjI4MmYtYmMzNC00YWUxLTk2MjEtY2MxNzVmMjc2ZWVhIiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.rxkMRuuDuP7vLBzYdMrprmpo8nQq4rPj-drENwhDNdQ)

![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/8b393e21-dc65-4a31-9ebb-da3dc9d850eb/d61fbdc0-a527-4f8c-a3bd-a874ff485907?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi84YjM5M2UyMS1kYzY1LTRhMzEtOWViYi1kYTNkYzlkODUwZWIvZDYxZmJkYzAtYTUyNy00ZjhjLWEzYmQtYTg3NGZmNDg1OTA3IiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.wi0YNSk-peLVD3tJxnr_mu-NFEBYG0mRxhr4b8SV0B8)

![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/b6638dbe-db07-45fb-8adf-396822a2c578/e2aad844-0fbb-4e2e-b81d-9bcfaa3b138e?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi9iNjYzOGRiZS1kYjA3LTQ1ZmItOGFkZi0zOTY4MjJhMmM1NzgvZTJhYWQ4NDQtMGZiYi00ZTJlLWI4MWQtOWJjZmFhM2IxMzhlIiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.u_vgjnRV7o8sZ9W1R-oIwAa0S672y1Fd0z_bJc90KpM)

- **Status:** Todo
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-131: Remove "send single email" option from admin options

**Description:**
Bulk email works but not sending a single email - remove that option or fix it

- **Status:** Backlog
- **Priority:** Medium
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-134: Kaleido WATERMARK on Video JD and Video Intros - ask Eric

**Description:**
How do we brand it with our logo but also give the companies enough exposure that talent does not think its a job at kaleido.

- **Status:** In Progress
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-20: Action Buttons need to have different color so they are easily visible

- **Status:** Canceled
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-27: P2: Match and Rank Compliance: Change " Decision" to "Outcome" or "Analysis"

**Description:**
Due to the EU AI pact we cant make decisions so we need to change it

Also we cant say" not recommended to hire" we need to say: good fit, poor fit or so or "critical gaps identified"

- **Status:** In Review
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-29: All buttons in match and rank should be in one place and have a different color that makes it easy to see them

- **Status:** Canceled
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-30: P1 PoC - Upload CVs to Talent Hub

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-31: P1: Jon: Possibility to share talent profile summary digitally

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-32: P1: VIDEO INTRO: Trigger Email to Talent

**Description:**
~~Issue:~~

- ~~**Questions do not save - when leaving the section and opening it again, the questions are gone**~~

Email needs to contain:

- some background information (for people who were scouted and don;t know whats happening)
- link to JD on our job baord
- company details who is hiring
- possibility / button to create a profile to be able to be part of process (fast log in)

* **Status:** Todo
* **Priority:** Urgent
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL2-33: Batch "Scouted" or ... needs to right on top next to the name

**Description:**
its not properly visible right now

- **Status:** Canceled
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-35: we need to improve UX design a bit.

- **Status:** Canceled
- **Priority:** Low
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-37: P2: Help Section on Website

**Description:**
In case App is down and no access to intercom, add intercom bubble to website as well

- **Status:** Todo
- **Priority:** High
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-38: Gap/POC: How will companies be able to access our talent pool in the future and add them to their recruitment process

- **Status:** Todo
- **Priority:** High
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-143: Ability to add own picture to Video JD avatars

- **Status:** Backlog
- **Priority:** Low
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-150: Should have option to delete current video JD

**Description:**
Right now users can only edit and create a new one, they should be able to delete and have no video JD if they want.

- **Status:** In Progress
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-161: Button to share video JD with Kaleido Team

**Description:**
Way to easily send video JDs to team so we can post it on our socials.

- **Status:** Todo
- **Priority:** Medium
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-162: PoC required for onboarding of additional team members

- **Status:** Duplicate
- **Priority:** Urgent
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-44: P2: Can we create JD in another language?

- **Status:** In Progress
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-165: Notifications: Only showing notifications for video JD

- **Status:** Todo
- **Priority:** Low
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL2-47: P1: Steve Onboarding

**Description:**

- ~~**website information** from auto-creation feature did not work~~
- ~~**Video intro** process does not work: (1) email link does not open, (2) cell phone recording does not work, (3) Video is not transferred back to the platform - how would we know in the future that there are issues when clients are using it~~
- **Cross-LogIn** happened during the session when clicking on match and rank
- **URL** for talent dashboard did not work
- **pop up for show ranking results** required after matching (people cant find the small button)
- **PDF summary** after print did not work
- **Key Metrics** on Dashboard is not updating

* **Status:** Todo
* **Priority:** Urgent
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL2-50: P2: Sourcing: We should not source any talent that is below 50% match?

**Description:**
Sourcing with more metrics on Apollo

- Need to add a disclaimer that it might return lower ranked candidates, not guaranteed to be a top candidate.

* **Status:** Todo
* **Priority:** High
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL2-51: P1: Carolin and Michael to simplify end to end flow

**Description:**

- too many buttons and steps
- visibility of buttons and steps needs to be improved
- no possibility in video intro question creation to send the video intro to a candidate - need to go back to match and rank
- URL access next to video JD

- **UX Design:**
- buttons and functionaries are disappearing with the dark backround - people start to get lost maybe we need to switch to lighter design like the webpage
- Action Buttons need to have different color so they are easily visible
- All buttons in match and rank should be in one place and have a different color that makes it easy to see them
- Batch "Scouted" or ... needs to right on top next to the name

* **Status:** Todo
* **Priority:** Urgent
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-168: P1: Input field validation Talent & Employer

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-169: Interview process

**Description:**

- A bit unintuitive to get to scheduling an interview. Need to press status then the interview button, maybe there should be a separate "Schedule Interview" button that appears
- Reschedule functionality? Right now they are just told to contact us, but can we automate using calendar invites?

* **Status:** Todo
* **Priority:** High
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-173: Set up video intro should redirect to match and rank page

**Description:**

- Right now after going to the video intro setup from match and rank screen (which happens the questions are not set before you send the first video intro email), it redirects to the culture fit page as shown. Should instead land the user back on the match and rank screen they were on.
- ![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/c9d71084-2357-4bf1-8ae2-d8c0e75d57d2/8739d884-df79-4742-b398-0369c551bd2c?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi9jOWQ3MTA4NC0yMzU3LTRiZjEtOGFlMi1kOGMwZTc1ZDU3ZDIvODczOWQ4ODQtZGY3OS00NzQyLWIzOTgtMDM2OWM1NTFiZDJjIiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.liwyioHnbCw481Uj9US_RIce8qtiNvN7QCzjETK9Wjo)

* **Status:** Todo
* **Priority:** High
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL2-52: P1: Help Talent

**Description:**

## **1. Building Community and Networks**

- **Networking and Referrals**
  - Promote and facilitate access to community forums, mentoring platforms, online job fairs, and networking events, since the vast majority of real opportunities in oversupplied markets come through referrals or personal connections.
- **Showcase Success Stories**
  - Share and celebrate candidate success stories and career pivots to encourage persistence, confidence, and continued engagement.

## **2. Policy, Ecosystem, and Broader Solutions**

- **Collaboration with Local Initiatives**
  - Partner with government and private upskilling and employment support programs (e.g., Nafis, Mawaheb, job readiness centers), creating referral pathways for candidates to access sectoral training and employer introductions.
- **Transparency About Market Realities**
  - Inform job seekers realistically about supply/demand trends, salary expectations, and best strategies for entry or transition, reducing false hope and enabling smarter choices.

## **3. Remote Jobs**

## **Table: Tech & Human Solutions—Directly Helping Job Seekers**

| **Solution Area**      | **How It Helps Job Seekers**                            |     |
| ---------------------- | ------------------------------------------------------- | --- |
| Profile Optimization   | Increases visibility; highlights unique strengths       |     |
| AI Matching            | Surfaces best-fit jobs, not just “volume” opportunities |     |
| Real-Time Feedback     | Reduces false hope; speeds next steps                   |     |
| Upskilling Integration | Builds in-demand skills for better job matches          |     |
| Networking Platforms   | Access to “hidden” jobs, referrals                      |     |
| Transparency/Guidance  | Prepares seekers for realities; encourages persistence  |     |

## **IMPORTANT**

The real solution to helping people get a job, especially in a saturated market, is to combine **smart technology**, **clear communication**, **continued learning**, and **human connection**—not just speed up filtering or redirect rejections. Platforms, employers, and policy-makers all need to work together to empower talent with honest feedback, skill-building, and direct pathways to roles where they can thrive, not just apply.

This approach moves job seekers from frustration to _real opportunity_, making the process fairer, more human, and more effective for all participants.

- **Status:** Triage
- **Priority:** Urgent
- **Project:** nan
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-174: Platform Refresh issues

**Description:**

- Talent Insights : Creating/finishing an insight needs a refresh for the process to be shown as completed and updated.
- When shortlisting a candidate requires a refresh for it to be updated

* **Status:** Todo
* **Priority:** Urgent
* **Project:** Talent
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-177: Feedback people Improvement

**Description:**

- M&R - Remove people from final list
- Your own AI Avatar
- Change My Jobs to My Job Board (needs to differ from Jobs, these are also their jobs)

* **Status:** Todo
* **Priority:** High
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-178: Video Intro Resubmit option

**Description:**

- Need functionality for employer to be able to re-send the video intro email if they change any questions etc.
- Need functionality for the user to resubmit the video after recording a response. There should be a confirmation button for them to either send their current recorded response or record a new one.
- Need functionality to make the video intro optional; should be a button in the email that says "Don't want to record a video intro" or something similar. It should still mark the process as completed but with a different tag to indicate that they didn’t record a video.
- Do we need functionality to delete current response after submitting one and having the option to record a new one?

* **Status:** In Progress
* **Priority:** Urgent
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-180: P2- POC: JD focusses on describing / highlighting problem statement

**Description:**
We need to make sure that it's very clear to the talent what problem needs solving within the company for the respective decision

- **Status:** Todo
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-182: Candidate profile download button on match and rank not working

**Description:**
![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/02301c6d-433d-443e-bc28-8b3e6c25ff36/dea9bdbd-374d-496d-b224-954cc3da4678?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.1y0AcSfVp1RvMBlFUMbC-KYSlYa9l5nRP3WYy94alJs)

![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/ab7ef5b8-0c31-4c07-90a7-ee7f95e7480f/b608065d-48df-4b2d-97e3-f1eed3024417?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.iBG0ki3PtTG7oDiSrfInbe2DkBNA9iXbzGpAlewML5I)

- **Status:** Todo
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-183: Automatic Website fetching resulted in mistakes

- **Status:** In Progress
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-185: Clicking Track Application from email leads to dashboard

**Description:**
![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/6f1b5cb3-3079-41f9-8803-b0c19c1b0e2a/6460ac03-63a4-467a-8275-6fe927a3e8ac?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.Sv0hIoZ6RnVKPE514-pzRBLfSUgK_w-zAZDC6cDhCeo)

- **Status:** Todo
- **Priority:** High
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-186: Add kaleido branding to email verification

**Description:**

- When creating a new account it sends an email to verify your email address.
- The email is from Auth0 as shown. We don't have any indication/reminder/popup to tell users to verify email. This verification email should also have custom Kaleido messaging on it to make it clear that this is email verification for your Kaleido account.

![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/1ffdd5cc-aea3-4f15-8f93-c29f6c0941db/dacc677c-ef5f-4cdc-a201-a0e945067bfd?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.xoQR6Jb5_xVjDKby9MtKB_4ir7nDAgr3eY1qJ6DJOUQ)

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-188: Scheduled interview email images not loading properly

**Description:**
![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/d96914e3-bad1-43e2-94ab-e0a1a2c513fb/cd3460ff-d082-4e58-a92e-65172a51c538?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************.e5zNMix43iQlC89kOejC_ZmcaGBzDlZ3p1jTNJI0Qwg)

- **Status:** Todo
- **Priority:** Low
- **Project:** Talent
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-190: Optimization for mobile usage

**Description:**
App needs to be optimized for mobile, the new UI changes make it not usable on mobile right now.

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Solution
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-191: Talent hub not working

**Description:**
Says no records and doesn't show any candidates

- **Status:** Canceled
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-194: Final step (Hired) application status

**Description:**

- Start date should not be mandatory
-

* **Status:** Backlog
* **Priority:** No priority
* **Project:** nan
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-195: P1 - Ali: Would need to post every job on their own job board (mandatory) - how to we solve that?

**Description:**

- integration required?
- we need to identify how this process would work - we give them a link so talent applies through our platform?

* **Status:** Todo
* **Priority:** Urgent
* **Project:** Employer
* **Creator:** <EMAIL>
* **Completed:** nan

---

## KAL-196: Video Intro - Add hiring manager / HR email as sender email

- **Status:** Todo
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-201: JD can be published without deparment, job title and experience

**Description:**
![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/9924bc98-3bdd-469c-bde7-f154694938fc/094c82ce-afab-4883-9700-b6ce3343104a?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi85OTI0YmM5OC0zYmRkLTQ2OWMtYmRlNy1mMTU0Njk0OTM4ZmMvMDk0YzgyY2UtYWZhYi00ODgzLTk3MDAtYjZjZTMzNDMxMDRhIiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.grQKYFdE_zwErmKnvKzM3FSm7sqEyYUgvQ2K-9g8ACc)

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-202: Error on save threshold

**Description:**
![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/546a892a-51cd-4a42-9025-04dc76588ecc/1297c3aa-34bd-4a19-a211-10bf0b73a93d?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi81NDZhODkyYS01MWNkLTRhNDItOTAyNS0wNGRjNzY1ODhlY2MvMTI5N2MzYWEtMzRiZC00YTE5LWEyMTEtMTBiZjBiNzNhOTNkIiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.lYkKQkmMgs2smWuMDaZIeORxuMVd3muQpRhIZVBkkrg)

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-203: Email messaging for uploaded CV vs scouted candidates needs to be updated

- **Status:** Todo
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-204: StatusManager not showing progress on upload

**Description:**
![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/1ac7d24c-6694-4261-bd9c-4f79c64d666e/2dc2c981-94ad-4da3-8b9a-c08d35627270?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi8xYWM3ZDI0Yy02Njk0LTQyNjEtYmQ5Yy00Zjc5YzY0ZDY2NmUvMmRjMmM5ODEtOTRhZC00ZGEzLThiOWEtYzA4ZDM1NjI3MjcwIiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.ayjKSXe3lhtvNMpJ5V4gYVUqvJwKfvZw6ubirdvUMV0)

- **Status:** Todo
- **Priority:** High
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---

## KAL-205: AI parsing issues, need to improve AI output

**Description:**
Milos has 7 years experience but it gave him 5%

![image.png](https://uploads.linear.app/670183b2-8f44-4d21-aec6-9e632d4a4952/591c0a96-df91-4a04-a05b-e29ac4cebf11/78a55fc1-357f-4e3e-8d0f-21030d21dc52?signature=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXRoIjoiLzY3MDE4M2IyLThmNDQtNGQyMS1hZWM2LTllNjMyZDRhNDk1Mi81OTFjMGE5Ni1kZjkxLTRhMDQtYTA1Yi1lMjlhYzRjZWJmMTEvNzhhNTVmYzEtMzU3Zi00ZTNlLThkMGYtMjEwMzBkMjFkYzUyIiwiaWF0IjoxNzU0Mzk3ODQxLCJleHAiOjQzODUyNzc4NDF9.YRmwfY-xpJjfnYybeFj-c054T3dweXTmqPx13GK3bWI)

- **Status:** Todo
- **Priority:** Urgent
- **Project:** Employer
- **Creator:** <EMAIL>
- **Completed:** nan

---
