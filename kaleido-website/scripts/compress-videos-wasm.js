const { FFmpeg } = require('@ffmpeg/ffmpeg');
const { fetchFile, toBlobURL } = require('@ffmpeg/util');
const fs = require('fs').promises;
const path = require('path');

const INPUT_DIR = path.join(__dirname, '../__VIDEOS');
const OUTPUT_DIR = path.join(__dirname, '../public/videos');

const COMPRESSION_SETTINGS = {
  quality: 28,
  maxWidth: 1920,
  maxHeight: 1080,
  videoBitrate: '2M',
  audioBitrate: '128k',
  preset: 'fast'
};

async function ensureDirectoryExists(dir) {
  try {
    await fs.access(dir);
  } catch {
    await fs.mkdir(dir, { recursive: true });
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function loadFFmpeg() {
  const ffmpeg = new FFmpeg();
  const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';
  
  console.log('Loading FFmpeg WASM...');
  await ffmpeg.load({
    coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
    wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
  });
  
  ffmpeg.on('progress', ({ progress }) => {
    if (progress) {
      process.stdout.write(`Progress: ${Math.round(progress * 100)}%\r`);
    }
  });
  
  return ffmpeg;
}

async function compressVideo(ffmpeg, inputPath, outputPath) {
  const inputFileName = 'input' + path.extname(inputPath);
  const outputFileName = 'output.mp4';
  
  console.log(`\nCompressing: ${path.basename(inputPath)}`);
  
  // Read input file
  const inputData = await fs.readFile(inputPath);
  
  // Write to FFmpeg virtual file system
  await ffmpeg.writeFile(inputFileName, new Uint8Array(inputData));
  
  // Build FFmpeg command
  const ffmpegArgs = [
    '-i', inputFileName,
    '-c:v', 'libx264',
    '-preset', COMPRESSION_SETTINGS.preset,
    '-crf', COMPRESSION_SETTINGS.quality.toString(),
    '-vf', `scale='min(${COMPRESSION_SETTINGS.maxWidth},iw)':min'(${COMPRESSION_SETTINGS.maxHeight},ih)':force_original_aspect_ratio=decrease`,
    '-c:a', 'aac',
    '-b:a', COMPRESSION_SETTINGS.audioBitrate,
    '-b:v', COMPRESSION_SETTINGS.videoBitrate,
    '-movflags', '+faststart',
    '-pix_fmt', 'yuv420p',
    outputFileName
  ];
  
  // Execute compression
  await ffmpeg.exec(ffmpegArgs);
  
  // Read output file
  const outputData = await ffmpeg.readFile(outputFileName);
  
  // Write to disk
  await fs.writeFile(outputPath, outputData);
  
  // Clean up virtual files
  await ffmpeg.deleteFile(inputFileName);
  await ffmpeg.deleteFile(outputFileName);
  
  // Get file sizes
  const originalSize = (await fs.stat(inputPath)).size;
  const compressedSize = (await fs.stat(outputPath)).size;
  const reduction = ((1 - compressedSize / originalSize) * 100).toFixed(2);
  
  console.log(`\n✓ Completed: ${path.basename(outputPath)}`);
  console.log(`  Original: ${formatBytes(originalSize)}`);
  console.log(`  Compressed: ${formatBytes(compressedSize)}`);
  console.log(`  Reduction: ${reduction}%`);
  
  return { originalSize, compressedSize, reduction };
}

async function main() {
  try {
    console.log('Video Compression Script (WASM)');
    console.log('================================\n');
    
    // Ensure output directory exists
    await ensureDirectoryExists(OUTPUT_DIR);
    
    // Get all video files from input directory
    const files = await fs.readdir(INPUT_DIR);
    const videoFiles = files.filter(file => 
      ['.mp4', '.mov', '.avi', '.mkv', '.webm'].includes(path.extname(file).toLowerCase())
    );
    
    if (videoFiles.length === 0) {
      console.log('No video files found in __VIDEOS directory.');
      return;
    }
    
    console.log(`Found ${videoFiles.length} video(s) to compress.\n`);
    
    // Load FFmpeg once
    const ffmpeg = await loadFFmpeg();
    console.log('FFmpeg WASM loaded successfully!\n');
    
    let totalOriginalSize = 0;
    let totalCompressedSize = 0;
    
    // Process each video
    for (const file of videoFiles) {
      const inputPath = path.join(INPUT_DIR, file);
      const outputFileName = path.basename(file, path.extname(file)) + '.mp4';
      const outputPath = path.join(OUTPUT_DIR, outputFileName);
      
      try {
        const result = await compressVideo(ffmpeg, inputPath, outputPath);
        totalOriginalSize += result.originalSize;
        totalCompressedSize += result.compressedSize;
      } catch (error) {
        console.error(`Failed to compress ${file}:`, error.message);
      }
    }
    
    // Summary
    console.log('\n================================');
    console.log('Compression Summary');
    console.log('================================');
    console.log(`Total original size: ${formatBytes(totalOriginalSize)}`);
    console.log(`Total compressed size: ${formatBytes(totalCompressedSize)}`);
    console.log(`Total reduction: ${((1 - totalCompressedSize / totalOriginalSize) * 100).toFixed(2)}%`);
    console.log(`\nCompressed videos saved to: ${OUTPUT_DIR}`);
    
  } catch (error) {
    console.error('Script error:', error);
    process.exit(1);
  }
}

// Run the script
main();