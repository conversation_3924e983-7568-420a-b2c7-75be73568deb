const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs').promises;
const path = require('path');

const INPUT_DIR = path.join(__dirname, '../__VIDEOS');
const OUTPUT_DIR = path.join(__dirname, '../public/videos');

const COMPRESSION_SETTINGS = {
  videoCodec: 'libx264',
  audioCodec: 'aac',
  videoBitrate: '2000k',
  audioBitrate: '128k',
  size: '1920x1080',
  preset: 'fast',
  crf: 28,
  format: 'mp4'
};

async function ensureDirectoryExists(dir) {
  try {
    await fs.access(dir);
  } catch {
    await fs.mkdir(dir, { recursive: true });
  }
}

async function getFileSize(filePath) {
  const stats = await fs.stat(filePath);
  return stats.size;
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function compressVideo(inputPath, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`\nCompressing: ${path.basename(inputPath)}`);
    
    ffmpeg(inputPath)
      .videoCodec(COMPRESSION_SETTINGS.videoCodec)
      .audioCodec(COMPRESSION_SETTINGS.audioCodec)
      .videoBitrate(COMPRESSION_SETTINGS.videoBitrate)
      .audioBitrate(COMPRESSION_SETTINGS.audioBitrate)
      .size(COMPRESSION_SETTINGS.size)
      .outputOptions([
        `-preset ${COMPRESSION_SETTINGS.preset}`,
        `-crf ${COMPRESSION_SETTINGS.crf}`,
        '-movflags +faststart',
        '-pix_fmt yuv420p'
      ])
      .format(COMPRESSION_SETTINGS.format)
      .on('start', (commandLine) => {
        console.log('FFmpeg command:', commandLine);
      })
      .on('progress', (progress) => {
        if (progress.percent) {
          process.stdout.write(`Progress: ${Math.round(progress.percent)}%\r`);
        }
      })
      .on('end', async () => {
        const originalSize = await getFileSize(inputPath);
        const compressedSize = await getFileSize(outputPath);
        const reduction = ((1 - compressedSize / originalSize) * 100).toFixed(2);
        
        console.log(`\n✓ Completed: ${path.basename(outputPath)}`);
        console.log(`  Original: ${formatBytes(originalSize)}`);
        console.log(`  Compressed: ${formatBytes(compressedSize)}`);
        console.log(`  Reduction: ${reduction}%`);
        
        resolve({ originalSize, compressedSize, reduction });
      })
      .on('error', (err) => {
        console.error(`\n✗ Error compressing ${path.basename(inputPath)}:`, err.message);
        reject(err);
      })
      .save(outputPath);
  });
}

async function main() {
  try {
    console.log('Video Compression Script');
    console.log('========================\n');
    
    // Ensure output directory exists
    await ensureDirectoryExists(OUTPUT_DIR);
    
    // Get all video files from input directory
    const files = await fs.readdir(INPUT_DIR);
    const videoFiles = files.filter(file => 
      ['.mp4', '.mov', '.avi', '.mkv', '.webm'].includes(path.extname(file).toLowerCase())
    );
    
    if (videoFiles.length === 0) {
      console.log('No video files found in __VIDEOS directory.');
      return;
    }
    
    console.log(`Found ${videoFiles.length} video(s) to compress.\n`);
    
    let totalOriginalSize = 0;
    let totalCompressedSize = 0;
    
    // Process each video
    for (const file of videoFiles) {
      const inputPath = path.join(INPUT_DIR, file);
      const outputFileName = path.basename(file, path.extname(file)) + '.mp4';
      const outputPath = path.join(OUTPUT_DIR, outputFileName);
      
      try {
        const result = await compressVideo(inputPath, outputPath);
        totalOriginalSize += result.originalSize;
        totalCompressedSize += result.compressedSize;
      } catch (error) {
        console.error(`Failed to compress ${file}:`, error.message);
      }
    }
    
    // Summary
    console.log('\n========================');
    console.log('Compression Summary');
    console.log('========================');
    console.log(`Total original size: ${formatBytes(totalOriginalSize)}`);
    console.log(`Total compressed size: ${formatBytes(totalCompressedSize)}`);
    console.log(`Total reduction: ${((1 - totalCompressedSize / totalOriginalSize) * 100).toFixed(2)}%`);
    console.log(`\nCompressed videos saved to: ${OUTPUT_DIR}`);
    
  } catch (error) {
    console.error('Script error:', error);
    process.exit(1);
  }
}

// Check if ffmpeg is installed
ffmpeg.getAvailableFormats((err, formats) => {
  if (err) {
    console.error('Error: FFmpeg is not installed or not in PATH.');
    console.error('Please install FFmpeg first:');
    console.error('  macOS: brew install ffmpeg');
    console.error('  Ubuntu/Debian: sudo apt update && sudo apt install ffmpeg');
    console.error('  Windows: Download from https://ffmpeg.org/download.html');
    process.exit(1);
  }
  
  // Run the main function
  main();
});