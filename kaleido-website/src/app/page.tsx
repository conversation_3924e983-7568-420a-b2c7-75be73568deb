"use client";

import AIEISection from "@/components/sections/AIEISection";
import CTASection from "@/components/sections/CTASection";
import FAQSection from "@/components/sections/FAQSection";
import HeroCarouselSection from "@/components/sections/HeroCarouselSection";
import JobBoardSection from "@/components/sections/JobBoardSection";
import MissionSection from "@/components/sections/MissionSection";
import PartnershipSection from "@/components/sections/PartnershipSection";
import PricingSection from "@/components/sections/PricingSection";
import ProblemSection from "@/components/sections/ProblemSection";
import ReferralPartnersSection from "@/components/sections/ReferralPartnersSection";
import SolutionsSection from "@/components/sections/SolutionsSection";
import WaitlistSection from "@/components/sections/WaitlistSection";
import WhyKaleidoTalentSection from "@/components/sections/WhyKaleidoTalentSection";

export default function Home() {
  return (
    <div className="overflow-x-hidden bg-white">
      <section id="home">
        <HeroCarouselSection />
      </section>
      <section id="ai-ei">
        <AIEISection />
      </section>
      {/* <ServicesSection /> */}
      <section id="problem">
        <ProblemSection />
      </section>
      <section id="solutions">
        <SolutionsSection />
      </section>
      <section id="mission">
        <MissionSection />
      </section>
      <section id="pricing">
        <PricingSection />
      </section>
      <section id="why-kaleido-talent">
        <WhyKaleidoTalentSection />
      </section>
      <section id="job-board">
        <JobBoardSection />
      </section>
      <section id="waitlist">
        <WaitlistSection />
      </section>
      <section id="partnership">
        <PartnershipSection />
      </section>
      <section id="referral-partners">
        <ReferralPartnersSection />
      </section>

      <section id="faq">
        <FAQSection />
      </section>
      {/* <NewsletterSection /> */}
      <section id="contact">
        <CTASection />
      </section>
    </div>
  );
}
