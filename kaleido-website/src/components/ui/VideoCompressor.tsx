'use client';

import React, { useState, useRef } from 'react';
import { compressVideo, getVideoMetadata, formatFileSize } from '@/utils/videoCompression';

interface CompressionResult {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  downloadUrl: string;
}

export default function VideoCompressor() {
  const [isCompressing, setIsCompressing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<CompressionResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setError(null);
    setResult(null);
    setIsCompressing(true);
    setProgress(0);

    try {
      // Get original video metadata
      const metadata = await getVideoMetadata(file);
      console.log('Original video metadata:', metadata);

      // Compress the video
      const compressedBlob = await compressVideo(file, {
        quality: 28,
        maxWidth: 1920,
        maxHeight: 1080,
        videoBitrate: '2M',
        audioBitrate: '128k',
        preset: 'fast'
      });

      // Create download URL
      const downloadUrl = URL.createObjectURL(compressedBlob);

      // Calculate compression ratio
      const compressionRatio = ((file.size - compressedBlob.size) / file.size) * 100;

      setResult({
        originalSize: file.size,
        compressedSize: compressedBlob.size,
        compressionRatio,
        downloadUrl
      });

      setProgress(100);
    } catch (err) {
      console.error('Compression error:', err);
      setError(err instanceof Error ? err.message : 'Failed to compress video');
    } finally {
      setIsCompressing(false);
    }
  };

  const handleDownload = () => {
    if (!result) return;

    const a = document.createElement('a');
    a.href = result.downloadUrl;
    a.download = 'compressed-video.mp4';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleReset = () => {
    if (result) {
      URL.revokeObjectURL(result.downloadUrl);
    }
    setResult(null);
    setError(null);
    setProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Video Compressor</h2>
      
      <div className="mb-6">
        <label className="block mb-2 text-sm font-medium text-gray-700">
          Select a video file to compress
        </label>
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*"
          onChange={handleFileSelect}
          disabled={isCompressing}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50"
        />
      </div>

      {isCompressing && (
        <div className="mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Compressing...</span>
            <span className="text-sm font-medium text-gray-700">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-500"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {result && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <h3 className="font-semibold text-green-800 mb-2">Compression Complete!</h3>
          <div className="space-y-1 text-sm text-green-700">
            <p>Original size: {formatFileSize(result.originalSize)}</p>
            <p>Compressed size: {formatFileSize(result.compressedSize)}</p>
            <p>Reduction: {result.compressionRatio.toFixed(2)}%</p>
          </div>
          <div className="mt-4 flex gap-3">
            <button
              onClick={handleDownload}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Download Compressed Video
            </button>
            <button
              onClick={handleReset}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Compress Another
            </button>
          </div>
        </div>
      )}
    </div>
  );
}