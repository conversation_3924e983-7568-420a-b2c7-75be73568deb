"use client";

import React from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { motion } from "framer-motion";
import { 
  Users, 
  DollarSign, 
  TrendingUp, 
  Share2, 
  ChevronRight,
  Sparkles
} from "lucide-react";
import Link from "next/link";

const ReferralPartnersSection = () => {
  const { t } = useTranslation();
  const appUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  const actionCards = [
    {
      icon: DollarSign,
      title: t("home.referralPartners.cards.earn"),
      gradient: "from-emerald-500 to-teal-600",
      link: `${appUrl}/referral-partner/onboarding`
    },
    {
      icon: Users,
      title: t("home.referralPartners.cards.connect"),
      gradient: "from-blue-500 to-indigo-600",
      link: `${appUrl}/referral-partner/onboarding`
    },
    {
      icon: TrendingUp,
      title: t("home.referralPartners.cards.grow"),
      gradient: "from-purple-500 to-pink-600",
      link: `${appUrl}/referral-partner`
    },
    {
      icon: Share2,
      title: t("home.referralPartners.cards.share"),
      gradient: "from-orange-500 to-red-600",
      link: `${appUrl}/referral-partner/onboarding`
    }
  ];

  return (
    <section className="relative h-[90vh] overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 z-0">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src="/videos/bubbles-color-and-happy-black-woman.mp4" type="video/mp4" />
        </video>
        <div className="absolute inset-0" style={{
          background: 'linear-gradient(to top, rgb(88, 28, 135) 0%, rgba(88, 28, 135, 0.8) 20%, rgba(88, 28, 135, 0.3) 35%, transparent 45%)'
        }} />
        {/* Smooth transition overlay for video loop */}
        <div 
          className="absolute inset-0 bg-gradient-to-r from-transparent via-black/5 to-transparent"
          style={{
            animation: 'fadeInOut 3s ease-in-out infinite'
          }}
        />
        <style jsx>{`
          @keyframes fadeInOut {
            0%, 100% { opacity: 0; }
            50% { opacity: 0.4; }
          }
        `}</style>
      </div>

      <div className="relative z-10 h-full flex flex-col">
        {/* Spacer to push content to bottom */}
        <div className="flex-1" />

        {/* Bottom Content with Header and Icons */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-12">
          {/* Header Text */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-8"
          >
            <div className="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-full text-white/90 text-sm font-medium mb-4 border border-white/20">
              <Sparkles className="w-4 h-4 mr-2" />
              {t("home.referralPartners.badge")}
            </div>
            <h2 className="relative max-w-2xl mx-auto">
              <span className="text-lg sm:text-xl lg:text-2xl font-medium text-white/90 drop-shadow-md block leading-none tracking-tight">
                {t("home.referralPartners.title.part1")}
              </span>
              <span className="relative inline-block mt-1 mb-1">
                <span className="text-3xl sm:text-4xl lg:text-5xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-300 via-pink-300 to-purple-300 drop-shadow-2xl italic leading-none">
                  {t("home.referralPartners.title.highlight")}
                </span>
                {/* Decorative underline */}
                <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-purple-400 to-transparent rounded-full" />
              </span>
              <span className="text-xl sm:text-2xl lg:text-3xl font-bold text-white drop-shadow-xl block leading-none">
                {t("home.referralPartners.title.part2")}
              </span>
            </h2>
            <p className="text-sm sm:text-base lg:text-lg text-white/80 mt-4 max-w-2xl mx-auto">
              {t("home.referralPartners.subtitle")}
            </p>
          </motion.div>

          {/* Action Icons */}
          <div className="flex items-center justify-center gap-8 md:gap-12 mb-8">
            {actionCards.map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative"
              >
                <Link
                  href={card.link}
                  className="group flex flex-col items-center"
                >
                  {/* Connecting Line (except for last item) */}
                  {index < actionCards.length - 1 && (
                    <div className="hidden md:block absolute left-full top-8 w-8 md:w-12 h-0.5 bg-gradient-to-r from-white/40 to-white/20" />
                  )}
                  
                  {/* Icon Circle */}
                  <div className="relative mb-3">
                    <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${card.gradient} p-4 group-hover:scale-110 transition-all duration-300 shadow-xl`}>
                      <card.icon className="w-full h-full text-white" />
                    </div>
                    <div className={`absolute inset-0 rounded-full bg-gradient-to-br ${card.gradient} blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300`} />
                  </div>
                  
                  {/* Title */}
                  <h3 className="text-white font-medium text-sm text-center opacity-90 group-hover:opacity-100 transition-opacity">
                    {card.title}
                  </h3>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              href={`${appUrl}/referral-partner/onboarding`}
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-full hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              {t("home.referralPartners.cta.primaryButton")}
              <ChevronRight className="w-5 h-5 ml-2" />
            </Link>
            <Link
              href={`${appUrl}/referral-partner`}
              className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm text-white font-medium rounded-full hover:bg-white/20 transition-all duration-300 border border-white/30"
            >
              {t("home.referralPartners.cta.secondaryButton")}
            </Link>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ReferralPartnersSection;