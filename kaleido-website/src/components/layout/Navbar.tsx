'use client';

import React, { useEffect, useState } from "react";


import Image from "next/image";
import Link from "next/link";

import LanguageSwitcher from "@/components/LanguageSwitcher";

import { useTranslation } from "@/hooks/useTranslation";
import { usePathname } from "next/navigation";
import { CandidatesDropdown } from "./CandidatesDropdown";

// Smooth scroll utility function
const scrollToSection = (sectionId: string, pathname?: string) => {
  // If we're not on the home page, redirect to home page with the section
  if (pathname && pathname !== "/") {
    window.location.href = `/#${sectionId}`;
    return;
  }

  const element = document.getElementById(sectionId);
  if (element) {
    const headerOffset = 80; // Account for fixed header height
    const elementPosition = element.getBoundingClientRect().top;
    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

    window.scrollTo({
      top: offsetPosition,
      behavior: "smooth",
    });
  }
};

interface NavbarProps {
  className?: string;
}

export const Navbar: React.FC<NavbarProps> = ({ className = "" }) => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Check if we're on the waitlist page
  const isWaitlistPage = pathname === "/waitlist";

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [scrolled]);

  return (
    <header
      className={`fixed top-0 start-0 end-0 z-50 transition-all duration-300 ${
        scrolled ? "bg-white/95 backdrop-blur-md shadow-lg" : "bg-transparent"
      } ${className}`}>
      <div className="w-full px-4 sm:px-6 lg:px-8 py-3 sm:py-4 flex items-center justify-between">
        {/* Logo - Far Left */}
        <Link
          href="/"
          className="hover:scale-105 transition-transform duration-200 flex-shrink-0"
          onClick={() => scrollToSection("home")}>
          <Image
            src={
              scrolled
                ? "/images/logos/kaleido-logo-full.webp"
                : "/images/logos/kaleido-logo-full-white.webp"
            }
            alt={t("common.ui.logo.alt")}
            width={200}
            height={50}
            className="h-10 sm:h-12 w-auto"
            priority
          />
        </Link>

        {/* Desktop Navigation - Center */}
        <nav className="hidden lg:flex items-center justify-center flex-1 mx-8">
          <div className="flex space-x-6 xl:space-x-8 rtl:space-x-reverse">
            <button
              type="button"
              onClick={() => scrollToSection("problem", pathname)}
              className={`font-medium transition-colors px-2 py-1 rounded-md ${
                scrolled
                  ? "text-gray-700 hover:text-primary hover:bg-gray-50"
                  : "text-white hover:text-gray-200 hover:bg-white/10"
              }`}>
              {t("common.navigation.problem")}
            </button>
            <button
              type="button"
              onClick={() => scrollToSection("solutions", pathname)}
              className={`font-medium transition-colors px-2 py-1 rounded-md ${
                scrolled
                  ? "text-gray-700 hover:text-primary hover:bg-gray-50"
                  : "text-white hover:text-gray-200 hover:bg-white/10"
              }`}>
              {t("common.navigation.solutions")}
            </button>
            <button
              type="button"
              onClick={() => scrollToSection("pricing", pathname)}
              className={`font-medium transition-colors px-2 py-1 rounded-md ${
                scrolled
                  ? "text-gray-700 hover:text-primary hover:bg-gray-50"
                  : "text-white hover:text-gray-200 hover:bg-white/10"
              }`}>
              {t("common.navigation.pricing")}
            </button>
            <button
              type="button"
              onClick={() => scrollToSection("job-board", pathname)}
              className={`font-medium transition-colors px-2 py-1 rounded-md ${
                scrolled
                  ? "text-gray-700 hover:text-primary hover:bg-gray-50"
                  : "text-white hover:text-gray-200 hover:bg-white/10"
              }`}>
              Open Jobs
            </button>
            <button
              type="button"
              onClick={() => scrollToSection("referral-partners", pathname)}
              className={`font-medium transition-colors px-2 py-1 rounded-md ${
                scrolled
                  ? "text-gray-700 hover:text-primary hover:bg-gray-50"
                  : "text-white hover:text-gray-200 hover:bg-white/10"
              }`}>
              {t("common.navigation.referralPartners")}
            </button>
            {!isWaitlistPage && (
              <button
                type="button"
                onClick={() => scrollToSection("waitlist", pathname)}
                className="bg-gradient-primary text-white px-4 xl:px-6 py-2 rounded-full hover:shadow-lg transition-all text-sm xl:text-base font-medium">
                {t("common.buttons.letsWorkTogether")}
              </button>
            )}
          </div>
        </nav>

        {/* Desktop CTA and Language Switcher - Far Right */}
        <div className="hidden lg:flex items-center space-x-3 xl:space-x-4 rtl:space-x-reverse flex-shrink-0">
          <LanguageSwitcher mode="toggle" scrolled={scrolled} />
          <CandidatesDropdown scrolled={scrolled} />
        </div>

        {/* Mobile Menu Button */}
        <div className="lg:hidden flex items-center space-x-2 rtl:space-x-reverse">
          <LanguageSwitcher
            mode="select"
            className="mr-2"
            scrolled={scrolled}
          />
          <button
            type="button"
            className={`focus:outline-none p-2 rounded-md transition-colors ${
              scrolled
                ? "text-gray-700 hover:bg-gray-100"
                : "text-white hover:bg-white/10"
            }`}
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label={
              mobileMenuOpen
                ? t("common.ui.menu.close")
                : t("common.ui.menu.open")
            }
            aria-expanded={mobileMenuOpen}>
            {mobileMenuOpen ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`lg:hidden bg-white shadow-lg transition-all duration-300 overflow-hidden ${
          mobileMenuOpen ? "max-h-screen" : "max-h-0"
        }`}
        aria-hidden={!mobileMenuOpen}>
        <div className="container mx-auto px-4 sm:px-6 py-4 flex flex-col space-y-3">
          <button
            type="button"
            onClick={() => {
              scrollToSection("problem");
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            {t("common.navigation.problem")}
          </button>
          <button
            type="button"
            onClick={() => {
              scrollToSection("solutions");
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            {t("common.navigation.solutions")}
          </button>
          <button
            type="button"
            onClick={() => {
              scrollToSection("pricing");
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            {t("common.navigation.pricing")}
          </button>
          <button
            type="button"
            onClick={() => {
              scrollToSection("job-board", pathname);
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            Open Jobs
          </button>
          <button
            type="button"
            onClick={() => {
              scrollToSection("referral-partners", pathname);
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            {t("common.navigation.referralPartners")}
          </button>
          {/* <button
            type="button"
            onClick={() => {
              scrollToSection("waitlist");
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            {t("common.navigation.exclusiveAccess")}
          </button> */}
          <button
            type="button"
            onClick={() => {
              scrollToSection("partnership");
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            {t("common.navigation.partnership")}
          </button>
          <button
            type="button"
            onClick={() => {
              scrollToSection("faq");
              setMobileMenuOpen(false);
            }}
            className="font-medium text-gray-700 hover:text-primary transition-colors py-2 text-left">
            {t("common.navigation.faq")}
          </button>
          <div className="border-t border-gray-200 pt-2">
            <p className="font-medium text-gray-700 py-2">For Candidates</p>
            <div className="pl-4 space-y-2">
              <a
                href={`${
                  process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
                }/api/auth/login`}
                className="block text-gray-600 hover:text-primary transition-colors py-1">
                Sign In
              </a>
              <a
                href={`${
                  process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
                }/api/auth/signup`}
                className="block text-gray-600 hover:text-primary transition-colors py-1">
                Join Now
              </a>
            </div>
          </div>
          <div className="pt-2 border-t border-gray-200">
            <button
              type="button"
              onClick={() => {
                scrollToSection("waitlist");
                setMobileMenuOpen(false);
              }}
              className="bg-gradient-primary text-white px-6 py-3 rounded-full hover:shadow-lg transition-all text-center w-full">
              {t("common.buttons.letsWorkTogether")}
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
