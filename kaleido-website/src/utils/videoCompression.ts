import { FFmpeg } from '@ffmpeg/ffmpeg';
import { toBlobURL, fetchFile } from '@ffmpeg/util';

interface CompressionOptions {
  quality?: number; // CRF value: 0-51 (lower = better quality, larger file)
  maxWidth?: number;
  maxHeight?: number;
  videoBitrate?: string; // e.g., '1M' for 1 Mbps
  audioBitrate?: string; // e.g., '128k' for 128 kbps
  preset?: 'ultrafast' | 'superfast' | 'veryfast' | 'faster' | 'fast' | 'medium' | 'slow' | 'slower' | 'veryslow';
}

const DEFAULT_OPTIONS: CompressionOptions = {
  quality: 28, // Good balance of quality and file size
  maxWidth: 1920,
  maxHeight: 1080,
  videoBitrate: '2M',
  audioBitrate: '128k',
  preset: 'fast'
};

let ffmpeg: FFmpeg | null = null;

async function loadFFmpeg(): Promise<FFmpeg> {
  if (!ffmpeg) {
    ffmpeg = new FFmpeg();
    const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';
    await ffmpeg.load({
      coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
      wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
    });
  }
  return ffmpeg;
}

export async function compressVideo(
  inputFile: File,
  options: CompressionOptions = {}
): Promise<Blob> {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
  const ffmpegInstance = await loadFFmpeg();
  
  const inputFileName = 'input' + getFileExtension(inputFile.name);
  const outputFileName = 'output.mp4';
  
  // Write input file to FFmpeg virtual file system
  await ffmpegInstance.writeFile(inputFileName, await fetchFile(inputFile));
  
  // Build FFmpeg command
  const ffmpegArgs = [
    '-i', inputFileName,
    '-c:v', 'libx264',
    '-preset', mergedOptions.preset!,
    '-crf', mergedOptions.quality!.toString(),
    '-vf', `scale='min(${mergedOptions.maxWidth},iw)':min'(${mergedOptions.maxHeight},ih)':force_original_aspect_ratio=decrease`,
    '-c:a', 'aac',
    '-b:a', mergedOptions.audioBitrate!,
    '-b:v', mergedOptions.videoBitrate!,
    '-movflags', '+faststart', // Optimize for web streaming
    '-pix_fmt', 'yuv420p', // Ensure compatibility
    outputFileName
  ];
  
  // Execute compression
  await ffmpegInstance.exec(ffmpegArgs);
  
  // Read the output file
  const data = await ffmpegInstance.readFile(outputFileName);
  
  // Clean up
  await ffmpegInstance.deleteFile(inputFileName);
  await ffmpegInstance.deleteFile(outputFileName);
  
  // Convert to Blob
  return new Blob([data], { type: 'video/mp4' });
}

export async function getVideoMetadata(file: File): Promise<{
  duration: number;
  width: number;
  height: number;
  size: number;
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.preload = 'metadata';
    
    video.onloadedmetadata = () => {
      resolve({
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        size: file.size
      });
      URL.revokeObjectURL(video.src);
    };
    
    video.onerror = () => {
      reject(new Error('Failed to load video metadata'));
      URL.revokeObjectURL(video.src);
    };
    
    video.src = URL.createObjectURL(file);
  });
}

function getFileExtension(filename: string): string {
  const lastDot = filename.lastIndexOf('.');
  return lastDot === -1 ? '' : filename.substring(lastDot);
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}