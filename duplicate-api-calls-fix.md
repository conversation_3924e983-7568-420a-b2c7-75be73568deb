# Duplicate API Calls Fix

## Issue Summary
When saving job threshold changes in the JobForm component, the application was making multiple duplicate API calls (20+ requests) for a single save operation. All requests were being traced to FileUploader.tsx:824.

## Root Causes Identified

1. **Multiple API Calls from JobForm**: The save handler was making both `updateJob` and `updateJobMatchRankCriteria` API calls for the same data.

2. **Event Cascade**: The save operation was triggering various event listeners that caused unnecessary refreshes:
   - `matchRankJobCompleted` event triggered FileUploader refresh
   - `uploadJobStatusChanged` event triggered upload completion handlers
   - Multiple `useEffect` hooks listening for changes

3. **Missing Event Filtering**: Event handlers weren't checking the source/action type before triggering refreshes.

## Fixes Applied

### 1. Frontend - JobForm Component (JobForm.tsx)
- **Removed duplicate API call** to `updateJobMatchRankCriteria` (line 330)
- **Added concurrent save prevention** with `isSaving` check (line 283)
- **Added debug logging** to track save operations

### 2. Frontend - FileUploader Component (FileUploader.tsx)
- **Added source filtering** for upload status events (lines 139-143)
  - Only processes events from actual upload workers
- **Added action filtering** for matchRank events (line 875)
  - Only refreshes for actual matchRank operations, not simple saves
- **Conditional refresh logic** (lines 231-252)
  - Only refreshes job criteria when new candidates are actually uploaded
- **Added debug logging** to track event processing

### 3. Backend - JobService (job.service.ts:515-601)
- **Fixed deadlock issue** that was causing retries and multiple requests
- **Added retry logic** with exponential backoff
- **Improved transaction handling** with proper row locking

## Testing Checklist

1. **Single Save Test**
   - Open a job in MatchRank view
   - Change thresholds
   - Click Save
   - Check browser console - should see:
     - "Starting job save for jobId: [id]"
     - Single PATCH request to `/api/jobs/[id]`
     - No duplicate GET requests

2. **Multiple Field Changes**
   - Change both thresholds and requirements
   - Save once
   - Verify only one API call is made

3. **Rapid Save Test**
   - Click Save multiple times quickly
   - Should see "Save already in progress, skipping duplicate save"
   - Only one API request should go through

4. **Upload vs Save Distinction**
   - Save job changes - should NOT trigger upload refresh logic
   - Upload candidates - should trigger refresh logic
   - Check console for appropriate log messages

## Expected Console Output

### For Job Save:
```
Starting job save for jobId: ea5d9267-d52e-4497-b9c6-1ac696d70e2a
Skipping non-upload status change event from source: [if any]
```

### For Candidate Upload:
```
Processing upload completion for job: ea5d9267-d52e-4497-b9c6-1ac696d70e2a
Refreshing job criteria after successful upload with new candidates
```

## Monitoring Points

1. **Network Tab**: Should show only 1-2 API calls per save:
   - PATCH to `/api/jobs/[id]`
   - Possibly one GET for UI refresh (but not 20+)

2. **Console Logs**: Watch for:
   - Source filtering messages
   - Save progress indicators
   - No "refreshing job criteria" on simple saves

3. **Backend Logs**: Should show:
   - Single update operation
   - No deadlock errors
   - Clean transaction completion